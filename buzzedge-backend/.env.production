# Production Environment Variables for BuzzEdge Backend
# Copy these to your deployment platform (Railway, Render, etc.)

# Application
NODE_ENV=production
PORT=5001

# Database
MONGODB_URI=mongodb+srv://bhavanishankar:<EMAIL>/buzzedge?retryWrites=true&w=majority&appName=Cluster0

# Authentication
JWT_SECRET=your-super-secret-jwt-key-here-make-it-long-and-random-at-least-32-characters

# CORS & Frontend
FRONTEND_URL=https://your-frontend-domain.vercel.app
CORS_ORIGIN=https://your-frontend-domain.vercel.app

# AI Integration (Optional but recommended)
GEMINI_API_KEY=your-gemini-api-key-here

# Email Configuration (Optional - for future features)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password

# Security
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Logging
LOG_LEVEL=info
