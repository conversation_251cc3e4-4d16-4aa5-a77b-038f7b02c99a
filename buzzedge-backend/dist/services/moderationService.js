"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ModerationService = void 0;
const generative_ai_1 = require("@google/generative-ai");
// Initialize Gemini client (optional - for advanced AI moderation)
const gemini = process.env.GEMINI_API_KEY
    ? new generative_ai_1.GoogleGenerativeAI(process.env.GEMINI_API_KEY)
    : null;
class ModerationService {
    /**
     * Basic rule-based moderation
     */
    static async basicModeration(content, authorEmail) {
        const reasons = [];
        let confidence = 100;
        let category = "clean";
        // Check for spam keywords
        const spamScore = this.checkSpamKeywords(content);
        if (spamScore > 0.1) {
            // Lower threshold for better detection
            reasons.push("Contains spam keywords");
            confidence -= 40;
            category = "spam";
        }
        // Check for inappropriate content
        const inappropriateScore = this.checkInappropriateContent(content);
        if (inappropriateScore > 0.2) {
            reasons.push("Contains inappropriate language");
            confidence -= 40;
            category = "inappropriate";
        }
        // Check for suspicious patterns
        const suspiciousScore = this.checkSuspiciousPatterns(content);
        if (suspiciousScore > 0.2) {
            // Lower threshold for better detection
            reasons.push("Contains suspicious patterns");
            confidence -= 25;
            category = "suspicious";
        }
        // Check content length
        if (content.length < 10) {
            reasons.push("Content too short");
            confidence -= 15;
        }
        if (content.length > 2000) {
            reasons.push("Content too long");
            confidence -= 10;
        }
        // Check for excessive links
        const linkCount = (content.match(/https?:\/\/[^\s]+/gi) || []).length;
        if (linkCount > 2) {
            reasons.push("Too many links");
            confidence -= 25;
            category = "spam";
        }
        // Check for new user patterns (if email is new)
        if (await this.isNewUser(authorEmail)) {
            confidence -= 10; // Slightly more cautious with new users
        }
        const isApproved = confidence >= 70 && category === "clean";
        return {
            isApproved,
            confidence,
            reasons,
            category,
            autoModerated: true,
        };
    }
    /**
     * AI-powered moderation using Google Gemini
     */
    static async aiModeration(content) {
        if (!gemini) {
            // Fallback to basic moderation if Gemini is not configured
            return this.basicModeration(content, "");
        }
        try {
            // Get the Gemini model with safety settings
            const model = gemini.getGenerativeModel({
                model: "gemini-1.5-flash",
                safetySettings: [
                    {
                        category: generative_ai_1.HarmCategory.HARM_CATEGORY_HARASSMENT,
                        threshold: generative_ai_1.HarmBlockThreshold.BLOCK_LOW_AND_ABOVE,
                    },
                    {
                        category: generative_ai_1.HarmCategory.HARM_CATEGORY_HATE_SPEECH,
                        threshold: generative_ai_1.HarmBlockThreshold.BLOCK_LOW_AND_ABOVE,
                    },
                    {
                        category: generative_ai_1.HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT,
                        threshold: generative_ai_1.HarmBlockThreshold.BLOCK_LOW_AND_ABOVE,
                    },
                    {
                        category: generative_ai_1.HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT,
                        threshold: generative_ai_1.HarmBlockThreshold.BLOCK_LOW_AND_ABOVE,
                    },
                ],
            });
            // Create a prompt for content moderation
            const prompt = `
        Analyze this comment for content moderation. Determine if it should be approved or rejected.

        Comment: "${content}"

        Check for:
        1. Spam or promotional content
        2. Hate speech or harassment
        3. Inappropriate language
        4. Harmful or dangerous content
        5. Sexual or explicit content

        Respond with a JSON object containing:
        - "approved": boolean (true if safe, false if should be rejected)
        - "confidence": number (0-100, how confident you are in the decision)
        - "reasons": array of strings (specific issues found, if any)
        - "category": string ("clean", "spam", "inappropriate", "suspicious")

        Example: {"approved": true, "confidence": 95, "reasons": [], "category": "clean"}
      `;
            const result = await model.generateContent(prompt);
            const response = result.response;
            const text = response.text();
            // Parse the JSON response
            let analysis;
            try {
                // Extract JSON from the response (remove any markdown formatting)
                const jsonMatch = text.match(/\{[\s\S]*\}/);
                if (jsonMatch) {
                    analysis = JSON.parse(jsonMatch[0]);
                }
                else {
                    throw new Error("No JSON found in response");
                }
            }
            catch (parseError) {
                console.error("Failed to parse Gemini response:", text);
                // Fallback to basic moderation
                return this.basicModeration(content, "");
            }
            return {
                isApproved: analysis.approved || false,
                confidence: analysis.confidence || 50,
                reasons: analysis.reasons || [],
                category: analysis.category || "suspicious",
                autoModerated: true,
            };
        }
        catch (error) {
            console.error("Gemini moderation failed:", error);
            // Fallback to basic moderation
            return this.basicModeration(content, "");
        }
    }
    /**
     * Combined moderation strategy
     */
    static async moderateComment(content, authorEmail, useAI = true) {
        // First, try AI moderation if available
        if (useAI && gemini) {
            const aiResult = await this.aiModeration(content);
            // If AI is confident, use its result
            if (aiResult.confidence >= 85) {
                return aiResult;
            }
        }
        // Use basic rule-based moderation
        const basicResult = await this.basicModeration(content, authorEmail);
        // Combine results if both were used
        return basicResult;
    }
    /**
     * Check for spam keywords
     */
    static checkSpamKeywords(content) {
        const lowerContent = content.toLowerCase();
        const foundKeywords = this.spamKeywords.filter((keyword) => lowerContent.includes(keyword.toLowerCase()));
        return foundKeywords.length / this.spamKeywords.length;
    }
    /**
     * Check for inappropriate content
     */
    static checkInappropriateContent(content) {
        const lowerContent = content.toLowerCase();
        const foundKeywords = this.inappropriateKeywords.filter((keyword) => lowerContent.includes(keyword.toLowerCase()));
        return foundKeywords.length / this.inappropriateKeywords.length;
    }
    /**
     * Check for suspicious patterns
     */
    static checkSuspiciousPatterns(content) {
        let suspiciousCount = 0;
        this.suspiciousPatterns.forEach((pattern) => {
            if (pattern.test(content)) {
                suspiciousCount++;
            }
        });
        return suspiciousCount / this.suspiciousPatterns.length;
    }
    /**
     * Check if user is new (for reputation-based moderation)
     */
    static async isNewUser(email) {
        // This would check your user database
        // For now, return false (implement based on your user model)
        return false;
    }
    /**
     * Auto-approve trusted users
     */
    static async isTrustedUser(email) {
        // Implement logic to check if user is trusted
        // Could be based on:
        // - Number of approved comments
        // - Account age
        // - Verification status
        // - Manual trust list
        return false;
    }
    /**
     * Rate limiting check
     */
    static async checkRateLimit(email, ipAddress) {
        // Implement rate limiting logic
        // Could use Redis or in-memory cache
        // For now, return true (no rate limit exceeded)
        return true;
    }
}
exports.ModerationService = ModerationService;
// Spam keywords and patterns
ModerationService.spamKeywords = [
    "buy now",
    "click here",
    "free money",
    "make money fast",
    "viagra",
    "casino",
    "lottery",
    "winner",
    "congratulations",
    "urgent",
    "limited time",
    "act now",
    "guaranteed",
    "risk free",
    "no obligation",
];
ModerationService.inappropriateKeywords = [
    "hate",
    "kill",
    "die",
    "stupid",
    "idiot",
    "moron",
    "dumb",
    // Add more inappropriate words as needed
];
ModerationService.suspiciousPatterns = [
    /https?:\/\/[^\s]+/gi, // URLs
    /\b\d{10,}\b/g, // Phone numbers
    /[A-Z]{5,}/g, // Excessive caps
    /(.)\1{4,}/g, // Repeated characters
];
exports.default = ModerationService;
//# sourceMappingURL=moderationService.js.map