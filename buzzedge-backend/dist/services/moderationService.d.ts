export interface ModerationResult {
    isApproved: boolean;
    confidence: number;
    reasons: string[];
    category: "spam" | "inappropriate" | "clean" | "suspicious";
    autoModerated: boolean;
}
export declare class ModerationService {
    private static spamKeywords;
    private static inappropriateKeywords;
    private static suspiciousPatterns;
    /**
     * Basic rule-based moderation
     */
    static basicModeration(content: string, authorEmail: string): Promise<ModerationResult>;
    /**
     * AI-powered moderation using Google Gemini
     */
    static aiModeration(content: string): Promise<ModerationResult>;
    /**
     * Combined moderation strategy
     */
    static moderateComment(content: string, authorEmail: string, useAI?: boolean): Promise<ModerationResult>;
    /**
     * Check for spam keywords
     */
    private static checkSpamKeywords;
    /**
     * Check for inappropriate content
     */
    private static checkInappropriateContent;
    /**
     * Check for suspicious patterns
     */
    private static checkSuspiciousPatterns;
    /**
     * Check if user is new (for reputation-based moderation)
     */
    private static isNewUser;
    /**
     * Auto-approve trusted users
     */
    static isTrustedUser(email: string): Promise<boolean>;
    /**
     * Rate limiting check
     */
    static checkRateLimit(email: string, ipAddress: string): Promise<boolean>;
}
export default ModerationService;
//# sourceMappingURL=moderationService.d.ts.map