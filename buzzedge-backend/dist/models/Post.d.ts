import mongoose, { Document } from 'mongoose';
export interface IPost extends Document {
    title: string;
    slug: string;
    excerpt: string;
    content: string;
    featuredImage: {
        url: string;
        alt: string;
        cloudinaryId?: string;
    };
    seo: {
        metaTitle: string;
        metaDescription: string;
        keywords: string[];
        canonicalUrl?: string;
    };
    category: string;
    tags: string[];
    toolInfo?: {
        name: string;
        website?: string;
        pricing: {
            free: boolean;
            paidPlans: string[];
        };
        rating?: number;
        pros: string[];
        cons: string[];
    };
    status: 'draft' | 'pending' | 'published' | 'archived';
    publishedAt?: Date;
    scheduledFor?: Date;
    author: string;
    analytics: {
        views: number;
        shares: number;
        timeOnPage: number;
        bounceRate: number;
    };
    aiGeneration: {
        prompt: string;
        model: string;
        generatedAt: Date;
        qualityScore: number;
        humanEdited: boolean;
    };
    createdAt: Date;
    updatedAt: Date;
}
declare const _default: mongoose.Model<IPost, {}, {}, {}, mongoose.Document<unknown, {}, IPost, {}> & IPost & Required<{
    _id: unknown;
}> & {
    __v: number;
}, any>;
export default _default;
//# sourceMappingURL=Post.d.ts.map