import mongoose, { Document } from 'mongoose';
export interface IPublicUser extends Document {
    email: string;
    password?: string;
    name: string;
    avatar?: string;
    bio?: string;
    website?: string;
    isVerified: boolean;
    isActive: boolean;
    preferences: {
        emailNotifications: boolean;
        marketingEmails: boolean;
        commentReplies: boolean;
    };
    socialLinks: {
        twitter?: string;
        linkedin?: string;
        github?: string;
    };
    stats: {
        commentsCount: number;
        likesReceived: number;
        likesGiven: number;
    };
    lastLogin?: Date;
    verificationToken?: string;
    passwordResetToken?: string;
    passwordResetExpires?: Date;
    createdAt: Date;
    updatedAt: Date;
    comparePassword?(candidatePassword: string): Promise<boolean>;
}
declare const _default: mongoose.Model<IPublicUser, {}, {}, {}, mongoose.Document<unknown, {}, IPublicUser, {}> & IPublicUser & Required<{
    _id: unknown;
}> & {
    __v: number;
}, any>;
export default _default;
//# sourceMappingURL=PublicUser.d.ts.map