import mongoose, { Document } from "mongoose";
export interface IComment extends Document {
    content: string;
    author: {
        name: string;
        email: string;
        avatar?: string;
        userId?: mongoose.Types.ObjectId;
    };
    post: mongoose.Types.ObjectId;
    parentComment?: mongoose.Types.ObjectId;
    status: "pending" | "approved" | "rejected" | "spam";
    likes: number;
    likedBy: mongoose.Types.ObjectId[];
    isEdited: boolean;
    editedAt?: Date;
    moderationNotes?: string;
    ipAddress?: string;
    userAgent?: string;
    createdAt: Date;
    updatedAt: Date;
}
export interface ICommentModel extends mongoose.Model<IComment> {
    getPostComments(postId: string, includeReplies?: boolean): Promise<any[]>;
    getCommentStats(): Promise<any>;
}
declare const _default: ICommentModel;
export default _default;
//# sourceMappingURL=Comment.d.ts.map