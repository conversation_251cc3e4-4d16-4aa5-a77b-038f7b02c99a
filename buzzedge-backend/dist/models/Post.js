"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
const mongoose_1 = __importStar(require("mongoose"));
const PostSchema = new mongoose_1.Schema({
    title: {
        type: String,
        required: true,
        trim: true,
        minlength: 10,
        maxlength: 100
    },
    slug: {
        type: String,
        required: true,
        unique: true,
        lowercase: true,
        trim: true
    },
    excerpt: {
        type: String,
        required: true,
        trim: true,
        minlength: 50,
        maxlength: 200
    },
    content: {
        type: String,
        required: true,
        minlength: 500
    },
    featuredImage: {
        url: { type: String, required: true },
        alt: { type: String, required: true },
        cloudinaryId: String
    },
    seo: {
        metaTitle: { type: String, required: true, maxlength: 60 },
        metaDescription: { type: String, required: true, maxlength: 160 },
        keywords: [{ type: String, trim: true }],
        canonicalUrl: String
    },
    category: {
        type: String,
        required: true,
        enum: ['AI Tools', 'Productivity', 'Developer Tools', 'Design Tools']
    },
    tags: [{ type: String, trim: true }],
    toolInfo: {
        name: { type: String, required: true },
        website: String,
        pricing: {
            free: { type: Boolean, default: false },
            paidPlans: [String]
        },
        rating: { type: Number, min: 1, max: 5 },
        pros: [String],
        cons: [String]
    },
    status: {
        type: String,
        enum: ['draft', 'pending', 'published', 'archived'],
        default: 'draft'
    },
    publishedAt: Date,
    scheduledFor: Date,
    author: {
        type: String,
        default: 'AI Agent'
    },
    analytics: {
        views: { type: Number, default: 0 },
        shares: { type: Number, default: 0 },
        timeOnPage: { type: Number, default: 0 },
        bounceRate: { type: Number, default: 0 }
    },
    aiGeneration: {
        prompt: { type: String, required: true },
        model: { type: String, required: true },
        generatedAt: { type: Date, required: true },
        qualityScore: { type: Number, min: 0, max: 100 },
        humanEdited: { type: Boolean, default: false }
    }
}, {
    timestamps: true
});
// Indexes
PostSchema.index({ status: 1, publishedAt: -1 });
PostSchema.index({ slug: 1 }, { unique: true });
PostSchema.index({ category: 1, publishedAt: -1 });
PostSchema.index({ tags: 1, publishedAt: -1 });
PostSchema.index({ title: 'text', content: 'text', excerpt: 'text' });
PostSchema.index({ 'analytics.views': -1 });
// Pre-save middleware to generate slug
PostSchema.pre('save', function (next) {
    if (this.isModified('title') && !this.slug) {
        this.slug = this.title
            .toLowerCase()
            .replace(/[^a-z0-9]+/g, '-')
            .replace(/(^-|-$)/g, '');
    }
    next();
});
exports.default = mongoose_1.default.model('Post', PostSchema);
//# sourceMappingURL=Post.js.map