"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const mongoose_1 = __importStar(require("mongoose"));
const bcryptjs_1 = __importDefault(require("bcryptjs"));
const PublicUserSchema = new mongoose_1.Schema({
    email: {
        type: String,
        required: true,
        unique: true,
        lowercase: true,
        trim: true,
        match: [
            /^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/,
            'Please enter a valid email'
        ]
    },
    password: {
        type: String,
        minlength: 6,
        select: false // Don't include password in queries by default
    },
    name: {
        type: String,
        required: true,
        trim: true,
        maxlength: 50
    },
    avatar: {
        type: String,
        default: ''
    },
    bio: {
        type: String,
        trim: true,
        maxlength: 200
    },
    website: {
        type: String,
        trim: true,
        match: [
            /^https?:\/\/.+/,
            'Please enter a valid URL'
        ]
    },
    isVerified: {
        type: Boolean,
        default: false
    },
    isActive: {
        type: Boolean,
        default: true
    },
    preferences: {
        emailNotifications: {
            type: Boolean,
            default: true
        },
        marketingEmails: {
            type: Boolean,
            default: false
        },
        commentReplies: {
            type: Boolean,
            default: true
        }
    },
    socialLinks: {
        twitter: {
            type: String,
            trim: true,
            match: [/^https?:\/\/(www\.)?twitter\.com\/\w+/, 'Please enter a valid Twitter URL']
        },
        linkedin: {
            type: String,
            trim: true,
            match: [/^https?:\/\/(www\.)?linkedin\.com\/in\/[\w-]+/, 'Please enter a valid LinkedIn URL']
        },
        github: {
            type: String,
            trim: true,
            match: [/^https?:\/\/(www\.)?github\.com\/\w+/, 'Please enter a valid GitHub URL']
        }
    },
    stats: {
        commentsCount: {
            type: Number,
            default: 0,
            min: 0
        },
        likesReceived: {
            type: Number,
            default: 0,
            min: 0
        },
        likesGiven: {
            type: Number,
            default: 0,
            min: 0
        }
    },
    lastLogin: Date,
    verificationToken: String,
    passwordResetToken: String,
    passwordResetExpires: Date
}, {
    timestamps: true
});
// Indexes
PublicUserSchema.index({ email: 1 }, { unique: true });
PublicUserSchema.index({ isActive: 1 });
PublicUserSchema.index({ isVerified: 1 });
PublicUserSchema.index({ 'stats.commentsCount': -1 });
// Pre-save middleware to hash password
PublicUserSchema.pre('save', async function (next) {
    // Only hash the password if it has been modified (or is new) and exists
    if (!this.isModified('password') || !this.password)
        return next();
    try {
        // Hash password with cost of 10 (lighter than admin users)
        const salt = await bcryptjs_1.default.genSalt(10);
        this.password = await bcryptjs_1.default.hash(this.password, salt);
        next();
    }
    catch (error) {
        next(error);
    }
});
// Pre-save middleware to generate avatar if not provided
PublicUserSchema.pre('save', function (next) {
    if (!this.avatar && this.email) {
        // Generate Gravatar URL
        const crypto = require('crypto');
        const hash = crypto.createHash('md5').update(this.email.toLowerCase()).digest('hex');
        this.avatar = `https://www.gravatar.com/avatar/${hash}?d=identicon&s=80`;
    }
    next();
});
// Instance method to compare password
PublicUserSchema.methods.comparePassword = async function (candidatePassword) {
    if (!this.password)
        return false;
    try {
        return await bcryptjs_1.default.compare(candidatePassword, this.password);
    }
    catch (error) {
        throw error;
    }
};
// Static method to create guest user (for comments without registration)
PublicUserSchema.statics.createGuestUser = async function (name, email) {
    const existingUser = await this.findOne({ email });
    if (existingUser) {
        return existingUser;
    }
    return this.create({
        name,
        email,
        isVerified: false,
        // No password for guest users
    });
};
// Static method to get user statistics
PublicUserSchema.statics.getUserStats = async function () {
    const stats = await this.aggregate([
        {
            $group: {
                _id: null,
                totalUsers: { $sum: 1 },
                verifiedUsers: {
                    $sum: { $cond: ['$isVerified', 1, 0] }
                },
                activeUsers: {
                    $sum: { $cond: ['$isActive', 1, 0] }
                },
                totalComments: { $sum: '$stats.commentsCount' }
            }
        }
    ]);
    return stats[0] || {
        totalUsers: 0,
        verifiedUsers: 0,
        activeUsers: 0,
        totalComments: 0
    };
};
exports.default = mongoose_1.default.model('PublicUser', PublicUserSchema);
//# sourceMappingURL=PublicUser.js.map