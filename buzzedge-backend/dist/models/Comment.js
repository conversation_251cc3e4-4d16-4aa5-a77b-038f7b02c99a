"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
const mongoose_1 = __importStar(require("mongoose"));
const CommentSchema = new mongoose_1.Schema({
    content: {
        type: String,
        required: true,
        trim: true,
        minlength: 1,
        maxlength: 1000,
    },
    author: {
        name: {
            type: String,
            required: true,
            trim: true,
            maxlength: 50,
        },
        email: {
            type: String,
            required: true,
            lowercase: true,
            trim: true,
            match: [
                /^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/,
                "Please enter a valid email",
            ],
        },
        avatar: {
            type: String,
            default: "",
        },
        userId: {
            type: mongoose_1.Schema.Types.ObjectId,
            ref: "PublicUser",
            required: false,
        },
    },
    post: {
        type: mongoose_1.Schema.Types.ObjectId,
        ref: "Post",
        required: true,
    },
    parentComment: {
        type: mongoose_1.Schema.Types.ObjectId,
        ref: "Comment",
        required: false,
    },
    status: {
        type: String,
        enum: ["pending", "approved", "rejected", "spam"],
        default: "pending",
    },
    likes: {
        type: Number,
        default: 0,
        min: 0,
    },
    likedBy: [
        {
            type: mongoose_1.Schema.Types.ObjectId,
            ref: "PublicUser",
        },
    ],
    isEdited: {
        type: Boolean,
        default: false,
    },
    editedAt: Date,
    moderationNotes: {
        type: String,
        trim: true,
        maxlength: 500,
    },
    moderationScore: {
        type: Number,
        min: 0,
        max: 100,
    },
    moderatedAt: Date,
    moderatedBy: {
        type: mongoose_1.Schema.Types.ObjectId,
        ref: "User",
    },
    ipAddress: String,
    userAgent: String,
}, {
    timestamps: true,
});
// Indexes
CommentSchema.index({ post: 1, status: 1, createdAt: -1 });
CommentSchema.index({ "author.email": 1 });
CommentSchema.index({ status: 1, createdAt: -1 });
CommentSchema.index({ parentComment: 1 });
CommentSchema.index({ likes: -1 });
// Virtual for replies count
CommentSchema.virtual("repliesCount", {
    ref: "Comment",
    localField: "_id",
    foreignField: "parentComment",
    count: true,
    match: { status: "approved" },
});
// Pre-save middleware to generate avatar if not provided
CommentSchema.pre("save", function (next) {
    if (!this.author.avatar && this.author.email) {
        // Generate Gravatar URL
        const crypto = require("crypto");
        const hash = crypto
            .createHash("md5")
            .update(this.author.email.toLowerCase())
            .digest("hex");
        this.author.avatar = `https://www.gravatar.com/avatar/${hash}?d=identicon&s=40`;
    }
    next();
});
// Static method to get comments for a post
CommentSchema.statics.getPostComments = async function (postId, includeReplies = true) {
    const pipeline = [
        {
            $match: {
                post: new mongoose_1.default.Types.ObjectId(postId),
                status: "approved",
                parentComment: { $exists: false }, // Only top-level comments
            },
        },
        {
            $sort: { createdAt: -1 },
        },
    ];
    if (includeReplies) {
        pipeline.push({
            $lookup: {
                from: "comments",
                let: { commentId: "$_id" },
                pipeline: [
                    {
                        $match: {
                            $expr: {
                                $and: [
                                    { $eq: ["$parentComment", "$$commentId"] },
                                    { $eq: ["$status", "approved"] },
                                ],
                            },
                        },
                    },
                    { $sort: { createdAt: 1 } },
                ],
                as: "replies",
            },
        });
    }
    return this.aggregate(pipeline);
};
// Static method to get comment statistics
CommentSchema.statics.getCommentStats = async function () {
    const stats = await this.aggregate([
        {
            $group: {
                _id: "$status",
                count: { $sum: 1 },
            },
        },
    ]);
    const result = {
        total: 0,
        pending: 0,
        approved: 0,
        rejected: 0,
        spam: 0,
    };
    stats.forEach((stat) => {
        result[stat._id] = stat.count;
        result.total += stat.count;
    });
    return result;
};
exports.default = mongoose_1.default.model("Comment", CommentSchema);
//# sourceMappingURL=Comment.js.map