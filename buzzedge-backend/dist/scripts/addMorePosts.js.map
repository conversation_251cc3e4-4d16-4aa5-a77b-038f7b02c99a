{"version": 3, "file": "addMorePosts.js", "sourceRoot": "", "sources": ["../../src/scripts/addMorePosts.ts"], "names": [], "mappings": ";;;;;AACA,oDAA4B;AAC5B,gDAA8C;AAC9C,0DAAkC;AAElC,6BAA6B;AAC7B,gBAAM,CAAC,MAAM,EAAE,CAAC;AAEhB,MAAM,eAAe,GAAG;IACtB;QACE,KAAK,EAAE,yDAAyD;QAChE,IAAI,EAAE,+CAA+C;QACrD,OAAO,EAAE,uIAAuI;QAChJ,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;6IA8DgI;QACzI,aAAa,EAAE;YACb,GAAG,EAAE,iEAAiE;YACtE,GAAG,EAAE,sCAAsC;SAC5C;QACD,GAAG,EAAE;YACH,SAAS,EAAE,yDAAyD;YACpE,eAAe,EAAE,iIAAiI;YAClJ,QAAQ,EAAE,CAAC,OAAO,EAAE,UAAU,EAAE,cAAc,EAAE,OAAO,EAAE,YAAY,CAAC;SACvE;QACD,QAAQ,EAAE,cAAc;QACxB,IAAI,EAAE,CAAC,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,YAAY,CAAC;QAC5D,QAAQ,EAAE;YACR,IAAI,EAAE,mBAAmB;YACzB,OAAO,EAAE;gBACP,IAAI,EAAE,IAAI;gBACV,SAAS,EAAE,CAAC,uBAAuB,EAAE,yBAAyB,CAAC;aAChE;YACD,MAAM,EAAE,GAAG;YACX,IAAI,EAAE,CAAC,yBAAyB,EAAE,sBAAsB,EAAE,mBAAmB,CAAC;YAC9E,IAAI,EAAE,CAAC,gBAAgB,EAAE,iCAAiC,CAAC;SAC5D;QACD,MAAM,EAAE,WAAoB;QAC5B,WAAW,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;QACnC,YAAY,EAAE;YACZ,MAAM,EAAE,0EAA0E;YAClF,KAAK,EAAE,OAAO;YACd,WAAW,EAAE,IAAI,IAAI,EAAE;YACvB,YAAY,EAAE,EAAE;YAChB,WAAW,EAAE,KAAK;SACnB;KACF;IACD;QACE,KAAK,EAAE,2DAA2D;QAClE,IAAI,EAAE,0DAA0D;QAChE,OAAO,EAAE,mJAAmJ;QAC5J,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4HAmE+G;QACxH,aAAa,EAAE;YACb,GAAG,EAAE,oEAAoE;YACzE,GAAG,EAAE,qCAAqC;SAC3C;QACD,GAAG,EAAE;YACH,SAAS,EAAE,6DAA6D;YACxE,eAAe,EAAE,gIAAgI;YACjJ,QAAQ,EAAE,CAAC,SAAS,EAAE,WAAW,EAAE,KAAK,EAAE,aAAa,EAAE,aAAa,CAAC;SACxE;QACD,QAAQ,EAAE,iBAAiB;QAC3B,IAAI,EAAE,CAAC,aAAa,EAAE,SAAS,EAAE,WAAW,EAAE,KAAK,EAAE,aAAa,CAAC;QACnE,QAAQ,EAAE;YACR,IAAI,EAAE,sBAAsB;YAC5B,OAAO,EAAE;gBACP,IAAI,EAAE,IAAI;gBACV,SAAS,EAAE,CAAC,gBAAgB,EAAE,2BAA2B,CAAC;aAC3D;YACD,MAAM,EAAE,GAAG;YACX,IAAI,EAAE,CAAC,mBAAmB,EAAE,wBAAwB,EAAE,oBAAoB,CAAC;YAC3E,IAAI,EAAE,CAAC,4BAA4B,EAAE,sCAAsC,CAAC;SAC7E;QACD,MAAM,EAAE,WAAoB;QAC5B,WAAW,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;QACnC,YAAY,EAAE;YACZ,MAAM,EAAE,sEAAsE;YAC9E,KAAK,EAAE,OAAO;YACd,WAAW,EAAE,IAAI,IAAI,EAAE;YACvB,YAAY,EAAE,EAAE;YAChB,WAAW,EAAE,KAAK;SACnB;KACF;IACD;QACE,KAAK,EAAE,iDAAiD;QACxD,IAAI,EAAE,gDAAgD;QACtD,OAAO,EAAE,sIAAsI;QAC/I,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iJAwEoI;QAC7I,aAAa,EAAE;YACb,GAAG,EAAE,oEAAoE;YACzE,GAAG,EAAE,mCAAmC;SACzC;QACD,GAAG,EAAE;YACH,SAAS,EAAE,0DAA0D;YACrE,eAAe,EAAE,4HAA4H;YAC7I,QAAQ,EAAE,CAAC,SAAS,EAAE,UAAU,EAAE,iBAAiB,EAAE,cAAc,EAAE,MAAM,CAAC;SAC7E;QACD,QAAQ,EAAE,cAAc;QACxB,IAAI,EAAE,CAAC,cAAc,EAAE,SAAS,EAAE,UAAU,EAAE,iBAAiB,EAAE,MAAM,CAAC;QACxE,QAAQ,EAAE;YACR,IAAI,EAAE,qBAAqB;YAC3B,OAAO,EAAE;gBACP,IAAI,EAAE,IAAI;gBACV,SAAS,EAAE,CAAC,wBAAwB,EAAE,wBAAwB,CAAC;aAChE;YACD,MAAM,EAAE,GAAG;YACX,IAAI,EAAE,CAAC,cAAc,EAAE,mBAAmB,EAAE,0BAA0B,CAAC;YACvE,IAAI,EAAE,CAAC,qBAAqB,EAAE,4CAA4C,CAAC;SAC5E;QACD,MAAM,EAAE,WAAoB;QAC5B,WAAW,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;QACnC,YAAY,EAAE;YACZ,MAAM,EAAE,sEAAsE;YAC9E,KAAK,EAAE,OAAO;YACd,WAAW,EAAE,IAAI,IAAI,EAAE;YACvB,YAAY,EAAE,EAAE;YAChB,WAAW,EAAE,KAAK;SACnB;KACF;CACF,CAAC;AAEF,KAAK,UAAU,YAAY;IACzB,IAAI,CAAC;QACH,OAAO,CAAC,GAAG,CAAC,gCAAgC,CAAC,CAAC;QAE9C,sBAAsB;QACtB,MAAM,IAAA,oBAAS,GAAE,CAAC;QAElB,2BAA2B;QAC3B,MAAM,cAAI,CAAC,UAAU,CAAC,eAAe,CAAC,CAAC;QACvC,OAAO,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC;QAE1C,OAAO,CAAC,GAAG,CAAC,mCAAmC,CAAC,CAAC;QACjD,OAAO,CAAC,GAAG,CAAC,YAAY,eAAe,CAAC,MAAM,iBAAiB,CAAC,CAAC;QAEjE,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,wBAAwB,EAAE,KAAK,CAAC,CAAC;QAC/C,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC;AACH,CAAC;AAED,mBAAmB;AACnB,YAAY,EAAE,CAAC"}