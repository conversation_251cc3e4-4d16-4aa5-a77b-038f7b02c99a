"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const dotenv_1 = __importDefault(require("dotenv"));
const database_1 = require("../utils/database");
const Post_1 = __importDefault(require("../models/Post"));
const Category_1 = __importDefault(require("../models/Category"));
const User_1 = __importDefault(require("../models/User"));
// Load environment variables
dotenv_1.default.config();
const samplePosts = [
    {
        title: "ChatGPT vs Claude: Ultimate AI Assistant Comparison 2024",
        slug: "chatgpt-vs-claude-ai-assistant-comparison-2024",
        excerpt: "A comprehensive comparison between <PERSON><PERSON><PERSON><PERSON> and <PERSON>, two leading AI assistants. We analyze features, pricing, capabilities, and help you choose the right AI tool for your needs.",
        content: `# ChatGP<PERSON> vs <PERSON>: Ultimate AI Assistant Comparison 2024

## Introduction

In the rapidly evolving world of artificial intelligence, two names stand out as leaders in conversational AI: ChatGPT by OpenAI and Claude by Anthropic. Both have revolutionized how we interact with AI, but which one is right for you?

## ChatGPT Overview

ChatGPT, developed by OpenAI, has become synonymous with AI conversation. With its GPT-4 architecture, it offers:

### Key Features
- Advanced natural language processing
- Code generation and debugging
- Creative writing assistance
- Image analysis (GPT-4V)
- Plugin ecosystem

### Pricing
- **Free tier**: GPT-3.5 with usage limits
- **ChatGPT Plus**: $20/month for GPT-4 access
- **ChatGPT Team**: $25/user/month for teams
- **Enterprise**: Custom pricing

## Claude Overview

Claude, created by Anthropic, focuses on safety and helpfulness:

### Key Features
- Constitutional AI for safer responses
- Large context window (100K+ tokens)
- Strong reasoning capabilities
- Document analysis
- Coding assistance

### Pricing
- **Free tier**: Limited usage
- **Claude Pro**: $20/month
- **Claude Team**: $25/user/month
- **Enterprise**: Custom pricing

## Head-to-Head Comparison

| Feature | ChatGPT | Claude |
|---------|---------|--------|
| Context Window | 32K tokens | 100K+ tokens |
| Image Analysis | Yes (GPT-4V) | Yes |
| Plugins | Yes | No |
| Safety Focus | Good | Excellent |
| Coding | Excellent | Very Good |

## Which Should You Choose?

**Choose ChatGPT if:**
- You need plugin integrations
- You want the largest ecosystem
- You prioritize cutting-edge features

**Choose Claude if:**
- You work with long documents
- Safety is a top priority
- You need reliable, consistent responses

## Conclusion

Both ChatGPT and Claude are excellent AI assistants with their own strengths. Your choice depends on your specific needs, budget, and use cases.`,
        featuredImage: {
            url: "https://images.unsplash.com/photo-1677442136019-21780ecad995?w=800",
            alt: "AI comparison illustration",
        },
        seo: {
            metaTitle: "ChatGPT vs Claude: Ultimate AI Assistant Comparison 2024",
            metaDescription: "Compare ChatGPT and Claude AI assistants. Features, pricing, capabilities analysis to help you choose the best AI tool for your needs.",
            keywords: [
                "ChatGPT",
                "Claude",
                "AI assistant",
                "comparison",
                "artificial intelligence",
            ],
        },
        category: "AI Tools",
        tags: ["AI", "ChatGPT", "Claude", "Comparison", "Review"],
        toolInfo: {
            name: "ChatGPT vs Claude",
            pricing: {
                free: true,
                paidPlans: ["$20/month (ChatGPT Plus)", "$20/month (Claude Pro)"],
            },
            rating: 4.5,
            pros: [
                "Powerful AI capabilities",
                "Multiple pricing tiers",
                "Active development",
            ],
            cons: [
                "Usage limits on free tiers",
                "Learning curve for advanced features",
            ],
        },
        status: "published",
        publishedAt: new Date("2024-12-01"),
        aiGeneration: {
            prompt: "Write a comprehensive comparison between ChatGPT and Claude AI assistants",
            model: "gpt-4",
            generatedAt: new Date(),
            qualityScore: 95,
            humanEdited: false,
        },
    },
    {
        title: "Notion AI vs ClickUp AI: Smart Workspace Comparison",
        slug: "notion-ai-vs-clickup-ai-smart-workspace-comparison",
        excerpt: "Compare Notion AI and ClickUp AI features, pricing, and capabilities. Find the best AI-powered workspace tool for your productivity needs.",
        content: `# Notion AI vs ClickUp AI: Smart Workspace Comparison

## Introduction

As AI transforms productivity tools, Notion and ClickUp have integrated powerful AI features into their platforms. Let's compare these AI-enhanced workspace solutions.

## Notion AI Features

### Writing Assistant
- Content generation and editing
- Grammar and style improvements
- Translation capabilities
- Summarization tools

### Database Intelligence
- Automated data entry
- Smart categorization
- Intelligent filtering
- Pattern recognition

### Pricing
- **Notion AI**: $10/month per user (add-on)
- **Base Notion**: Free tier available

## ClickUp AI Features

### Task Management
- Automated task creation
- Smart scheduling
- Priority suggestions
- Progress tracking

### Content Creation
- Meeting summaries
- Action item extraction
- Document generation
- Email drafting

### Pricing
- **ClickUp AI**: $5/month per user (add-on)
- **Base ClickUp**: Free tier available

## Comparison Summary

**Choose Notion AI if:**
- You prioritize writing and documentation
- You need flexible database management
- You prefer a minimalist interface

**Choose ClickUp AI if:**
- You focus on project management
- You need advanced task automation
- You want comprehensive team collaboration

## Conclusion

Both tools offer excellent AI capabilities, but serve different primary use cases. Consider your team's workflow and priorities when choosing.`,
        featuredImage: {
            url: "https://images.unsplash.com/photo-1611224923853-80b023f02d71?w=800",
            alt: "Productivity workspace illustration",
        },
        seo: {
            metaTitle: "Notion AI vs ClickUp AI: Smart Workspace Comparison 2024",
            metaDescription: "Compare Notion AI and ClickUp AI features, pricing, and capabilities. Find the best AI-powered workspace tool for productivity.",
            keywords: [
                "Notion AI",
                "ClickUp AI",
                "productivity",
                "workspace",
                "comparison",
            ],
        },
        category: "Productivity",
        tags: ["Productivity", "Notion", "ClickUp", "AI", "Workspace"],
        toolInfo: {
            name: "Notion AI vs ClickUp AI",
            pricing: {
                free: true,
                paidPlans: ["$10/month (Notion AI)", "$5/month (ClickUp AI)"],
            },
            rating: 4.3,
            pros: [
                "AI-powered automation",
                "Flexible pricing",
                "Strong integrations",
            ],
            cons: ["Learning curve", "AI features require paid plans"],
        },
        status: "published",
        publishedAt: new Date("2024-11-30"),
        aiGeneration: {
            prompt: "Write a comparison between Notion AI and ClickUp AI workspace tools",
            model: "gpt-4",
            generatedAt: new Date(),
            qualityScore: 88,
            humanEdited: false,
        },
    },
    {
        title: "GitHub Copilot vs Tabnine: AI Code Assistant Review",
        slug: "github-copilot-vs-tabnine-ai-code-assistant-review",
        excerpt: "Detailed comparison of GitHub Copilot and Tabnine AI coding assistants. Features, pricing, IDE support, and code quality analysis.",
        content: `# GitHub Copilot vs Tabnine: AI Code Assistant Review

## Introduction

AI-powered code assistants have revolutionized software development. GitHub Copilot and Tabnine are two leading solutions that help developers write code faster and more efficiently.

## GitHub Copilot

### Features
- Whole-line and function suggestions
- Multi-language support
- Context-aware completions
- Chat interface for code explanations
- Security vulnerability detection

### Pricing
- **Individual**: $10/month
- **Business**: $19/user/month
- **Enterprise**: $39/user/month

### IDE Support
- VS Code (native)
- JetBrains IDEs
- Neovim
- Visual Studio

## Tabnine

### Features
- Real-time code completions
- Team training on private codebases
- Local and cloud processing options
- Advanced AI models
- Code review assistance

### Pricing
- **Starter**: Free
- **Pro**: $12/month
- **Enterprise**: Custom pricing

### IDE Support
- VS Code
- IntelliJ IDEA
- Eclipse
- Sublime Text
- Vim/Neovim

## Performance Comparison

### Code Quality
- **GitHub Copilot**: Excellent for popular languages
- **Tabnine**: Strong across all supported languages

### Privacy
- **GitHub Copilot**: Code sent to GitHub servers
- **Tabnine**: Offers local processing option

### Accuracy
- **GitHub Copilot**: 35-40% acceptance rate
- **Tabnine**: 25-30% acceptance rate

## Verdict

**Choose GitHub Copilot if:**
- You work primarily with popular languages
- You want the most advanced AI suggestions
- You're comfortable with cloud processing

**Choose Tabnine if:**
- Privacy is a top concern
- You need local processing
- You work with diverse programming languages

## Conclusion

Both tools significantly boost developer productivity. Your choice depends on privacy requirements, language preferences, and budget considerations.`,
        featuredImage: {
            url: "https://images.unsplash.com/photo-1555066931-4365d14bab8c?w=800",
            alt: "Code development illustration",
        },
        seo: {
            metaTitle: "GitHub Copilot vs Tabnine: AI Code Assistant Review 2024",
            metaDescription: "Compare GitHub Copilot and Tabnine AI coding assistants. Features, pricing, IDE support, and code quality analysis.",
            keywords: [
                "GitHub Copilot",
                "Tabnine",
                "AI coding",
                "code assistant",
                "developer tools",
            ],
        },
        category: "Developer Tools",
        tags: ["Development", "GitHub", "Copilot", "Tabnine", "AI", "Coding"],
        toolInfo: {
            name: "GitHub Copilot vs Tabnine",
            pricing: {
                free: true,
                paidPlans: ["$10/month (Copilot)", "$12/month (Tabnine Pro)"],
            },
            rating: 4.6,
            pros: [
                "Significant productivity boost",
                "Multi-language support",
                "IDE integration",
            ],
            cons: [
                "Subscription required for full features",
                "Privacy considerations",
            ],
        },
        status: "published",
        publishedAt: new Date("2024-11-29"),
        aiGeneration: {
            prompt: "Write a detailed comparison of GitHub Copilot and Tabnine AI code assistants",
            model: "gpt-4",
            generatedAt: new Date(),
            qualityScore: 92,
            humanEdited: false,
        },
    },
];
async function seedDatabase() {
    try {
        console.log("🌱 Starting database seeding...");
        // Connect to database
        await (0, database_1.connectDB)();
        // Clear existing data
        await Promise.all([
            Post_1.default.deleteMany({}),
            Category_1.default.deleteMany({}),
            User_1.default.deleteMany({}),
        ]);
        console.log("🗑️  Cleared existing data");
        // Create admin user
        const adminUser = new User_1.default({
            email: process.env.ADMIN_EMAIL || "<EMAIL>",
            password: process.env.ADMIN_PASSWORD || "admin123456",
            name: "Admin User",
            role: "admin",
        });
        await adminUser.save();
        console.log("✅ Admin user created");
        // Create categories
        const categories = [
            {
                name: "AI Tools",
                slug: "ai-tools",
                description: "Artificial Intelligence tools and platforms for various use cases",
                color: "#8B5CF6",
                icon: "brain",
                seo: {
                    metaTitle: "AI Tools Reviews - Latest Artificial Intelligence Software",
                    metaDescription: "Comprehensive reviews of the latest AI tools and platforms. Find the best artificial intelligence software for your needs.",
                },
            },
            {
                name: "Productivity",
                slug: "productivity",
                description: "Tools and apps to boost productivity and streamline workflows",
                color: "#10B981",
                icon: "zap",
                seo: {
                    metaTitle: "Productivity Tools Reviews - Best Apps for Efficiency",
                    metaDescription: "Discover the best productivity tools and apps to boost your efficiency and streamline your workflow.",
                },
            },
            {
                name: "Developer Tools",
                slug: "developer-tools",
                description: "Development tools, IDEs, and platforms for software engineers",
                color: "#F59E0B",
                icon: "code",
                seo: {
                    metaTitle: "Developer Tools Reviews - Best Software Development Tools",
                    metaDescription: "Reviews of the best developer tools, IDEs, and platforms for software engineers and programmers.",
                },
            },
        ];
        await Category_1.default.insertMany(categories);
        console.log("✅ Categories created");
        // Create sample posts
        await Post_1.default.insertMany(samplePosts);
        console.log("✅ Sample posts created");
        console.log("🎉 Database seeding completed successfully!");
        console.log(`📊 Created ${samplePosts.length} blog posts`);
        process.exit(0);
    }
    catch (error) {
        console.error("❌ Database seeding failed:", error);
        process.exit(1);
    }
}
// Run the seed function
seedDatabase();
//# sourceMappingURL=seedDatabase.js.map