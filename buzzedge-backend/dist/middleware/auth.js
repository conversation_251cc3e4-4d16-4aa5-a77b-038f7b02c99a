"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.generateToken = exports.requireAdmin = exports.authenticateUser = exports.authenticateAdmin = void 0;
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const User_1 = __importDefault(require("../models/User"));
const PublicUser_1 = __importDefault(require("../models/PublicUser"));
const authenticateAdmin = async (req, res, next) => {
    try {
        let token;
        // Check for token in Authorization header
        if (req.headers.authorization &&
            req.headers.authorization.startsWith("Bearer")) {
            token = req.headers.authorization.split(" ")[1];
        }
        // Check for token in cookies
        else if (req.cookies?.token) {
            token = req.cookies.token;
        }
        if (!token) {
            const error = new Error("Access denied. No token provided.");
            error.statusCode = 401;
            return next(error);
        }
        try {
            // Verify token
            const decoded = jsonwebtoken_1.default.verify(token, process.env.JWT_SECRET);
            // Get user from database
            const user = await User_1.default.findById(decoded.id);
            if (!user || !user.isActive) {
                const error = new Error("Access denied. User not found or inactive.");
                error.statusCode = 401;
                return next(error);
            }
            // Check if user has admin or editor role
            if (!["admin", "editor"].includes(user.role)) {
                const error = new Error("Access denied. Insufficient permissions.");
                error.statusCode = 403;
                return next(error);
            }
            // Add user to request object
            req.user = user;
            next();
        }
        catch (jwtError) {
            const error = new Error("Access denied. Invalid token.");
            error.statusCode = 401;
            return next(error);
        }
    }
    catch (error) {
        next(error);
    }
};
exports.authenticateAdmin = authenticateAdmin;
// Authenticate public users
const authenticateUser = async (req, res, next) => {
    try {
        let token;
        // Check for token in Authorization header
        if (req.headers.authorization &&
            req.headers.authorization.startsWith("Bearer")) {
            token = req.headers.authorization.split(" ")[1];
        }
        if (!token) {
            const error = new Error("Access denied. No token provided.");
            error.statusCode = 401;
            return next(error);
        }
        try {
            // Verify token
            const decoded = jsonwebtoken_1.default.verify(token, process.env.JWT_SECRET);
            // Try to find user in PublicUser collection first
            let user = await PublicUser_1.default.findById(decoded.id);
            if (!user || !user.isActive) {
                const error = new Error("Access denied. User not found or inactive.");
                error.statusCode = 401;
                return next(error);
            }
            // Add user to request object
            req.user = user;
            next();
        }
        catch (jwtError) {
            const error = new Error("Access denied. Invalid token.");
            error.statusCode = 401;
            return next(error);
        }
    }
    catch (error) {
        next(error);
    }
};
exports.authenticateUser = authenticateUser;
const requireAdmin = async (req, res, next) => {
    try {
        if (!req.user) {
            const error = new Error("Authentication required.");
            error.statusCode = 401;
            return next(error);
        }
        // Type guard to check if user is admin user
        if ("role" in req.user && req.user.role !== "admin") {
            const error = new Error("Admin access required.");
            error.statusCode = 403;
            return next(error);
        }
        next();
    }
    catch (error) {
        next(error);
    }
};
exports.requireAdmin = requireAdmin;
const generateToken = (userId) => {
    const secret = process.env.JWT_SECRET;
    if (!secret) {
        throw new Error("JWT_SECRET is not defined");
    }
    const expiresIn = process.env.JWT_EXPIRES_IN || "7d";
    return jsonwebtoken_1.default.sign({ id: userId }, secret, { expiresIn });
};
exports.generateToken = generateToken;
//# sourceMappingURL=auth.js.map