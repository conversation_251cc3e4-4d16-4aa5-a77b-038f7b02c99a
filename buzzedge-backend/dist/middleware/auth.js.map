{"version": 3, "file": "auth.js", "sourceRoot": "", "sources": ["../../src/middleware/auth.ts"], "names": [], "mappings": ";;;;;;AACA,gEAA+B;AAC/B,0DAA6C;AAYtC,MAAM,iBAAiB,GAAG,KAAK,EACpC,GAAY,EACZ,GAAa,EACb,IAAkB,EAClB,EAAE;IACF,IAAI,CAAC;QACH,IAAI,KAAyB,CAAC;QAE9B,0CAA0C;QAC1C,IACE,GAAG,CAAC,OAAO,CAAC,aAAa;YACzB,GAAG,CAAC,OAAO,CAAC,aAAa,CAAC,UAAU,CAAC,QAAQ,CAAC,EAC9C,CAAC;YACD,KAAK,GAAG,GAAG,CAAC,OAAO,CAAC,aAAa,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAClD,CAAC;QACD,6BAA6B;aACxB,IAAI,GAAG,CAAC,OAAO,EAAE,KAAK,EAAE,CAAC;YAC5B,KAAK,GAAG,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC;QAC5B,CAAC;QAED,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,MAAM,KAAK,GAAgB,IAAI,KAAK,CAAC,mCAAmC,CAAC,CAAC;YAC1E,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC;YACvB,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC;QACrB,CAAC;QAED,IAAI,CAAC;YACH,eAAe;YACf,MAAM,OAAO,GAAG,sBAAG,CAAC,MAAM,CAAC,KAAK,EAAE,OAAO,CAAC,GAAG,CAAC,UAAoB,CAEjE,CAAC;YAEF,yBAAyB;YACzB,MAAM,IAAI,GAAG,MAAM,cAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;YAE7C,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;gBAC5B,MAAM,KAAK,GAAgB,IAAI,KAAK,CAClC,4CAA4C,CAC7C,CAAC;gBACF,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC;gBACvB,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC;YACrB,CAAC;YAED,yCAAyC;YACzC,IAAI,CAAC,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;gBAC7C,MAAM,KAAK,GAAgB,IAAI,KAAK,CAClC,0CAA0C,CAC3C,CAAC;gBACF,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC;gBACvB,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC;YACrB,CAAC;YAED,6BAA6B;YAC7B,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC;YAChB,IAAI,EAAE,CAAC;QACT,CAAC;QAAC,OAAO,QAAQ,EAAE,CAAC;YAClB,MAAM,KAAK,GAAgB,IAAI,KAAK,CAAC,+BAA+B,CAAC,CAAC;YACtE,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC;YACvB,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC;QACrB,CAAC;IACH,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,IAAI,CAAC,KAAK,CAAC,CAAC;IACd,CAAC;AACH,CAAC,CAAC;AA/DW,QAAA,iBAAiB,qBA+D5B;AAEK,MAAM,YAAY,GAAG,KAAK,EAC/B,GAAY,EACZ,GAAa,EACb,IAAkB,EAClB,EAAE;IACF,IAAI,CAAC;QACH,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;YACd,MAAM,KAAK,GAAgB,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;YACjE,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC;YACvB,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC;QACrB,CAAC;QAED,IAAI,GAAG,CAAC,IAAI,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;YAC9B,MAAM,KAAK,GAAgB,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC;YAC/D,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC;YACvB,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC;QACrB,CAAC;QAED,IAAI,EAAE,CAAC;IACT,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,IAAI,CAAC,KAAK,CAAC,CAAC;IACd,CAAC;AACH,CAAC,CAAC;AAtBW,QAAA,YAAY,gBAsBvB;AAEK,MAAM,aAAa,GAAG,CAAC,MAAc,EAAU,EAAE;IACtD,MAAM,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC;IACtC,IAAI,CAAC,MAAM,EAAE,CAAC;QACZ,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;IAC/C,CAAC;IACD,MAAM,SAAS,GAAG,OAAO,CAAC,GAAG,CAAC,cAAc,IAAI,IAAI,CAAC;IACrD,OAAO,sBAAG,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,MAAM,EAAE,EAAE,SAAS,EAAqB,CAAC,CAAC;AAC5E,CAAC,CAAC;AAPW,QAAA,aAAa,iBAOxB"}