"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.seedDatabase = void 0;
const User_1 = __importDefault(require("../models/User"));
const Category_1 = __importDefault(require("../models/Category"));
const Post_1 = __importDefault(require("../models/Post"));
const seedDatabase = async () => {
    try {
        console.log('🌱 Starting database seeding...');
        // Clear existing data
        await Promise.all([
            User_1.default.deleteMany({}),
            Category_1.default.deleteMany({}),
            Post_1.default.deleteMany({})
        ]);
        // Create admin user
        const adminUser = new User_1.default({
            email: process.env.ADMIN_EMAIL || '<EMAIL>',
            password: process.env.ADMIN_PASSWORD || 'admin123456',
            name: 'Admin User',
            role: 'admin'
        });
        await adminUser.save();
        console.log('✅ Admin user created');
        // Create categories
        const categories = [
            {
                name: 'AI Tools',
                description: 'Artificial Intelligence tools and platforms for various use cases',
                color: '#8B5CF6',
                icon: 'brain',
                seo: {
                    metaTitle: 'AI Tools Reviews - Latest Artificial Intelligence Software',
                    metaDescription: 'Comprehensive reviews of the latest AI tools and platforms. Find the best artificial intelligence software for your needs.'
                }
            },
            {
                name: 'Productivity',
                description: 'Tools and apps to boost productivity and streamline workflows',
                color: '#10B981',
                icon: 'zap',
                seo: {
                    metaTitle: 'Productivity Tools Reviews - Best Apps for Efficiency',
                    metaDescription: 'Discover the best productivity tools and apps to boost your efficiency and streamline your workflow.'
                }
            },
            {
                name: 'Developer Tools',
                description: 'Development tools, IDEs, and platforms for software engineers',
                color: '#F59E0B',
                icon: 'code',
                seo: {
                    metaTitle: 'Developer Tools Reviews - Best Software Development Tools',
                    metaDescription: 'Reviews of the best developer tools, IDEs, and platforms for software engineers and programmers.'
                }
            },
            {
                name: 'Design Tools',
                description: 'Design software and creative tools for designers and artists',
                color: '#EF4444',
                icon: 'palette',
                seo: {
                    metaTitle: 'Design Tools Reviews - Best Creative Software for Designers',
                    metaDescription: 'Comprehensive reviews of design tools and creative software for graphic designers, UI/UX designers, and artists.'
                }
            }
        ];
        const createdCategories = await Category_1.default.insertMany(categories);
        console.log('✅ Categories created');
        // Create sample posts
        const samplePosts = [
            {
                title: 'ChatGPT vs Claude: Ultimate AI Assistant Comparison 2024',
                slug: 'chatgpt-vs-claude-ai-assistant-comparison-2024',
                excerpt: 'A comprehensive comparison between ChatGPT and Claude, two leading AI assistants. We analyze features, pricing, capabilities, and help you choose the right AI tool for your needs.',
                content: `# ChatGPT vs Claude: Ultimate AI Assistant Comparison 2024

## Introduction

In the rapidly evolving world of artificial intelligence, two names stand out as leaders in conversational AI: ChatGPT by OpenAI and Claude by Anthropic. Both have revolutionized how we interact with AI, but which one is right for you?

## ChatGPT Overview

ChatGPT, developed by OpenAI, has become synonymous with AI conversation. With its GPT-4 architecture, it offers:

### Key Features
- Advanced natural language processing
- Code generation and debugging
- Creative writing assistance
- Image analysis (GPT-4V)
- Plugin ecosystem

### Pricing
- **Free tier**: GPT-3.5 with usage limits
- **ChatGPT Plus**: $20/month for GPT-4 access
- **ChatGPT Team**: $25/user/month for teams
- **Enterprise**: Custom pricing

## Claude Overview

Claude, created by Anthropic, focuses on safety and helpfulness:

### Key Features
- Constitutional AI for safer responses
- Large context window (100K+ tokens)
- Strong reasoning capabilities
- Document analysis
- Coding assistance

### Pricing
- **Free tier**: Limited usage
- **Claude Pro**: $20/month
- **Claude Team**: $25/user/month
- **Enterprise**: Custom pricing

## Head-to-Head Comparison

| Feature | ChatGPT | Claude |
|---------|---------|--------|
| Context Window | 32K tokens | 100K+ tokens |
| Image Analysis | Yes (GPT-4V) | Yes |
| Plugins | Yes | No |
| Safety Focus | Good | Excellent |
| Coding | Excellent | Very Good |

## Which Should You Choose?

**Choose ChatGPT if:**
- You need plugin integrations
- You want the largest ecosystem
- You prioritize cutting-edge features

**Choose Claude if:**
- You work with long documents
- Safety is a top priority
- You need reliable, consistent responses

## Conclusion

Both ChatGPT and Claude are excellent AI assistants with their own strengths. Your choice depends on your specific needs, budget, and use cases.`,
                featuredImage: {
                    url: 'https://images.unsplash.com/photo-1677442136019-21780ecad995?w=800',
                    alt: 'AI comparison illustration'
                },
                seo: {
                    metaTitle: 'ChatGPT vs Claude: Ultimate AI Assistant Comparison 2024',
                    metaDescription: 'Compare ChatGPT and Claude AI assistants. Features, pricing, capabilities analysis to help you choose the best AI tool for your needs.',
                    keywords: ['ChatGPT', 'Claude', 'AI assistant', 'comparison', 'artificial intelligence']
                },
                category: 'AI Tools',
                tags: ['AI', 'ChatGPT', 'Claude', 'Comparison', 'Review'],
                toolInfo: {
                    name: 'ChatGPT vs Claude',
                    pricing: {
                        free: true,
                        paidPlans: ['$20/month (ChatGPT Plus)', '$20/month (Claude Pro)']
                    },
                    rating: 4.5,
                    pros: ['Powerful AI capabilities', 'Multiple pricing tiers', 'Active development'],
                    cons: ['Usage limits on free tiers', 'Learning curve for advanced features']
                },
                status: 'published',
                publishedAt: new Date(),
                aiGeneration: {
                    prompt: 'Write a comprehensive comparison between ChatGPT and Claude AI assistants',
                    model: 'gpt-4',
                    generatedAt: new Date(),
                    qualityScore: 95,
                    humanEdited: false
                }
            }
        ];
        await Post_1.default.insertMany(samplePosts);
        console.log('✅ Sample posts created');
        console.log('🎉 Database seeding completed successfully!');
    }
    catch (error) {
        console.error('❌ Database seeding failed:', error);
        throw error;
    }
};
exports.seedDatabase = seedDatabase;
//# sourceMappingURL=seedData.js.map