"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const authController_1 = require("../controllers/authController");
const auth_1 = require("../middleware/auth");
const router = express_1.default.Router();
// POST /api/auth/register - Register a new user
router.post('/register', authController_1.registerUser);
// POST /api/auth/login - Login user
router.post('/login', authController_1.loginUser);
// POST /api/auth/verify-email - Verify email address
router.post('/verify-email', authController_1.verifyEmail);
// POST /api/auth/forgot-password - Request password reset
router.post('/forgot-password', authController_1.forgotPassword);
// POST /api/auth/reset-password - Reset password
router.post('/reset-password', authController_1.resetPassword);
// Protected routes (require authentication)
router.use(auth_1.authenticateUser);
// GET /api/auth/me - Get current user
router.get('/me', authController_1.getCurrentUser);
// PUT /api/auth/profile - Update user profile
router.put('/profile', authController_1.updateProfile);
exports.default = router;
//# sourceMappingURL=authRoutes.js.map