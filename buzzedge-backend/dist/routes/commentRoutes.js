"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const commentController_1 = require("../controllers/commentController");
const auth_1 = require("../middleware/auth");
const router = express_1.default.Router();
// GET /api/comments/:postId - Get comments for a post (public)
router.get('/:postId', commentController_1.getPostComments);
// POST /api/comments - Create a new comment (public, but can be authenticated)
router.post('/', commentController_1.createComment);
// PUT /api/comments/:id - Update a comment (authenticated users only)
router.put('/:id', auth_1.authenticateUser, commentController_1.updateComment);
// DELETE /api/comments/:id - Delete a comment (authenticated users only)
router.delete('/:id', auth_1.authenticateUser, commentController_1.deleteComment);
// POST /api/comments/:id/like - Like/unlike a comment (authenticated users only)
router.post('/:id/like', auth_1.authenticateUser, commentController_1.toggleCommentLike);
// GET /api/comments/stats - Get comment statistics (admin only)
router.get('/admin/stats', auth_1.authenticateAdmin, commentController_1.getCommentStats);
exports.default = router;
//# sourceMappingURL=commentRoutes.js.map