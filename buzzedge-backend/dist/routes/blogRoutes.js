"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const blogController_1 = require("../controllers/blogController");
const router = express_1.default.Router();
// GET /api/blogs - Get all published posts with pagination
router.get('/', blogController_1.getAllPosts);
// GET /api/blogs/search?q=query - Search posts
router.get('/search', blogController_1.searchPosts);
// GET /api/blogs/category/:category - Get posts by category
router.get('/category/:category', blogController_1.getPostsByCategory);
// GET /api/blogs/:slug - Get single post by slug
router.get('/:slug', blogController_1.getPostBySlug);
// POST /api/blogs/:slug/view - Increment post views
router.post('/:slug/view', blogController_1.incrementPostViews);
exports.default = router;
//# sourceMappingURL=blogRoutes.js.map