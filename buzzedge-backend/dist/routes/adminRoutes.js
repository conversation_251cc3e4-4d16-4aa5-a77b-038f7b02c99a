"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const adminController_1 = require("../controllers/adminController");
const auth_1 = require("../middleware/auth");
const router = express_1.default.Router();
// POST /api/admin/login - Admin login
router.post('/login', adminController_1.loginAdmin);
// Protected routes (require authentication)
router.use(auth_1.authenticateAdmin);
// GET /api/admin/dashboard - Get dashboard statistics
router.get('/dashboard', adminController_1.getDashboardStats);
// GET /api/admin/posts/pending - Get pending posts for review
router.get('/posts/pending', adminController_1.getPendingPosts);
// POST /api/admin/posts/:id/approve - Approve a post
router.post('/posts/:id/approve', adminController_1.approvePost);
// POST /api/admin/posts/:id/reject - Reject a post
router.post('/posts/:id/reject', adminController_1.rejectPost);
// PUT /api/admin/posts/:id - Edit a post
router.put('/posts/:id', adminController_1.editPost);
// DELETE /api/admin/posts/:id - Delete a post
router.delete('/posts/:id', adminController_1.deletePost);
// POST /api/admin/generate-content - Trigger AI content generation
router.post('/generate-content', adminController_1.generateContent);
exports.default = router;
//# sourceMappingURL=adminRoutes.js.map