"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.generateContent = exports.deletePost = exports.editPost = exports.rejectPost = exports.approvePost = exports.getPendingPosts = exports.getDashboardStats = exports.loginAdmin = void 0;
const User_1 = __importDefault(require("../models/User"));
const Post_1 = __importDefault(require("../models/Post"));
const auth_1 = require("../middleware/auth");
// POST /api/admin/login - Admin login
const loginAdmin = async (req, res, next) => {
    try {
        const { email, password } = req.body;
        if (!email || !password) {
            const error = new Error('Please provide email and password');
            error.statusCode = 400;
            return next(error);
        }
        try {
            // Use the static method we defined in User model
            const user = await User_1.default.getAuthenticated(email, password);
            // Generate JWT token
            const token = (0, auth_1.generateToken)(user._id);
            // Set cookie (optional, for web interface)
            res.cookie('token', token, {
                expires: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days
                httpOnly: true,
                secure: process.env.NODE_ENV === 'production',
                sameSite: 'strict'
            });
            res.status(200).json({
                success: true,
                data: {
                    token,
                    user: {
                        id: user._id,
                        email: user.email,
                        name: user.name,
                        role: user.role,
                        avatar: user.avatar
                    }
                }
            });
        }
        catch (authError) {
            const error = new Error('Invalid credentials');
            error.statusCode = 401;
            return next(error);
        }
    }
    catch (error) {
        next(error);
    }
};
exports.loginAdmin = loginAdmin;
// GET /api/admin/dashboard - Get dashboard statistics
const getDashboardStats = async (req, res, next) => {
    try {
        const [totalPosts, publishedPosts, pendingPosts, draftPosts, totalViews, recentPosts] = await Promise.all([
            Post_1.default.countDocuments(),
            Post_1.default.countDocuments({ status: 'published' }),
            Post_1.default.countDocuments({ status: 'pending' }),
            Post_1.default.countDocuments({ status: 'draft' }),
            Post_1.default.aggregate([
                { $match: { status: 'published' } },
                { $group: { _id: null, totalViews: { $sum: '$analytics.views' } } }
            ]),
            Post_1.default.find()
                .select('title status publishedAt analytics.views createdAt')
                .sort({ createdAt: -1 })
                .limit(5)
        ]);
        // Get top performing posts
        const topPosts = await Post_1.default.find({ status: 'published' })
            .select('title slug analytics.views publishedAt')
            .sort({ 'analytics.views': -1 })
            .limit(5);
        res.status(200).json({
            success: true,
            data: {
                stats: {
                    totalPosts,
                    publishedPosts,
                    pendingPosts,
                    draftPosts,
                    totalViews: totalViews[0]?.totalViews || 0
                },
                recentPosts,
                topPosts
            }
        });
    }
    catch (error) {
        next(error);
    }
};
exports.getDashboardStats = getDashboardStats;
// GET /api/admin/posts/pending - Get pending posts for review
const getPendingPosts = async (req, res, next) => {
    try {
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 10;
        const skip = (page - 1) * limit;
        const posts = await Post_1.default.find({ status: 'pending' })
            .select('title excerpt featuredImage category aiGeneration createdAt')
            .sort({ createdAt: -1 })
            .skip(skip)
            .limit(limit);
        const total = await Post_1.default.countDocuments({ status: 'pending' });
        res.status(200).json({
            success: true,
            data: {
                posts,
                pagination: {
                    currentPage: page,
                    totalPages: Math.ceil(total / limit),
                    totalPosts: total
                }
            }
        });
    }
    catch (error) {
        next(error);
    }
};
exports.getPendingPosts = getPendingPosts;
// POST /api/admin/posts/:id/approve - Approve a post
const approvePost = async (req, res, next) => {
    try {
        const { id } = req.params;
        const { publishedAt } = req.body;
        const post = await Post_1.default.findByIdAndUpdate(id, {
            status: 'published',
            publishedAt: publishedAt ? new Date(publishedAt) : new Date()
        }, { new: true });
        if (!post) {
            const error = new Error('Post not found');
            error.statusCode = 404;
            return next(error);
        }
        res.status(200).json({
            success: true,
            data: { post },
            message: 'Post approved and published successfully'
        });
    }
    catch (error) {
        next(error);
    }
};
exports.approvePost = approvePost;
// POST /api/admin/posts/:id/reject - Reject a post
const rejectPost = async (req, res, next) => {
    try {
        const { id } = req.params;
        const { reason } = req.body;
        const post = await Post_1.default.findByIdAndUpdate(id, {
            status: 'draft',
            rejectionReason: reason || 'Quality standards not met'
        }, { new: true });
        if (!post) {
            const error = new Error('Post not found');
            error.statusCode = 404;
            return next(error);
        }
        res.status(200).json({
            success: true,
            data: { post },
            message: 'Post rejected successfully'
        });
    }
    catch (error) {
        next(error);
    }
};
exports.rejectPost = rejectPost;
// PUT /api/admin/posts/:id - Edit a post
const editPost = async (req, res, next) => {
    try {
        const { id } = req.params;
        const updateData = req.body;
        // Mark as human edited if content is modified
        if (updateData.content || updateData.title) {
            updateData['aiGeneration.humanEdited'] = true;
        }
        const post = await Post_1.default.findByIdAndUpdate(id, updateData, {
            new: true,
            runValidators: true
        });
        if (!post) {
            const error = new Error('Post not found');
            error.statusCode = 404;
            return next(error);
        }
        res.status(200).json({
            success: true,
            data: { post },
            message: 'Post updated successfully'
        });
    }
    catch (error) {
        next(error);
    }
};
exports.editPost = editPost;
// DELETE /api/admin/posts/:id - Delete a post
const deletePost = async (req, res, next) => {
    try {
        const { id } = req.params;
        const post = await Post_1.default.findByIdAndDelete(id);
        if (!post) {
            const error = new Error('Post not found');
            error.statusCode = 404;
            return next(error);
        }
        res.status(200).json({
            success: true,
            message: 'Post deleted successfully'
        });
    }
    catch (error) {
        next(error);
    }
};
exports.deletePost = deletePost;
// POST /api/admin/generate-content - Trigger AI content generation
const generateContent = async (req, res, next) => {
    try {
        const { topic, category, priority } = req.body;
        // This would typically trigger a background job
        // For now, we'll just return a success message
        // TODO: Implement actual AI content generation service
        res.status(200).json({
            success: true,
            message: 'Content generation job queued successfully',
            data: {
                topic,
                category,
                priority: priority || 5,
                estimatedCompletion: new Date(Date.now() + 10 * 60 * 1000) // 10 minutes
            }
        });
    }
    catch (error) {
        next(error);
    }
};
exports.generateContent = generateContent;
//# sourceMappingURL=adminController.js.map