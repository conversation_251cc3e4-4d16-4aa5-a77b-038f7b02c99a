"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.resetPassword = exports.forgotPassword = exports.verifyEmail = exports.updateProfile = exports.getCurrentUser = exports.loginUser = exports.registerUser = void 0;
const PublicUser_1 = __importDefault(require("../models/PublicUser"));
const auth_1 = require("../middleware/auth");
const crypto_1 = __importDefault(require("crypto"));
// POST /api/auth/register - Register a new user
const registerUser = async (req, res, next) => {
    try {
        const { name, email, password } = req.body;
        // Validate required fields
        if (!name || !email || !password) {
            const error = new Error("Name, email, and password are required");
            error.statusCode = 400;
            return next(error);
        }
        // Check if user already exists
        const existingUser = await PublicUser_1.default.findOne({ email });
        if (existingUser) {
            const error = new Error("User already exists with this email");
            error.statusCode = 409;
            return next(error);
        }
        // Create verification token
        const verificationToken = crypto_1.default.randomBytes(32).toString("hex");
        // Create user
        const user = new PublicUser_1.default({
            name,
            email,
            password,
            verificationToken,
            isVerified: false, // Require email verification
        });
        await user.save();
        // Generate JWT token
        const token = (0, auth_1.generateToken)(String(user._id));
        // TODO: Send verification email
        // await sendVerificationEmail(user.email, verificationToken);
        res.status(201).json({
            success: true,
            data: {
                token,
                user: {
                    id: user._id,
                    name: user.name,
                    email: user.email,
                    avatar: user.avatar,
                    isVerified: user.isVerified,
                },
            },
            message: "User registered successfully. Please check your email for verification.",
        });
    }
    catch (error) {
        next(error);
    }
};
exports.registerUser = registerUser;
// POST /api/auth/login - Login user
const loginUser = async (req, res, next) => {
    try {
        const { email, password } = req.body;
        // Validate required fields
        if (!email || !password) {
            const error = new Error("Email and password are required");
            error.statusCode = 400;
            return next(error);
        }
        // Find user and include password
        const user = await PublicUser_1.default.findOne({ email, isActive: true }).select("+password");
        if (!user) {
            const error = new Error("Invalid credentials");
            error.statusCode = 401;
            return next(error);
        }
        // Check password
        const isMatch = await user.comparePassword(password);
        if (!isMatch) {
            const error = new Error("Invalid credentials");
            error.statusCode = 401;
            return next(error);
        }
        // Update last login
        user.lastLogin = new Date();
        await user.save();
        // Generate JWT token
        const token = (0, auth_1.generateToken)(String(user._id));
        res.status(200).json({
            success: true,
            data: {
                token,
                user: {
                    id: user._id,
                    name: user.name,
                    email: user.email,
                    avatar: user.avatar,
                    isVerified: user.isVerified,
                    bio: user.bio,
                    website: user.website,
                },
            },
        });
    }
    catch (error) {
        next(error);
    }
};
exports.loginUser = loginUser;
// GET /api/auth/me - Get current user
const getCurrentUser = async (req, res, next) => {
    try {
        const userId = req.user?.id;
        const user = await PublicUser_1.default.findById(userId);
        if (!user) {
            const error = new Error("User not found");
            error.statusCode = 404;
            return next(error);
        }
        res.status(200).json({
            success: true,
            data: {
                user: {
                    id: user._id,
                    name: user.name,
                    email: user.email,
                    avatar: user.avatar,
                    isVerified: user.isVerified,
                    bio: user.bio,
                    website: user.website,
                    socialLinks: user.socialLinks,
                    preferences: user.preferences,
                    stats: user.stats,
                },
            },
        });
    }
    catch (error) {
        next(error);
    }
};
exports.getCurrentUser = getCurrentUser;
// PUT /api/auth/profile - Update user profile
const updateProfile = async (req, res, next) => {
    try {
        const userId = req.user?.id;
        const { name, bio, website, socialLinks, preferences } = req.body;
        const user = await PublicUser_1.default.findById(userId);
        if (!user) {
            const error = new Error("User not found");
            error.statusCode = 404;
            return next(error);
        }
        // Update fields
        if (name)
            user.name = name;
        if (bio !== undefined)
            user.bio = bio;
        if (website !== undefined)
            user.website = website;
        if (socialLinks)
            user.socialLinks = { ...user.socialLinks, ...socialLinks };
        if (preferences)
            user.preferences = { ...user.preferences, ...preferences };
        await user.save();
        res.status(200).json({
            success: true,
            data: {
                user: {
                    id: user._id,
                    name: user.name,
                    email: user.email,
                    avatar: user.avatar,
                    isVerified: user.isVerified,
                    bio: user.bio,
                    website: user.website,
                    socialLinks: user.socialLinks,
                    preferences: user.preferences,
                    stats: user.stats,
                },
            },
            message: "Profile updated successfully",
        });
    }
    catch (error) {
        next(error);
    }
};
exports.updateProfile = updateProfile;
// POST /api/auth/verify-email - Verify email address
const verifyEmail = async (req, res, next) => {
    try {
        const { token } = req.body;
        if (!token) {
            const error = new Error("Verification token is required");
            error.statusCode = 400;
            return next(error);
        }
        const user = await PublicUser_1.default.findOne({ verificationToken: token });
        if (!user) {
            const error = new Error("Invalid or expired verification token");
            error.statusCode = 400;
            return next(error);
        }
        // Verify user
        user.isVerified = true;
        user.verificationToken = undefined;
        await user.save();
        res.status(200).json({
            success: true,
            message: "Email verified successfully",
        });
    }
    catch (error) {
        next(error);
    }
};
exports.verifyEmail = verifyEmail;
// POST /api/auth/forgot-password - Request password reset
const forgotPassword = async (req, res, next) => {
    try {
        const { email } = req.body;
        if (!email) {
            const error = new Error("Email is required");
            error.statusCode = 400;
            return next(error);
        }
        const user = await PublicUser_1.default.findOne({ email, isActive: true });
        if (!user) {
            // Don't reveal if user exists
            return res.status(200).json({
                success: true,
                message: "If an account with that email exists, a password reset link has been sent.",
            });
        }
        // Generate reset token
        const resetToken = crypto_1.default.randomBytes(32).toString("hex");
        user.passwordResetToken = resetToken;
        user.passwordResetExpires = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes
        await user.save();
        // TODO: Send password reset email
        // await sendPasswordResetEmail(user.email, resetToken);
        res.status(200).json({
            success: true,
            message: "If an account with that email exists, a password reset link has been sent.",
        });
    }
    catch (error) {
        next(error);
    }
};
exports.forgotPassword = forgotPassword;
// POST /api/auth/reset-password - Reset password
const resetPassword = async (req, res, next) => {
    try {
        const { token, password } = req.body;
        if (!token || !password) {
            const error = new Error("Token and password are required");
            error.statusCode = 400;
            return next(error);
        }
        const user = await PublicUser_1.default.findOne({
            passwordResetToken: token,
            passwordResetExpires: { $gt: Date.now() },
        });
        if (!user) {
            const error = new Error("Invalid or expired reset token");
            error.statusCode = 400;
            return next(error);
        }
        // Update password
        user.password = password;
        user.passwordResetToken = undefined;
        user.passwordResetExpires = undefined;
        await user.save();
        res.status(200).json({
            success: true,
            message: "Password reset successfully",
        });
    }
    catch (error) {
        next(error);
    }
};
exports.resetPassword = resetPassword;
//# sourceMappingURL=authController.js.map