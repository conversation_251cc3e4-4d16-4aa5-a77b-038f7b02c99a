import { Request, Response, NextFunction } from 'express';
export declare const loginAdmin: (req: Request, res: Response, next: NextFunction) => Promise<void>;
export declare const getDashboardStats: (req: Request, res: Response, next: NextFunction) => Promise<void>;
export declare const getPendingPosts: (req: Request, res: Response, next: NextFunction) => Promise<void>;
export declare const approvePost: (req: Request, res: Response, next: NextFunction) => Promise<void>;
export declare const rejectPost: (req: Request, res: Response, next: NextFunction) => Promise<void>;
export declare const editPost: (req: Request, res: Response, next: NextFunction) => Promise<void>;
export declare const deletePost: (req: Request, res: Response, next: NextFunction) => Promise<void>;
export declare const generateContent: (req: Request, res: Response, next: NextFunction) => Promise<void>;
//# sourceMappingURL=adminController.d.ts.map