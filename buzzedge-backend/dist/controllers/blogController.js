"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.incrementPostViews = exports.searchPosts = exports.getPostsByCategory = exports.getPostBySlug = exports.getAllPosts = void 0;
const Post_1 = __importDefault(require("../models/Post"));
// GET /api/blogs - Get all published posts with pagination
const getAllPosts = async (req, res, next) => {
    try {
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 10;
        const skip = (page - 1) * limit;
        const posts = await Post_1.default.find({ status: "published" })
            .select("title slug excerpt featuredImage category tags publishedAt analytics.views")
            .sort({ publishedAt: -1 })
            .skip(skip)
            .limit(limit);
        const total = await Post_1.default.countDocuments({ status: "published" });
        const totalPages = Math.ceil(total / limit);
        res.status(200).json({
            success: true,
            data: {
                posts,
                pagination: {
                    currentPage: page,
                    totalPages,
                    totalPosts: total,
                    hasNext: page < totalPages,
                    hasPrev: page > 1,
                },
            },
        });
    }
    catch (error) {
        next(error);
    }
};
exports.getAllPosts = getAllPosts;
// GET /api/blogs/:slug - Get single post by slug
const getPostBySlug = async (req, res, next) => {
    try {
        const { slug } = req.params;
        const post = await Post_1.default.findOne({ slug, status: "published" });
        if (!post) {
            const error = new Error("Post not found");
            error.statusCode = 404;
            return next(error);
        }
        // Get related posts (same category, excluding current post)
        const relatedPosts = await Post_1.default.find({
            category: post.category,
            status: "published",
            _id: { $ne: post._id },
        })
            .select("title slug excerpt featuredImage publishedAt")
            .sort({ publishedAt: -1 })
            .limit(3);
        res.status(200).json({
            success: true,
            data: {
                post,
                relatedPosts,
            },
        });
    }
    catch (error) {
        next(error);
    }
};
exports.getPostBySlug = getPostBySlug;
// GET /api/blogs/category/:category - Get posts by category
const getPostsByCategory = async (req, res, next) => {
    try {
        const { category } = req.params;
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 10;
        const skip = (page - 1) * limit;
        const posts = await Post_1.default.find({
            category: { $regex: new RegExp(category, "i") },
            status: "published",
        })
            .select("title slug excerpt featuredImage category tags publishedAt analytics.views")
            .sort({ publishedAt: -1 })
            .skip(skip)
            .limit(limit);
        const total = await Post_1.default.countDocuments({
            category: { $regex: new RegExp(category, "i") },
            status: "published",
        });
        res.status(200).json({
            success: true,
            data: {
                posts,
                category,
                pagination: {
                    currentPage: page,
                    totalPages: Math.ceil(total / limit),
                    totalPosts: total,
                },
            },
        });
    }
    catch (error) {
        next(error);
    }
};
exports.getPostsByCategory = getPostsByCategory;
// GET /api/blogs/search?q=query - Search posts
const searchPosts = async (req, res, next) => {
    try {
        const { q } = req.query;
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 10;
        const skip = (page - 1) * limit;
        if (!q || typeof q !== "string") {
            const error = new Error("Search query is required");
            error.statusCode = 400;
            return next(error);
        }
        const posts = await Post_1.default.find({
            $and: [
                { status: "published" },
                {
                    $or: [
                        { title: { $regex: q, $options: "i" } },
                        { excerpt: { $regex: q, $options: "i" } },
                        { content: { $regex: q, $options: "i" } },
                        { tags: { $in: [new RegExp(q, "i")] } },
                    ],
                },
            ],
        })
            .select("title slug excerpt featuredImage category tags publishedAt analytics.views")
            .sort({ publishedAt: -1 })
            .skip(skip)
            .limit(limit);
        const total = await Post_1.default.countDocuments({
            $and: [
                { status: "published" },
                {
                    $or: [
                        { title: { $regex: q, $options: "i" } },
                        { excerpt: { $regex: q, $options: "i" } },
                        { content: { $regex: q, $options: "i" } },
                        { tags: { $in: [new RegExp(q, "i")] } },
                    ],
                },
            ],
        });
        res.status(200).json({
            success: true,
            data: {
                posts,
                query: q,
                pagination: {
                    currentPage: page,
                    totalPages: Math.ceil(total / limit),
                    totalPosts: total,
                },
            },
        });
    }
    catch (error) {
        next(error);
    }
};
exports.searchPosts = searchPosts;
// POST /api/blogs/:slug/view - Increment post views
const incrementPostViews = async (req, res, next) => {
    try {
        const { slug } = req.params;
        const post = await Post_1.default.findOneAndUpdate({ slug, status: "published" }, { $inc: { "analytics.views": 1 } }, { new: true });
        if (!post) {
            const error = new Error("Post not found");
            error.statusCode = 404;
            return next(error);
        }
        res.status(200).json({
            success: true,
            data: {
                views: post.analytics.views,
            },
        });
    }
    catch (error) {
        next(error);
    }
};
exports.incrementPostViews = incrementPostViews;
//# sourceMappingURL=blogController.js.map