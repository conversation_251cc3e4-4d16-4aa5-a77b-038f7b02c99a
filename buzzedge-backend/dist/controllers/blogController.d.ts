import { Request, Response, NextFunction } from 'express';
export declare const getAllPosts: (req: Request, res: Response, next: NextFunction) => Promise<void>;
export declare const getPostBySlug: (req: Request, res: Response, next: NextFunction) => Promise<void>;
export declare const getPostsByCategory: (req: Request, res: Response, next: NextFunction) => Promise<void>;
export declare const searchPosts: (req: Request, res: Response, next: NextFunction) => Promise<void>;
export declare const incrementPostViews: (req: Request, res: Response, next: NextFunction) => Promise<void>;
//# sourceMappingURL=blogController.d.ts.map