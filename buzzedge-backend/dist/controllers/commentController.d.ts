import { Request, Response, NextFunction } from "express";
export declare const getPostComments: (req: Request, res: Response, next: NextFunction) => Promise<void>;
export declare const createComment: (req: Request, res: Response, next: NextFunction) => Promise<void>;
export declare const updateComment: (req: Request, res: Response, next: NextFunction) => Promise<void>;
export declare const deleteComment: (req: Request, res: Response, next: NextFunction) => Promise<void>;
export declare const toggleCommentLike: (req: Request, res: Response, next: NextFunction) => Promise<void>;
export declare const getPendingComments: (req: Request, res: Response, next: NextFunction) => Promise<void>;
export declare const approveComment: (req: Request, res: Response, next: NextFunction) => Promise<void>;
export declare const rejectComment: (req: Request, res: Response, next: NextFunction) => Promise<void>;
export declare const bulkModerationAction: (req: Request, res: Response, next: NextFunction) => Promise<void>;
export declare const getCommentStats: (_req: Request, res: Response, next: NextFunction) => Promise<void>;
//# sourceMappingURL=commentController.d.ts.map