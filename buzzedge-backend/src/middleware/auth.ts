import { Request, Response, NextFunction } from "express";
import jwt from "jsonwebtoken";
import User, { IUser } from "../models/User";
import { CustomError } from "./errorHandler";

// Extend Request interface to include user
declare global {
  namespace Express {
    interface Request {
      user?: IUser;
    }
  }
}

export const authenticateAdmin = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    let token: string | undefined;

    // Check for token in Authorization header
    if (
      req.headers.authorization &&
      req.headers.authorization.startsWith("Bearer")
    ) {
      token = req.headers.authorization.split(" ")[1];
    }
    // Check for token in cookies
    else if (req.cookies?.token) {
      token = req.cookies.token;
    }

    if (!token) {
      const error: CustomError = new Error("Access denied. No token provided.");
      error.statusCode = 401;
      return next(error);
    }

    try {
      // Verify token
      const decoded = jwt.verify(token, process.env.JWT_SECRET as string) as {
        id: string;
      };

      // Get user from database
      const user = await User.findById(decoded.id);

      if (!user || !user.isActive) {
        const error: CustomError = new Error(
          "Access denied. User not found or inactive."
        );
        error.statusCode = 401;
        return next(error);
      }

      // Check if user has admin or editor role
      if (!["admin", "editor"].includes(user.role)) {
        const error: CustomError = new Error(
          "Access denied. Insufficient permissions."
        );
        error.statusCode = 403;
        return next(error);
      }

      // Add user to request object
      req.user = user;
      next();
    } catch (jwtError) {
      const error: CustomError = new Error("Access denied. Invalid token.");
      error.statusCode = 401;
      return next(error);
    }
  } catch (error) {
    next(error);
  }
};

export const requireAdmin = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    if (!req.user) {
      const error: CustomError = new Error("Authentication required.");
      error.statusCode = 401;
      return next(error);
    }

    if (req.user.role !== "admin") {
      const error: CustomError = new Error("Admin access required.");
      error.statusCode = 403;
      return next(error);
    }

    next();
  } catch (error) {
    next(error);
  }
};

export const generateToken = (userId: string): string => {
  const secret = process.env.JWT_SECRET;
  if (!secret) {
    throw new Error("JWT_SECRET is not defined");
  }
  const expiresIn = process.env.JWT_EXPIRES_IN || "7d";
  return jwt.sign({ id: userId }, secret, { expiresIn } as jwt.SignOptions);
};
