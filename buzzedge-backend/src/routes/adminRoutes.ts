import express from 'express';
import { 
  loginAdmin,
  getDashboardStats,
  getPendingPosts,
  approvePost,
  rejectPost,
  editPost,
  deletePost,
  generateContent
} from '../controllers/adminController';
import { authenticateAdmin } from '../middleware/auth';

const router = express.Router();

// POST /api/admin/login - Admin login
router.post('/login', loginAdmin);

// Protected routes (require authentication)
router.use(authenticateAdmin);

// GET /api/admin/dashboard - Get dashboard statistics
router.get('/dashboard', getDashboardStats);

// GET /api/admin/posts/pending - Get pending posts for review
router.get('/posts/pending', getPendingPosts);

// POST /api/admin/posts/:id/approve - Approve a post
router.post('/posts/:id/approve', approvePost);

// POST /api/admin/posts/:id/reject - Reject a post
router.post('/posts/:id/reject', rejectPost);

// PUT /api/admin/posts/:id - Edit a post
router.put('/posts/:id', editPost);

// DELETE /api/admin/posts/:id - Delete a post
router.delete('/posts/:id', deletePost);

// POST /api/admin/generate-content - Trigger AI content generation
router.post('/generate-content', generateContent);

export default router;
