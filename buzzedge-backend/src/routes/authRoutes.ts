import express from 'express';
import {
  registerUser,
  loginUser,
  getCurrentUser,
  updateProfile,
  verifyEmail,
  forgotPassword,
  resetPassword
} from '../controllers/authController';
import { authenticateUser } from '../middleware/auth';

const router = express.Router();

// POST /api/auth/register - Register a new user
router.post('/register', registerUser);

// POST /api/auth/login - Login user
router.post('/login', loginUser);

// POST /api/auth/verify-email - Verify email address
router.post('/verify-email', verifyEmail);

// POST /api/auth/forgot-password - Request password reset
router.post('/forgot-password', forgotPassword);

// POST /api/auth/reset-password - Reset password
router.post('/reset-password', resetPassword);

// Protected routes (require authentication)
router.use(authenticateUser);

// GET /api/auth/me - Get current user
router.get('/me', getCurrentUser);

// PUT /api/auth/profile - Update user profile
router.put('/profile', updateProfile);

export default router;
