import express from "express";
import {
  getPostComments,
  createComment,
  updateComment,
  deleteComment,
  toggleCommentLike,
  getCommentStats,
} from "../controllers/commentController";
import {
  authenticateUser,
  requireAdmin as authenticateAdmin,
} from "../middleware/auth";

const router = express.Router();

// GET /api/comments/:postId - Get comments for a post (public)
router.get("/:postId", getPostComments);

// POST /api/comments - Create a new comment (public, but can be authenticated)
router.post("/", createComment);

// PUT /api/comments/:id - Update a comment (authenticated users only)
router.put("/:id", authenticateUser, updateComment);

// DELETE /api/comments/:id - Delete a comment (authenticated users only)
router.delete("/:id", authenticateUser, deleteComment);

// POST /api/comments/:id/like - Like/unlike a comment (authenticated users only)
router.post("/:id/like", authenticateUser, toggleCommentLike);

// GET /api/comments/stats - Get comment statistics (admin only)
router.get("/admin/stats", authenticateAdmin, getCommentStats);

export default router;
