import express from "express";
import {
  getPostComments,
  createComment,
  updateComment,
  deleteComment,
  toggleCommentLike,
  getCommentStats,
  getPendingComments,
  approveComment,
  rejectComment,
  bulkModerationAction,
} from "../controllers/commentController";
import {
  authenticateUser,
  requireAdmin as authenticateAdmin,
} from "../middleware/auth";

const router = express.Router();

// GET /api/comments/:postId - Get comments for a post (public)
router.get("/:postId", getPostComments);

// POST /api/comments - Create a new comment (public, but can be authenticated)
router.post("/", createComment);

// PUT /api/comments/:id - Update a comment (authenticated users only)
router.put("/:id", authenticateUser, updateComment);

// DELETE /api/comments/:id - Delete a comment (authenticated users only)
router.delete("/:id", authenticateUser, deleteComment);

// POST /api/comments/:id/like - Like/unlike a comment (authenticated users only)
router.post("/:id/like", authenticateUser, toggleCommentLike);

// Admin routes for comment moderation
// GET /api/comments/admin/pending - Get pending comments for moderation
router.get("/admin/pending", authenticateAdmin, getPendingComments);

// POST /api/comments/admin/:id/approve - Approve a comment
router.post("/admin/:id/approve", authenticateAdmin, approveComment);

// POST /api/comments/admin/:id/reject - Reject a comment
router.post("/admin/:id/reject", authenticateAdmin, rejectComment);

// POST /api/comments/admin/bulk-action - Bulk approve/reject comments
router.post("/admin/bulk-action", authenticateAdmin, bulkModerationAction);

// GET /api/comments/admin/stats - Get comment statistics (admin only)
router.get("/admin/stats", authenticateAdmin, getCommentStats);

export default router;
