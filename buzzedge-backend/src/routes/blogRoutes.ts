import express from 'express';
import { 
  getAllPosts, 
  getPostBySlug, 
  getPostsByCategory,
  searchPosts,
  incrementPostViews
} from '../controllers/blogController';

const router = express.Router();

// GET /api/blogs - Get all published posts with pagination
router.get('/', getAllPosts);

// GET /api/blogs/search?q=query - Search posts
router.get('/search', searchPosts);

// GET /api/blogs/category/:category - Get posts by category
router.get('/category/:category', getPostsByCategory);

// GET /api/blogs/:slug - Get single post by slug
router.get('/:slug', getPostBySlug);

// POST /api/blogs/:slug/view - Increment post views
router.post('/:slug/view', incrementPostViews);

export default router;
