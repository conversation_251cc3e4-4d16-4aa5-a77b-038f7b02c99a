import { Request, Response, NextFunction } from "express";
import Post from "../models/Post";
import { CustomError } from "../middleware/errorHandler";

// GET /api/blogs - Get all published posts with pagination
export const getAllPosts = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    // Check if MongoDB is connected
    if (!process.env.MONGODB_URI) {
      // Return mock data for development
      const mockPosts = [
        {
          _id: "1",
          title: "ChatGPT vs Claude: Ultimate AI Assistant Comparison 2024",
          slug: "chatgpt-vs-claude-ai-assistant-comparison-2024",
          excerpt:
            "A comprehensive comparison between <PERSON><PERSON><PERSON><PERSON> and <PERSON>, two leading AI assistants. We analyze features, pricing, capabilities, and help you choose the right AI tool.",
          featuredImage: {
            url: "https://images.unsplash.com/photo-1677442136019-21780ecad995?w=800",
            alt: "AI comparison illustration",
          },
          category: "AI Tools",
          tags: ["AI", "Chat<PERSON><PERSON>", "<PERSON>", "Comparison"],
          publishedAt: new Date().toISOString(),
          analytics: { views: 1250 },
        },
      ];

      return res.status(200).json({
        success: true,
        data: {
          posts: mockPosts,
          pagination: {
            currentPage: 1,
            totalPages: 1,
            totalPosts: 1,
            hasNext: false,
            hasPrev: false,
          },
        },
      });
    }

    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const skip = (page - 1) * limit;

    const posts = await Post.find({ status: "published" })
      .select(
        "title slug excerpt featuredImage category tags publishedAt analytics.views"
      )
      .sort({ publishedAt: -1 })
      .skip(skip)
      .limit(limit);

    const total = await Post.countDocuments({ status: "published" });
    const totalPages = Math.ceil(total / limit);

    res.status(200).json({
      success: true,
      data: {
        posts,
        pagination: {
          currentPage: page,
          totalPages,
          totalPosts: total,
          hasNext: page < totalPages,
          hasPrev: page > 1,
        },
      },
    });
  } catch (error) {
    next(error);
  }
};

// GET /api/blogs/:slug - Get single post by slug
export const getPostBySlug = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const { slug } = req.params;

    const post = await Post.findOne({ slug, status: "published" });

    if (!post) {
      const error: CustomError = new Error("Post not found");
      error.statusCode = 404;
      return next(error);
    }

    // Get related posts (same category, excluding current post)
    const relatedPosts = await Post.find({
      category: post.category,
      status: "published",
      _id: { $ne: post._id },
    })
      .select("title slug excerpt featuredImage publishedAt")
      .sort({ publishedAt: -1 })
      .limit(3);

    res.status(200).json({
      success: true,
      data: {
        post,
        relatedPosts,
      },
    });
  } catch (error) {
    next(error);
  }
};

// GET /api/blogs/category/:category - Get posts by category
export const getPostsByCategory = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const { category } = req.params;
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const skip = (page - 1) * limit;

    const posts = await Post.find({
      category: { $regex: new RegExp(category, "i") },
      status: "published",
    })
      .select(
        "title slug excerpt featuredImage category tags publishedAt analytics.views"
      )
      .sort({ publishedAt: -1 })
      .skip(skip)
      .limit(limit);

    const total = await Post.countDocuments({
      category: { $regex: new RegExp(category, "i") },
      status: "published",
    });

    res.status(200).json({
      success: true,
      data: {
        posts,
        category,
        pagination: {
          currentPage: page,
          totalPages: Math.ceil(total / limit),
          totalPosts: total,
        },
      },
    });
  } catch (error) {
    next(error);
  }
};

// GET /api/blogs/search?q=query - Search posts
export const searchPosts = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const { q } = req.query;
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const skip = (page - 1) * limit;

    if (!q || typeof q !== "string") {
      const error: CustomError = new Error("Search query is required");
      error.statusCode = 400;
      return next(error);
    }

    const posts = await Post.find({
      $and: [
        { status: "published" },
        {
          $or: [
            { title: { $regex: q, $options: "i" } },
            { excerpt: { $regex: q, $options: "i" } },
            { content: { $regex: q, $options: "i" } },
            { tags: { $in: [new RegExp(q, "i")] } },
          ],
        },
      ],
    })
      .select(
        "title slug excerpt featuredImage category tags publishedAt analytics.views"
      )
      .sort({ publishedAt: -1 })
      .skip(skip)
      .limit(limit);

    const total = await Post.countDocuments({
      $and: [
        { status: "published" },
        {
          $or: [
            { title: { $regex: q, $options: "i" } },
            { excerpt: { $regex: q, $options: "i" } },
            { content: { $regex: q, $options: "i" } },
            { tags: { $in: [new RegExp(q, "i")] } },
          ],
        },
      ],
    });

    res.status(200).json({
      success: true,
      data: {
        posts,
        query: q,
        pagination: {
          currentPage: page,
          totalPages: Math.ceil(total / limit),
          totalPosts: total,
        },
      },
    });
  } catch (error) {
    next(error);
  }
};

// POST /api/blogs/:slug/view - Increment post views
export const incrementPostViews = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const { slug } = req.params;

    const post = await Post.findOneAndUpdate(
      { slug, status: "published" },
      { $inc: { "analytics.views": 1 } },
      { new: true }
    );

    if (!post) {
      const error: CustomError = new Error("Post not found");
      error.statusCode = 404;
      return next(error);
    }

    res.status(200).json({
      success: true,
      data: {
        views: post.analytics.views,
      },
    });
  } catch (error) {
    next(error);
  }
};
