import { Request, Response, NextFunction } from "express";
import Comment, { ICommentModel } from "../models/Comment";
import PublicUser from "../models/PublicUser";
import Post from "../models/Post";
import { CustomError } from "../middleware/errorHandler";

// GET /api/comments/:postId - Get comments for a post
export const getPostComments = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const { postId } = req.params;
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const skip = (page - 1) * limit;

    // Verify post exists
    const post = await Post.findById(postId);
    if (!post) {
      const error: CustomError = new Error("Post not found");
      error.statusCode = 404;
      return next(error);
    }

    // Get comments with replies
    const comments = await (Comment as ICommentModel).getPostComments(postId);

    // Apply pagination to top-level comments
    const paginatedComments = comments.slice(skip, skip + limit);
    const totalComments = comments.length;

    res.status(200).json({
      success: true,
      data: {
        comments: paginatedComments,
        pagination: {
          currentPage: page,
          totalPages: Math.ceil(totalComments / limit),
          totalComments,
          hasNext: page * limit < totalComments,
          hasPrev: page > 1,
        },
      },
    });
  } catch (error) {
    next(error);
  }
};

// POST /api/comments - Create a new comment
export const createComment = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const { content, postId, parentCommentId, author } = req.body;

    // Validate required fields
    if (!content || !postId || !author?.name || !author?.email) {
      const error: CustomError = new Error("Missing required fields");
      error.statusCode = 400;
      return next(error);
    }

    // Verify post exists
    const post = await Post.findById(postId);
    if (!post) {
      const error: CustomError = new Error("Post not found");
      error.statusCode = 404;
      return next(error);
    }

    // Verify parent comment exists if provided
    if (parentCommentId) {
      const parentComment = await Comment.findById(parentCommentId);
      if (!parentComment) {
        const error: CustomError = new Error("Parent comment not found");
        error.statusCode = 404;
        return next(error);
      }
    }

    // Create or find guest user
    let user = null;
    if (author.userId) {
      user = await PublicUser.findById(author.userId);
    } else {
      user = await (PublicUser as any).createGuestUser(
        author.name,
        author.email
      );
    }

    // Create comment
    const comment = new Comment({
      content,
      post: postId,
      parentComment: parentCommentId || undefined,
      author: {
        name: author.name,
        email: author.email,
        avatar: user?.avatar || author.avatar,
        userId: user?._id,
      },
      ipAddress: req.ip,
      userAgent: req.get("User-Agent"),
    });

    await comment.save();

    // Update user comment count
    if (user) {
      await PublicUser.findByIdAndUpdate(user._id, {
        $inc: { "stats.commentsCount": 1 },
      });
    }

    res.status(201).json({
      success: true,
      data: { comment },
      message: "Comment submitted for moderation",
    });
  } catch (error) {
    next(error);
  }
};

// PUT /api/comments/:id - Update a comment
export const updateComment = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const { id } = req.params;
    const { content } = req.body;
    const userId = req.user?.id; // From auth middleware

    if (!content) {
      const error: CustomError = new Error("Content is required");
      error.statusCode = 400;
      return next(error);
    }

    const comment = await Comment.findById(id);
    if (!comment) {
      const error: CustomError = new Error("Comment not found");
      error.statusCode = 404;
      return next(error);
    }

    // Check if user owns the comment
    if (comment.author.userId?.toString() !== userId) {
      const error: CustomError = new Error(
        "Not authorized to edit this comment"
      );
      error.statusCode = 403;
      return next(error);
    }

    // Update comment
    comment.content = content;
    comment.isEdited = true;
    comment.editedAt = new Date();
    comment.status = "pending"; // Re-moderate edited comments

    await comment.save();

    res.status(200).json({
      success: true,
      data: { comment },
      message: "Comment updated and submitted for re-moderation",
    });
  } catch (error) {
    next(error);
  }
};

// DELETE /api/comments/:id - Delete a comment
export const deleteComment = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const { id } = req.params;
    const userId = req.user?.id; // From auth middleware

    const comment = await Comment.findById(id);
    if (!comment) {
      const error: CustomError = new Error("Comment not found");
      error.statusCode = 404;
      return next(error);
    }

    // Check if user owns the comment
    if (comment.author.userId?.toString() !== userId) {
      const error: CustomError = new Error(
        "Not authorized to delete this comment"
      );
      error.statusCode = 403;
      return next(error);
    }

    // Soft delete by updating status
    comment.status = "rejected";
    await comment.save();

    // Update user comment count
    if (comment.author.userId) {
      await PublicUser.findByIdAndUpdate(comment.author.userId, {
        $inc: { "stats.commentsCount": -1 },
      });
    }

    res.status(200).json({
      success: true,
      message: "Comment deleted successfully",
    });
  } catch (error) {
    next(error);
  }
};

// POST /api/comments/:id/like - Like/unlike a comment
export const toggleCommentLike = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const { id } = req.params;
    const userId = req.user?.id; // From auth middleware

    if (!userId) {
      const error: CustomError = new Error("Authentication required");
      error.statusCode = 401;
      return next(error);
    }

    const comment = await Comment.findById(id);
    if (!comment) {
      const error: CustomError = new Error("Comment not found");
      error.statusCode = 404;
      return next(error);
    }

    const userObjectId = new (require("mongoose").Types.ObjectId)(userId);
    const hasLiked = comment.likedBy.includes(userObjectId);

    if (hasLiked) {
      // Unlike
      comment.likedBy = comment.likedBy.filter(
        (id) => !id.equals(userObjectId)
      );
      comment.likes = Math.max(0, comment.likes - 1);
    } else {
      // Like
      comment.likedBy.push(userObjectId);
      comment.likes += 1;
    }

    await comment.save();

    res.status(200).json({
      success: true,
      data: {
        likes: comment.likes,
        hasLiked: !hasLiked,
      },
    });
  } catch (error) {
    next(error);
  }
};

// GET /api/comments/stats - Get comment statistics (admin only)
export const getCommentStats = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const stats = await (Comment as ICommentModel).getCommentStats();

    res.status(200).json({
      success: true,
      data: { stats },
    });
  } catch (error) {
    next(error);
  }
};
