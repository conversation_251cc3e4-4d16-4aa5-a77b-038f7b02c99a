import { Request, Response, NextFunction } from "express";
import Comment, { ICommentModel } from "../models/Comment";
import PublicUser from "../models/PublicUser";
import Post from "../models/Post";
import { CustomError } from "../middleware/errorHandler";
import ModerationService from "../services/moderationService";

// GET /api/comments/:postId - Get comments for a post
export const getPostComments = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const { postId } = req.params;
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const skip = (page - 1) * limit;

    // Verify post exists
    const post = await Post.findById(postId);
    if (!post) {
      const error: CustomError = new Error("Post not found");
      error.statusCode = 404;
      return next(error);
    }

    // Get comments with replies
    const comments = await (Comment as ICommentModel).getPostComments(postId);

    // Apply pagination to top-level comments
    const paginatedComments = comments.slice(skip, skip + limit);
    const totalComments = comments.length;

    res.status(200).json({
      success: true,
      data: {
        comments: paginatedComments,
        pagination: {
          currentPage: page,
          totalPages: Math.ceil(totalComments / limit),
          totalComments,
          hasNext: page * limit < totalComments,
          hasPrev: page > 1,
        },
      },
    });
  } catch (error) {
    next(error);
  }
};

// POST /api/comments - Create a new comment
export const createComment = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const { content, postId, parentCommentId, author } = req.body;

    // Validate required fields
    if (!content || !postId || !author?.name || !author?.email) {
      const error: CustomError = new Error("Missing required fields");
      error.statusCode = 400;
      return next(error);
    }

    // Verify post exists
    const post = await Post.findById(postId);
    if (!post) {
      const error: CustomError = new Error("Post not found");
      error.statusCode = 404;
      return next(error);
    }

    // Verify parent comment exists if provided
    if (parentCommentId) {
      const parentComment = await Comment.findById(parentCommentId);
      if (!parentComment) {
        const error: CustomError = new Error("Parent comment not found");
        error.statusCode = 404;
        return next(error);
      }
    }

    // Create or find guest user
    let user = null;
    if (author.userId) {
      user = await PublicUser.findById(author.userId);
    } else {
      user = await (PublicUser as any).createGuestUser(
        author.name,
        author.email
      );
    }

    // Automatic content moderation
    const moderationResult = await ModerationService.moderateComment(
      content,
      author.email,
      true // Use AI moderation if available
    );

    // Determine comment status based on moderation
    let status = "pending";
    let moderationNotes = "";

    if (moderationResult.isApproved && moderationResult.confidence >= 90) {
      // High confidence approval - auto-approve
      status = "approved";
      moderationNotes = `Auto-approved (confidence: ${moderationResult.confidence}%)`;
    } else if (moderationResult.confidence <= 30) {
      // Very low confidence - auto-reject
      status = "rejected";
      moderationNotes = `Auto-rejected: ${moderationResult.reasons.join(", ")}`;
    } else {
      // Medium confidence - manual review needed
      status = "pending";
      moderationNotes = `Pending review: ${moderationResult.reasons.join(
        ", "
      )}`;
    }

    // Check if user is trusted (auto-approve trusted users)
    const isTrusted = await ModerationService.isTrustedUser(author.email);
    if (isTrusted && status !== "rejected") {
      status = "approved";
      moderationNotes = "Auto-approved (trusted user)";
    }

    // Create comment
    const comment = new Comment({
      content,
      post: postId,
      parentComment: parentCommentId || undefined,
      author: {
        name: author.name,
        email: author.email,
        avatar: user?.avatar || author.avatar,
        userId: user?._id,
      },
      status,
      moderationNotes,
      moderationScore: moderationResult.confidence,
      ipAddress: req.ip,
      userAgent: req.get("User-Agent"),
    });

    await comment.save();

    // Update user comment count
    if (user) {
      await PublicUser.findByIdAndUpdate(user._id, {
        $inc: { "stats.commentsCount": 1 },
      });
    }

    // Determine response message based on status
    let responseMessage = "";
    let responseStatus = 201;

    switch (status) {
      case "approved":
        responseMessage = "Comment posted successfully!";
        break;
      case "rejected":
        responseMessage =
          "Comment was rejected due to content policy violations.";
        responseStatus = 400;
        break;
      default:
        responseMessage = "Comment submitted for moderation review.";
    }

    res.status(responseStatus).json({
      success: status !== "rejected",
      data: {
        comment: {
          ...comment.toObject(),
          moderationInfo: {
            autoModerated: moderationResult.autoModerated,
            confidence: moderationResult.confidence,
            category: moderationResult.category,
          },
        },
      },
      message: responseMessage,
    });
  } catch (error) {
    next(error);
  }
};

// PUT /api/comments/:id - Update a comment
export const updateComment = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const { id } = req.params;
    const { content } = req.body;
    const userId = req.user?.id; // From auth middleware

    if (!content) {
      const error: CustomError = new Error("Content is required");
      error.statusCode = 400;
      return next(error);
    }

    const comment = await Comment.findById(id);
    if (!comment) {
      const error: CustomError = new Error("Comment not found");
      error.statusCode = 404;
      return next(error);
    }

    // Check if user owns the comment
    if (comment.author.userId?.toString() !== userId) {
      const error: CustomError = new Error(
        "Not authorized to edit this comment"
      );
      error.statusCode = 403;
      return next(error);
    }

    // Update comment
    comment.content = content;
    comment.isEdited = true;
    comment.editedAt = new Date();
    comment.status = "pending"; // Re-moderate edited comments

    await comment.save();

    res.status(200).json({
      success: true,
      data: { comment },
      message: "Comment updated and submitted for re-moderation",
    });
  } catch (error) {
    next(error);
  }
};

// DELETE /api/comments/:id - Delete a comment
export const deleteComment = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const { id } = req.params;
    const userId = req.user?.id; // From auth middleware

    const comment = await Comment.findById(id);
    if (!comment) {
      const error: CustomError = new Error("Comment not found");
      error.statusCode = 404;
      return next(error);
    }

    // Check if user owns the comment
    if (comment.author.userId?.toString() !== userId) {
      const error: CustomError = new Error(
        "Not authorized to delete this comment"
      );
      error.statusCode = 403;
      return next(error);
    }

    // Soft delete by updating status
    comment.status = "rejected";
    await comment.save();

    // Update user comment count
    if (comment.author.userId) {
      await PublicUser.findByIdAndUpdate(comment.author.userId, {
        $inc: { "stats.commentsCount": -1 },
      });
    }

    res.status(200).json({
      success: true,
      message: "Comment deleted successfully",
    });
  } catch (error) {
    next(error);
  }
};

// POST /api/comments/:id/like - Like/unlike a comment
export const toggleCommentLike = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const { id } = req.params;
    const userId = req.user?.id; // From auth middleware

    if (!userId) {
      const error: CustomError = new Error("Authentication required");
      error.statusCode = 401;
      return next(error);
    }

    const comment = await Comment.findById(id);
    if (!comment) {
      const error: CustomError = new Error("Comment not found");
      error.statusCode = 404;
      return next(error);
    }

    const userObjectId = new (require("mongoose").Types.ObjectId)(userId);
    const hasLiked = comment.likedBy.includes(userObjectId);

    if (hasLiked) {
      // Unlike
      comment.likedBy = comment.likedBy.filter(
        (id) => !id.equals(userObjectId)
      );
      comment.likes = Math.max(0, comment.likes - 1);
    } else {
      // Like
      comment.likedBy.push(userObjectId);
      comment.likes += 1;
    }

    await comment.save();

    res.status(200).json({
      success: true,
      data: {
        likes: comment.likes,
        hasLiked: !hasLiked,
      },
    });
  } catch (error) {
    next(error);
  }
};

// GET /api/comments/admin/pending - Get pending comments for moderation
export const getPendingComments = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 20;
    const skip = (page - 1) * limit;

    const pendingComments = await Comment.find({ status: "pending" })
      .populate("post", "title slug")
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit);

    const totalPending = await Comment.countDocuments({ status: "pending" });

    res.status(200).json({
      success: true,
      data: {
        comments: pendingComments,
        pagination: {
          currentPage: page,
          totalPages: Math.ceil(totalPending / limit),
          totalComments: totalPending,
          hasNext: page * limit < totalPending,
          hasPrev: page > 1,
        },
      },
    });
  } catch (error) {
    next(error);
  }
};

// POST /api/comments/admin/:id/approve - Approve a comment
export const approveComment = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const { id } = req.params;
    const { moderationNotes } = req.body;

    const comment = await Comment.findById(id);
    if (!comment) {
      const error: CustomError = new Error("Comment not found");
      error.statusCode = 404;
      return next(error);
    }

    comment.status = "approved";
    comment.moderationNotes = moderationNotes || "Manually approved by admin";
    comment.moderatedAt = new Date();
    comment.moderatedBy = req.user?.id;

    await comment.save();

    res.status(200).json({
      success: true,
      data: { comment },
      message: "Comment approved successfully",
    });
  } catch (error) {
    next(error);
  }
};

// POST /api/comments/admin/:id/reject - Reject a comment
export const rejectComment = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const { id } = req.params;
    const { moderationNotes } = req.body;

    const comment = await Comment.findById(id);
    if (!comment) {
      const error: CustomError = new Error("Comment not found");
      error.statusCode = 404;
      return next(error);
    }

    comment.status = "rejected";
    comment.moderationNotes = moderationNotes || "Manually rejected by admin";
    comment.moderatedAt = new Date();
    comment.moderatedBy = req.user?.id;

    await comment.save();

    res.status(200).json({
      success: true,
      data: { comment },
      message: "Comment rejected successfully",
    });
  } catch (error) {
    next(error);
  }
};

// POST /api/comments/admin/bulk-action - Bulk approve/reject comments
export const bulkModerationAction = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const { commentIds, action, moderationNotes } = req.body;

    if (!commentIds || !Array.isArray(commentIds) || commentIds.length === 0) {
      const error: CustomError = new Error("Comment IDs are required");
      error.statusCode = 400;
      return next(error);
    }

    if (!["approve", "reject"].includes(action)) {
      const error: CustomError = new Error(
        "Invalid action. Use 'approve' or 'reject'"
      );
      error.statusCode = 400;
      return next(error);
    }

    const updateData = {
      status: action === "approve" ? "approved" : "rejected",
      moderationNotes: moderationNotes || `Bulk ${action}d by admin`,
      moderatedAt: new Date(),
      moderatedBy: req.user?.id,
    };

    const result = await Comment.updateMany(
      { _id: { $in: commentIds } },
      updateData
    );

    res.status(200).json({
      success: true,
      data: {
        modifiedCount: result.modifiedCount,
        action,
      },
      message: `${result.modifiedCount} comments ${action}d successfully`,
    });
  } catch (error) {
    next(error);
  }
};

// GET /api/comments/stats - Get comment statistics (admin only)
export const getCommentStats = async (
  _req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const stats = await (Comment as ICommentModel).getCommentStats();

    // Additional moderation stats
    const moderationStats = await Comment.aggregate([
      {
        $group: {
          _id: "$status",
          count: { $sum: 1 },
        },
      },
    ]);

    const autoModerationStats = await Comment.aggregate([
      {
        $match: { moderationScore: { $exists: true } },
      },
      {
        $group: {
          _id: null,
          avgConfidence: { $avg: "$moderationScore" },
          autoApproved: {
            $sum: {
              $cond: [{ $eq: ["$status", "approved"] }, 1, 0],
            },
          },
          autoRejected: {
            $sum: {
              $cond: [{ $eq: ["$status", "rejected"] }, 1, 0],
            },
          },
          pendingReview: {
            $sum: {
              $cond: [{ $eq: ["$status", "pending"] }, 1, 0],
            },
          },
        },
      },
    ]);

    res.status(200).json({
      success: true,
      data: {
        stats,
        moderationStats,
        autoModerationStats: autoModerationStats[0] || {
          avgConfidence: 0,
          autoApproved: 0,
          autoRejected: 0,
          pendingReview: 0,
        },
      },
    });
  } catch (error) {
    next(error);
  }
};
