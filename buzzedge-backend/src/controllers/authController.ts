import { Request, Response, NextFunction } from "express";
import PublicUser from "../models/PublicUser";
import { CustomError } from "../middleware/errorHandler";
import { generateToken } from "../middleware/auth";
import crypto from "crypto";

// POST /api/auth/register - Register a new user
export const registerUser = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const { name, email, password } = req.body;

    // Validate required fields
    if (!name || !email || !password) {
      const error: CustomError = new Error(
        "Name, email, and password are required"
      );
      error.statusCode = 400;
      return next(error);
    }

    // Check if user already exists
    const existingUser = await PublicUser.findOne({ email });
    if (existingUser) {
      const error: CustomError = new Error(
        "User already exists with this email"
      );
      error.statusCode = 409;
      return next(error);
    }

    // Create verification token
    const verificationToken = crypto.randomBytes(32).toString("hex");

    // Create user
    const user = new PublicUser({
      name,
      email,
      password,
      verificationToken,
      isVerified: false, // Require email verification
    });

    await user.save();

    // Generate JWT token
    const token = generateToken(String(user._id));

    // TODO: Send verification email
    // await sendVerificationEmail(user.email, verificationToken);

    res.status(201).json({
      success: true,
      data: {
        token,
        user: {
          id: user._id,
          name: user.name,
          email: user.email,
          avatar: user.avatar,
          isVerified: user.isVerified,
        },
      },
      message:
        "User registered successfully. Please check your email for verification.",
    });
  } catch (error) {
    next(error);
  }
};

// POST /api/auth/login - Login user
export const loginUser = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const { email, password } = req.body;

    // Validate required fields
    if (!email || !password) {
      const error: CustomError = new Error("Email and password are required");
      error.statusCode = 400;
      return next(error);
    }

    // Find user and include password
    const user = await PublicUser.findOne({ email, isActive: true }).select(
      "+password"
    );
    if (!user) {
      const error: CustomError = new Error("Invalid credentials");
      error.statusCode = 401;
      return next(error);
    }

    // Check password
    const isMatch = await user.comparePassword!(password);
    if (!isMatch) {
      const error: CustomError = new Error("Invalid credentials");
      error.statusCode = 401;
      return next(error);
    }

    // Update last login
    user.lastLogin = new Date();
    await user.save();

    // Generate JWT token
    const token = generateToken(String(user._id));

    res.status(200).json({
      success: true,
      data: {
        token,
        user: {
          id: user._id,
          name: user.name,
          email: user.email,
          avatar: user.avatar,
          isVerified: user.isVerified,
          bio: user.bio,
          website: user.website,
        },
      },
    });
  } catch (error) {
    next(error);
  }
};

// GET /api/auth/me - Get current user
export const getCurrentUser = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const userId = req.user?.id;

    const user = await PublicUser.findById(userId);
    if (!user) {
      const error: CustomError = new Error("User not found");
      error.statusCode = 404;
      return next(error);
    }

    res.status(200).json({
      success: true,
      data: {
        user: {
          id: user._id,
          name: user.name,
          email: user.email,
          avatar: user.avatar,
          isVerified: user.isVerified,
          bio: user.bio,
          website: user.website,
          socialLinks: user.socialLinks,
          preferences: user.preferences,
          stats: user.stats,
        },
      },
    });
  } catch (error) {
    next(error);
  }
};

// PUT /api/auth/profile - Update user profile
export const updateProfile = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const userId = req.user?.id;
    const { name, bio, website, socialLinks, preferences } = req.body;

    const user = await PublicUser.findById(userId);
    if (!user) {
      const error: CustomError = new Error("User not found");
      error.statusCode = 404;
      return next(error);
    }

    // Update fields
    if (name) user.name = name;
    if (bio !== undefined) user.bio = bio;
    if (website !== undefined) user.website = website;
    if (socialLinks) user.socialLinks = { ...user.socialLinks, ...socialLinks };
    if (preferences) user.preferences = { ...user.preferences, ...preferences };

    await user.save();

    res.status(200).json({
      success: true,
      data: {
        user: {
          id: user._id,
          name: user.name,
          email: user.email,
          avatar: user.avatar,
          isVerified: user.isVerified,
          bio: user.bio,
          website: user.website,
          socialLinks: user.socialLinks,
          preferences: user.preferences,
          stats: user.stats,
        },
      },
      message: "Profile updated successfully",
    });
  } catch (error) {
    next(error);
  }
};

// POST /api/auth/verify-email - Verify email address
export const verifyEmail = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const { token } = req.body;

    if (!token) {
      const error: CustomError = new Error("Verification token is required");
      error.statusCode = 400;
      return next(error);
    }

    const user = await PublicUser.findOne({ verificationToken: token });
    if (!user) {
      const error: CustomError = new Error(
        "Invalid or expired verification token"
      );
      error.statusCode = 400;
      return next(error);
    }

    // Verify user
    user.isVerified = true;
    user.verificationToken = undefined;
    await user.save();

    res.status(200).json({
      success: true,
      message: "Email verified successfully",
    });
  } catch (error) {
    next(error);
  }
};

// POST /api/auth/forgot-password - Request password reset
export const forgotPassword = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const { email } = req.body;

    if (!email) {
      const error: CustomError = new Error("Email is required");
      error.statusCode = 400;
      return next(error);
    }

    const user = await PublicUser.findOne({ email, isActive: true });
    if (!user) {
      // Don't reveal if user exists
      return res.status(200).json({
        success: true,
        message:
          "If an account with that email exists, a password reset link has been sent.",
      });
    }

    // Generate reset token
    const resetToken = crypto.randomBytes(32).toString("hex");
    user.passwordResetToken = resetToken;
    user.passwordResetExpires = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes

    await user.save();

    // TODO: Send password reset email
    // await sendPasswordResetEmail(user.email, resetToken);

    res.status(200).json({
      success: true,
      message:
        "If an account with that email exists, a password reset link has been sent.",
    });
  } catch (error) {
    next(error);
  }
};

// POST /api/auth/reset-password - Reset password
export const resetPassword = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const { token, password } = req.body;

    if (!token || !password) {
      const error: CustomError = new Error("Token and password are required");
      error.statusCode = 400;
      return next(error);
    }

    const user = await PublicUser.findOne({
      passwordResetToken: token,
      passwordResetExpires: { $gt: Date.now() },
    });

    if (!user) {
      const error: CustomError = new Error("Invalid or expired reset token");
      error.statusCode = 400;
      return next(error);
    }

    // Update password
    user.password = password;
    user.passwordResetToken = undefined;
    user.passwordResetExpires = undefined;

    await user.save();

    res.status(200).json({
      success: true,
      message: "Password reset successfully",
    });
  } catch (error) {
    next(error);
  }
};
