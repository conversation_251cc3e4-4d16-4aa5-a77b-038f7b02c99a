import mongoose, { Document, Schema } from "mongoose";

export interface IComment extends Document {
  content: string;
  author: {
    name: string;
    email: string;
    avatar?: string;
    userId?: mongoose.Types.ObjectId;
  };
  post: mongoose.Types.ObjectId;
  parentComment?: mongoose.Types.ObjectId; // For nested replies
  status: "pending" | "approved" | "rejected" | "spam";
  likes: number;
  likedBy: mongoose.Types.ObjectId[];
  isEdited: boolean;
  editedAt?: Date;
  moderationNotes?: string;
  ipAddress?: string;
  userAgent?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface ICommentModel extends mongoose.Model<IComment> {
  getPostComments(postId: string, includeReplies?: boolean): Promise<any[]>;
  getCommentStats(): Promise<any>;
}

const CommentSchema = new Schema<IComment>(
  {
    content: {
      type: String,
      required: true,
      trim: true,
      minlength: 1,
      maxlength: 1000,
    },
    author: {
      name: {
        type: String,
        required: true,
        trim: true,
        maxlength: 50,
      },
      email: {
        type: String,
        required: true,
        lowercase: true,
        trim: true,
        match: [
          /^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/,
          "Please enter a valid email",
        ],
      },
      avatar: {
        type: String,
        default: "",
      },
      userId: {
        type: Schema.Types.ObjectId,
        ref: "PublicUser",
        required: false,
      },
    },
    post: {
      type: Schema.Types.ObjectId,
      ref: "Post",
      required: true,
    },
    parentComment: {
      type: Schema.Types.ObjectId,
      ref: "Comment",
      required: false,
    },
    status: {
      type: String,
      enum: ["pending", "approved", "rejected", "spam"],
      default: "pending",
    },
    likes: {
      type: Number,
      default: 0,
      min: 0,
    },
    likedBy: [
      {
        type: Schema.Types.ObjectId,
        ref: "PublicUser",
      },
    ],
    isEdited: {
      type: Boolean,
      default: false,
    },
    editedAt: Date,
    moderationNotes: {
      type: String,
      trim: true,
      maxlength: 500,
    },
    ipAddress: String,
    userAgent: String,
  },
  {
    timestamps: true,
  }
);

// Indexes
CommentSchema.index({ post: 1, status: 1, createdAt: -1 });
CommentSchema.index({ "author.email": 1 });
CommentSchema.index({ status: 1, createdAt: -1 });
CommentSchema.index({ parentComment: 1 });
CommentSchema.index({ likes: -1 });

// Virtual for replies count
CommentSchema.virtual("repliesCount", {
  ref: "Comment",
  localField: "_id",
  foreignField: "parentComment",
  count: true,
  match: { status: "approved" },
});

// Pre-save middleware to generate avatar if not provided
CommentSchema.pre("save", function (next) {
  if (!this.author.avatar && this.author.email) {
    // Generate Gravatar URL
    const crypto = require("crypto");
    const hash = crypto
      .createHash("md5")
      .update(this.author.email.toLowerCase())
      .digest("hex");
    this.author.avatar = `https://www.gravatar.com/avatar/${hash}?d=identicon&s=40`;
  }
  next();
});

// Static method to get comments for a post
CommentSchema.statics.getPostComments = async function (
  postId: string,
  includeReplies: boolean = true
) {
  const pipeline = [
    {
      $match: {
        post: new mongoose.Types.ObjectId(postId),
        status: "approved",
        parentComment: { $exists: false }, // Only top-level comments
      },
    },
    {
      $sort: { createdAt: -1 as const },
    },
  ];

  if (includeReplies) {
    pipeline.push({
      $lookup: {
        from: "comments",
        let: { commentId: "$_id" },
        pipeline: [
          {
            $match: {
              $expr: {
                $and: [
                  { $eq: ["$parentComment", "$$commentId"] },
                  { $eq: ["$status", "approved"] },
                ],
              },
            },
          },
          { $sort: { createdAt: 1 as const } },
        ],
        as: "replies",
      },
    } as any);
  }

  return this.aggregate(pipeline);
};

// Static method to get comment statistics
CommentSchema.statics.getCommentStats = async function () {
  const stats = await this.aggregate([
    {
      $group: {
        _id: "$status",
        count: { $sum: 1 },
      },
    },
  ]);

  const result = {
    total: 0,
    pending: 0,
    approved: 0,
    rejected: 0,
    spam: 0,
  };

  stats.forEach((stat: any) => {
    result[stat._id as keyof typeof result] = stat.count;
    result.total += stat.count;
  });

  return result;
};

export default mongoose.model<IComment, ICommentModel>(
  "Comment",
  CommentSchema
);
