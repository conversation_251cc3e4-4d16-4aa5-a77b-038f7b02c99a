import mongoose, { Document, Schema } from 'mongoose';
import bcrypt from 'bcryptjs';

export interface IPublicUser extends Document {
  email: string;
  password?: string; // Optional for guest users
  name: string;
  avatar?: string;
  bio?: string;
  website?: string;
  isVerified: boolean;
  isActive: boolean;
  preferences: {
    emailNotifications: boolean;
    marketingEmails: boolean;
    commentReplies: boolean;
  };
  socialLinks: {
    twitter?: string;
    linkedin?: string;
    github?: string;
  };
  stats: {
    commentsCount: number;
    likesReceived: number;
    likesGiven: number;
  };
  lastLogin?: Date;
  verificationToken?: string;
  passwordResetToken?: string;
  passwordResetExpires?: Date;
  createdAt: Date;
  updatedAt: Date;
  comparePassword?(candidatePassword: string): Promise<boolean>;
}

const PublicUserSchema = new Schema<IPublicUser>({
  email: {
    type: String,
    required: true,
    unique: true,
    lowercase: true,
    trim: true,
    match: [
      /^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/,
      'Please enter a valid email'
    ]
  },
  password: {
    type: String,
    minlength: 6,
    select: false // Don't include password in queries by default
  },
  name: {
    type: String,
    required: true,
    trim: true,
    maxlength: 50
  },
  avatar: {
    type: String,
    default: ''
  },
  bio: {
    type: String,
    trim: true,
    maxlength: 200
  },
  website: {
    type: String,
    trim: true,
    match: [
      /^https?:\/\/.+/,
      'Please enter a valid URL'
    ]
  },
  isVerified: {
    type: Boolean,
    default: false
  },
  isActive: {
    type: Boolean,
    default: true
  },
  preferences: {
    emailNotifications: {
      type: Boolean,
      default: true
    },
    marketingEmails: {
      type: Boolean,
      default: false
    },
    commentReplies: {
      type: Boolean,
      default: true
    }
  },
  socialLinks: {
    twitter: {
      type: String,
      trim: true,
      match: [/^https?:\/\/(www\.)?twitter\.com\/\w+/, 'Please enter a valid Twitter URL']
    },
    linkedin: {
      type: String,
      trim: true,
      match: [/^https?:\/\/(www\.)?linkedin\.com\/in\/[\w-]+/, 'Please enter a valid LinkedIn URL']
    },
    github: {
      type: String,
      trim: true,
      match: [/^https?:\/\/(www\.)?github\.com\/\w+/, 'Please enter a valid GitHub URL']
    }
  },
  stats: {
    commentsCount: {
      type: Number,
      default: 0,
      min: 0
    },
    likesReceived: {
      type: Number,
      default: 0,
      min: 0
    },
    likesGiven: {
      type: Number,
      default: 0,
      min: 0
    }
  },
  lastLogin: Date,
  verificationToken: String,
  passwordResetToken: String,
  passwordResetExpires: Date
}, {
  timestamps: true
});

// Indexes
PublicUserSchema.index({ email: 1 }, { unique: true });
PublicUserSchema.index({ isActive: 1 });
PublicUserSchema.index({ isVerified: 1 });
PublicUserSchema.index({ 'stats.commentsCount': -1 });

// Pre-save middleware to hash password
PublicUserSchema.pre('save', async function(next) {
  // Only hash the password if it has been modified (or is new) and exists
  if (!this.isModified('password') || !this.password) return next();

  try {
    // Hash password with cost of 10 (lighter than admin users)
    const salt = await bcrypt.genSalt(10);
    this.password = await bcrypt.hash(this.password, salt);
    next();
  } catch (error) {
    next(error as Error);
  }
});

// Pre-save middleware to generate avatar if not provided
PublicUserSchema.pre('save', function(next) {
  if (!this.avatar && this.email) {
    // Generate Gravatar URL
    const crypto = require('crypto');
    const hash = crypto.createHash('md5').update(this.email.toLowerCase()).digest('hex');
    this.avatar = `https://www.gravatar.com/avatar/${hash}?d=identicon&s=80`;
  }
  next();
});

// Instance method to compare password
PublicUserSchema.methods.comparePassword = async function(candidatePassword: string): Promise<boolean> {
  if (!this.password) return false;
  
  try {
    return await bcrypt.compare(candidatePassword, this.password);
  } catch (error) {
    throw error;
  }
};

// Static method to create guest user (for comments without registration)
PublicUserSchema.statics.createGuestUser = async function(name: string, email: string) {
  const existingUser = await this.findOne({ email });
  if (existingUser) {
    return existingUser;
  }

  return this.create({
    name,
    email,
    isVerified: false,
    // No password for guest users
  });
};

// Static method to get user statistics
PublicUserSchema.statics.getUserStats = async function() {
  const stats = await this.aggregate([
    {
      $group: {
        _id: null,
        totalUsers: { $sum: 1 },
        verifiedUsers: {
          $sum: { $cond: ['$isVerified', 1, 0] }
        },
        activeUsers: {
          $sum: { $cond: ['$isActive', 1, 0] }
        },
        totalComments: { $sum: '$stats.commentsCount' }
      }
    }
  ]);

  return stats[0] || {
    totalUsers: 0,
    verifiedUsers: 0,
    activeUsers: 0,
    totalComments: 0
  };
};

export default mongoose.model<IPublicUser>('PublicUser', PublicUserSchema);
