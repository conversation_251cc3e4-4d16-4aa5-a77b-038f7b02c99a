import mongoose, { Document, Schema } from 'mongoose';

export interface IPost extends Document {
  title: string;
  slug: string;
  excerpt: string;
  content: string;
  featuredImage: {
    url: string;
    alt: string;
    cloudinaryId?: string;
  };
  seo: {
    metaTitle: string;
    metaDescription: string;
    keywords: string[];
    canonicalUrl?: string;
  };
  category: string;
  tags: string[];
  toolInfo?: {
    name: string;
    website?: string;
    pricing: {
      free: boolean;
      paidPlans: string[];
    };
    rating?: number;
    pros: string[];
    cons: string[];
  };
  status: 'draft' | 'pending' | 'published' | 'archived';
  publishedAt?: Date;
  scheduledFor?: Date;
  author: string;
  analytics: {
    views: number;
    shares: number;
    timeOnPage: number;
    bounceRate: number;
  };
  aiGeneration: {
    prompt: string;
    model: string;
    generatedAt: Date;
    qualityScore: number;
    humanEdited: boolean;
  };
  createdAt: Date;
  updatedAt: Date;
}

const PostSchema = new Schema<IPost>({
  title: {
    type: String,
    required: true,
    trim: true,
    minlength: 10,
    maxlength: 100
  },
  slug: {
    type: String,
    required: true,
    unique: true,
    lowercase: true,
    trim: true
  },
  excerpt: {
    type: String,
    required: true,
    trim: true,
    minlength: 50,
    maxlength: 200
  },
  content: {
    type: String,
    required: true,
    minlength: 500
  },
  featuredImage: {
    url: { type: String, required: true },
    alt: { type: String, required: true },
    cloudinaryId: String
  },
  seo: {
    metaTitle: { type: String, required: true, maxlength: 60 },
    metaDescription: { type: String, required: true, maxlength: 160 },
    keywords: [{ type: String, trim: true }],
    canonicalUrl: String
  },
  category: {
    type: String,
    required: true,
    enum: ['AI Tools', 'Productivity', 'Developer Tools', 'Design Tools']
  },
  tags: [{ type: String, trim: true }],
  toolInfo: {
    name: { type: String, required: true },
    website: String,
    pricing: {
      free: { type: Boolean, default: false },
      paidPlans: [String]
    },
    rating: { type: Number, min: 1, max: 5 },
    pros: [String],
    cons: [String]
  },
  status: {
    type: String,
    enum: ['draft', 'pending', 'published', 'archived'],
    default: 'draft'
  },
  publishedAt: Date,
  scheduledFor: Date,
  author: {
    type: String,
    default: 'AI Agent'
  },
  analytics: {
    views: { type: Number, default: 0 },
    shares: { type: Number, default: 0 },
    timeOnPage: { type: Number, default: 0 },
    bounceRate: { type: Number, default: 0 }
  },
  aiGeneration: {
    prompt: { type: String, required: true },
    model: { type: String, required: true },
    generatedAt: { type: Date, required: true },
    qualityScore: { type: Number, min: 0, max: 100 },
    humanEdited: { type: Boolean, default: false }
  }
}, {
  timestamps: true
});

// Indexes
PostSchema.index({ status: 1, publishedAt: -1 });
PostSchema.index({ slug: 1 }, { unique: true });
PostSchema.index({ category: 1, publishedAt: -1 });
PostSchema.index({ tags: 1, publishedAt: -1 });
PostSchema.index({ title: 'text', content: 'text', excerpt: 'text' });
PostSchema.index({ 'analytics.views': -1 });

// Pre-save middleware to generate slug
PostSchema.pre('save', function(next) {
  if (this.isModified('title') && !this.slug) {
    this.slug = this.title
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/(^-|-$)/g, '');
  }
  next();
});

export default mongoose.model<IPost>('Post', PostSchema);
