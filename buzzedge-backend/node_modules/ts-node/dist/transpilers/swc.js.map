{"version": 3, "file": "swc.js", "sourceRoot": "", "sources": ["../../src/transpilers/swc.ts"], "names": [], "mappings": ";;;AAKA,kDAA6D;AAY7D,SAAgB,MAAM,CAAC,aAAmC;IACxD,MAAM,EACJ,GAAG,EACH,OAAO,EAAE,EAAE,MAAM,EAAE,yBAAyB,EAAE,EAC9C,kCAAkC,EAClC,kBAAkB,GACnB,GAAG,aAAa,CAAC;IAElB,oBAAoB;IACpB,IAAI,WAAwB,CAAC;IAC7B,gEAAgE;IAChE,IAAI,UAAU,GAAW,KAAK,CAAC;IAC/B,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE;QAC3B,UAAU,GAAG,GAAG,CAAC;QACjB,WAAW,GAAG,OAAO,CAAC,kCAAkC,CAAC,GAAG,EAAE,IAAI,CAAC,CAAgB,CAAC;KACrF;SAAM,IAAI,GAAG,IAAI,IAAI,EAAE;QACtB,IAAI,WAAW,CAAC;QAChB,IAAI;YACF,UAAU,GAAG,WAAW,CAAC;YACzB,WAAW,GAAG,kCAAkC,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;SACpE;QAAC,OAAO,CAAC,EAAE;YACV,IAAI;gBACF,UAAU,GAAG,WAAW,CAAC;gBACzB,WAAW,GAAG,kCAAkC,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;aACpE;YAAC,OAAO,CAAC,EAAE;gBACV,MAAM,IAAI,KAAK,CACb,2IAA2I,CAC5I,CAAC;aACH;SACF;QACD,WAAW,GAAG,OAAO,CAAC,WAAW,CAAgB,CAAC;KACnD;SAAM;QACL,WAAW,GAAG,GAAyB,CAAC;KACzC;IAED,+DAA+D;IAC/D,MAAM,EAAE,aAAa,EAAE,UAAU,EAAE,GAAG,gBAAgB,CAAC,MAAM,CAAC,OAAO,EAAE,kBAAkB,EAAE,WAAW,EAAE,UAAU,CAAC,CAAC;IAEpH,MAAM,SAAS,GAA4B,CAAC,KAAK,EAAE,gBAAgB,EAAE,EAAE;QACrE,MAAM,EAAE,QAAQ,EAAE,GAAG,gBAAgB,CAAC;QACtC,MAAM,UAAU,GAAG,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,aAAa,CAAC;QACvG,MAAM,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,WAAW,CAAC,aAAa,CAAC,KAAK,EAAE;YACrD,GAAG,UAAU;YACb,QAAQ,EAAE,QAAQ;SACnB,CAAC,CAAC;QACH,OAAO,EAAE,UAAU,EAAE,IAAI,EAAE,aAAa,EAAE,GAAG,EAAE,CAAC;IAClD,CAAC,CAAC;IAEF,OAAO;QACL,SAAS;KACV,CAAC;AACJ,CAAC;AAnDD,wBAmDC;AAED,gBAAgB;AACH,QAAA,aAAa,GAAG,IAAI,GAAG,EAA8B,CAAC;AACnE,qBAAa,CAAC,GAAG,CAAC,yBAAyB,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;AACtD,qBAAa,CAAC,GAAG,CAAC,yBAAyB,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;AACtD,qBAAa,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC;AAC5D,qBAAa,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC;AAC5D,qBAAa,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC;AAC5D,qBAAa,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC;AAC5D,qBAAa,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC;AAC5D,qBAAa,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC;AAC5D,qBAAa,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC;AAC5D,qBAAa,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC;AAC5D,qBAAa,CAAC,GAAG,CAAC,4BAA4B,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC;AAG7D;;;GAGG;AACH,MAAM,UAAU,GAAG;IACjB,KAAK;IACL,KAAK;IACL,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;CACA,CAAC;AAEX,MAAM,UAAU,GAAG;IACjB,IAAI,EAAE,CAAC;IACP,QAAQ,EAAE,CAAC;IACX,GAAG,EAAE,CAAC;IACN,GAAG,EAAE,CAAC;IACN,MAAM,EAAE,CAAC;IACT,MAAM,EAAE,CAAC;IACT,MAAM,EAAE,CAAC;IACT,MAAM,EAAE,EAAE;IACV,MAAM,EAAE,GAAG;IACX,QAAQ,EAAE,GAAG;CACL,CAAC;AAEX,MAAM,OAAO,GAAG;IACd,QAAQ,EAAE,yBAAyB,CAAC,CAAC;IACrC,WAAW,EAAE,4BAA4B,CAAC,CAAC;CACnC,CAAC;AAEX;;;GAGG;AACH,SAAgB,gBAAgB,CAC9B,eAAmC,EACnC,kBAAkD,EAClD,WAAwB,EACxB,UAAkB;;IAElB,MAAM,EACJ,eAAe,EACf,SAAS,EACT,aAAa,EACb,sBAAsB,EACtB,qBAAqB,EACrB,MAAM,EACN,MAAM,EACN,GAAG,EACH,UAAU,EACV,kBAAkB,EAClB,MAAM,EACN,YAAY,EACZ,mBAAmB,EACnB,eAAe,GAChB,GAAG,eAAe,CAAC;IAEpB,IAAI,SAAS,GAAG,MAAA,qBAAa,CAAC,GAAG,CAAC,MAAO,CAAC,mCAAI,KAAK,CAAC;IACpD,yEAAyE;IACzE,+CAA+C;IAC/C,kDAAkD;IAClD,IAAI,cAAc,GAAG,UAAU,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;IACnD,OAAO,cAAc,IAAI,CAAC,EAAE,cAAc,EAAE,EAAE;QAC5C,IAAI;YACF,WAAW,CAAC,aAAa,CAAC,EAAE,EAAE;gBAC5B,GAAG,EAAE,EAAE,MAAM,EAAE,UAAU,CAAC,cAAc,CAAsB,EAAE;aACjE,CAAC,CAAC;YACH,MAAM;SACP;QAAC,OAAO,CAAC,EAAE,GAAE;KACf;IACD,SAAS,GAAG,UAAU,CAAC,cAAc,CAAC,CAAC;IACvC,MAAM,cAAc,GAAG,MAAO,IAAI,4BAA4B,CAAC,CAAC,CAAC;IACjE,MAAM,gBAAgB,GAAG,MAAM,KAAK,UAAU,CAAC,MAAM,IAAI,MAAM,KAAK,UAAU,CAAC,QAAQ,CAAC;IACxF,6EAA6E;IAC7E,MAAM,UAAU,GACd,MAAM,KAAK,UAAU,CAAC,QAAQ;QAC5B,CAAC,CAAC,UAAU;QACZ,CAAC,CAAC,MAAM,KAAK,UAAU,CAAC,GAAG;YAC3B,CAAC,CAAC,KAAK;YACP,CAAC,CAAC,MAAM,KAAK,UAAU,CAAC,GAAG;gBAC3B,CAAC,CAAC,KAAK;gBACP,CAAC,CAAC,gBAAgB,IAAI,kBAAkB,KAAK,SAAS;oBACtD,CAAC,CAAC,UAAU;oBACZ,CAAC,CAAC,gBAAgB,IAAI,kBAAkB,KAAK,SAAS;wBACtD,CAAC,CAAC,KAAK;wBACP,CAAC,CAAC,KAAK,CAAC;IACZ,UAAU;IACV,wIAAwI;IACxI,iEAAiE;IACjE,6BAA6B;IAC7B,4HAA4H;IAC5H,2HAA2H;IAC3H,wIAAwI;IAExI,8FAA8F;IAC9F,MAAM,UAAU;IACd,2FAA2F;IAC3F,CAAC,YAAY,KAAK,KAAK,IAAI,CAAC,YAAY,KAAK,IAAI,IAAI,MAAM,KAAK,IAAI,CAAC,CAAC;QACtE,oCAAoC;QACpC,mBAAmB,KAAK,IAAI;QAC1B,CAAC,CAAC,KAAK;QACP,CAAC,CAAC,IAAI,CAAC;IAEX,MAAM,UAAU,GACd,GAAG,KAAK,OAAO,CAAC,QAAQ,IAAI,GAAG,KAAK,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,SAAS,CAAC;IACpF,MAAM,cAAc,GAAwC,GAAG,KAAK,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC;IAE3G,MAAM,uBAAuB,GAAG,IAAA,yCAA0B,EAAC,eAAe,CAAC,CAAC;IAE5E,MAAM,aAAa,GAAG,aAAa,CAAC,KAAK,CAAC,CAAC;IAC3C,MAAM,UAAU,GAAG,aAAa,CAAC,IAAI,CAAC,CAAC;IACvC,OAAO,EAAE,aAAa,EAAE,UAAU,EAAE,CAAC;IAErC,SAAS,aAAa,CAAC,KAAc;QACnC,MAAM,UAAU,GAAqB;YACnC,UAAU,EAAE,SAAS;YACrB,kBAAkB;YAClB,MAAM,EAAE,UAAU;gBAChB,CAAC,CAAC;oBACE,IAAI,EAAE,UAAU;oBAChB,GAAG,CAAC,UAAU,KAAK,KAAK,IAAI,UAAU,KAAK,UAAU,IAAI,UAAU,KAAK,KAAK;wBAC3E,CAAC,CAAC;4BACE,SAAS,EAAE,CAAC,eAAe;4BAC3B,UAAU;4BACV,4EAA4E;4BAC5E,aAAa,EAAE,kBAAkB,KAAK,SAAS;yBAChD;wBACH,CAAC,CAAC,EAAE,CAAC;iBACR;gBACH,CAAC,CAAC,SAAS;YACb,KAAK,EAAE,KAAK;YACZ,GAAG,EAAE;gBACH,eAAe,EAAE,aAAa;gBAC9B,MAAM,EAAE;oBACN,MAAM,EAAE,YAAY;oBACpB,GAAG,EAAE,KAAK;oBACV,UAAU,EAAE,sBAAsB;oBAClC,aAAa,EAAE,IAAI;oBACnB,gBAAgB,EAAE,IAAI;iBACG;gBAC3B,MAAM,EAAE,SAA8B;gBACtC,SAAS,EAAE;oBACT,iBAAiB,EAAE,qBAAqB;oBACxC,eAAe,EAAE,IAAI;oBACrB,KAAK,EAAE;wBACL,gBAAgB,EAAE,KAAK;wBACvB,WAAW,EAAE,cAAc;wBAC3B,WAAW,EAAE,KAAK;wBAClB,MAAM,EAAE,UAAW;wBACnB,UAAU,EAAE,kBAAmB;wBAC/B,OAAO,EAAE,UAAU;wBACnB,YAAY,EAAE,eAAe;qBAC9B;oBACD,uBAAuB;iBACxB;gBACD,cAAc;gBACd,YAAY,EAAE;oBACZ,oBAAoB,EAAE,IAAI;oBAC1B,6BAA6B,EAAE,IAAI;iBACE;aACxC;SACF,CAAC;QAEF,0FAA0F;QAC1F,IAAI;YACF,WAAW,CAAC,aAAa,CAAC,EAAE,EAAE,UAAU,CAAC,CAAC;SAC3C;QAAC,OAAO,CAAC,EAAE;YACV,MAAM,IAAI,KAAK,CACb,GAAG,UAAU,qEAAqE;gBAChF,8FAA8F;gBAC9F,+CAA+C;gBAC/C,2BAA2B;iBAC1B,CAAW,aAAX,CAAC,uBAAD,CAAC,CAAY,OAAO,CAAA,CACxB,CAAC;SACH;QAED,OAAO,UAAU,CAAC;IACpB,CAAC;AACH,CAAC;AAhJD,4CAgJC", "sourcesContent": ["import type * as ts from 'typescript';\nimport type * as swcWasm from '@swc/wasm';\nimport type * as swcTypes from '@swc/core';\nimport type { CreateTranspilerOptions, Transpiler } from './types';\nimport type { NodeModuleEmitKind } from '..';\nimport { getUseDefineForClassFields } from '../ts-internals';\n\ntype SwcInstance = typeof swcTypes;\nexport interface SwcTranspilerOptions extends CreateTranspilerOptions {\n  /**\n   * swc compiler to use for compilation\n   * Set to '@swc/wasm' to use swc's WASM compiler\n   * Default: '@swc/core', falling back to '@swc/wasm'\n   */\n  swc?: string | typeof swcWasm;\n}\n\nexport function create(createOptions: SwcTranspilerOptions): Transpiler {\n  const {\n    swc,\n    service: { config, projectLocalResolveHelper },\n    transpilerConfigLocalResolveHelper,\n    nodeModuleEmitKind,\n  } = createOptions;\n\n  // Load swc compiler\n  let swcInstance: SwcInstance;\n  // Used later in diagnostics; merely needs to be human-readable.\n  let swcDepName: string = 'swc';\n  if (typeof swc === 'string') {\n    swcDepName = swc;\n    swcInstance = require(transpilerConfigLocalResolveHelper(swc, true)) as SwcInstance;\n  } else if (swc == null) {\n    let swcResolved;\n    try {\n      swcDepName = '@swc/core';\n      swcResolved = transpilerConfigLocalResolveHelper(swcDepName, true);\n    } catch (e) {\n      try {\n        swcDepName = '@swc/wasm';\n        swcResolved = transpilerConfigLocalResolveHelper(swcDepName, true);\n      } catch (e) {\n        throw new Error(\n          'swc compiler requires either @swc/core or @swc/wasm to be installed as a dependency.  See https://typestrong.org/ts-node/docs/transpilers'\n        );\n      }\n    }\n    swcInstance = require(swcResolved) as SwcInstance;\n  } else {\n    swcInstance = swc as any as SwcInstance;\n  }\n\n  // Prepare SWC options derived from typescript compiler options\n  const { nonTsxOptions, tsxOptions } = createSwcOptions(config.options, nodeModuleEmitKind, swcInstance, swcDepName);\n\n  const transpile: Transpiler['transpile'] = (input, transpileOptions) => {\n    const { fileName } = transpileOptions;\n    const swcOptions = fileName.endsWith('.tsx') || fileName.endsWith('.jsx') ? tsxOptions : nonTsxOptions;\n    const { code, map } = swcInstance.transformSync(input, {\n      ...swcOptions,\n      filename: fileName,\n    });\n    return { outputText: code, sourceMapText: map };\n  };\n\n  return {\n    transpile,\n  };\n}\n\n/** @internal */\nexport const targetMapping = new Map<ts.ScriptTarget, SwcTarget>();\ntargetMapping.set(/* ts.ScriptTarget.ES3 */ 0, 'es3');\ntargetMapping.set(/* ts.ScriptTarget.ES5 */ 1, 'es5');\ntargetMapping.set(/* ts.ScriptTarget.ES2015 */ 2, 'es2015');\ntargetMapping.set(/* ts.ScriptTarget.ES2016 */ 3, 'es2016');\ntargetMapping.set(/* ts.ScriptTarget.ES2017 */ 4, 'es2017');\ntargetMapping.set(/* ts.ScriptTarget.ES2018 */ 5, 'es2018');\ntargetMapping.set(/* ts.ScriptTarget.ES2019 */ 6, 'es2019');\ntargetMapping.set(/* ts.ScriptTarget.ES2020 */ 7, 'es2020');\ntargetMapping.set(/* ts.ScriptTarget.ES2021 */ 8, 'es2021');\ntargetMapping.set(/* ts.ScriptTarget.ES2022 */ 9, 'es2022');\ntargetMapping.set(/* ts.ScriptTarget.ESNext */ 99, 'esnext');\n\ntype SwcTarget = typeof swcTargets[number];\n/**\n * @internal\n * We use this list to downgrade to a prior target when we probe swc to detect if it supports a particular target\n */\nconst swcTargets = [\n  'es3',\n  'es5',\n  'es2015',\n  'es2016',\n  'es2017',\n  'es2018',\n  'es2019',\n  'es2020',\n  'es2021',\n  'es2022',\n  'esnext',\n] as const;\n\nconst ModuleKind = {\n  None: 0,\n  CommonJS: 1,\n  AMD: 2,\n  UMD: 3,\n  System: 4,\n  ES2015: 5,\n  ES2020: 6,\n  ESNext: 99,\n  Node16: 100,\n  NodeNext: 199,\n} as const;\n\nconst JsxEmit = {\n  ReactJSX: /* ts.JsxEmit.ReactJSX */ 4,\n  ReactJSXDev: /* ts.JsxEmit.ReactJSXDev */ 5,\n} as const;\n\n/**\n * Prepare SWC options derived from typescript compiler options.\n * @internal exported for testing\n */\nexport function createSwcOptions(\n  compilerOptions: ts.CompilerOptions,\n  nodeModuleEmitKind: NodeModuleEmitKind | undefined,\n  swcInstance: SwcInstance,\n  swcDepName: string\n) {\n  const {\n    esModuleInterop,\n    sourceMap,\n    importHelpers,\n    experimentalDecorators,\n    emitDecoratorMetadata,\n    target,\n    module,\n    jsx,\n    jsxFactory,\n    jsxFragmentFactory,\n    strict,\n    alwaysStrict,\n    noImplicitUseStrict,\n    jsxImportSource,\n  } = compilerOptions;\n\n  let swcTarget = targetMapping.get(target!) ?? 'es3';\n  // Downgrade to lower target if swc does not support the selected target.\n  // Perhaps project has an older version of swc.\n  // TODO cache the results of this; slightly faster\n  let swcTargetIndex = swcTargets.indexOf(swcTarget);\n  for (; swcTargetIndex >= 0; swcTargetIndex--) {\n    try {\n      swcInstance.transformSync('', {\n        jsc: { target: swcTargets[swcTargetIndex] as swcWasm.JscTarget },\n      });\n      break;\n    } catch (e) {}\n  }\n  swcTarget = swcTargets[swcTargetIndex];\n  const keepClassNames = target! >= /* ts.ScriptTarget.ES2016 */ 3;\n  const isNodeModuleKind = module === ModuleKind.Node16 || module === ModuleKind.NodeNext;\n  // swc only supports these 4x module options [MUST_UPDATE_FOR_NEW_MODULEKIND]\n  const moduleType =\n    module === ModuleKind.CommonJS\n      ? 'commonjs'\n      : module === ModuleKind.AMD\n      ? 'amd'\n      : module === ModuleKind.UMD\n      ? 'umd'\n      : isNodeModuleKind && nodeModuleEmitKind === 'nodecjs'\n      ? 'commonjs'\n      : isNodeModuleKind && nodeModuleEmitKind === 'nodeesm'\n      ? 'es6'\n      : 'es6';\n  // In swc:\n  //   strictMode means `\"use strict\"` is *always* emitted for non-ES module, *never* for ES module where it is assumed it can be omitted.\n  //   (this assumption is invalid, but that's the way swc behaves)\n  // tsc is a bit more complex:\n  //   alwaysStrict will force emitting it always unless `import`/`export` syntax is emitted which implies it per the JS spec.\n  //   if not alwaysStrict, will emit implicitly whenever module target is non-ES *and* transformed module syntax is emitted.\n  // For node, best option is to assume that all scripts are modules (commonjs or esm) and thus should get tsc's implicit strict behavior.\n\n  // Always set strictMode, *unless* alwaysStrict is disabled and noImplicitUseStrict is enabled\n  const strictMode =\n    // if `alwaysStrict` is disabled, remembering that `strict` defaults `alwaysStrict` to true\n    (alwaysStrict === false || (alwaysStrict !== true && strict !== true)) &&\n    // if noImplicitUseStrict is enabled\n    noImplicitUseStrict === true\n      ? false\n      : true;\n\n  const jsxRuntime: swcTypes.ReactConfig['runtime'] =\n    jsx === JsxEmit.ReactJSX || jsx === JsxEmit.ReactJSXDev ? 'automatic' : undefined;\n  const jsxDevelopment: swcTypes.ReactConfig['development'] = jsx === JsxEmit.ReactJSXDev ? true : undefined;\n\n  const useDefineForClassFields = getUseDefineForClassFields(compilerOptions);\n\n  const nonTsxOptions = createVariant(false);\n  const tsxOptions = createVariant(true);\n  return { nonTsxOptions, tsxOptions };\n\n  function createVariant(isTsx: boolean): swcTypes.Options {\n    const swcOptions: swcTypes.Options = {\n      sourceMaps: sourceMap,\n      // isModule: true,\n      module: moduleType\n        ? {\n            type: moduleType,\n            ...(moduleType === 'amd' || moduleType === 'commonjs' || moduleType === 'umd'\n              ? {\n                  noInterop: !esModuleInterop,\n                  strictMode,\n                  // For NodeNext and Node12, emit as CJS but do not transform dynamic imports\n                  ignoreDynamic: nodeModuleEmitKind === 'nodecjs',\n                }\n              : {}),\n          }\n        : undefined,\n      swcrc: false,\n      jsc: {\n        externalHelpers: importHelpers,\n        parser: {\n          syntax: 'typescript',\n          tsx: isTsx,\n          decorators: experimentalDecorators,\n          dynamicImport: true,\n          importAssertions: true,\n        } as swcWasm.TsParserConfig,\n        target: swcTarget as swcWasm.JscTarget,\n        transform: {\n          decoratorMetadata: emitDecoratorMetadata,\n          legacyDecorator: true,\n          react: {\n            throwIfNamespace: false,\n            development: jsxDevelopment,\n            useBuiltins: false,\n            pragma: jsxFactory!,\n            pragmaFrag: jsxFragmentFactory!,\n            runtime: jsxRuntime,\n            importSource: jsxImportSource,\n          },\n          useDefineForClassFields,\n        },\n        keepClassNames,\n        experimental: {\n          keepImportAttributes: true,\n          emitAssertForImportAttributes: true,\n        } as swcTypes.JscConfig['experimental'],\n      },\n    };\n\n    // Throw a helpful error if swc version is old, for example, if it rejects `ignoreDynamic`\n    try {\n      swcInstance.transformSync('', swcOptions);\n    } catch (e) {\n      throw new Error(\n        `${swcDepName} threw an error when attempting to validate swc compiler options.\\n` +\n          'You may be using an old version of swc which does not support the options used by ts-node.\\n' +\n          'Try upgrading to the latest version of swc.\\n' +\n          'Error message from swc:\\n' +\n          (e as Error)?.message\n      );\n    }\n\n    return swcOptions;\n  }\n}\n"]}