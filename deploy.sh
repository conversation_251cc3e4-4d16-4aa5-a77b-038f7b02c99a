#!/bin/bash

# BuzzEdge Deployment Script
# This script helps deploy both frontend and backend

set -e  # Exit on any error

echo "🚀 BuzzEdge Deployment Script"
echo "=============================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if required tools are installed
check_dependencies() {
    print_status "Checking dependencies..."
    
    if ! command -v node &> /dev/null; then
        print_error "Node.js is not installed. Please install Node.js first."
        exit 1
    fi
    
    if ! command -v npm &> /dev/null; then
        print_error "npm is not installed. Please install npm first."
        exit 1
    fi
    
    print_success "Dependencies check passed"
}

# Build backend
build_backend() {
    print_status "Building backend..."
    cd buzzedge-backend
    
    if [ ! -d "node_modules" ]; then
        print_status "Installing backend dependencies..."
        npm install
    fi
    
    print_status "Building TypeScript..."
    npm run build
    
    if [ $? -eq 0 ]; then
        print_success "Backend build completed"
    else
        print_error "Backend build failed"
        exit 1
    fi
    
    cd ..
}

# Build frontend
build_frontend() {
    print_status "Building frontend..."
    cd buzzedge-frontend
    
    if [ ! -d "node_modules" ]; then
        print_status "Installing frontend dependencies..."
        npm install
    fi
    
    print_status "Building Next.js application..."
    npm run build
    
    if [ $? -eq 0 ]; then
        print_success "Frontend build completed"
    else
        print_error "Frontend build failed"
        exit 1
    fi
    
    cd ..
}

# Deploy to Railway (Backend)
deploy_backend() {
    print_status "Deploying backend to Railway..."
    
    if ! command -v railway &> /dev/null; then
        print_warning "Railway CLI not found. Installing..."
        npm install -g @railway/cli
    fi
    
    cd buzzedge-backend
    
    # Check if railway is initialized
    if [ ! -f "railway.json" ]; then
        print_status "Initializing Railway project..."
        railway init
    fi
    
    print_status "Deploying to Railway..."
    railway up
    
    if [ $? -eq 0 ]; then
        print_success "Backend deployed to Railway"
    else
        print_error "Backend deployment failed"
        exit 1
    fi
    
    cd ..
}

# Deploy to Vercel (Frontend)
deploy_frontend() {
    print_status "Deploying frontend to Vercel..."
    
    if ! command -v vercel &> /dev/null; then
        print_warning "Vercel CLI not found. Installing..."
        npm install -g vercel
    fi
    
    cd buzzedge-frontend
    
    print_status "Deploying to Vercel..."
    vercel --prod
    
    if [ $? -eq 0 ]; then
        print_success "Frontend deployed to Vercel"
    else
        print_error "Frontend deployment failed"
        exit 1
    fi
    
    cd ..
}

# Main deployment function
main() {
    echo ""
    print_status "Starting BuzzEdge deployment process..."
    echo ""
    
    # Check dependencies
    check_dependencies
    echo ""
    
    # Ask user what to deploy
    echo "What would you like to deploy?"
    echo "1) Backend only"
    echo "2) Frontend only" 
    echo "3) Both (recommended)"
    echo "4) Build only (no deployment)"
    read -p "Enter your choice (1-4): " choice
    echo ""
    
    case $choice in
        1)
            build_backend
            deploy_backend
            ;;
        2)
            build_frontend
            deploy_frontend
            ;;
        3)
            build_backend
            build_frontend
            deploy_backend
            deploy_frontend
            ;;
        4)
            build_backend
            build_frontend
            print_success "Build completed. Ready for manual deployment."
            ;;
        *)
            print_error "Invalid choice. Please run the script again."
            exit 1
            ;;
    esac
    
    echo ""
    print_success "🎉 Deployment process completed!"
    echo ""
    echo "Next steps:"
    echo "1. Configure environment variables on your deployment platforms"
    echo "2. Update CORS settings with your actual domain URLs"
    echo "3. Test your deployed application"
    echo "4. Set up monitoring and analytics"
    echo ""
    echo "Useful links:"
    echo "- Railway Dashboard: https://railway.app/dashboard"
    echo "- Vercel Dashboard: https://vercel.com/dashboard"
    echo "- MongoDB Atlas: https://cloud.mongodb.com/"
    echo ""
}

# Run main function
main
