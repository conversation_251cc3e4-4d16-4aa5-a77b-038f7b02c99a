# 🚀 Quick Deploy Steps - BuzzEdge Application

## 🎯 **Choose Your Deployment Method**

### **Option 1: Automated Deployment (Recommended)**
```bash
# Run the deployment script
./deploy.sh
```

### **Option 2: Manual Step-by-Step Deployment**

## 📋 **Step 1: Prepare for Deployment**

### **1.1 Install Required Tools**
```bash
# Install Railway CLI
npm install -g @railway/cli

# Install Vercel CLI
npm install -g vercel

# Login to services
railway login
vercel login
```

### **1.2 Build Applications**
```bash
# Build backend
cd buzzedge-backend
npm install
npm run build
cd ..

# Build frontend
cd buzzedge-frontend
npm install
npm run build
cd ..
```

## 🔧 **Step 2: Deploy Backend to Railway**

### **2.1 Deploy Backend**
```bash
cd buzzedge-backend

# Initialize Railway project
railway init

# Deploy
railway up
```

### **2.2 Configure Environment Variables**
Go to [Railway Dashboard](https://railway.app/dashboard) and add:

```bash
NODE_ENV=production
PORT=5001
MONGODB_URI=mongodb+srv://bhavanishankar:<EMAIL>/buzzedge?retryWrites=true&w=majority&appName=Cluster0
JWT_SECRET=your-super-secret-jwt-key-here-make-it-long-and-random-at-least-32-characters
FRONTEND_URL=https://your-frontend-domain.vercel.app
CORS_ORIGIN=https://your-frontend-domain.vercel.app
GEMINI_API_KEY=your-gemini-api-key-here
```

### **2.3 Get Backend URL**
After deployment, Railway will provide a URL like:
`https://buzzedge-backend-production.up.railway.app`

## 🌐 **Step 3: Deploy Frontend to Vercel**

### **3.1 Deploy Frontend**
```bash
cd buzzedge-frontend

# Deploy to Vercel
vercel

# Follow prompts:
# - Link to existing project? No
# - Project name: buzzedge
# - Directory: ./
# - Override settings? No
```

### **3.2 Configure Environment Variables**
Go to [Vercel Dashboard](https://vercel.com/dashboard) and add:

```bash
NEXT_PUBLIC_API_URL=https://your-backend-domain.railway.app/api
NEXT_PUBLIC_SITE_URL=https://your-frontend-domain.vercel.app
NEXT_PUBLIC_SITE_NAME=BuzzEdge
NEXT_PUBLIC_SITE_DESCRIPTION=AI-Powered Tech Tool Reviews and Comparisons
```

### **3.3 Get Frontend URL**
Vercel will provide a URL like:
`https://buzzedge.vercel.app`

## 🔄 **Step 4: Update CORS Configuration**

### **4.1 Update Backend Environment**
In Railway dashboard, update:
```bash
FRONTEND_URL=https://buzzedge.vercel.app
CORS_ORIGIN=https://buzzedge.vercel.app
```

### **4.2 Update Frontend Environment**
In Vercel dashboard, update:
```bash
NEXT_PUBLIC_API_URL=https://buzzedge-backend-production.up.railway.app/api
```

## ✅ **Step 5: Test Deployment**

### **5.1 Test Backend**
```bash
# Test API health
curl https://your-backend-domain.railway.app/api/health

# Test comment creation
curl -X POST https://your-backend-domain.railway.app/api/comments \
  -H "Content-Type: application/json" \
  -d '{"content":"Test deployment","postId":"test","author":{"name":"Test","email":"<EMAIL>"}}'
```

### **5.2 Test Frontend**
1. Visit your Vercel URL
2. Navigate to a blog post
3. Try creating a comment
4. Check admin dashboard at `/admin/comments`

## 🎯 **Step 6: Post-Deployment Configuration**

### **6.1 Set Up Custom Domain (Optional)**

#### **Frontend Domain**
1. Go to Vercel Dashboard → Your Project → Settings → Domains
2. Add your custom domain (e.g., `buzzedge.com`)
3. Configure DNS as instructed

#### **Backend Domain**
1. Go to Railway Dashboard → Your Service → Settings → Domains
2. Add custom domain (e.g., `api.buzzedge.com`)
3. Update environment variables with new URLs

### **6.2 Enable Analytics (Optional)**
```bash
# Add to Vercel environment variables
NEXT_PUBLIC_GA_ID=G-XXXXXXXXXX
```

## 🔍 **Troubleshooting Common Issues**

### **Issue 1: CORS Errors**
**Solution**: Ensure `FRONTEND_URL` in backend matches your actual frontend domain

### **Issue 2: API Not Found**
**Solution**: Check `NEXT_PUBLIC_API_URL` in frontend environment variables

### **Issue 3: Database Connection Failed**
**Solution**: Verify `MONGODB_URI` is correctly set in backend environment

### **Issue 4: Authentication Not Working**
**Solution**: Ensure `JWT_SECRET` is set and consistent

## 📊 **Monitoring Your Deployment**

### **Railway Monitoring**
- CPU and Memory usage
- Response times
- Error rates
- Logs and metrics

### **Vercel Analytics**
- Page views and performance
- Core Web Vitals
- Function execution times
- Error tracking

## 🔐 **Security Checklist**

- [ ] **Environment variables set correctly**
- [ ] **HTTPS enabled on both domains**
- [ ] **CORS configured properly**
- [ ] **Strong JWT secret (32+ characters)**
- [ ] **MongoDB IP whitelist configured**
- [ ] **Rate limiting enabled**
- [ ] **Security headers configured**

## 🎉 **Success! Your App is Live**

### **Your Live URLs:**
- 🌐 **Frontend**: `https://your-app.vercel.app`
- 🔧 **Backend**: `https://your-app.railway.app`
- 🎛️ **Admin**: `https://your-app.vercel.app/admin/comments`

### **Next Steps:**
1. ✅ Test all functionality
2. ✅ Set up monitoring alerts
3. ✅ Configure backup strategies
4. ✅ Add custom domain
5. ✅ Enable analytics
6. ✅ Set up CI/CD pipeline

---

**Congratulations! Your BuzzEdge application is now live and ready for users!** 🚀

### **Need Help?**
- 📚 Check the full deployment guide: `DEPLOYMENT_GUIDE_COMPLETE.md`
- 🔧 Review configuration files in your project
- 🌐 Visit platform documentation:
  - [Railway Docs](https://docs.railway.app/)
  - [Vercel Docs](https://vercel.com/docs)
