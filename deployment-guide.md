# BuzzEdge Deployment Guide

## CI/CD Pipeline Setup

### Required Secrets in GitHub Repository

#### Vercel Deployment
```bash
VERCEL_TOKEN=your-vercel-token
VERCEL_ORG_ID=your-org-id
VERCEL_PROJECT_ID=your-project-id
NEXT_PUBLIC_API_URL=https://api.buzzedge.com
```

#### Railway Deployment (Backend)
```bash
RAILWAY_TOKEN=your-railway-token
```

#### Slack Notifications (Optional)
```bash
SLACK_WEBHOOK_URL=your-slack-webhook-url
```

### Environment Variables for Production

#### Frontend (.env.production)
```bash
NEXT_PUBLIC_API_URL=https://api.buzzedge.com
NEXT_PUBLIC_SITE_URL=https://buzzedge.com
NEXT_PUBLIC_GA_ID=G-XXXXXXXXXX
```

#### Backend (Railway Environment Variables)
```bash
NODE_ENV=production
PORT=3000
MONGODB_URI=mongodb+srv://username:<EMAIL>/buzzedge
JWT_SECRET=your-super-secure-jwt-secret
OPENAI_API_KEY=sk-your-openai-key
CLOUDINARY_CLOUD_NAME=your-cloud-name
CLOUDINARY_API_KEY=your-api-key
CLOUDINARY_API_SECRET=your-api-secret
FRONTEND_URL=https://buzzedge.com
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
```

## Manual Deployment Steps

### 1. Frontend Deployment (Vercel)

```bash
# Install Vercel CLI
npm i -g vercel

# Login to Vercel
vercel login

# Deploy from frontend directory
cd buzzedge-frontend
vercel --prod
```

### 2. Backend Deployment (Railway)

```bash
# Install Railway CLI
npm install -g @railway/cli

# Login to Railway
railway login

# Initialize project
railway init

# Deploy
railway up
```

### 3. Database Setup (MongoDB Atlas)

1. Create MongoDB Atlas account
2. Create new cluster
3. Set up database user
4. Configure network access (0.0.0.0/0 for production)
5. Get connection string
6. Update environment variables

## Monitoring and Maintenance

### Health Checks
- Frontend: Vercel automatic health checks
- Backend: `/health` endpoint monitoring
- Database: MongoDB Atlas monitoring

### Backup Strategy
- MongoDB Atlas automatic backups
- Code repository in GitHub
- Environment variables documented

### Performance Monitoring
- Vercel Analytics for frontend
- Railway metrics for backend
- MongoDB Atlas performance advisor

### Security Considerations
- Environment variables in secure storage
- HTTPS enforced on all endpoints
- Rate limiting configured
- CORS properly configured
- Helmet.js security headers

## Rollback Procedures

### Frontend Rollback
```bash
# List deployments
vercel ls

# Rollback to specific deployment
vercel rollback [deployment-url]
```

### Backend Rollback
```bash
# Railway rollback
railway rollback [deployment-id]
```

### Database Rollback
- Use MongoDB Atlas point-in-time recovery
- Restore from specific backup snapshot

## Scaling Considerations

### Frontend Scaling
- Vercel handles automatic scaling
- CDN distribution worldwide
- Edge functions for dynamic content

### Backend Scaling
- Railway automatic scaling based on load
- Horizontal scaling with load balancer
- Database connection pooling

### Database Scaling
- MongoDB Atlas auto-scaling
- Read replicas for read-heavy workloads
- Sharding for large datasets

## Cost Optimization

### Vercel
- Free tier: 100GB bandwidth
- Pro tier: $20/month for team features
- Enterprise: Custom pricing

### Railway
- Free tier: $5 credit monthly
- Pro tier: Pay-as-you-go
- Estimated cost: $10-50/month

### MongoDB Atlas
- Free tier: 512MB storage
- Shared clusters: $9/month
- Dedicated clusters: $57/month+

### Total Estimated Monthly Cost
- Development: $0 (free tiers)
- Production (low traffic): $30-80/month
- Production (high traffic): $100-300/month
