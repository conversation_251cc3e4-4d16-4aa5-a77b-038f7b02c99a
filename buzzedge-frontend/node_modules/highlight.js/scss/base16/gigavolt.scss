pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
/*!
  Theme: Gigavolt
  Author: <PERSON> (http://github.com/Whillikers)
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/
/*
  WARNING: DO NOT EDIT THIS FILE DIRECTLY.

  This theme file was auto-generated from the Base16 scheme gigavolt
  by the Highlight.js Base16 template builder.

  - https://github.com/highlightjs/base16-highlightjs
*/
/*
base00  #202126  Default Background
base01  #2d303d  Lighter Background (Used for status bars, line number and folding marks)
base02  #5a576e  Selection Background
base03  #a1d2e6  Comments, Invisibles, Line Highlighting
base04  #cad3ff  Dark Foreground (Used for status bars)
base05  #e9e7e1  Default Foreground, Caret, Delimiters, Operators
base06  #eff0f9  Light Foreground (Not often used)
base07  #f2fbff  Light Background (Not often used)
base08  #ff661a  Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted
base09  #19f988  Integers, Boolean, Constants, XML Attributes, Markup Link Url
base0A  #ffdc2d  Classes, Markup Bold, Search Text Background
base0B  #f2e6a9  Strings, Inherited Class, Markup Code, Diff Inserted
base0C  #fb6acb  Support, Regular Expressions, Escape Characters, Markup Quotes
base0D  #40bfff  Functions, Methods, Attribute IDs, Headings
base0E  #ae94f9  Keywords, Storage, Selector, Markup Italic, Diff Changed
base0F  #6187ff  Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?>
*/
pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
.hljs {
  color: #e9e7e1;
  background: #202126
}
.hljs::selection,
.hljs ::selection {
  background-color: #5a576e;
  color: #e9e7e1
}
/* purposely do not highlight these things */
.hljs-formula,
.hljs-params,
.hljs-property {
  
}
/* base03 - #a1d2e6 -  Comments, Invisibles, Line Highlighting */
.hljs-comment {
  color: #a1d2e6
}
/* base04 - #cad3ff -  Dark Foreground (Used for status bars) */
.hljs-tag {
  color: #cad3ff
}
/* base05 - #e9e7e1 -  Default Foreground, Caret, Delimiters, Operators */
.hljs-subst,
.hljs-punctuation,
.hljs-operator {
  color: #e9e7e1
}
.hljs-operator {
  opacity: 0.7
}
/* base08 - Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted */
.hljs-bullet,
.hljs-variable,
.hljs-template-variable,
.hljs-selector-tag,
.hljs-name,
.hljs-deletion {
  color: #ff661a
}
/* base09 - Integers, Boolean, Constants, XML Attributes, Markup Link Url */
.hljs-symbol,
.hljs-number,
.hljs-link,
.hljs-attr,
.hljs-variable.constant_,
.hljs-literal {
  color: #19f988
}
/* base0A - Classes, Markup Bold, Search Text Background */
.hljs-title,
.hljs-class .hljs-title,
.hljs-title.class_ {
  color: #ffdc2d
}
.hljs-strong {
  font-weight: bold;
  color: #ffdc2d
}
/* base0B - Strings, Inherited Class, Markup Code, Diff Inserted */
.hljs-code,
.hljs-addition,
.hljs-title.class_.inherited__,
.hljs-string {
  color: #f2e6a9
}
/* base0C - Support, Regular Expressions, Escape Characters, Markup Quotes */
/* guessing */
.hljs-built_in,
.hljs-doctag,
.hljs-quote,
.hljs-keyword.hljs-atrule,
.hljs-regexp {
  color: #fb6acb
}
/* base0D - Functions, Methods, Attribute IDs, Headings */
.hljs-function .hljs-title,
.hljs-attribute,
.ruby .hljs-property,
.hljs-title.function_,
.hljs-section {
  color: #40bfff
}
/* base0E - Keywords, Storage, Selector, Markup Italic, Diff Changed */
/* .hljs-selector-id, */
/* .hljs-selector-class, */
/* .hljs-selector-attr, */
/* .hljs-selector-pseudo, */
.hljs-type,
.hljs-template-tag,
.diff .hljs-meta,
.hljs-keyword {
  color: #ae94f9
}
.hljs-emphasis {
  color: #ae94f9;
  font-style: italic
}
/* base0F - Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?> */
/*
  prevent top level .keyword and .string scopes
  from leaking into meta by accident
*/
.hljs-meta,
.hljs-meta .hljs-keyword,
.hljs-meta .hljs-string {
  color: #6187ff
}
/* for v10 compatible themes */
.hljs-meta .hljs-keyword,
.hljs-meta-keyword {
  font-weight: bold
}