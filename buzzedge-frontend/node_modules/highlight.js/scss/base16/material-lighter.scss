pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
/*!
  Theme: Material Lighter
  Author: <PERSON>
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/
/*
  WARNING: DO NOT EDIT THIS FILE DIRECTLY.

  This theme file was auto-generated from the Base16 scheme material-lighter
  by the Highlight.js Base16 template builder.

  - https://github.com/highlightjs/base16-highlightjs
*/
/*
base00  #FAFAFA  Default Background
base01  #E7EAEC  Lighter Background (Used for status bars, line number and folding marks)
base02  #CCEAE7  Selection Background
base03  #CCD7DA  Comments, Invisibles, Line Highlighting
base04  #8796B0  Dark Foreground (Used for status bars)
base05  #80CBC4  Default Foreground, Caret, Delimiters, Operators
base06  #80CBC4  Light Foreground (Not often used)
base07  #FFFFFF  Light Background (Not often used)
base08  #FF5370  Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted
base09  #F76D47  Integers, Boolean, Constants, XML Attributes, Markup Link Url
base0A  #FFB62C  Classes, Markup Bold, Search Text Background
base0B  #91B859  Strings, Inherited Class, Markup Code, Diff Inserted
base0C  #39ADB5  Support, Regular Expressions, Escape Characters, Markup Quotes
base0D  #6182B8  Functions, Methods, Attribute IDs, Headings
base0E  #7C4DFF  Keywords, Storage, Selector, Markup Italic, Diff Changed
base0F  #E53935  Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?>
*/
pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
.hljs {
  color: #80CBC4;
  background: #FAFAFA
}
.hljs::selection,
.hljs ::selection {
  background-color: #CCEAE7;
  color: #80CBC4
}
/* purposely do not highlight these things */
.hljs-formula,
.hljs-params,
.hljs-property {
  
}
/* base03 - #CCD7DA -  Comments, Invisibles, Line Highlighting */
.hljs-comment {
  color: #CCD7DA
}
/* base04 - #8796B0 -  Dark Foreground (Used for status bars) */
.hljs-tag {
  color: #8796B0
}
/* base05 - #80CBC4 -  Default Foreground, Caret, Delimiters, Operators */
.hljs-subst,
.hljs-punctuation,
.hljs-operator {
  color: #80CBC4
}
.hljs-operator {
  opacity: 0.7
}
/* base08 - Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted */
.hljs-bullet,
.hljs-variable,
.hljs-template-variable,
.hljs-selector-tag,
.hljs-name,
.hljs-deletion {
  color: #FF5370
}
/* base09 - Integers, Boolean, Constants, XML Attributes, Markup Link Url */
.hljs-symbol,
.hljs-number,
.hljs-link,
.hljs-attr,
.hljs-variable.constant_,
.hljs-literal {
  color: #F76D47
}
/* base0A - Classes, Markup Bold, Search Text Background */
.hljs-title,
.hljs-class .hljs-title,
.hljs-title.class_ {
  color: #FFB62C
}
.hljs-strong {
  font-weight: bold;
  color: #FFB62C
}
/* base0B - Strings, Inherited Class, Markup Code, Diff Inserted */
.hljs-code,
.hljs-addition,
.hljs-title.class_.inherited__,
.hljs-string {
  color: #91B859
}
/* base0C - Support, Regular Expressions, Escape Characters, Markup Quotes */
/* guessing */
.hljs-built_in,
.hljs-doctag,
.hljs-quote,
.hljs-keyword.hljs-atrule,
.hljs-regexp {
  color: #39ADB5
}
/* base0D - Functions, Methods, Attribute IDs, Headings */
.hljs-function .hljs-title,
.hljs-attribute,
.ruby .hljs-property,
.hljs-title.function_,
.hljs-section {
  color: #6182B8
}
/* base0E - Keywords, Storage, Selector, Markup Italic, Diff Changed */
/* .hljs-selector-id, */
/* .hljs-selector-class, */
/* .hljs-selector-attr, */
/* .hljs-selector-pseudo, */
.hljs-type,
.hljs-template-tag,
.diff .hljs-meta,
.hljs-keyword {
  color: #7C4DFF
}
.hljs-emphasis {
  color: #7C4DFF;
  font-style: italic
}
/* base0F - Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?> */
/*
  prevent top level .keyword and .string scopes
  from leaking into meta by accident
*/
.hljs-meta,
.hljs-meta .hljs-keyword,
.hljs-meta .hljs-string {
  color: #E53935
}
/* for v10 compatible themes */
.hljs-meta .hljs-keyword,
.hljs-meta-keyword {
  font-weight: bold
}