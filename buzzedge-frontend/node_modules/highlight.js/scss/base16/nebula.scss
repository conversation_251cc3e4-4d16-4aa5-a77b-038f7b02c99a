pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
/*!
  Theme: Nebula
  Author: <PERSON> (https://github.com/Misterio77)
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/
/*
  WARNING: DO NOT EDIT THIS FILE DIRECTLY.

  This theme file was auto-generated from the Base16 scheme nebula
  by the Highlight.js Base16 template builder.

  - https://github.com/highlightjs/base16-highlightjs
*/
/*
base00  #22273b  Default Background
base01  #414f60  Lighter Background (Used for status bars, line number and folding marks)
base02  #5a8380  Selection Background
base03  #6e6f72  Comments, Invisibles, Line Highlighting
base04  #87888b  Dark Foreground (Used for status bars)
base05  #a4a6a9  Default Foreground, Caret, Delimiters, Operators
base06  #c7c9cd  Light Foreground (Not often used)
base07  #8dbdaa  Light Background (Not often used)
base08  #777abc  Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted
base09  #94929e  Integers, Boolean, Constants, XML Attributes, Markup Link Url
base0A  #4f9062  Classes, Markup Bold, Search Text Background
base0B  #6562a8  Strings, Inherited Class, Markup Code, Diff Inserted
base0C  #226f68  Support, Regular Expressions, Escape Characters, Markup Quotes
base0D  #4d6bb6  Functions, Methods, Attribute IDs, Headings
base0E  #716cae  Keywords, Storage, Selector, Markup Italic, Diff Changed
base0F  #8c70a7  Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?>
*/
pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
.hljs {
  color: #a4a6a9;
  background: #22273b
}
.hljs::selection,
.hljs ::selection {
  background-color: #5a8380;
  color: #a4a6a9
}
/* purposely do not highlight these things */
.hljs-formula,
.hljs-params,
.hljs-property {
  
}
/* base03 - #6e6f72 -  Comments, Invisibles, Line Highlighting */
.hljs-comment {
  color: #6e6f72
}
/* base04 - #87888b -  Dark Foreground (Used for status bars) */
.hljs-tag {
  color: #87888b
}
/* base05 - #a4a6a9 -  Default Foreground, Caret, Delimiters, Operators */
.hljs-subst,
.hljs-punctuation,
.hljs-operator {
  color: #a4a6a9
}
.hljs-operator {
  opacity: 0.7
}
/* base08 - Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted */
.hljs-bullet,
.hljs-variable,
.hljs-template-variable,
.hljs-selector-tag,
.hljs-name,
.hljs-deletion {
  color: #777abc
}
/* base09 - Integers, Boolean, Constants, XML Attributes, Markup Link Url */
.hljs-symbol,
.hljs-number,
.hljs-link,
.hljs-attr,
.hljs-variable.constant_,
.hljs-literal {
  color: #94929e
}
/* base0A - Classes, Markup Bold, Search Text Background */
.hljs-title,
.hljs-class .hljs-title,
.hljs-title.class_ {
  color: #4f9062
}
.hljs-strong {
  font-weight: bold;
  color: #4f9062
}
/* base0B - Strings, Inherited Class, Markup Code, Diff Inserted */
.hljs-code,
.hljs-addition,
.hljs-title.class_.inherited__,
.hljs-string {
  color: #6562a8
}
/* base0C - Support, Regular Expressions, Escape Characters, Markup Quotes */
/* guessing */
.hljs-built_in,
.hljs-doctag,
.hljs-quote,
.hljs-keyword.hljs-atrule,
.hljs-regexp {
  color: #226f68
}
/* base0D - Functions, Methods, Attribute IDs, Headings */
.hljs-function .hljs-title,
.hljs-attribute,
.ruby .hljs-property,
.hljs-title.function_,
.hljs-section {
  color: #4d6bb6
}
/* base0E - Keywords, Storage, Selector, Markup Italic, Diff Changed */
/* .hljs-selector-id, */
/* .hljs-selector-class, */
/* .hljs-selector-attr, */
/* .hljs-selector-pseudo, */
.hljs-type,
.hljs-template-tag,
.diff .hljs-meta,
.hljs-keyword {
  color: #716cae
}
.hljs-emphasis {
  color: #716cae;
  font-style: italic
}
/* base0F - Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?> */
/*
  prevent top level .keyword and .string scopes
  from leaking into meta by accident
*/
.hljs-meta,
.hljs-meta .hljs-keyword,
.hljs-meta .hljs-string {
  color: #8c70a7
}
/* for v10 compatible themes */
.hljs-meta .hljs-keyword,
.hljs-meta-keyword {
  font-weight: bold
}