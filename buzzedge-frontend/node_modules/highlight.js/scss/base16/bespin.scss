pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
/*!
  Theme: Bespin
  Author: Jan <PERSON>
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/
/*
  WARNING: DO NOT EDIT THIS FILE DIRECTLY.

  This theme file was auto-generated from the Base16 scheme bespin
  by the Highlight.js Base16 template builder.

  - https://github.com/highlightjs/base16-highlightjs
*/
/*
base00  #28211c  Default Background
base01  #36312e  Lighter Background (Used for status bars, line number and folding marks)
base02  #5e5d5c  Selection Background
base03  #666666  Comments, Invisibles, Line Highlighting
base04  #797977  Dark Foreground (Used for status bars)
base05  #8a8986  Default Foreground, Caret, Delimiters, Operators
base06  #9d9b97  Light Foreground (Not often used)
base07  #baae9e  Light Background (Not often used)
base08  #cf6a4c  Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted
base09  #cf7d34  Integers, Boolean, Constants, XML Attributes, Markup Link Url
base0A  #f9ee98  Classes, Markup Bold, Search Text Background
base0B  #54be0d  Strings, Inherited Class, Markup Code, Diff Inserted
base0C  #afc4db  Support, Regular Expressions, Escape Characters, Markup Quotes
base0D  #5ea6ea  Functions, Methods, Attribute IDs, Headings
base0E  #9b859d  Keywords, Storage, Selector, Markup Italic, Diff Changed
base0F  #937121  Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?>
*/
pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
.hljs {
  color: #8a8986;
  background: #28211c
}
.hljs::selection,
.hljs ::selection {
  background-color: #5e5d5c;
  color: #8a8986
}
/* purposely do not highlight these things */
.hljs-formula,
.hljs-params,
.hljs-property {
  
}
/* base03 - #666666 -  Comments, Invisibles, Line Highlighting */
.hljs-comment {
  color: #666666
}
/* base04 - #797977 -  Dark Foreground (Used for status bars) */
.hljs-tag {
  color: #797977
}
/* base05 - #8a8986 -  Default Foreground, Caret, Delimiters, Operators */
.hljs-subst,
.hljs-punctuation,
.hljs-operator {
  color: #8a8986
}
.hljs-operator {
  opacity: 0.7
}
/* base08 - Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted */
.hljs-bullet,
.hljs-variable,
.hljs-template-variable,
.hljs-selector-tag,
.hljs-name,
.hljs-deletion {
  color: #cf6a4c
}
/* base09 - Integers, Boolean, Constants, XML Attributes, Markup Link Url */
.hljs-symbol,
.hljs-number,
.hljs-link,
.hljs-attr,
.hljs-variable.constant_,
.hljs-literal {
  color: #cf7d34
}
/* base0A - Classes, Markup Bold, Search Text Background */
.hljs-title,
.hljs-class .hljs-title,
.hljs-title.class_ {
  color: #f9ee98
}
.hljs-strong {
  font-weight: bold;
  color: #f9ee98
}
/* base0B - Strings, Inherited Class, Markup Code, Diff Inserted */
.hljs-code,
.hljs-addition,
.hljs-title.class_.inherited__,
.hljs-string {
  color: #54be0d
}
/* base0C - Support, Regular Expressions, Escape Characters, Markup Quotes */
/* guessing */
.hljs-built_in,
.hljs-doctag,
.hljs-quote,
.hljs-keyword.hljs-atrule,
.hljs-regexp {
  color: #afc4db
}
/* base0D - Functions, Methods, Attribute IDs, Headings */
.hljs-function .hljs-title,
.hljs-attribute,
.ruby .hljs-property,
.hljs-title.function_,
.hljs-section {
  color: #5ea6ea
}
/* base0E - Keywords, Storage, Selector, Markup Italic, Diff Changed */
/* .hljs-selector-id, */
/* .hljs-selector-class, */
/* .hljs-selector-attr, */
/* .hljs-selector-pseudo, */
.hljs-type,
.hljs-template-tag,
.diff .hljs-meta,
.hljs-keyword {
  color: #9b859d
}
.hljs-emphasis {
  color: #9b859d;
  font-style: italic
}
/* base0F - Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?> */
/*
  prevent top level .keyword and .string scopes
  from leaking into meta by accident
*/
.hljs-meta,
.hljs-meta .hljs-keyword,
.hljs-meta .hljs-string {
  color: #937121
}
/* for v10 compatible themes */
.hljs-meta .hljs-keyword,
.hljs-meta-keyword {
  font-weight: bold
}