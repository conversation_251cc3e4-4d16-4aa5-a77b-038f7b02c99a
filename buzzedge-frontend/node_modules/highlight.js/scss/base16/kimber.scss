pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
/*!
  Theme: Kimber
  Author: <PERSON><PERSON><PERSON> (https://github.com/akhsiM)
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/
/*
  WARNING: DO NOT EDIT THIS FILE DIRECTLY.

  This theme file was auto-generated from the Base16 scheme kimber
  by the Highlight.js Base16 template builder.

  - https://github.com/highlightjs/base16-highlightjs
*/
/*
base00  #222222  Default Background
base01  #313131  Lighter Background (Used for status bars, line number and folding marks)
base02  #555D55  Selection Background
base03  #644646  Comments, Invisibles, Line Highlighting
base04  #5A5A5A  Dark Foreground (Used for status bars)
base05  #DEDEE7  Default Foreground, Caret, Delimiters, Operators
base06  #C3C3B4  Light Foreground (Not often used)
base07  #FFFFE6  Light Background (Not often used)
base08  #C88C8C  Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted
base09  #476C88  Integers, Boolean, Constants, XML Attributes, Markup Link Url
base0A  #D8B56D  Classes, Markup Bold, Search Text Background
base0B  #99C899  Strings, Inherited Class, Markup Code, Diff Inserted
base0C  #78B4B4  Support, Regular Expressions, Escape Characters, Markup Quotes
base0D  #537C9C  Functions, Methods, Attribute IDs, Headings
base0E  #86CACD  Keywords, Storage, Selector, Markup Italic, Diff Changed
base0F  #704F4F  Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?>
*/
pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
.hljs {
  color: #DEDEE7;
  background: #222222
}
.hljs::selection,
.hljs ::selection {
  background-color: #555D55;
  color: #DEDEE7
}
/* purposely do not highlight these things */
.hljs-formula,
.hljs-params,
.hljs-property {
  
}
/* base03 - #644646 -  Comments, Invisibles, Line Highlighting */
.hljs-comment {
  color: #644646
}
/* base04 - #5A5A5A -  Dark Foreground (Used for status bars) */
.hljs-tag {
  color: #5A5A5A
}
/* base05 - #DEDEE7 -  Default Foreground, Caret, Delimiters, Operators */
.hljs-subst,
.hljs-punctuation,
.hljs-operator {
  color: #DEDEE7
}
.hljs-operator {
  opacity: 0.7
}
/* base08 - Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted */
.hljs-bullet,
.hljs-variable,
.hljs-template-variable,
.hljs-selector-tag,
.hljs-name,
.hljs-deletion {
  color: #C88C8C
}
/* base09 - Integers, Boolean, Constants, XML Attributes, Markup Link Url */
.hljs-symbol,
.hljs-number,
.hljs-link,
.hljs-attr,
.hljs-variable.constant_,
.hljs-literal {
  color: #476C88
}
/* base0A - Classes, Markup Bold, Search Text Background */
.hljs-title,
.hljs-class .hljs-title,
.hljs-title.class_ {
  color: #D8B56D
}
.hljs-strong {
  font-weight: bold;
  color: #D8B56D
}
/* base0B - Strings, Inherited Class, Markup Code, Diff Inserted */
.hljs-code,
.hljs-addition,
.hljs-title.class_.inherited__,
.hljs-string {
  color: #99C899
}
/* base0C - Support, Regular Expressions, Escape Characters, Markup Quotes */
/* guessing */
.hljs-built_in,
.hljs-doctag,
.hljs-quote,
.hljs-keyword.hljs-atrule,
.hljs-regexp {
  color: #78B4B4
}
/* base0D - Functions, Methods, Attribute IDs, Headings */
.hljs-function .hljs-title,
.hljs-attribute,
.ruby .hljs-property,
.hljs-title.function_,
.hljs-section {
  color: #537C9C
}
/* base0E - Keywords, Storage, Selector, Markup Italic, Diff Changed */
/* .hljs-selector-id, */
/* .hljs-selector-class, */
/* .hljs-selector-attr, */
/* .hljs-selector-pseudo, */
.hljs-type,
.hljs-template-tag,
.diff .hljs-meta,
.hljs-keyword {
  color: #86CACD
}
.hljs-emphasis {
  color: #86CACD;
  font-style: italic
}
/* base0F - Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?> */
/*
  prevent top level .keyword and .string scopes
  from leaking into meta by accident
*/
.hljs-meta,
.hljs-meta .hljs-keyword,
.hljs-meta .hljs-string {
  color: #704F4F
}
/* for v10 compatible themes */
.hljs-meta .hljs-keyword,
.hljs-meta-keyword {
  font-weight: bold
}