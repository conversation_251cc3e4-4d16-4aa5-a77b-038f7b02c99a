pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
/*!
  Theme: Qualia
  Author: isaac<PERSON>hanson
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/
/*
  WARNING: DO NOT EDIT THIS FILE DIRECTLY.

  This theme file was auto-generated from the Base16 scheme qualia
  by the Highlight.js Base16 template builder.

  - https://github.com/highlightjs/base16-highlightjs
*/
/*
base00  #101010  Default Background
base01  #454545  Lighter Background (Used for status bars, line number and folding marks)
base02  #454545  Selection Background
base03  #454545  Comments, Invisibles, Line Highlighting
base04  #808080  Dark Foreground (Used for status bars)
base05  #C0C0C0  Default Foreground, Caret, Delimiters, Operators
base06  #C0C0C0  Light Foreground (Not often used)
base07  #454545  Light Background (Not often used)
base08  #EFA6A2  Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted
base09  #A3B8EF  Integers, Boolean, Constants, XML Attributes, Markup Link Url
base0A  #E6A3DC  Classes, Markup Bold, Search Text Background
base0B  #80C990  Strings, Inherited Class, Markup Code, Diff Inserted
base0C  #C8C874  Support, Regular Expressions, Escape Characters, Markup Quotes
base0D  #50CACD  Functions, Methods, Attribute IDs, Headings
base0E  #E0AF85  Keywords, Storage, Selector, Markup Italic, Diff Changed
base0F  #808080  Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?>
*/
pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
.hljs {
  color: #C0C0C0;
  background: #101010
}
.hljs::selection,
.hljs ::selection {
  background-color: #454545;
  color: #C0C0C0
}
/* purposely do not highlight these things */
.hljs-formula,
.hljs-params,
.hljs-property {
  
}
/* base03 - #454545 -  Comments, Invisibles, Line Highlighting */
.hljs-comment {
  color: #454545
}
/* base04 - #808080 -  Dark Foreground (Used for status bars) */
.hljs-tag {
  color: #808080
}
/* base05 - #C0C0C0 -  Default Foreground, Caret, Delimiters, Operators */
.hljs-subst,
.hljs-punctuation,
.hljs-operator {
  color: #C0C0C0
}
.hljs-operator {
  opacity: 0.7
}
/* base08 - Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted */
.hljs-bullet,
.hljs-variable,
.hljs-template-variable,
.hljs-selector-tag,
.hljs-name,
.hljs-deletion {
  color: #EFA6A2
}
/* base09 - Integers, Boolean, Constants, XML Attributes, Markup Link Url */
.hljs-symbol,
.hljs-number,
.hljs-link,
.hljs-attr,
.hljs-variable.constant_,
.hljs-literal {
  color: #A3B8EF
}
/* base0A - Classes, Markup Bold, Search Text Background */
.hljs-title,
.hljs-class .hljs-title,
.hljs-title.class_ {
  color: #E6A3DC
}
.hljs-strong {
  font-weight: bold;
  color: #E6A3DC
}
/* base0B - Strings, Inherited Class, Markup Code, Diff Inserted */
.hljs-code,
.hljs-addition,
.hljs-title.class_.inherited__,
.hljs-string {
  color: #80C990
}
/* base0C - Support, Regular Expressions, Escape Characters, Markup Quotes */
/* guessing */
.hljs-built_in,
.hljs-doctag,
.hljs-quote,
.hljs-keyword.hljs-atrule,
.hljs-regexp {
  color: #C8C874
}
/* base0D - Functions, Methods, Attribute IDs, Headings */
.hljs-function .hljs-title,
.hljs-attribute,
.ruby .hljs-property,
.hljs-title.function_,
.hljs-section {
  color: #50CACD
}
/* base0E - Keywords, Storage, Selector, Markup Italic, Diff Changed */
/* .hljs-selector-id, */
/* .hljs-selector-class, */
/* .hljs-selector-attr, */
/* .hljs-selector-pseudo, */
.hljs-type,
.hljs-template-tag,
.diff .hljs-meta,
.hljs-keyword {
  color: #E0AF85
}
.hljs-emphasis {
  color: #E0AF85;
  font-style: italic
}
/* base0F - Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?> */
/*
  prevent top level .keyword and .string scopes
  from leaking into meta by accident
*/
.hljs-meta,
.hljs-meta .hljs-keyword,
.hljs-meta .hljs-string {
  color: #808080
}
/* for v10 compatible themes */
.hljs-meta .hljs-keyword,
.hljs-meta-keyword {
  font-weight: bold
}