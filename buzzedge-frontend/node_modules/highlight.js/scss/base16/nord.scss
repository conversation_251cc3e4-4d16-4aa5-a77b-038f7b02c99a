pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
/*!
  Theme: Nord
  Author: arcticicestudio
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/
/*
  WARNING: DO NOT EDIT THIS FILE DIRECTLY.

  This theme file was auto-generated from the Base16 scheme nord
  by the Highlight.js Base16 template builder.

  - https://github.com/highlightjs/base16-highlightjs
*/
/*
base00  #2E3440  Default Background
base01  #3B4252  Lighter Background (Used for status bars, line number and folding marks)
base02  #434C5E  Selection Background
base03  #4C566A  Comments, Invisibles, Line Highlighting
base04  #D8DEE9  Dark Foreground (Used for status bars)
base05  #E5E9F0  Default Foreground, Caret, Delimiters, Operators
base06  #ECEFF4  Light Foreground (Not often used)
base07  #8FBCBB  Light Background (Not often used)
base08  #BF616A  Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted
base09  #D08770  Integers, Boolean, Constants, XML Attributes, Markup Link Url
base0A  #EBCB8B  Classes, Markup Bold, Search Text Background
base0B  #A3BE8C  Strings, Inherited Class, Markup Code, Diff Inserted
base0C  #88C0D0  Support, Regular Expressions, Escape Characters, Markup Quotes
base0D  #81A1C1  Functions, Methods, Attribute IDs, Headings
base0E  #B48EAD  Keywords, Storage, Selector, Markup Italic, Diff Changed
base0F  #5E81AC  Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?>
*/
pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
.hljs {
  color: #E5E9F0;
  background: #2E3440
}
.hljs::selection,
.hljs ::selection {
  background-color: #434C5E;
  color: #E5E9F0
}
/* purposely do not highlight these things */
.hljs-formula,
.hljs-params,
.hljs-property {
  
}
/* base03 - #4C566A -  Comments, Invisibles, Line Highlighting */
.hljs-comment {
  color: #4C566A
}
/* base04 - #D8DEE9 -  Dark Foreground (Used for status bars) */
.hljs-tag {
  color: #D8DEE9
}
/* base05 - #E5E9F0 -  Default Foreground, Caret, Delimiters, Operators */
.hljs-subst,
.hljs-punctuation,
.hljs-operator {
  color: #E5E9F0
}
.hljs-operator {
  opacity: 0.7
}
/* base08 - Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted */
.hljs-bullet,
.hljs-variable,
.hljs-template-variable,
.hljs-selector-tag,
.hljs-name,
.hljs-deletion {
  color: #BF616A
}
/* base09 - Integers, Boolean, Constants, XML Attributes, Markup Link Url */
.hljs-symbol,
.hljs-number,
.hljs-link,
.hljs-attr,
.hljs-variable.constant_,
.hljs-literal {
  color: #D08770
}
/* base0A - Classes, Markup Bold, Search Text Background */
.hljs-title,
.hljs-class .hljs-title,
.hljs-title.class_ {
  color: #EBCB8B
}
.hljs-strong {
  font-weight: bold;
  color: #EBCB8B
}
/* base0B - Strings, Inherited Class, Markup Code, Diff Inserted */
.hljs-code,
.hljs-addition,
.hljs-title.class_.inherited__,
.hljs-string {
  color: #A3BE8C
}
/* base0C - Support, Regular Expressions, Escape Characters, Markup Quotes */
/* guessing */
.hljs-built_in,
.hljs-doctag,
.hljs-quote,
.hljs-keyword.hljs-atrule,
.hljs-regexp {
  color: #88C0D0
}
/* base0D - Functions, Methods, Attribute IDs, Headings */
.hljs-function .hljs-title,
.hljs-attribute,
.ruby .hljs-property,
.hljs-title.function_,
.hljs-section {
  color: #81A1C1
}
/* base0E - Keywords, Storage, Selector, Markup Italic, Diff Changed */
/* .hljs-selector-id, */
/* .hljs-selector-class, */
/* .hljs-selector-attr, */
/* .hljs-selector-pseudo, */
.hljs-type,
.hljs-template-tag,
.diff .hljs-meta,
.hljs-keyword {
  color: #B48EAD
}
.hljs-emphasis {
  color: #B48EAD;
  font-style: italic
}
/* base0F - Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?> */
/*
  prevent top level .keyword and .string scopes
  from leaking into meta by accident
*/
.hljs-meta,
.hljs-meta .hljs-keyword,
.hljs-meta .hljs-string {
  color: #5E81AC
}
/* for v10 compatible themes */
.hljs-meta .hljs-keyword,
.hljs-meta-keyword {
  font-weight: bold
}