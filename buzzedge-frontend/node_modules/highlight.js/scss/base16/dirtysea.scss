pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
/*!
  Theme: dirtysea
  Author: <PERSON><PERSON><PERSON> (<PERSON><PERSON>) <PERSON>dgson
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/
/*
  WARNING: DO NOT EDIT THIS FILE DIRECTLY.

  This theme file was auto-generated from the Base16 scheme dirtysea
  by the Highlight.js Base16 template builder.

  - https://github.com/highlightjs/base16-highlightjs
*/
/*
base00  #e0e0e0  Default Background
base01  #d0dad0  Lighter Background (Used for status bars, line number and folding marks)
base02  #d0d0d0  Selection Background
base03  #707070  Comments, Invisibles, Line Highlighting
base04  #202020  Dark Foreground (Used for status bars)
base05  #000000  Default Foreground, Caret, Delimiters, Operators
base06  #f8f8f8  Light Foreground (Not often used)
base07  #c4d9c4  Light Background (Not often used)
base08  #840000  Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted
base09  #006565  Integers, Boolean, Constants, XML Attributes, Markup Link Url
base0A  #755B00  Classes, Markup Bold, Search Text Background
base0B  #730073  Strings, Inherited Class, Markup Code, Diff Inserted
base0C  #755B00  Support, Regular Expressions, Escape Characters, Markup Quotes
base0D  #007300  Functions, Methods, Attribute IDs, Headings
base0E  #000090  Keywords, Storage, Selector, Markup Italic, Diff Changed
base0F  #755B00  Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?>
*/
pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
.hljs {
  color: #000000;
  background: #e0e0e0
}
.hljs::selection,
.hljs ::selection {
  background-color: #d0d0d0;
  color: #000000
}
/* purposely do not highlight these things */
.hljs-formula,
.hljs-params,
.hljs-property {
  
}
/* base03 - #707070 -  Comments, Invisibles, Line Highlighting */
.hljs-comment {
  color: #707070
}
/* base04 - #202020 -  Dark Foreground (Used for status bars) */
.hljs-tag {
  color: #202020
}
/* base05 - #000000 -  Default Foreground, Caret, Delimiters, Operators */
.hljs-subst,
.hljs-punctuation,
.hljs-operator {
  color: #000000
}
.hljs-operator {
  opacity: 0.7
}
/* base08 - Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted */
.hljs-bullet,
.hljs-variable,
.hljs-template-variable,
.hljs-selector-tag,
.hljs-name,
.hljs-deletion {
  color: #840000
}
/* base09 - Integers, Boolean, Constants, XML Attributes, Markup Link Url */
.hljs-symbol,
.hljs-number,
.hljs-link,
.hljs-attr,
.hljs-variable.constant_,
.hljs-literal {
  color: #006565
}
/* base0A - Classes, Markup Bold, Search Text Background */
.hljs-title,
.hljs-class .hljs-title,
.hljs-title.class_ {
  color: #755B00
}
.hljs-strong {
  font-weight: bold;
  color: #755B00
}
/* base0B - Strings, Inherited Class, Markup Code, Diff Inserted */
.hljs-code,
.hljs-addition,
.hljs-title.class_.inherited__,
.hljs-string {
  color: #730073
}
/* base0C - Support, Regular Expressions, Escape Characters, Markup Quotes */
/* guessing */
.hljs-built_in,
.hljs-doctag,
.hljs-quote,
.hljs-keyword.hljs-atrule,
.hljs-regexp {
  color: #755B00
}
/* base0D - Functions, Methods, Attribute IDs, Headings */
.hljs-function .hljs-title,
.hljs-attribute,
.ruby .hljs-property,
.hljs-title.function_,
.hljs-section {
  color: #007300
}
/* base0E - Keywords, Storage, Selector, Markup Italic, Diff Changed */
/* .hljs-selector-id, */
/* .hljs-selector-class, */
/* .hljs-selector-attr, */
/* .hljs-selector-pseudo, */
.hljs-type,
.hljs-template-tag,
.diff .hljs-meta,
.hljs-keyword {
  color: #000090
}
.hljs-emphasis {
  color: #000090;
  font-style: italic
}
/* base0F - Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?> */
/*
  prevent top level .keyword and .string scopes
  from leaking into meta by accident
*/
.hljs-meta,
.hljs-meta .hljs-keyword,
.hljs-meta .hljs-string {
  color: #755B00
}
/* for v10 compatible themes */
.hljs-meta .hljs-keyword,
.hljs-meta-keyword {
  font-weight: bold
}