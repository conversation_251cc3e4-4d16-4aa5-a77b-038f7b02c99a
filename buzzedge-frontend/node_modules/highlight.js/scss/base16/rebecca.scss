pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
/*!
  Theme: Rebecca
  Author: <PERSON> (http://github.com/vic) based on <PERSON> Theme (http://github.com/vic/rebecca-theme)
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/
/*
  WARNING: DO NOT EDIT THIS FILE DIRECTLY.

  This theme file was auto-generated from the Base16 scheme rebecca
  by the Highlight.js Base16 template builder.

  - https://github.com/highlightjs/base16-highlightjs
*/
/*
base00  #292a44  Default Background
base01  #663399  Lighter Background (Used for status bars, line number and folding marks)
base02  #383a62  Selection Background
base03  #666699  Comments, Invisibles, Line Highlighting
base04  #a0a0c5  Dark Foreground (Used for status bars)
base05  #f1eff8  Default Foreground, Caret, Delimiters, Operators
base06  #ccccff  Light Foreground (Not often used)
base07  #53495d  Light Background (Not often used)
base08  #a0a0c5  Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted
base09  #efe4a1  Integers, Boolean, Constants, XML Attributes, Markup Link Url
base0A  #ae81ff  Classes, Markup Bold, Search Text Background
base0B  #6dfedf  Strings, Inherited Class, Markup Code, Diff Inserted
base0C  #8eaee0  Support, Regular Expressions, Escape Characters, Markup Quotes
base0D  #2de0a7  Functions, Methods, Attribute IDs, Headings
base0E  #7aa5ff  Keywords, Storage, Selector, Markup Italic, Diff Changed
base0F  #ff79c6  Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?>
*/
pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
.hljs {
  color: #f1eff8;
  background: #292a44
}
.hljs::selection,
.hljs ::selection {
  background-color: #383a62;
  color: #f1eff8
}
/* purposely do not highlight these things */
.hljs-formula,
.hljs-params,
.hljs-property {
  
}
/* base03 - #666699 -  Comments, Invisibles, Line Highlighting */
.hljs-comment {
  color: #666699
}
/* base04 - #a0a0c5 -  Dark Foreground (Used for status bars) */
.hljs-tag {
  color: #a0a0c5
}
/* base05 - #f1eff8 -  Default Foreground, Caret, Delimiters, Operators */
.hljs-subst,
.hljs-punctuation,
.hljs-operator {
  color: #f1eff8
}
.hljs-operator {
  opacity: 0.7
}
/* base08 - Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted */
.hljs-bullet,
.hljs-variable,
.hljs-template-variable,
.hljs-selector-tag,
.hljs-name,
.hljs-deletion {
  color: #a0a0c5
}
/* base09 - Integers, Boolean, Constants, XML Attributes, Markup Link Url */
.hljs-symbol,
.hljs-number,
.hljs-link,
.hljs-attr,
.hljs-variable.constant_,
.hljs-literal {
  color: #efe4a1
}
/* base0A - Classes, Markup Bold, Search Text Background */
.hljs-title,
.hljs-class .hljs-title,
.hljs-title.class_ {
  color: #ae81ff
}
.hljs-strong {
  font-weight: bold;
  color: #ae81ff
}
/* base0B - Strings, Inherited Class, Markup Code, Diff Inserted */
.hljs-code,
.hljs-addition,
.hljs-title.class_.inherited__,
.hljs-string {
  color: #6dfedf
}
/* base0C - Support, Regular Expressions, Escape Characters, Markup Quotes */
/* guessing */
.hljs-built_in,
.hljs-doctag,
.hljs-quote,
.hljs-keyword.hljs-atrule,
.hljs-regexp {
  color: #8eaee0
}
/* base0D - Functions, Methods, Attribute IDs, Headings */
.hljs-function .hljs-title,
.hljs-attribute,
.ruby .hljs-property,
.hljs-title.function_,
.hljs-section {
  color: #2de0a7
}
/* base0E - Keywords, Storage, Selector, Markup Italic, Diff Changed */
/* .hljs-selector-id, */
/* .hljs-selector-class, */
/* .hljs-selector-attr, */
/* .hljs-selector-pseudo, */
.hljs-type,
.hljs-template-tag,
.diff .hljs-meta,
.hljs-keyword {
  color: #7aa5ff
}
.hljs-emphasis {
  color: #7aa5ff;
  font-style: italic
}
/* base0F - Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?> */
/*
  prevent top level .keyword and .string scopes
  from leaking into meta by accident
*/
.hljs-meta,
.hljs-meta .hljs-keyword,
.hljs-meta .hljs-string {
  color: #ff79c6
}
/* for v10 compatible themes */
.hljs-meta .hljs-keyword,
.hljs-meta-keyword {
  font-weight: bold
}