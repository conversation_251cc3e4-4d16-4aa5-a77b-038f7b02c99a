pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
/*!
  Theme: Materia
  Author: Defman21
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/
/*
  WARNING: DO NOT EDIT THIS FILE DIRECTLY.

  This theme file was auto-generated from the Base16 scheme materia
  by the Highlight.js Base16 template builder.

  - https://github.com/highlightjs/base16-highlightjs
*/
/*
base00  #263238  Default Background
base01  #2C393F  Lighter Background (Used for status bars, line number and folding marks)
base02  #37474F  Selection Background
base03  #707880  Comments, Invisibles, Line Highlighting
base04  #C9CCD3  Dark Foreground (Used for status bars)
base05  #CDD3DE  Default Foreground, Caret, Delimiters, Operators
base06  #D5DBE5  Light Foreground (Not often used)
base07  #FFFFFF  Light Background (Not often used)
base08  #EC5F67  Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted
base09  #EA9560  Integers, Boolean, Constants, XML Attributes, Markup Link Url
base0A  #FFCC00  Classes, Markup Bold, Search Text Background
base0B  #8BD649  Strings, Inherited Class, Markup Code, Diff Inserted
base0C  #80CBC4  Support, Regular Expressions, Escape Characters, Markup Quotes
base0D  #89DDFF  Functions, Methods, Attribute IDs, Headings
base0E  #82AAFF  Keywords, Storage, Selector, Markup Italic, Diff Changed
base0F  #EC5F67  Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?>
*/
pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
.hljs {
  color: #CDD3DE;
  background: #263238
}
.hljs::selection,
.hljs ::selection {
  background-color: #37474F;
  color: #CDD3DE
}
/* purposely do not highlight these things */
.hljs-formula,
.hljs-params,
.hljs-property {
  
}
/* base03 - #707880 -  Comments, Invisibles, Line Highlighting */
.hljs-comment {
  color: #707880
}
/* base04 - #C9CCD3 -  Dark Foreground (Used for status bars) */
.hljs-tag {
  color: #C9CCD3
}
/* base05 - #CDD3DE -  Default Foreground, Caret, Delimiters, Operators */
.hljs-subst,
.hljs-punctuation,
.hljs-operator {
  color: #CDD3DE
}
.hljs-operator {
  opacity: 0.7
}
/* base08 - Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted */
.hljs-bullet,
.hljs-variable,
.hljs-template-variable,
.hljs-selector-tag,
.hljs-name,
.hljs-deletion {
  color: #EC5F67
}
/* base09 - Integers, Boolean, Constants, XML Attributes, Markup Link Url */
.hljs-symbol,
.hljs-number,
.hljs-link,
.hljs-attr,
.hljs-variable.constant_,
.hljs-literal {
  color: #EA9560
}
/* base0A - Classes, Markup Bold, Search Text Background */
.hljs-title,
.hljs-class .hljs-title,
.hljs-title.class_ {
  color: #FFCC00
}
.hljs-strong {
  font-weight: bold;
  color: #FFCC00
}
/* base0B - Strings, Inherited Class, Markup Code, Diff Inserted */
.hljs-code,
.hljs-addition,
.hljs-title.class_.inherited__,
.hljs-string {
  color: #8BD649
}
/* base0C - Support, Regular Expressions, Escape Characters, Markup Quotes */
/* guessing */
.hljs-built_in,
.hljs-doctag,
.hljs-quote,
.hljs-keyword.hljs-atrule,
.hljs-regexp {
  color: #80CBC4
}
/* base0D - Functions, Methods, Attribute IDs, Headings */
.hljs-function .hljs-title,
.hljs-attribute,
.ruby .hljs-property,
.hljs-title.function_,
.hljs-section {
  color: #89DDFF
}
/* base0E - Keywords, Storage, Selector, Markup Italic, Diff Changed */
/* .hljs-selector-id, */
/* .hljs-selector-class, */
/* .hljs-selector-attr, */
/* .hljs-selector-pseudo, */
.hljs-type,
.hljs-template-tag,
.diff .hljs-meta,
.hljs-keyword {
  color: #82AAFF
}
.hljs-emphasis {
  color: #82AAFF;
  font-style: italic
}
/* base0F - Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?> */
/*
  prevent top level .keyword and .string scopes
  from leaking into meta by accident
*/
.hljs-meta,
.hljs-meta .hljs-keyword,
.hljs-meta .hljs-string {
  color: #EC5F67
}
/* for v10 compatible themes */
.hljs-meta .hljs-keyword,
.hljs-meta-keyword {
  font-weight: bold
}