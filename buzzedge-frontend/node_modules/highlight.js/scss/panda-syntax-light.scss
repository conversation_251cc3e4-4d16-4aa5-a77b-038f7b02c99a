pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
/**
 * Panda Syntax Theme for Highlight.js
 * Based on: https://github.com/tinkertrain/panda-syntax-vscode
 * Author: <PERSON><PERSON><PERSON> <https://github.com/ann<PERSON>ie-switzer>
 */
.hljs {
  color: #2a2c2d;
  background: #e6e6e6
}
.hljs-emphasis {
  font-style: italic
}
.hljs-strong {
  font-weight: bold
}
.hljs-link {
  text-decoration: underline
}
.hljs-comment,
.hljs-quote {
  color: #676B79;
  font-style: italic
}
.hljs-params {
  color: #676B79
}
.hljs-punctuation,
.hljs-attr {
  color: #2a2c2d
}
.hljs-selector-tag,
.hljs-name,
.hljs-meta,
.hljs-operator,
.hljs-char.escape_ {
  color: #c56200
}
.hljs-keyword,
.hljs-deletion {
  color: #d92792
}
.hljs-regexp,
.hljs-selector-pseudo,
.hljs-selector-attr,
.hljs-variable.language_ {
  color: #cc5e91
}
.hljs-subst,
.hljs-property,
.hljs-code,
.hljs-formula,
.hljs-section,
.hljs-title.function_ {
  color: #3787c7
}
.hljs-string,
.hljs-symbol,
.hljs-bullet,
.hljs-addition,
.hljs-selector-class,
.hljs-title.class_,
.hljs-title.class_.inherited__,
.hljs-meta .hljs-string {
  color: #0d7d6c
}
.hljs-variable,
.hljs-template-variable,
.hljs-number,
.hljs-literal,
.hljs-type,
.hljs-link,
.hljs-built_in,
.hljs-title,
.hljs-selector-id,
.hljs-tag,
.hljs-doctag,
.hljs-attribute,
.hljs-template-tag,
.hljs-meta .hljs-keyword {
  color: #7641bb
}