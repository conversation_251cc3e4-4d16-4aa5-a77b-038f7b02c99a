{"version": 3, "sources": ["../../src/nextjs/index.tsx", "../../src/react/index.tsx", "../../package.json", "../../src/queue.ts", "../../src/utils.ts", "../../src/generic.ts", "../../src/react/utils.ts", "../../src/nextjs/utils.ts"], "sourcesContent": ["'use client';\nimport React, { Suspense, type ReactNode } from 'react';\nimport { Analytics as AnalyticsScript } from '../react';\nimport type { AnalyticsProps, BeforeSend, BeforeSendEvent } from '../types';\nimport { getBasePath, useRoute } from './utils';\n\ntype Props = Omit<AnalyticsProps, 'route' | 'disableAutoTrack'>;\n\nfunction AnalyticsComponent(props: Props): ReactNode {\n  const { route, path } = useRoute();\n  return (\n    <AnalyticsScript\n      path={path}\n      route={route}\n      {...props}\n      basePath={getBasePath()}\n      framework=\"next\"\n    />\n  );\n}\n\nexport function Analytics(props: Props): null {\n  // Because of incompatible types between ReactNode in React 19 and React 18 we return null (which is also what we render)\n  return (\n    <Suspense fallback={null}>\n      <AnalyticsComponent {...props} />\n    </Suspense>\n  ) as never;\n}\n\nexport type { AnalyticsProps, BeforeSend, BeforeSendEvent };\n", "'use client';\nimport { useEffect } from 'react';\nimport { inject, track, pageview } from '../generic';\nimport type { AnalyticsProps, BeforeSend, BeforeSendEvent } from '../types';\nimport { getBasePath } from './utils';\n\n/**\n * Injects the Vercel Web Analytics script into the page head and starts tracking page views. Read more in our [documentation](https://vercel.com/docs/concepts/analytics/package).\n * @param [props] - Analytics options.\n * @param [props.mode] - The mode to use for the analytics script. Defaults to `auto`.\n *  - `auto` - Automatically detect the environment.  Uses `production` if the environment cannot be determined.\n *  - `production` - Always use the production script. (Sends events to the server)\n *  - `development` - Always use the development script. (Logs events to the console)\n * @param [props.debug] - Whether to enable debug logging in development. Defaults to `true`.\n * @param [props.beforeSend] - A middleware function to modify events before they are sent. Should return the event object or `null` to cancel the event.\n * @example\n * ```js\n * import { Analytics } from '@vercel/analytics/react';\n *\n * export default function App() {\n *  return (\n *   <div>\n *    <Analytics />\n *    <h1>My App</h1>\n *  </div>\n * );\n * }\n * ```\n */\nfunction Analytics(\n  props: AnalyticsProps & {\n    framework?: string;\n    route?: string | null;\n    path?: string | null;\n    basePath?: string;\n  }\n): null {\n  useEffect(() => {\n    if (props.beforeSend) {\n      window.va?.('beforeSend', props.beforeSend);\n    }\n  }, [props.beforeSend]);\n\n  // biome-ignore lint/correctness/useExhaustiveDependencies: only run once\n  useEffect(() => {\n    inject({\n      framework: props.framework || 'react',\n      basePath: props.basePath ?? getBasePath(),\n      ...(props.route !== undefined && { disableAutoTrack: true }),\n      ...props,\n    });\n    // eslint-disable-next-line react-hooks/exhaustive-deps -- only run once\n  }, []);\n\n  useEffect(() => {\n    // explicitely track page view, since we disabled auto tracking\n    if (props.route && props.path) {\n      pageview({ route: props.route, path: props.path });\n    }\n  }, [props.route, props.path]);\n\n  return null;\n}\n\nexport { track, Analytics };\nexport type { AnalyticsProps, BeforeSend, BeforeSendEvent };\n", "{\n  \"name\": \"@vercel/analytics\",\n  \"version\": \"1.5.0\",\n  \"description\": \"Gain real-time traffic insights with Vercel Web Analytics\",\n  \"keywords\": [\n    \"analytics\",\n    \"vercel\"\n  ],\n  \"repository\": {\n    \"url\": \"github:vercel/analytics\",\n    \"directory\": \"packages/web\"\n  },\n  \"license\": \"MPL-2.0\",\n  \"exports\": {\n    \"./package.json\": \"./package.json\",\n    \".\": {\n      \"browser\": \"./dist/index.mjs\",\n      \"import\": \"./dist/index.mjs\",\n      \"require\": \"./dist/index.js\"\n    },\n    \"./astro\": {\n      \"import\": \"./dist/astro/component.ts\"\n    },\n    \"./next\": {\n      \"browser\": \"./dist/next/index.mjs\",\n      \"import\": \"./dist/next/index.mjs\",\n      \"require\": \"./dist/next/index.js\"\n    },\n    \"./nuxt\": {\n      \"browser\": \"./dist/nuxt/index.mjs\",\n      \"import\": \"./dist/nuxt/index.mjs\",\n      \"require\": \"./dist/nuxt/index.js\"\n    },\n    \"./react\": {\n      \"browser\": \"./dist/react/index.mjs\",\n      \"import\": \"./dist/react/index.mjs\",\n      \"require\": \"./dist/react/index.js\"\n    },\n    \"./remix\": {\n      \"browser\": \"./dist/remix/index.mjs\",\n      \"import\": \"./dist/remix/index.mjs\",\n      \"require\": \"./dist/remix/index.js\"\n    },\n    \"./server\": {\n      \"node\": \"./dist/server/index.mjs\",\n      \"edge-light\": \"./dist/server/index.mjs\",\n      \"import\": \"./dist/server/index.mjs\",\n      \"require\": \"./dist/server/index.js\",\n      \"default\": \"./dist/server/index.js\"\n    },\n    \"./sveltekit\": {\n      \"svelte\": \"./dist/sveltekit/index.mjs\",\n      \"types\": \"./dist/sveltekit/index.d.ts\"\n    },\n    \"./vue\": {\n      \"browser\": \"./dist/vue/index.mjs\",\n      \"import\": \"./dist/vue/index.mjs\",\n      \"require\": \"./dist/vue/index.js\"\n    }\n  },\n  \"main\": \"./dist/index.mjs\",\n  \"types\": \"./dist/index.d.ts\",\n  \"typesVersions\": {\n    \"*\": {\n      \"*\": [\n        \"dist/index.d.ts\"\n      ],\n      \"next\": [\n        \"dist/next/index.d.ts\"\n      ],\n      \"nuxt\": [\n        \"dist/nuxt/index.d.ts\"\n      ],\n      \"react\": [\n        \"dist/react/index.d.ts\"\n      ],\n      \"remix\": [\n        \"dist/remix/index.d.ts\"\n      ],\n      \"server\": [\n        \"dist/server/index.d.ts\"\n      ],\n      \"sveltekit\": [\n        \"dist/sveltekit/index.d.ts\"\n      ],\n      \"vue\": [\n        \"dist/vue/index.d.ts\"\n      ]\n    }\n  },\n  \"scripts\": {\n    \"build\": \"tsup && pnpm copy-astro\",\n    \"copy-astro\": \"cp -R src/astro dist/\",\n    \"dev\": \"pnpm copy-astro && tsup --watch\",\n    \"lint\": \"eslint .\",\n    \"lint-fix\": \"eslint . --fix\",\n    \"test\": \"vitest\",\n    \"type-check\": \"tsc --noEmit\"\n  },\n  \"eslintConfig\": {\n    \"extends\": [\n      \"@vercel/eslint-config\"\n    ],\n    \"rules\": {\n      \"tsdoc/syntax\": \"off\"\n    },\n    \"ignorePatterns\": [\n      \"jest.setup.ts\"\n    ]\n  },\n  \"devDependencies\": {\n    \"@swc/core\": \"^1.9.2\",\n    \"@testing-library/jest-dom\": \"^6.6.3\",\n    \"@testing-library/react\": \"^16.0.1\",\n    \"@types/node\": \"^22.9.0\",\n    \"@types/react\": \"^18.3.12\",\n    \"@vercel/eslint-config\": \"workspace:0.0.0\",\n    \"server-only\": \"^0.0.1\",\n    \"svelte\": \"^5.1.10\",\n    \"tsup\": \"8.3.5\",\n    \"vitest\": \"^2.1.5\",\n    \"vue\": \"^3.5.12\",\n    \"vue-router\": \"^4.4.5\"\n  },\n  \"peerDependencies\": {\n    \"@remix-run/react\": \"^2\",\n    \"@sveltejs/kit\": \"^1 || ^2\",\n    \"next\": \">= 13\",\n    \"react\": \"^18 || ^19 || ^19.0.0-rc\",\n    \"svelte\": \">= 4\",\n    \"vue\": \"^3\",\n    \"vue-router\": \"^4\"\n  },\n  \"peerDependenciesMeta\": {\n    \"@remix-run/react\": {\n      \"optional\": true\n    },\n    \"@sveltejs/kit\": {\n      \"optional\": true\n    },\n    \"next\": {\n      \"optional\": true\n    },\n    \"react\": {\n      \"optional\": true\n    },\n    \"svelte\": {\n      \"optional\": true\n    },\n    \"vue\": {\n      \"optional\": true\n    },\n    \"vue-router\": {\n      \"optional\": true\n    }\n  }\n}\n", "export const initQueue = (): void => {\n  // initialize va until script is loaded\n  if (window.va) return;\n\n  window.va = function a(...params): void {\n    (window.vaq = window.vaq || []).push(params);\n  };\n};\n", "import type { AllowedPropertyValues, AnalyticsProps, Mode } from './types';\n\nexport function isBrowser(): boolean {\n  return typeof window !== 'undefined';\n}\n\nfunction detectEnvironment(): 'development' | 'production' {\n  try {\n    const env = process.env.NODE_ENV;\n    if (env === 'development' || env === 'test') {\n      return 'development';\n    }\n  } catch (e) {\n    // do nothing, this is okay\n  }\n  return 'production';\n}\n\nexport function setMode(mode: Mode = 'auto'): void {\n  if (mode === 'auto') {\n    window.vam = detectEnvironment();\n    return;\n  }\n\n  window.vam = mode;\n}\n\nexport function getMode(): Mode {\n  const mode = isBrowser() ? window.vam : detectEnvironment();\n  return mode || 'production';\n}\n\nexport function isProduction(): boolean {\n  return getMode() === 'production';\n}\n\nexport function isDevelopment(): boolean {\n  return getMode() === 'development';\n}\n\nfunction removeKey(\n  key: string,\n  { [key]: _, ...rest }\n): Record<string, unknown> {\n  return rest;\n}\n\nexport function parseProperties(\n  properties: Record<string, unknown> | undefined,\n  options: {\n    strip?: boolean;\n  }\n): Error | Record<string, AllowedPropertyValues> | undefined {\n  if (!properties) return undefined;\n  let props = properties;\n  const errorProperties: string[] = [];\n  for (const [key, value] of Object.entries(properties)) {\n    if (typeof value === 'object' && value !== null) {\n      if (options.strip) {\n        props = removeKey(key, props);\n      } else {\n        errorProperties.push(key);\n      }\n    }\n  }\n\n  if (errorProperties.length > 0 && !options.strip) {\n    throw Error(\n      `The following properties are not valid: ${errorProperties.join(\n        ', '\n      )}. Only strings, numbers, booleans, and null are allowed.`\n    );\n  }\n  return props as Record<string, AllowedPropertyValues>;\n}\n\nexport function computeRoute(\n  pathname: string | null,\n  pathParams: Record<string, string | string[]> | null\n): string | null {\n  if (!pathname || !pathParams) {\n    return pathname;\n  }\n\n  let result = pathname;\n  try {\n    const entries = Object.entries(pathParams);\n    // simple keys must be handled first\n    for (const [key, value] of entries) {\n      if (!Array.isArray(value)) {\n        const matcher = turnValueToRegExp(value);\n        if (matcher.test(result)) {\n          result = result.replace(matcher, `/[${key}]`);\n        }\n      }\n    }\n    // array values next\n    for (const [key, value] of entries) {\n      if (Array.isArray(value)) {\n        const matcher = turnValueToRegExp(value.join('/'));\n        if (matcher.test(result)) {\n          result = result.replace(matcher, `/[...${key}]`);\n        }\n      }\n    }\n    return result;\n  } catch (e) {\n    return pathname;\n  }\n}\n\nfunction turnValueToRegExp(value: string): RegExp {\n  return new RegExp(`/${escapeRegExp(value)}(?=[/?#]|$)`);\n}\n\nfunction escapeRegExp(string: string): string {\n  return string.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&');\n}\n\nexport function getScriptSrc(\n  props: AnalyticsProps & { basePath?: string }\n): string {\n  if (props.scriptSrc) {\n    return props.scriptSrc;\n  }\n  if (isDevelopment()) {\n    return 'https://va.vercel-scripts.com/v1/script.debug.js';\n  }\n  if (props.basePath) {\n    return `${props.basePath}/insights/script.js`;\n  }\n  return '/_vercel/insights/script.js';\n}\n", "import { name as packageName, version } from '../package.json';\nimport { initQueue } from './queue';\nimport type {\n  AllowedPropertyValues,\n  AnalyticsProps,\n  FlagsDataInput,\n  BeforeSend,\n  BeforeSendEvent,\n} from './types';\nimport {\n  isBrowser,\n  parseProperties,\n  setMode,\n  isDevelopment,\n  isProduction,\n  computeRoute,\n  getScriptSrc,\n} from './utils';\n\n/**\n * Injects the Vercel Web Analytics script into the page head and starts tracking page views. Read more in our [documentation](https://vercel.com/docs/concepts/analytics/package).\n * @param [props] - Analytics options.\n * @param [props.mode] - The mode to use for the analytics script. Defaults to `auto`.\n *  - `auto` - Automatically detect the environment.  Uses `production` if the environment cannot be determined.\n *  - `production` - Always use the production script. (Sends events to the server)\n *  - `development` - Always use the development script. (Logs events to the console)\n * @param [props.debug] - Whether to enable debug logging in development. Defaults to `true`.\n * @param [props.beforeSend] - A middleware function to modify events before they are sent. Should return the event object or `null` to cancel the event.\n * @param [props.dsn] - The DSN of the project to send events to. Only required when self-hosting.\n * @param [props.disableAutoTrack] - Whether the injected script should track page views from pushState events. Disable if route is updated after pushState, a manually call page pageview().\n */\nfunction inject(\n  props: AnalyticsProps & {\n    framework?: string;\n    disableAutoTrack?: boolean;\n    basePath?: string;\n  } = {\n    debug: true,\n  }\n): void {\n  if (!isBrowser()) return;\n\n  setMode(props.mode);\n\n  initQueue();\n\n  if (props.beforeSend) {\n    window.va?.('beforeSend', props.beforeSend);\n  }\n\n  const src = getScriptSrc(props);\n\n  if (document.head.querySelector(`script[src*=\"${src}\"]`)) return;\n\n  const script = document.createElement('script');\n  script.src = src;\n  script.defer = true;\n  script.dataset.sdkn =\n    packageName + (props.framework ? `/${props.framework}` : '');\n  script.dataset.sdkv = version;\n\n  if (props.disableAutoTrack) {\n    script.dataset.disableAutoTrack = '1';\n  }\n  if (props.endpoint) {\n    script.dataset.endpoint = props.endpoint;\n  } else if (props.basePath) {\n    script.dataset.endpoint = `${props.basePath}/insights`;\n  }\n  if (props.dsn) {\n    script.dataset.dsn = props.dsn;\n  }\n\n  script.onerror = (): void => {\n    const errorMessage = isDevelopment()\n      ? 'Please check if any ad blockers are enabled and try again.'\n      : 'Be sure to enable Web Analytics for your project and deploy again. See https://vercel.com/docs/analytics/quickstart for more information.';\n\n    // eslint-disable-next-line no-console -- Logging to console is intentional\n    console.log(\n      `[Vercel Web Analytics] Failed to load script from ${src}. ${errorMessage}`\n    );\n  };\n\n  if (isDevelopment() && props.debug === false) {\n    script.dataset.debug = 'false';\n  }\n\n  document.head.appendChild(script);\n}\n\n/**\n * Tracks a custom event. Please refer to the [documentation](https://vercel.com/docs/concepts/analytics/custom-events) for more information on custom events.\n * @param name - The name of the event.\n * * Examples: `Purchase`, `Click Button`, or `Play Video`.\n * @param [properties] - Additional properties of the event. Nested objects are not supported. Allowed values are `string`, `number`, `boolean`, and `null`.\n */\nfunction track(\n  name: string,\n  properties?: Record<string, AllowedPropertyValues>,\n  options?: {\n    flags?: FlagsDataInput;\n  }\n): void {\n  if (!isBrowser()) {\n    const msg =\n      '[Vercel Web Analytics] Please import `track` from `@vercel/analytics/server` when using this function in a server environment';\n\n    if (isProduction()) {\n      // eslint-disable-next-line no-console -- Show warning in production\n      console.warn(msg);\n    } else {\n      throw new Error(msg);\n    }\n\n    return;\n  }\n\n  if (!properties) {\n    window.va?.('event', { name, options });\n    return;\n  }\n\n  try {\n    const props = parseProperties(properties, {\n      strip: isProduction(),\n    });\n\n    window.va?.('event', {\n      name,\n      data: props,\n      options,\n    });\n  } catch (err) {\n    if (err instanceof Error && isDevelopment()) {\n      // eslint-disable-next-line no-console -- Logging to console is intentional\n      console.error(err);\n    }\n  }\n}\n\nfunction pageview({\n  route,\n  path,\n}: {\n  route?: string | null;\n  path?: string;\n}): void {\n  window.va?.('pageview', { route, path });\n}\n\nexport { inject, track, pageview, computeRoute };\nexport type { AnalyticsProps, BeforeSend, BeforeSendEvent };\n\n// eslint-disable-next-line import/no-default-export -- Default export is intentional\nexport default {\n  inject,\n  track,\n  computeRoute,\n};\n", "export function getBasePath(): string | undefined {\n  // !! important !!\n  // do not access env variables using process.env[varname]\n  // some bundles won't replace the value at build time.\n  // eslint-disable-next-line @typescript-eslint/prefer-optional-chain -- we can't use optionnal here, it'll break if process does not exist.\n  if (typeof process === 'undefined' || typeof process.env === 'undefined') {\n    return undefined;\n  }\n  return process.env.REACT_APP_VERCEL_OBSERVABILITY_BASEPATH;\n}\n", "'use client';\n/* eslint-disable @typescript-eslint/no-unnecessary-condition -- can be empty in pages router */\nimport { useParams, usePathname, useSearchParams } from 'next/navigation.js';\nimport { computeRoute } from '../utils';\n\nexport const useRoute = (): {\n  route: string | null;\n  path: string;\n} => {\n  const params = useParams();\n  const searchParams = useSearchParams();\n  const path = usePathname();\n\n  // Until we have route parameters, we don't compute the route\n  if (!params) {\n    return { route: null, path };\n  }\n  // in Next.js@13, useParams() could return an empty object for pages router, and we default to searchParams.\n  const finalParams = Object.keys(params).length\n    ? params\n    : Object.fromEntries(searchParams.entries());\n  return { route: computeRoute(path, finalParams), path };\n};\n\nexport function getBasePath(): string | undefined {\n  // !! important !!\n  // do not access env variables using process.env[varname]\n  // some bundles won't replace the value at build time.\n  // eslint-disable-next-line @typescript-eslint/prefer-optional-chain -- we can't use optionnal here, it'll break if process does not exist.\n  if (typeof process === 'undefined' || typeof process.env === 'undefined') {\n    return undefined;\n  }\n  return process.env.NEXT_PUBLIC_VERCEL_OBSERVABILITY_BASEPATH;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA,mBAAAA;AAAA;AAAA;AACA,IAAAC,gBAAgD;;;ACAhD,mBAA0B;;;ACAxB,WAAQ;AACR,cAAW;;;ACFN,IAAM,YAAY,MAAY;AAEnC,MAAI,OAAO,GAAI;AAEf,SAAO,KAAK,SAAS,KAAK,QAAc;AACtC,KAAC,OAAO,MAAM,OAAO,OAAO,CAAC,GAAG,KAAK,MAAM;AAAA,EAC7C;AACF;;;ACLO,SAAS,YAAqB;AACnC,SAAO,OAAO,WAAW;AAC3B;AAEA,SAAS,oBAAkD;AACzD,MAAI;AACF,UAAM,MAAM,QAAQ,IAAI;AACxB,QAAI,QAAQ,iBAAiB,QAAQ,QAAQ;AAC3C,aAAO;AAAA,IACT;AAAA,EACF,SAAS,GAAG;AAAA,EAEZ;AACA,SAAO;AACT;AAEO,SAAS,QAAQ,OAAa,QAAc;AACjD,MAAI,SAAS,QAAQ;AACnB,WAAO,MAAM,kBAAkB;AAC/B;AAAA,EACF;AAEA,SAAO,MAAM;AACf;AAEO,SAAS,UAAgB;AAC9B,QAAM,OAAO,UAAU,IAAI,OAAO,MAAM,kBAAkB;AAC1D,SAAO,QAAQ;AACjB;AAMO,SAAS,gBAAyB;AACvC,SAAO,QAAQ,MAAM;AACvB;AAsCO,SAAS,aACd,UACA,YACe;AACf,MAAI,CAAC,YAAY,CAAC,YAAY;AAC5B,WAAO;AAAA,EACT;AAEA,MAAI,SAAS;AACb,MAAI;AACF,UAAM,UAAU,OAAO,QAAQ,UAAU;AAEzC,eAAW,CAAC,KAAK,KAAK,KAAK,SAAS;AAClC,UAAI,CAAC,MAAM,QAAQ,KAAK,GAAG;AACzB,cAAM,UAAU,kBAAkB,KAAK;AACvC,YAAI,QAAQ,KAAK,MAAM,GAAG;AACxB,mBAAS,OAAO,QAAQ,SAAS,KAAK,GAAG,GAAG;AAAA,QAC9C;AAAA,MACF;AAAA,IACF;AAEA,eAAW,CAAC,KAAK,KAAK,KAAK,SAAS;AAClC,UAAI,MAAM,QAAQ,KAAK,GAAG;AACxB,cAAM,UAAU,kBAAkB,MAAM,KAAK,GAAG,CAAC;AACjD,YAAI,QAAQ,KAAK,MAAM,GAAG;AACxB,mBAAS,OAAO,QAAQ,SAAS,QAAQ,GAAG,GAAG;AAAA,QACjD;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT,SAAS,GAAG;AACV,WAAO;AAAA,EACT;AACF;AAEA,SAAS,kBAAkB,OAAuB;AAChD,SAAO,IAAI,OAAO,IAAI,aAAa,KAAK,CAAC,aAAa;AACxD;AAEA,SAAS,aAAa,QAAwB;AAC5C,SAAO,OAAO,QAAQ,uBAAuB,MAAM;AACrD;AAEO,SAAS,aACd,OACQ;AACR,MAAI,MAAM,WAAW;AACnB,WAAO,MAAM;AAAA,EACf;AACA,MAAI,cAAc,GAAG;AACnB,WAAO;AAAA,EACT;AACA,MAAI,MAAM,UAAU;AAClB,WAAO,GAAG,MAAM,QAAQ;AAAA,EAC1B;AACA,SAAO;AACT;;;ACrGA,SAAS,OACP,QAII;AAAA,EACF,OAAO;AACT,GACM;AAvCR;AAwCE,MAAI,CAAC,UAAU,EAAG;AAElB,UAAQ,MAAM,IAAI;AAElB,YAAU;AAEV,MAAI,MAAM,YAAY;AACpB,iBAAO,OAAP,gCAAY,cAAc,MAAM;AAAA,EAClC;AAEA,QAAM,MAAM,aAAa,KAAK;AAE9B,MAAI,SAAS,KAAK,cAAc,gBAAgB,GAAG,IAAI,EAAG;AAE1D,QAAM,SAAS,SAAS,cAAc,QAAQ;AAC9C,SAAO,MAAM;AACb,SAAO,QAAQ;AACf,SAAO,QAAQ,OACb,QAAe,MAAM,YAAY,IAAI,MAAM,SAAS,KAAK;AAC3D,SAAO,QAAQ,OAAO;AAEtB,MAAI,MAAM,kBAAkB;AAC1B,WAAO,QAAQ,mBAAmB;AAAA,EACpC;AACA,MAAI,MAAM,UAAU;AAClB,WAAO,QAAQ,WAAW,MAAM;AAAA,EAClC,WAAW,MAAM,UAAU;AACzB,WAAO,QAAQ,WAAW,GAAG,MAAM,QAAQ;AAAA,EAC7C;AACA,MAAI,MAAM,KAAK;AACb,WAAO,QAAQ,MAAM,MAAM;AAAA,EAC7B;AAEA,SAAO,UAAU,MAAY;AAC3B,UAAM,eAAe,cAAc,IAC/B,+DACA;AAGJ,YAAQ;AAAA,MACN,qDAAqD,GAAG,KAAK,YAAY;AAAA,IAC3E;AAAA,EACF;AAEA,MAAI,cAAc,KAAK,MAAM,UAAU,OAAO;AAC5C,WAAO,QAAQ,QAAQ;AAAA,EACzB;AAEA,WAAS,KAAK,YAAY,MAAM;AAClC;AAoDA,SAAS,SAAS;AAAA,EAChB;AAAA,EACA;AACF,GAGS;AAnJT;AAoJE,eAAO,OAAP,gCAAY,YAAY,EAAE,OAAO,KAAK;AACxC;;;ACrJO,SAAS,cAAkC;AAKhD,MAAI,OAAO,YAAY,eAAe,OAAO,QAAQ,QAAQ,aAAa;AACxE,WAAO;AAAA,EACT;AACA,SAAO,QAAQ,IAAI;AACrB;;;ALoBA,SAAS,UACP,OAMM;AACN,8BAAU,MAAM;AArClB;AAsCI,QAAI,MAAM,YAAY;AACpB,mBAAO,OAAP,gCAAY,cAAc,MAAM;AAAA,IAClC;AAAA,EACF,GAAG,CAAC,MAAM,UAAU,CAAC;AAGrB,8BAAU,MAAM;AACd,WAAO;AAAA,MACL,WAAW,MAAM,aAAa;AAAA,MAC9B,UAAU,MAAM,YAAY,YAAY;AAAA,MACxC,GAAI,MAAM,UAAU,UAAa,EAAE,kBAAkB,KAAK;AAAA,MAC1D,GAAG;AAAA,IACL,CAAC;AAAA,EAEH,GAAG,CAAC,CAAC;AAEL,8BAAU,MAAM;AAEd,QAAI,MAAM,SAAS,MAAM,MAAM;AAC7B,eAAS,EAAE,OAAO,MAAM,OAAO,MAAM,MAAM,KAAK,CAAC;AAAA,IACnD;AAAA,EACF,GAAG,CAAC,MAAM,OAAO,MAAM,IAAI,CAAC;AAE5B,SAAO;AACT;;;AM5DA,wBAAwD;AAGjD,IAAM,WAAW,MAGnB;AACH,QAAM,aAAS,6BAAU;AACzB,QAAM,mBAAe,mCAAgB;AACrC,QAAM,WAAO,+BAAY;AAGzB,MAAI,CAAC,QAAQ;AACX,WAAO,EAAE,OAAO,MAAM,KAAK;AAAA,EAC7B;AAEA,QAAM,cAAc,OAAO,KAAK,MAAM,EAAE,SACpC,SACA,OAAO,YAAY,aAAa,QAAQ,CAAC;AAC7C,SAAO,EAAE,OAAO,aAAa,MAAM,WAAW,GAAG,KAAK;AACxD;AAEO,SAASC,eAAkC;AAKhD,MAAI,OAAO,YAAY,eAAe,OAAO,QAAQ,QAAQ,aAAa;AACxE,WAAO;AAAA,EACT;AACA,SAAO,QAAQ,IAAI;AACrB;;;APzBA,SAAS,mBAAmB,OAAyB;AACnD,QAAM,EAAE,OAAO,KAAK,IAAI,SAAS;AACjC,SACE,8BAAAC,QAAA;AAAA,IAAC;AAAA;AAAA,MACC;AAAA,MACA;AAAA,MACC,GAAG;AAAA,MACJ,UAAUC,aAAY;AAAA,MACtB,WAAU;AAAA;AAAA,EACZ;AAEJ;AAEO,SAASC,WAAU,OAAoB;AAE5C,SACE,8BAAAF,QAAA,cAAC,0BAAS,UAAU,QAClB,8BAAAA,QAAA,cAAC,sBAAoB,GAAG,OAAO,CACjC;AAEJ;", "names": ["Analytics", "import_react", "get<PERSON><PERSON><PERSON><PERSON>", "React", "get<PERSON><PERSON><PERSON><PERSON>", "Analytics"]}