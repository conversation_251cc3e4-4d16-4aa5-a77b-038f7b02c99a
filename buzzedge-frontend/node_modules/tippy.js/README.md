<div align="center">
  <img alt="Tippy.js logo" src="https://github.com/atomiks/tippyjs/raw/master/logo.png" height="117" />
</div>

<div align="center">
  <h1>Tippy.js</h1>
  <p>The complete tooltip, popover, dropdown, and menu solution for the web</p>
  <a href="https://www.npmjs.com/package/tippy.js">
   <img src="https://img.shields.io/npm/dm/tippy.js.svg?color=%235599ff&style=for-the-badge" alt="npm Downloads per Month">
  <a>
  <a href="https://github.com/atomiks/tippyjs/blob/master/LICENSE">
    <img src="https://img.shields.io/npm/l/tippy.js.svg?color=%23c677cf&style=for-the-badge" alt="MIT License">
  </a>
  <br>
  <br>
</div>

## Demo and Documentation

➡️ **[View the latest demo & docs here](https://atomiks.github.io/tippyjs/)**

[Migration Guide](https://github.com/atomiks/tippyjs/blob/master/MIGRATION_GUIDE.md)

## Installation

### Package Managers

```bash
# npm
npm i tippy.js

# Yarn
yarn add tippy.js
```

Import the `tippy` constructor and the core CSS:

```js
import tippy from 'tippy.js';
import 'tippy.js/dist/tippy.css';
```

### CDN

```html
<script src="https://unpkg.com/@popperjs/core@2"></script>
<script src="https://unpkg.com/tippy.js@6"></script>
```

The core CSS comes bundled with the default unpkg import.

## Usage

For detailed usage information,
[visit the docs](https://atomiks.github.io/tippyjs/v6/getting-started/).

## Component Wrappers

React: [@tippyjs/react](https://github.com/atomiks/tippyjs-react)

## License

MIT
