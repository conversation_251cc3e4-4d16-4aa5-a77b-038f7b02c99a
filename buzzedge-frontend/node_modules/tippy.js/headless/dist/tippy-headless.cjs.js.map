{"version": 3, "file": "tippy-headless.cjs.js", "sources": ["../../src/constants.ts", "../../src/utils.ts", "../../src/dom-utils.ts", "../../src/bindGlobalEventListeners.ts", "../../src/browser.ts", "../../src/validation.ts", "../../src/props.ts", "../../src/template.ts", "../../src/createTippy.ts", "../../src/index.ts", "../../src/addons/createSingleton.ts", "../../src/addons/delegate.ts", "../../src/plugins/animateFill.ts", "../../src/plugins/followCursor.ts", "../../src/plugins/inlinePositioning.ts", "../../src/plugins/sticky.ts", "../../build/headless.js"], "sourcesContent": ["export const ROUND_ARROW =\n  '<svg width=\"16\" height=\"6\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M0 6s1.796-.013 4.67-3.615C5.851.9 6.93.006 8 0c1.07-.006 2.148.887 3.343 2.385C14.233 6.005 16 6 16 6H0z\"></svg>';\n\nexport const BOX_CLASS = `__NAMESPACE_PREFIX__-box`;\nexport const CONTENT_CLASS = `__NAMESPACE_PREFIX__-content`;\nexport const BACKDROP_CLASS = `__NAMESPACE_PREFIX__-backdrop`;\nexport const ARROW_CLASS = `__NAMESPACE_PREFIX__-arrow`;\nexport const SVG_ARROW_CLASS = `__NAMESPACE_PREFIX__-svg-arrow`;\n\nexport const TOUCH_OPTIONS = {passive: true, capture: true};\n\nexport const TIPPY_DEFAULT_APPEND_TO = () => document.body;\n", "import {BasePlacement, Placement} from './types';\n\nexport function hasOwnProperty(\n  obj: Record<string, unknown>,\n  key: string\n): boolean {\n  return {}.hasOwnProperty.call(obj, key);\n}\n\nexport function getValueAtIndexOrReturn<T>(\n  value: T | [T | null, T | null],\n  index: number,\n  defaultValue: T | [T, T]\n): T {\n  if (Array.isArray(value)) {\n    const v = value[index];\n    return v == null\n      ? Array.isArray(defaultValue)\n        ? defaultValue[index]\n        : defaultValue\n      : v;\n  }\n\n  return value;\n}\n\nexport function isType(value: any, type: string): boolean {\n  const str = {}.toString.call(value);\n  return str.indexOf('[object') === 0 && str.indexOf(`${type}]`) > -1;\n}\n\nexport function invokeWithArgsOrReturn(value: any, args: any[]): any {\n  return typeof value === 'function' ? value(...args) : value;\n}\n\nexport function debounce<T>(\n  fn: (arg: T) => void,\n  ms: number\n): (arg: T) => void {\n  // Avoid wrapping in `setTimeout` if ms is 0 anyway\n  if (ms === 0) {\n    return fn;\n  }\n\n  let timeout: any;\n\n  return (arg): void => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => {\n      fn(arg);\n    }, ms);\n  };\n}\n\nexport function removeProperties<T>(obj: T, keys: string[]): Partial<T> {\n  const clone = {...obj};\n  keys.forEach((key) => {\n    delete (clone as any)[key];\n  });\n  return clone;\n}\n\nexport function splitBySpaces(value: string): string[] {\n  return value.split(/\\s+/).filter(Boolean);\n}\n\nexport function normalizeToArray<T>(value: T | T[]): T[] {\n  return ([] as T[]).concat(value);\n}\n\nexport function pushIfUnique<T>(arr: T[], value: T): void {\n  if (arr.indexOf(value) === -1) {\n    arr.push(value);\n  }\n}\n\nexport function appendPxIfNumber(value: string | number): string {\n  return typeof value === 'number' ? `${value}px` : value;\n}\n\nexport function unique<T>(arr: T[]): T[] {\n  return arr.filter((item, index) => arr.indexOf(item) === index);\n}\n\nexport function getNumber(value: string | number): number {\n  return typeof value === 'number' ? value : parseFloat(value);\n}\n\nexport function getBasePlacement(placement: Placement): BasePlacement {\n  return placement.split('-')[0] as BasePlacement;\n}\n\nexport function arrayFrom(value: ArrayLike<any>): any[] {\n  return [].slice.call(value);\n}\n\nexport function removeUndefinedProps(\n  obj: Record<string, unknown>\n): Partial<Record<string, unknown>> {\n  return Object.keys(obj).reduce((acc, key) => {\n    if (obj[key] !== undefined) {\n      (acc as any)[key] = obj[key];\n    }\n\n    return acc;\n  }, {});\n}\n", "import {ReferenceElement, Targets} from './types';\nimport {PopperTreeData} from './types-internal';\nimport {arrayFrom, isType, normalizeToArray, getBasePlacement} from './utils';\n\nexport function div(): HTMLDivElement {\n  return document.createElement('div');\n}\n\nexport function isElement(value: unknown): value is Element | DocumentFragment {\n  return ['Element', 'Fragment'].some((type) => isType(value, type));\n}\n\nexport function isNodeList(value: unknown): value is NodeList {\n  return isType(value, 'NodeList');\n}\n\nexport function isMouseEvent(value: unknown): value is MouseEvent {\n  return isType(value, 'MouseEvent');\n}\n\nexport function isReferenceElement(value: any): value is ReferenceElement {\n  return !!(value && value._tippy && value._tippy.reference === value);\n}\n\nexport function getArrayOfElements(value: Targets): Element[] {\n  if (isElement(value)) {\n    return [value];\n  }\n\n  if (isNodeList(value)) {\n    return arrayFrom(value);\n  }\n\n  if (Array.isArray(value)) {\n    return value;\n  }\n\n  return arrayFrom(document.querySelectorAll(value));\n}\n\nexport function setTransitionDuration(\n  els: (HTMLDivElement | null)[],\n  value: number\n): void {\n  els.forEach((el) => {\n    if (el) {\n      el.style.transitionDuration = `${value}ms`;\n    }\n  });\n}\n\nexport function setVisibilityState(\n  els: (HTMLDivElement | null)[],\n  state: 'visible' | 'hidden'\n): void {\n  els.forEach((el) => {\n    if (el) {\n      el.setAttribute('data-state', state);\n    }\n  });\n}\n\nexport function getOwnerDocument(\n  elementOrElements: Element | Element[]\n): Document {\n  const [element] = normalizeToArray(elementOrElements);\n\n  // Elements created via a <template> have an ownerDocument with no reference to the body\n  return element?.ownerDocument?.body ? element.ownerDocument : document;\n}\n\nexport function isCursorOutsideInteractiveBorder(\n  popperTreeData: PopperTreeData[],\n  event: MouseEvent\n): boolean {\n  const {clientX, clientY} = event;\n\n  return popperTreeData.every(({popperRect, popperState, props}) => {\n    const {interactiveBorder} = props;\n    const basePlacement = getBasePlacement(popperState.placement);\n    const offsetData = popperState.modifiersData.offset;\n\n    if (!offsetData) {\n      return true;\n    }\n\n    const topDistance = basePlacement === 'bottom' ? offsetData.top!.y : 0;\n    const bottomDistance = basePlacement === 'top' ? offsetData.bottom!.y : 0;\n    const leftDistance = basePlacement === 'right' ? offsetData.left!.x : 0;\n    const rightDistance = basePlacement === 'left' ? offsetData.right!.x : 0;\n\n    const exceedsTop =\n      popperRect.top - clientY + topDistance > interactiveBorder;\n    const exceedsBottom =\n      clientY - popperRect.bottom - bottomDistance > interactiveBorder;\n    const exceedsLeft =\n      popperRect.left - clientX + leftDistance > interactiveBorder;\n    const exceedsRight =\n      clientX - popperRect.right - rightDistance > interactiveBorder;\n\n    return exceedsTop || exceedsBottom || exceedsLeft || exceedsRight;\n  });\n}\n\nexport function updateTransitionEndListener(\n  box: HTMLDivElement,\n  action: 'add' | 'remove',\n  listener: (event: TransitionEvent) => void\n): void {\n  const method = `${action}EventListener` as\n    | 'addEventListener'\n    | 'removeEventListener';\n\n  // some browsers apparently support `transition` (unprefixed) but only fire\n  // `webkitTransitionEnd`...\n  ['transitionend', 'webkitTransitionEnd'].forEach((event) => {\n    box[method](event, listener as EventListener);\n  });\n}\n\n/**\n * Compared to xxx.contains, this function works for dom structures with shadow\n * dom\n */\nexport function actualContains(parent: Element, child: Element): boolean {\n  let target = child;\n  while (target) {\n    if (parent.contains(target)) {\n      return true;\n    }\n    target = (target.getRootNode?.() as any)?.host;\n  }\n  return false;\n}\n", "import {TOUCH_OPTIONS} from './constants';\nimport {isReferenceElement} from './dom-utils';\n\nexport const currentInput = {isTouch: false};\nlet lastMouseMoveTime = 0;\n\n/**\n * When a `touchstart` event is fired, it's assumed the user is using touch\n * input. We'll bind a `mousemove` event listener to listen for mouse input in\n * the future. This way, the `isTouch` property is fully dynamic and will handle\n * hybrid devices that use a mix of touch + mouse input.\n */\nexport function onDocumentTouchStart(): void {\n  if (currentInput.isTouch) {\n    return;\n  }\n\n  currentInput.isTouch = true;\n\n  if (window.performance) {\n    document.addEventListener('mousemove', onDocumentMouseMove);\n  }\n}\n\n/**\n * When two `mousemove` event are fired consecutively within 20ms, it's assumed\n * the user is using mouse input again. `mousemove` can fire on touch devices as\n * well, but very rarely that quickly.\n */\nexport function onDocumentMouseMove(): void {\n  const now = performance.now();\n\n  if (now - lastMouseMoveTime < 20) {\n    currentInput.isTouch = false;\n\n    document.removeEventListener('mousemove', onDocumentMouseMove);\n  }\n\n  lastMouseMoveTime = now;\n}\n\n/**\n * When an element is in focus and has a tippy, leaving the tab/window and\n * returning causes it to show again. For mouse users this is unexpected, but\n * for keyboard use it makes sense.\n * TODO: find a better technique to solve this problem\n */\nexport function onWindowBlur(): void {\n  const activeElement = document.activeElement as HTMLElement | null;\n\n  if (isReferenceElement(activeElement)) {\n    const instance = activeElement._tippy!;\n\n    if (activeElement.blur && !instance.state.isVisible) {\n      activeElement.blur();\n    }\n  }\n}\n\nexport default function bindGlobalEventListeners(): void {\n  document.addEventListener('touchstart', onDocumentTouchStart, TOUCH_OPTIONS);\n  window.addEventListener('blur', onWindowBlur);\n}\n", "export const isBrowser =\n  typeof window !== 'undefined' && typeof document !== 'undefined';\n\nexport const isIE11 = isBrowser\n  ? // @ts-ignore\n    !!window.msCrypto\n  : false;\n", "import {Targets} from './types';\n\nexport function createMemoryLeakWarning(method: string): string {\n  const txt = method === 'destroy' ? 'n already-' : ' ';\n\n  return [\n    `${method}() was called on a${txt}destroyed instance. This is a no-op but`,\n    'indicates a potential memory leak.',\n  ].join(' ');\n}\n\nexport function clean(value: string): string {\n  const spacesAndTabs = /[ \\t]{2,}/g;\n  const lineStartWithSpaces = /^[ \\t]*/gm;\n\n  return value\n    .replace(spacesAndTabs, ' ')\n    .replace(lineStartWithSpaces, '')\n    .trim();\n}\n\nfunction getDevMessage(message: string): string {\n  return clean(`\n  %ctippy.js\n\n  %c${clean(message)}\n\n  %c👷‍ This is a development-only message. It will be removed in production.\n  `);\n}\n\nexport function getFormattedMessage(message: string): string[] {\n  return [\n    getDevMessage(message),\n    // title\n    'color: #00C584; font-size: 1.3em; font-weight: bold;',\n    // message\n    'line-height: 1.5',\n    // footer\n    'color: #a6a095;',\n  ];\n}\n\n// Assume warnings and errors never have the same message\nlet visitedMessages: Set<string>;\nif (__DEV__) {\n  resetVisitedMessages();\n}\n\nexport function resetVisitedMessages(): void {\n  visitedMessages = new Set();\n}\n\nexport function warnWhen(condition: boolean, message: string): void {\n  if (condition && !visitedMessages.has(message)) {\n    visitedMessages.add(message);\n    console.warn(...getFormattedMessage(message));\n  }\n}\n\nexport function errorWhen(condition: boolean, message: string): void {\n  if (condition && !visitedMessages.has(message)) {\n    visitedMessages.add(message);\n    console.error(...getFormattedMessage(message));\n  }\n}\n\nexport function validateTargets(targets: Targets): void {\n  const didPassFalsyValue = !targets;\n  const didPassPlainObject =\n    Object.prototype.toString.call(targets) === '[object Object]' &&\n    !(targets as any).addEventListener;\n\n  errorWhen(\n    didPassFalsyValue,\n    [\n      'tippy() was passed',\n      '`' + String(targets) + '`',\n      'as its targets (first) argument. Valid types are: String, Element,',\n      'Element[], or NodeList.',\n    ].join(' ')\n  );\n\n  errorWhen(\n    didPassPlainObject,\n    [\n      'tippy() was passed a plain object which is not supported as an argument',\n      'for virtual positioning. Use props.getReferenceClientRect instead.',\n    ].join(' ')\n  );\n}\n", "import {DefaultProps, Plugin, Props, ReferenceElement, Tippy} from './types';\nimport {\n  hasOwnProperty,\n  removeProperties,\n  invokeWithArgsOrReturn,\n} from './utils';\nimport {warnWhen} from './validation';\nimport {TIPPY_DEFAULT_APPEND_TO} from './constants';\n\nconst pluginProps = {\n  animateFill: false,\n  followCursor: false,\n  inlinePositioning: false,\n  sticky: false,\n};\n\nconst renderProps = {\n  allowHTML: false,\n  animation: 'fade',\n  arrow: true,\n  content: '',\n  inertia: false,\n  maxWidth: 350,\n  role: 'tooltip',\n  theme: '',\n  zIndex: 9999,\n};\n\nexport const defaultProps: DefaultProps = {\n  appendTo: TIPPY_DEFAULT_APPEND_TO,\n  aria: {\n    content: 'auto',\n    expanded: 'auto',\n  },\n  delay: 0,\n  duration: [300, 250],\n  getReferenceClientRect: null,\n  hideOnClick: true,\n  ignoreAttributes: false,\n  interactive: false,\n  interactiveBorder: 2,\n  interactiveDebounce: 0,\n  moveTransition: '',\n  offset: [0, 10],\n  onAfterUpdate() {},\n  onBeforeUpdate() {},\n  onCreate() {},\n  onDestroy() {},\n  onHidden() {},\n  onHide() {},\n  onMount() {},\n  onShow() {},\n  onShown() {},\n  onTrigger() {},\n  onUntrigger() {},\n  onClickOutside() {},\n  placement: 'top',\n  plugins: [],\n  popperOptions: {},\n  render: null,\n  showOnCreate: false,\n  touch: true,\n  trigger: 'mouseenter focus',\n  triggerTarget: null,\n  ...pluginProps,\n  ...renderProps,\n};\n\nconst defaultKeys = Object.keys(defaultProps);\n\nexport const setDefaultProps: Tippy['setDefaultProps'] = (partialProps) => {\n  /* istanbul ignore else */\n  if (__DEV__) {\n    validateProps(partialProps, []);\n  }\n\n  const keys = Object.keys(partialProps) as Array<keyof DefaultProps>;\n  keys.forEach((key) => {\n    (defaultProps as any)[key] = partialProps[key];\n  });\n};\n\nexport function getExtendedPassedProps(\n  passedProps: Partial<Props> & Record<string, unknown>\n): Partial<Props> {\n  const plugins = passedProps.plugins || [];\n  const pluginProps = plugins.reduce<Record<string, unknown>>((acc, plugin) => {\n    const {name, defaultValue} = plugin;\n\n    if (name) {\n      acc[name] =\n        passedProps[name] !== undefined\n          ? passedProps[name]\n          : (defaultProps as any)[name] ?? defaultValue;\n    }\n\n    return acc;\n  }, {});\n\n  return {\n    ...passedProps,\n    ...pluginProps,\n  };\n}\n\nexport function getDataAttributeProps(\n  reference: ReferenceElement,\n  plugins: Plugin[]\n): Record<string, unknown> {\n  const propKeys = plugins\n    ? Object.keys(getExtendedPassedProps({...defaultProps, plugins}))\n    : defaultKeys;\n\n  const props = propKeys.reduce(\n    (acc: Partial<Props> & Record<string, unknown>, key) => {\n      const valueAsString = (\n        reference.getAttribute(`data-tippy-${key}`) || ''\n      ).trim();\n\n      if (!valueAsString) {\n        return acc;\n      }\n\n      if (key === 'content') {\n        acc[key] = valueAsString;\n      } else {\n        try {\n          acc[key] = JSON.parse(valueAsString);\n        } catch (e) {\n          acc[key] = valueAsString;\n        }\n      }\n\n      return acc;\n    },\n    {}\n  );\n\n  return props;\n}\n\nexport function evaluateProps(\n  reference: ReferenceElement,\n  props: Props\n): Props {\n  const out = {\n    ...props,\n    content: invokeWithArgsOrReturn(props.content, [reference]),\n    ...(props.ignoreAttributes\n      ? {}\n      : getDataAttributeProps(reference, props.plugins)),\n  };\n\n  out.aria = {\n    ...defaultProps.aria,\n    ...out.aria,\n  };\n\n  out.aria = {\n    expanded:\n      out.aria.expanded === 'auto' ? props.interactive : out.aria.expanded,\n    content:\n      out.aria.content === 'auto'\n        ? props.interactive\n          ? null\n          : 'describedby'\n        : out.aria.content,\n  };\n\n  return out;\n}\n\nexport function validateProps(\n  partialProps: Partial<Props> = {},\n  plugins: Plugin[] = []\n): void {\n  const keys = Object.keys(partialProps) as Array<keyof Props>;\n  keys.forEach((prop) => {\n    const nonPluginProps = removeProperties(\n      defaultProps,\n      Object.keys(pluginProps)\n    );\n\n    let didPassUnknownProp = !hasOwnProperty(nonPluginProps, prop);\n\n    // Check if the prop exists in `plugins`\n    if (didPassUnknownProp) {\n      didPassUnknownProp =\n        plugins.filter((plugin) => plugin.name === prop).length === 0;\n    }\n\n    warnWhen(\n      didPassUnknownProp,\n      [\n        `\\`${prop}\\``,\n        \"is not a valid prop. You may have spelled it incorrectly, or if it's\",\n        'a plugin, forgot to pass it in an array as props.plugins.',\n        '\\n\\n',\n        'All props: https://atomiks.github.io/tippyjs/v6/all-props/\\n',\n        'Plugins: https://atomiks.github.io/tippyjs/v6/plugins/',\n      ].join(' ')\n    );\n  });\n}\n", "import {\n  ARROW_CLASS,\n  BACKDROP_CLASS,\n  BOX_CLASS,\n  CONTENT_CLASS,\n  SVG_ARROW_CLASS,\n} from './constants';\nimport {div, isElement} from './dom-utils';\nimport {Instance, PopperElement, Props} from './types';\nimport {PopperChildren} from './types-internal';\nimport {arrayFrom} from './utils';\n\n// Firefox extensions don't allow .innerHTML = \"...\" property. This tricks it.\nconst innerHTML = (): 'innerHTML' => 'innerHTML';\n\nfunction dangerouslySetInnerHTML(element: Element, html: string): void {\n  element[innerHTML()] = html;\n}\n\nfunction createArrowElement(value: Props['arrow']): HTMLDivElement {\n  const arrow = div();\n\n  if (value === true) {\n    arrow.className = ARROW_CLASS;\n  } else {\n    arrow.className = SVG_ARROW_CLASS;\n\n    if (isElement(value)) {\n      arrow.appendChild(value);\n    } else {\n      dangerouslySetInnerHTML(arrow, value as string);\n    }\n  }\n\n  return arrow;\n}\n\nexport function setContent(content: HTMLDivElement, props: Props): void {\n  if (isElement(props.content)) {\n    dangerouslySetInnerHTML(content, '');\n    content.appendChild(props.content);\n  } else if (typeof props.content !== 'function') {\n    if (props.allowHTML) {\n      dangerouslySetInnerHTML(content, props.content);\n    } else {\n      content.textContent = props.content;\n    }\n  }\n}\n\nexport function getChildren(popper: PopperElement): PopperChildren {\n  const box = popper.firstElementChild as HTMLDivElement;\n  const boxChildren = arrayFrom(box.children);\n\n  return {\n    box,\n    content: boxChildren.find((node) => node.classList.contains(CONTENT_CLASS)),\n    arrow: boxChildren.find(\n      (node) =>\n        node.classList.contains(ARROW_CLASS) ||\n        node.classList.contains(SVG_ARROW_CLASS)\n    ),\n    backdrop: boxChildren.find((node) =>\n      node.classList.contains(BACKDROP_CLASS)\n    ),\n  };\n}\n\nexport function render(\n  instance: Instance\n): {\n  popper: PopperElement;\n  onUpdate?: (prevProps: Props, nextProps: Props) => void;\n} {\n  const popper = div();\n\n  const box = div();\n  box.className = BOX_CLASS;\n  box.setAttribute('data-state', 'hidden');\n  box.setAttribute('tabindex', '-1');\n\n  const content = div();\n  content.className = CONTENT_CLASS;\n  content.setAttribute('data-state', 'hidden');\n\n  setContent(content, instance.props);\n\n  popper.appendChild(box);\n  box.appendChild(content);\n\n  onUpdate(instance.props, instance.props);\n\n  function onUpdate(prevProps: Props, nextProps: Props): void {\n    const {box, content, arrow} = getChildren(popper);\n\n    if (nextProps.theme) {\n      box.setAttribute('data-theme', nextProps.theme);\n    } else {\n      box.removeAttribute('data-theme');\n    }\n\n    if (typeof nextProps.animation === 'string') {\n      box.setAttribute('data-animation', nextProps.animation);\n    } else {\n      box.removeAttribute('data-animation');\n    }\n\n    if (nextProps.inertia) {\n      box.setAttribute('data-inertia', '');\n    } else {\n      box.removeAttribute('data-inertia');\n    }\n\n    box.style.maxWidth =\n      typeof nextProps.maxWidth === 'number'\n        ? `${nextProps.maxWidth}px`\n        : nextProps.maxWidth;\n\n    if (nextProps.role) {\n      box.setAttribute('role', nextProps.role);\n    } else {\n      box.removeAttribute('role');\n    }\n\n    if (\n      prevProps.content !== nextProps.content ||\n      prevProps.allowHTML !== nextProps.allowHTML\n    ) {\n      setContent(content, instance.props);\n    }\n\n    if (nextProps.arrow) {\n      if (!arrow) {\n        box.appendChild(createArrowElement(nextProps.arrow));\n      } else if (prevProps.arrow !== nextProps.arrow) {\n        box.removeChild(arrow);\n        box.appendChild(createArrowElement(nextProps.arrow));\n      }\n    } else if (arrow) {\n      box.removeChild(arrow!);\n    }\n  }\n\n  return {\n    popper,\n    onUpdate,\n  };\n}\n\n// Runtime check to identify if the render function is the default one; this\n// way we can apply default CSS transitions logic and it can be tree-shaken away\nrender.$$tippy = true;\n", "import {createPopper, StrictModifiers, Modifier} from '@popperjs/core';\nimport {currentInput} from './bindGlobalEventListeners';\nimport {isIE11} from './browser';\nimport {TIPPY_DEFAULT_APPEND_TO, TOUCH_OPTIONS} from './constants';\nimport {\n  actualContains,\n  div,\n  getOwnerDocument,\n  isCursorOutsideInteractiveBorder,\n  isMouseEvent,\n  setTransitionDuration,\n  setVisibilityState,\n  updateTransitionEndListener,\n} from './dom-utils';\nimport {defaultProps, evaluateProps, getExtendedPassedProps} from './props';\nimport {getChildren} from './template';\nimport {\n  Content,\n  Instance,\n  LifecycleHooks,\n  PopperElement,\n  Props,\n  ReferenceElement,\n} from './types';\nimport {ListenerObject, PopperTreeData, PopperChildren} from './types-internal';\nimport {\n  arrayFrom,\n  debounce,\n  getValueAtIndexOrReturn,\n  invokeWithArgsOrReturn,\n  normalizeToArray,\n  pushIfUnique,\n  splitBySpaces,\n  unique,\n  removeUndefinedProps,\n} from './utils';\nimport {createMemoryLeakWarning, errorWhen, warnWhen} from './validation';\n\nlet idCounter = 1;\nlet mouseMoveListeners: ((event: MouseEvent) => void)[] = [];\n\n// Used by `hideAll()`\nexport let mountedInstances: Instance[] = [];\n\nexport default function createTippy(\n  reference: ReferenceElement,\n  passedProps: Partial<Props>\n): Instance {\n  const props = evaluateProps(reference, {\n    ...defaultProps,\n    ...getExtendedPassedProps(removeUndefinedProps(passedProps)),\n  });\n\n  // ===========================================================================\n  // 🔒 Private members\n  // ===========================================================================\n  let showTimeout: any;\n  let hideTimeout: any;\n  let scheduleHideAnimationFrame: number;\n  let isVisibleFromClick = false;\n  let didHideDueToDocumentMouseDown = false;\n  let didTouchMove = false;\n  let ignoreOnFirstUpdate = false;\n  let lastTriggerEvent: Event | undefined;\n  let currentTransitionEndListener: (event: TransitionEvent) => void;\n  let onFirstUpdate: () => void;\n  let listeners: ListenerObject[] = [];\n  let debouncedOnMouseMove = debounce(onMouseMove, props.interactiveDebounce);\n  let currentTarget: Element;\n\n  // ===========================================================================\n  // 🔑 Public members\n  // ===========================================================================\n  const id = idCounter++;\n  const popperInstance = null;\n  const plugins = unique(props.plugins);\n\n  const state = {\n    // Is the instance currently enabled?\n    isEnabled: true,\n    // Is the tippy currently showing and not transitioning out?\n    isVisible: false,\n    // Has the instance been destroyed?\n    isDestroyed: false,\n    // Is the tippy currently mounted to the DOM?\n    isMounted: false,\n    // Has the tippy finished transitioning in?\n    isShown: false,\n  };\n\n  const instance: Instance = {\n    // properties\n    id,\n    reference,\n    popper: div(),\n    popperInstance,\n    props,\n    state,\n    plugins,\n    // methods\n    clearDelayTimeouts,\n    setProps,\n    setContent,\n    show,\n    hide,\n    hideWithInteractivity,\n    enable,\n    disable,\n    unmount,\n    destroy,\n  };\n\n  // TODO: Investigate why this early return causes a TDZ error in the tests —\n  // it doesn't seem to happen in the browser\n  /* istanbul ignore if */\n  if (!props.render) {\n    if (__DEV__) {\n      errorWhen(true, 'render() function has not been supplied.');\n    }\n\n    return instance;\n  }\n\n  // ===========================================================================\n  // Initial mutations\n  // ===========================================================================\n  const {popper, onUpdate} = props.render(instance);\n\n  popper.setAttribute('data-__NAMESPACE_PREFIX__-root', '');\n  popper.id = `__NAMESPACE_PREFIX__-${instance.id}`;\n\n  instance.popper = popper;\n  reference._tippy = instance;\n  popper._tippy = instance;\n\n  const pluginsHooks = plugins.map((plugin) => plugin.fn(instance));\n  const hasAriaExpanded = reference.hasAttribute('aria-expanded');\n\n  addListeners();\n  handleAriaExpandedAttribute();\n  handleStyles();\n\n  invokeHook('onCreate', [instance]);\n\n  if (props.showOnCreate) {\n    scheduleShow();\n  }\n\n  // Prevent a tippy with a delay from hiding if the cursor left then returned\n  // before it started hiding\n  popper.addEventListener('mouseenter', () => {\n    if (instance.props.interactive && instance.state.isVisible) {\n      instance.clearDelayTimeouts();\n    }\n  });\n\n  popper.addEventListener('mouseleave', () => {\n    if (\n      instance.props.interactive &&\n      instance.props.trigger.indexOf('mouseenter') >= 0\n    ) {\n      getDocument().addEventListener('mousemove', debouncedOnMouseMove);\n    }\n  });\n\n  return instance;\n\n  // ===========================================================================\n  // 🔒 Private methods\n  // ===========================================================================\n  function getNormalizedTouchSettings(): [string | boolean, number] {\n    const {touch} = instance.props;\n    return Array.isArray(touch) ? touch : [touch, 0];\n  }\n\n  function getIsCustomTouchBehavior(): boolean {\n    return getNormalizedTouchSettings()[0] === 'hold';\n  }\n\n  function getIsDefaultRenderFn(): boolean {\n    // @ts-ignore\n    return !!instance.props.render?.$$tippy;\n  }\n\n  function getCurrentTarget(): Element {\n    return currentTarget || reference;\n  }\n\n  function getDocument(): Document {\n    const parent = getCurrentTarget().parentNode as Element;\n    return parent ? getOwnerDocument(parent) : document;\n  }\n\n  function getDefaultTemplateChildren(): PopperChildren {\n    return getChildren(popper);\n  }\n\n  function getDelay(isShow: boolean): number {\n    // For touch or keyboard input, force `0` delay for UX reasons\n    // Also if the instance is mounted but not visible (transitioning out),\n    // ignore delay\n    if (\n      (instance.state.isMounted && !instance.state.isVisible) ||\n      currentInput.isTouch ||\n      (lastTriggerEvent && lastTriggerEvent.type === 'focus')\n    ) {\n      return 0;\n    }\n\n    return getValueAtIndexOrReturn(\n      instance.props.delay,\n      isShow ? 0 : 1,\n      defaultProps.delay\n    );\n  }\n\n  function handleStyles(fromHide = false): void {\n    popper.style.pointerEvents =\n      instance.props.interactive && !fromHide ? '' : 'none';\n    popper.style.zIndex = `${instance.props.zIndex}`;\n  }\n\n  function invokeHook(\n    hook: keyof LifecycleHooks,\n    args: [Instance, any?],\n    shouldInvokePropsHook = true\n  ): void {\n    pluginsHooks.forEach((pluginHooks) => {\n      if (pluginHooks[hook]) {\n        pluginHooks[hook]!(...args);\n      }\n    });\n\n    if (shouldInvokePropsHook) {\n      instance.props[hook](...args);\n    }\n  }\n\n  function handleAriaContentAttribute(): void {\n    const {aria} = instance.props;\n\n    if (!aria.content) {\n      return;\n    }\n\n    const attr = `aria-${aria.content}`;\n    const id = popper.id;\n    const nodes = normalizeToArray(instance.props.triggerTarget || reference);\n\n    nodes.forEach((node) => {\n      const currentValue = node.getAttribute(attr);\n\n      if (instance.state.isVisible) {\n        node.setAttribute(attr, currentValue ? `${currentValue} ${id}` : id);\n      } else {\n        const nextValue = currentValue && currentValue.replace(id, '').trim();\n\n        if (nextValue) {\n          node.setAttribute(attr, nextValue);\n        } else {\n          node.removeAttribute(attr);\n        }\n      }\n    });\n  }\n\n  function handleAriaExpandedAttribute(): void {\n    if (hasAriaExpanded || !instance.props.aria.expanded) {\n      return;\n    }\n\n    const nodes = normalizeToArray(instance.props.triggerTarget || reference);\n\n    nodes.forEach((node) => {\n      if (instance.props.interactive) {\n        node.setAttribute(\n          'aria-expanded',\n          instance.state.isVisible && node === getCurrentTarget()\n            ? 'true'\n            : 'false'\n        );\n      } else {\n        node.removeAttribute('aria-expanded');\n      }\n    });\n  }\n\n  function cleanupInteractiveMouseListeners(): void {\n    getDocument().removeEventListener('mousemove', debouncedOnMouseMove);\n    mouseMoveListeners = mouseMoveListeners.filter(\n      (listener) => listener !== debouncedOnMouseMove\n    );\n  }\n\n  function onDocumentPress(event: MouseEvent | TouchEvent): void {\n    // Moved finger to scroll instead of an intentional tap outside\n    if (currentInput.isTouch) {\n      if (didTouchMove || event.type === 'mousedown') {\n        return;\n      }\n    }\n\n    const actualTarget =\n      (event.composedPath && event.composedPath()[0]) || event.target;\n\n    // Clicked on interactive popper\n    if (\n      instance.props.interactive &&\n      actualContains(popper, actualTarget as Element)\n    ) {\n      return;\n    }\n\n    // Clicked on the event listeners target\n    if (\n      normalizeToArray(instance.props.triggerTarget || reference).some((el) =>\n        actualContains(el, actualTarget as Element)\n      )\n    ) {\n      if (currentInput.isTouch) {\n        return;\n      }\n\n      if (\n        instance.state.isVisible &&\n        instance.props.trigger.indexOf('click') >= 0\n      ) {\n        return;\n      }\n    } else {\n      invokeHook('onClickOutside', [instance, event]);\n    }\n\n    if (instance.props.hideOnClick === true) {\n      instance.clearDelayTimeouts();\n      instance.hide();\n\n      // `mousedown` event is fired right before `focus` if pressing the\n      // currentTarget. This lets a tippy with `focus` trigger know that it\n      // should not show\n      didHideDueToDocumentMouseDown = true;\n      setTimeout(() => {\n        didHideDueToDocumentMouseDown = false;\n      });\n\n      // The listener gets added in `scheduleShow()`, but this may be hiding it\n      // before it shows, and hide()'s early bail-out behavior can prevent it\n      // from being cleaned up\n      if (!instance.state.isMounted) {\n        removeDocumentPress();\n      }\n    }\n  }\n\n  function onTouchMove(): void {\n    didTouchMove = true;\n  }\n\n  function onTouchStart(): void {\n    didTouchMove = false;\n  }\n\n  function addDocumentPress(): void {\n    const doc = getDocument();\n    doc.addEventListener('mousedown', onDocumentPress, true);\n    doc.addEventListener('touchend', onDocumentPress, TOUCH_OPTIONS);\n    doc.addEventListener('touchstart', onTouchStart, TOUCH_OPTIONS);\n    doc.addEventListener('touchmove', onTouchMove, TOUCH_OPTIONS);\n  }\n\n  function removeDocumentPress(): void {\n    const doc = getDocument();\n    doc.removeEventListener('mousedown', onDocumentPress, true);\n    doc.removeEventListener('touchend', onDocumentPress, TOUCH_OPTIONS);\n    doc.removeEventListener('touchstart', onTouchStart, TOUCH_OPTIONS);\n    doc.removeEventListener('touchmove', onTouchMove, TOUCH_OPTIONS);\n  }\n\n  function onTransitionedOut(duration: number, callback: () => void): void {\n    onTransitionEnd(duration, () => {\n      if (\n        !instance.state.isVisible &&\n        popper.parentNode &&\n        popper.parentNode.contains(popper)\n      ) {\n        callback();\n      }\n    });\n  }\n\n  function onTransitionedIn(duration: number, callback: () => void): void {\n    onTransitionEnd(duration, callback);\n  }\n\n  function onTransitionEnd(duration: number, callback: () => void): void {\n    const box = getDefaultTemplateChildren().box;\n\n    function listener(event: TransitionEvent): void {\n      if (event.target === box) {\n        updateTransitionEndListener(box, 'remove', listener);\n        callback();\n      }\n    }\n\n    // Make callback synchronous if duration is 0\n    // `transitionend` won't fire otherwise\n    if (duration === 0) {\n      return callback();\n    }\n\n    updateTransitionEndListener(box, 'remove', currentTransitionEndListener);\n    updateTransitionEndListener(box, 'add', listener);\n\n    currentTransitionEndListener = listener;\n  }\n\n  function on(\n    eventType: string,\n    handler: EventListener,\n    options: boolean | Record<string, unknown> = false\n  ): void {\n    const nodes = normalizeToArray(instance.props.triggerTarget || reference);\n    nodes.forEach((node) => {\n      node.addEventListener(eventType, handler, options);\n      listeners.push({node, eventType, handler, options});\n    });\n  }\n\n  function addListeners(): void {\n    if (getIsCustomTouchBehavior()) {\n      on('touchstart', onTrigger, {passive: true});\n      on('touchend', onMouseLeave as EventListener, {passive: true});\n    }\n\n    splitBySpaces(instance.props.trigger).forEach((eventType) => {\n      if (eventType === 'manual') {\n        return;\n      }\n\n      on(eventType, onTrigger);\n\n      switch (eventType) {\n        case 'mouseenter':\n          on('mouseleave', onMouseLeave as EventListener);\n          break;\n        case 'focus':\n          on(isIE11 ? 'focusout' : 'blur', onBlurOrFocusOut as EventListener);\n          break;\n        case 'focusin':\n          on('focusout', onBlurOrFocusOut as EventListener);\n          break;\n      }\n    });\n  }\n\n  function removeListeners(): void {\n    listeners.forEach(({node, eventType, handler, options}: ListenerObject) => {\n      node.removeEventListener(eventType, handler, options);\n    });\n    listeners = [];\n  }\n\n  function onTrigger(event: Event): void {\n    let shouldScheduleClickHide = false;\n\n    if (\n      !instance.state.isEnabled ||\n      isEventListenerStopped(event) ||\n      didHideDueToDocumentMouseDown\n    ) {\n      return;\n    }\n\n    const wasFocused = lastTriggerEvent?.type === 'focus';\n\n    lastTriggerEvent = event;\n    currentTarget = event.currentTarget as Element;\n\n    handleAriaExpandedAttribute();\n\n    if (!instance.state.isVisible && isMouseEvent(event)) {\n      // If scrolling, `mouseenter` events can be fired if the cursor lands\n      // over a new target, but `mousemove` events don't get fired. This\n      // causes interactive tooltips to get stuck open until the cursor is\n      // moved\n      mouseMoveListeners.forEach((listener) => listener(event));\n    }\n\n    // Toggle show/hide when clicking click-triggered tooltips\n    if (\n      event.type === 'click' &&\n      (instance.props.trigger.indexOf('mouseenter') < 0 ||\n        isVisibleFromClick) &&\n      instance.props.hideOnClick !== false &&\n      instance.state.isVisible\n    ) {\n      shouldScheduleClickHide = true;\n    } else {\n      scheduleShow(event);\n    }\n\n    if (event.type === 'click') {\n      isVisibleFromClick = !shouldScheduleClickHide;\n    }\n\n    if (shouldScheduleClickHide && !wasFocused) {\n      scheduleHide(event);\n    }\n  }\n\n  function onMouseMove(event: MouseEvent): void {\n    const target = event.target as Node;\n    const isCursorOverReferenceOrPopper =\n      getCurrentTarget().contains(target) || popper.contains(target);\n\n    if (event.type === 'mousemove' && isCursorOverReferenceOrPopper) {\n      return;\n    }\n\n    const popperTreeData = getNestedPopperTree()\n      .concat(popper)\n      .map((popper) => {\n        const instance = popper._tippy!;\n        const state = instance.popperInstance?.state;\n\n        if (state) {\n          return {\n            popperRect: popper.getBoundingClientRect(),\n            popperState: state,\n            props,\n          };\n        }\n\n        return null;\n      })\n      .filter(Boolean) as PopperTreeData[];\n\n    if (isCursorOutsideInteractiveBorder(popperTreeData, event)) {\n      cleanupInteractiveMouseListeners();\n      scheduleHide(event);\n    }\n  }\n\n  function onMouseLeave(event: MouseEvent): void {\n    const shouldBail =\n      isEventListenerStopped(event) ||\n      (instance.props.trigger.indexOf('click') >= 0 && isVisibleFromClick);\n\n    if (shouldBail) {\n      return;\n    }\n\n    if (instance.props.interactive) {\n      instance.hideWithInteractivity(event);\n      return;\n    }\n\n    scheduleHide(event);\n  }\n\n  function onBlurOrFocusOut(event: FocusEvent): void {\n    if (\n      instance.props.trigger.indexOf('focusin') < 0 &&\n      event.target !== getCurrentTarget()\n    ) {\n      return;\n    }\n\n    // If focus was moved to within the popper\n    if (\n      instance.props.interactive &&\n      event.relatedTarget &&\n      popper.contains(event.relatedTarget as Element)\n    ) {\n      return;\n    }\n\n    scheduleHide(event);\n  }\n\n  function isEventListenerStopped(event: Event): boolean {\n    return currentInput.isTouch\n      ? getIsCustomTouchBehavior() !== event.type.indexOf('touch') >= 0\n      : false;\n  }\n\n  function createPopperInstance(): void {\n    destroyPopperInstance();\n\n    const {\n      popperOptions,\n      placement,\n      offset,\n      getReferenceClientRect,\n      moveTransition,\n    } = instance.props;\n\n    const arrow = getIsDefaultRenderFn() ? getChildren(popper).arrow : null;\n\n    const computedReference = getReferenceClientRect\n      ? {\n          getBoundingClientRect: getReferenceClientRect,\n          contextElement:\n            getReferenceClientRect.contextElement || getCurrentTarget(),\n        }\n      : reference;\n\n    const tippyModifier: Modifier<'$$tippy', Record<string, unknown>> = {\n      name: '$$tippy',\n      enabled: true,\n      phase: 'beforeWrite',\n      requires: ['computeStyles'],\n      fn({state}) {\n        if (getIsDefaultRenderFn()) {\n          const {box} = getDefaultTemplateChildren();\n\n          ['placement', 'reference-hidden', 'escaped'].forEach((attr) => {\n            if (attr === 'placement') {\n              box.setAttribute('data-placement', state.placement);\n            } else {\n              if (state.attributes.popper[`data-popper-${attr}`]) {\n                box.setAttribute(`data-${attr}`, '');\n              } else {\n                box.removeAttribute(`data-${attr}`);\n              }\n            }\n          });\n\n          state.attributes.popper = {};\n        }\n      },\n    };\n\n    type TippyModifier = Modifier<'$$tippy', Record<string, unknown>>;\n    type ExtendedModifiers = StrictModifiers | Partial<TippyModifier>;\n\n    const modifiers: Array<ExtendedModifiers> = [\n      {\n        name: 'offset',\n        options: {\n          offset,\n        },\n      },\n      {\n        name: 'preventOverflow',\n        options: {\n          padding: {\n            top: 2,\n            bottom: 2,\n            left: 5,\n            right: 5,\n          },\n        },\n      },\n      {\n        name: 'flip',\n        options: {\n          padding: 5,\n        },\n      },\n      {\n        name: 'computeStyles',\n        options: {\n          adaptive: !moveTransition,\n        },\n      },\n      tippyModifier,\n    ];\n\n    if (getIsDefaultRenderFn() && arrow) {\n      modifiers.push({\n        name: 'arrow',\n        options: {\n          element: arrow,\n          padding: 3,\n        },\n      });\n    }\n\n    modifiers.push(...(popperOptions?.modifiers || []));\n\n    instance.popperInstance = createPopper<ExtendedModifiers>(\n      computedReference,\n      popper,\n      {\n        ...popperOptions,\n        placement,\n        onFirstUpdate,\n        modifiers,\n      }\n    );\n  }\n\n  function destroyPopperInstance(): void {\n    if (instance.popperInstance) {\n      instance.popperInstance.destroy();\n      instance.popperInstance = null;\n    }\n  }\n\n  function mount(): void {\n    const {appendTo} = instance.props;\n\n    let parentNode: any;\n\n    // By default, we'll append the popper to the triggerTargets's parentNode so\n    // it's directly after the reference element so the elements inside the\n    // tippy can be tabbed to\n    // If there are clipping issues, the user can specify a different appendTo\n    // and ensure focus management is handled correctly manually\n    const node = getCurrentTarget();\n\n    if (\n      (instance.props.interactive && appendTo === TIPPY_DEFAULT_APPEND_TO) ||\n      appendTo === 'parent'\n    ) {\n      parentNode = node.parentNode;\n    } else {\n      parentNode = invokeWithArgsOrReturn(appendTo, [node]);\n    }\n\n    // The popper element needs to exist on the DOM before its position can be\n    // updated as Popper needs to read its dimensions\n    if (!parentNode.contains(popper)) {\n      parentNode.appendChild(popper);\n    }\n\n    instance.state.isMounted = true;\n\n    createPopperInstance();\n\n    /* istanbul ignore else */\n    if (__DEV__) {\n      // Accessibility check\n      warnWhen(\n        instance.props.interactive &&\n          appendTo === defaultProps.appendTo &&\n          node.nextElementSibling !== popper,\n        [\n          'Interactive tippy element may not be accessible via keyboard',\n          'navigation because it is not directly after the reference element',\n          'in the DOM source order.',\n          '\\n\\n',\n          'Using a wrapper <div> or <span> tag around the reference element',\n          'solves this by creating a new parentNode context.',\n          '\\n\\n',\n          'Specifying `appendTo: document.body` silences this warning, but it',\n          'assumes you are using a focus management solution to handle',\n          'keyboard navigation.',\n          '\\n\\n',\n          'See: https://atomiks.github.io/tippyjs/v6/accessibility/#interactivity',\n        ].join(' ')\n      );\n    }\n  }\n\n  function getNestedPopperTree(): PopperElement[] {\n    return arrayFrom(\n      popper.querySelectorAll('[data-__NAMESPACE_PREFIX__-root]')\n    );\n  }\n\n  function scheduleShow(event?: Event): void {\n    instance.clearDelayTimeouts();\n\n    if (event) {\n      invokeHook('onTrigger', [instance, event]);\n    }\n\n    addDocumentPress();\n\n    let delay = getDelay(true);\n    const [touchValue, touchDelay] = getNormalizedTouchSettings();\n\n    if (currentInput.isTouch && touchValue === 'hold' && touchDelay) {\n      delay = touchDelay;\n    }\n\n    if (delay) {\n      showTimeout = setTimeout(() => {\n        instance.show();\n      }, delay);\n    } else {\n      instance.show();\n    }\n  }\n\n  function scheduleHide(event: Event): void {\n    instance.clearDelayTimeouts();\n\n    invokeHook('onUntrigger', [instance, event]);\n\n    if (!instance.state.isVisible) {\n      removeDocumentPress();\n\n      return;\n    }\n\n    // For interactive tippies, scheduleHide is added to a document.body handler\n    // from onMouseLeave so must intercept scheduled hides from mousemove/leave\n    // events when trigger contains mouseenter and click, and the tip is\n    // currently shown as a result of a click.\n    if (\n      instance.props.trigger.indexOf('mouseenter') >= 0 &&\n      instance.props.trigger.indexOf('click') >= 0 &&\n      ['mouseleave', 'mousemove'].indexOf(event.type) >= 0 &&\n      isVisibleFromClick\n    ) {\n      return;\n    }\n\n    const delay = getDelay(false);\n\n    if (delay) {\n      hideTimeout = setTimeout(() => {\n        if (instance.state.isVisible) {\n          instance.hide();\n        }\n      }, delay);\n    } else {\n      // Fixes a `transitionend` problem when it fires 1 frame too\n      // late sometimes, we don't want hide() to be called.\n      scheduleHideAnimationFrame = requestAnimationFrame(() => {\n        instance.hide();\n      });\n    }\n  }\n\n  // ===========================================================================\n  // 🔑 Public methods\n  // ===========================================================================\n  function enable(): void {\n    instance.state.isEnabled = true;\n  }\n\n  function disable(): void {\n    // Disabling the instance should also hide it\n    // https://github.com/atomiks/tippy.js-react/issues/106\n    instance.hide();\n    instance.state.isEnabled = false;\n  }\n\n  function clearDelayTimeouts(): void {\n    clearTimeout(showTimeout);\n    clearTimeout(hideTimeout);\n    cancelAnimationFrame(scheduleHideAnimationFrame);\n  }\n\n  function setProps(partialProps: Partial<Props>): void {\n    /* istanbul ignore else */\n    if (__DEV__) {\n      warnWhen(instance.state.isDestroyed, createMemoryLeakWarning('setProps'));\n    }\n\n    if (instance.state.isDestroyed) {\n      return;\n    }\n\n    invokeHook('onBeforeUpdate', [instance, partialProps]);\n\n    removeListeners();\n\n    const prevProps = instance.props;\n    const nextProps = evaluateProps(reference, {\n      ...prevProps,\n      ...removeUndefinedProps(partialProps),\n      ignoreAttributes: true,\n    });\n\n    instance.props = nextProps;\n\n    addListeners();\n\n    if (prevProps.interactiveDebounce !== nextProps.interactiveDebounce) {\n      cleanupInteractiveMouseListeners();\n      debouncedOnMouseMove = debounce(\n        onMouseMove,\n        nextProps.interactiveDebounce\n      );\n    }\n\n    // Ensure stale aria-expanded attributes are removed\n    if (prevProps.triggerTarget && !nextProps.triggerTarget) {\n      normalizeToArray(prevProps.triggerTarget).forEach((node) => {\n        node.removeAttribute('aria-expanded');\n      });\n    } else if (nextProps.triggerTarget) {\n      reference.removeAttribute('aria-expanded');\n    }\n\n    handleAriaExpandedAttribute();\n    handleStyles();\n\n    if (onUpdate) {\n      onUpdate(prevProps, nextProps);\n    }\n\n    if (instance.popperInstance) {\n      createPopperInstance();\n\n      // Fixes an issue with nested tippies if they are all getting re-rendered,\n      // and the nested ones get re-rendered first.\n      // https://github.com/atomiks/tippyjs-react/issues/177\n      // TODO: find a cleaner / more efficient solution(!)\n      getNestedPopperTree().forEach((nestedPopper) => {\n        // React (and other UI libs likely) requires a rAF wrapper as it flushes\n        // its work in one\n        requestAnimationFrame(nestedPopper._tippy!.popperInstance!.forceUpdate);\n      });\n    }\n\n    invokeHook('onAfterUpdate', [instance, partialProps]);\n  }\n\n  function setContent(content: Content): void {\n    instance.setProps({content});\n  }\n\n  function show(): void {\n    /* istanbul ignore else */\n    if (__DEV__) {\n      warnWhen(instance.state.isDestroyed, createMemoryLeakWarning('show'));\n    }\n\n    // Early bail-out\n    const isAlreadyVisible = instance.state.isVisible;\n    const isDestroyed = instance.state.isDestroyed;\n    const isDisabled = !instance.state.isEnabled;\n    const isTouchAndTouchDisabled =\n      currentInput.isTouch && !instance.props.touch;\n    const duration = getValueAtIndexOrReturn(\n      instance.props.duration,\n      0,\n      defaultProps.duration\n    );\n\n    if (\n      isAlreadyVisible ||\n      isDestroyed ||\n      isDisabled ||\n      isTouchAndTouchDisabled\n    ) {\n      return;\n    }\n\n    // Normalize `disabled` behavior across browsers.\n    // Firefox allows events on disabled elements, but Chrome doesn't.\n    // Using a wrapper element (i.e. <span>) is recommended.\n    if (getCurrentTarget().hasAttribute('disabled')) {\n      return;\n    }\n\n    invokeHook('onShow', [instance], false);\n    if (instance.props.onShow(instance) === false) {\n      return;\n    }\n\n    instance.state.isVisible = true;\n\n    if (getIsDefaultRenderFn()) {\n      popper.style.visibility = 'visible';\n    }\n\n    handleStyles();\n    addDocumentPress();\n\n    if (!instance.state.isMounted) {\n      popper.style.transition = 'none';\n    }\n\n    // If flipping to the opposite side after hiding at least once, the\n    // animation will use the wrong placement without resetting the duration\n    if (getIsDefaultRenderFn()) {\n      const {box, content} = getDefaultTemplateChildren();\n      setTransitionDuration([box, content], 0);\n    }\n\n    onFirstUpdate = (): void => {\n      if (!instance.state.isVisible || ignoreOnFirstUpdate) {\n        return;\n      }\n\n      ignoreOnFirstUpdate = true;\n\n      // reflow\n      void popper.offsetHeight;\n\n      popper.style.transition = instance.props.moveTransition;\n\n      if (getIsDefaultRenderFn() && instance.props.animation) {\n        const {box, content} = getDefaultTemplateChildren();\n        setTransitionDuration([box, content], duration);\n        setVisibilityState([box, content], 'visible');\n      }\n\n      handleAriaContentAttribute();\n      handleAriaExpandedAttribute();\n\n      pushIfUnique(mountedInstances, instance);\n\n      // certain modifiers (e.g. `maxSize`) require a second update after the\n      // popper has been positioned for the first time\n      instance.popperInstance?.forceUpdate();\n\n      invokeHook('onMount', [instance]);\n\n      if (instance.props.animation && getIsDefaultRenderFn()) {\n        onTransitionedIn(duration, () => {\n          instance.state.isShown = true;\n          invokeHook('onShown', [instance]);\n        });\n      }\n    };\n\n    mount();\n  }\n\n  function hide(): void {\n    /* istanbul ignore else */\n    if (__DEV__) {\n      warnWhen(instance.state.isDestroyed, createMemoryLeakWarning('hide'));\n    }\n\n    // Early bail-out\n    const isAlreadyHidden = !instance.state.isVisible;\n    const isDestroyed = instance.state.isDestroyed;\n    const isDisabled = !instance.state.isEnabled;\n    const duration = getValueAtIndexOrReturn(\n      instance.props.duration,\n      1,\n      defaultProps.duration\n    );\n\n    if (isAlreadyHidden || isDestroyed || isDisabled) {\n      return;\n    }\n\n    invokeHook('onHide', [instance], false);\n    if (instance.props.onHide(instance) === false) {\n      return;\n    }\n\n    instance.state.isVisible = false;\n    instance.state.isShown = false;\n    ignoreOnFirstUpdate = false;\n    isVisibleFromClick = false;\n\n    if (getIsDefaultRenderFn()) {\n      popper.style.visibility = 'hidden';\n    }\n\n    cleanupInteractiveMouseListeners();\n    removeDocumentPress();\n    handleStyles(true);\n\n    if (getIsDefaultRenderFn()) {\n      const {box, content} = getDefaultTemplateChildren();\n\n      if (instance.props.animation) {\n        setTransitionDuration([box, content], duration);\n        setVisibilityState([box, content], 'hidden');\n      }\n    }\n\n    handleAriaContentAttribute();\n    handleAriaExpandedAttribute();\n\n    if (instance.props.animation) {\n      if (getIsDefaultRenderFn()) {\n        onTransitionedOut(duration, instance.unmount);\n      }\n    } else {\n      instance.unmount();\n    }\n  }\n\n  function hideWithInteractivity(event: MouseEvent): void {\n    /* istanbul ignore else */\n    if (__DEV__) {\n      warnWhen(\n        instance.state.isDestroyed,\n        createMemoryLeakWarning('hideWithInteractivity')\n      );\n    }\n\n    getDocument().addEventListener('mousemove', debouncedOnMouseMove);\n    pushIfUnique(mouseMoveListeners, debouncedOnMouseMove);\n    debouncedOnMouseMove(event);\n  }\n\n  function unmount(): void {\n    /* istanbul ignore else */\n    if (__DEV__) {\n      warnWhen(instance.state.isDestroyed, createMemoryLeakWarning('unmount'));\n    }\n\n    if (instance.state.isVisible) {\n      instance.hide();\n    }\n\n    if (!instance.state.isMounted) {\n      return;\n    }\n\n    destroyPopperInstance();\n\n    // If a popper is not interactive, it will be appended outside the popper\n    // tree by default. This seems mainly for interactive tippies, but we should\n    // find a workaround if possible\n    getNestedPopperTree().forEach((nestedPopper) => {\n      nestedPopper._tippy!.unmount();\n    });\n\n    if (popper.parentNode) {\n      popper.parentNode.removeChild(popper);\n    }\n\n    mountedInstances = mountedInstances.filter((i) => i !== instance);\n\n    instance.state.isMounted = false;\n    invokeHook('onHidden', [instance]);\n  }\n\n  function destroy(): void {\n    /* istanbul ignore else */\n    if (__DEV__) {\n      warnWhen(instance.state.isDestroyed, createMemoryLeakWarning('destroy'));\n    }\n\n    if (instance.state.isDestroyed) {\n      return;\n    }\n\n    instance.clearDelayTimeouts();\n    instance.unmount();\n\n    removeListeners();\n\n    delete reference._tippy;\n\n    instance.state.isDestroyed = true;\n\n    invokeHook('onDestroy', [instance]);\n  }\n}\n", "import bindGlobalEventListeners, {\n  currentInput,\n} from './bindGlobalEventListeners';\nimport createTippy, {mountedInstances} from './createTippy';\nimport {getArrayOfElements, isElement, isReferenceElement} from './dom-utils';\nimport {defaultProps, setDefaultProps, validateProps} from './props';\nimport {HideAll, HideAllOptions, Instance, Props, Targets} from './types';\nimport {validateTargets, warnWhen} from './validation';\n\nfunction tippy(\n  targets: Targets,\n  optionalProps: Partial<Props> = {}\n): Instance | Instance[] {\n  const plugins = defaultProps.plugins.concat(optionalProps.plugins || []);\n\n  /* istanbul ignore else */\n  if (__DEV__) {\n    validateTargets(targets);\n    validateProps(optionalProps, plugins);\n  }\n\n  bindGlobalEventListeners();\n\n  const passedProps: Partial<Props> = {...optionalProps, plugins};\n\n  const elements = getArrayOfElements(targets);\n\n  /* istanbul ignore else */\n  if (__DEV__) {\n    const isSingleContentElement = isElement(passedProps.content);\n    const isMoreThanOneReferenceElement = elements.length > 1;\n    warnWhen(\n      isSingleContentElement && isMoreThanOneReferenceElement,\n      [\n        'tippy() was passed an Element as the `content` prop, but more than',\n        'one tippy instance was created by this invocation. This means the',\n        'content element will only be appended to the last tippy instance.',\n        '\\n\\n',\n        'Instead, pass the .innerHTML of the element, or use a function that',\n        'returns a cloned version of the element instead.',\n        '\\n\\n',\n        '1) content: element.innerHTML\\n',\n        '2) content: () => element.cloneNode(true)',\n      ].join(' ')\n    );\n  }\n\n  const instances = elements.reduce<Instance[]>(\n    (acc, reference): Instance[] => {\n      const instance = reference && createTippy(reference, passedProps);\n\n      if (instance) {\n        acc.push(instance);\n      }\n\n      return acc;\n    },\n    []\n  );\n\n  return isElement(targets) ? instances[0] : instances;\n}\n\ntippy.defaultProps = defaultProps;\ntippy.setDefaultProps = setDefaultProps;\ntippy.currentInput = currentInput;\n\nexport default tippy;\n\nexport const hideAll: HideAll = ({\n  exclude: excludedReferenceOrInstance,\n  duration,\n}: HideAllOptions = {}) => {\n  mountedInstances.forEach((instance) => {\n    let isExcluded = false;\n\n    if (excludedReferenceOrInstance) {\n      isExcluded = isReferenceElement(excludedReferenceOrInstance)\n        ? instance.reference === excludedReferenceOrInstance\n        : instance.popper === (excludedReferenceOrInstance as Instance).popper;\n    }\n\n    if (!isExcluded) {\n      const originalDuration = instance.props.duration;\n\n      instance.setProps({duration});\n      instance.hide();\n\n      if (!instance.state.isDestroyed) {\n        instance.setProps({duration: originalDuration});\n      }\n    }\n  });\n};\n", "import tippy from '..';\nimport {div} from '../dom-utils';\nimport {\n  C<PERSON><PERSON><PERSON><PERSON>,\n  <PERSON><PERSON><PERSON>,\n  CreateSingletonProps,\n  ReferenceElement,\n  CreateSingletonInstance,\n  Instance,\n  Props,\n} from '../types';\nimport {normalizeToArray, removeProperties} from '../utils';\nimport {errorWhen} from '../validation';\nimport {applyStyles, Modifier} from '@popperjs/core';\n\n// The default `applyStyles` modifier has a cleanup function that gets called\n// every time the popper is destroyed (i.e. a new target), removing the styles\n// and causing transitions to break for singletons when the console is open, but\n// most notably for non-transform styles being used, `gpuAcceleration: false`.\nconst applyStylesModifier: Modifier<'applyStyles', Record<string, unknown>> = {\n  ...applyStyles,\n  effect({state}) {\n    const initialStyles = {\n      popper: {\n        position: state.options.strategy,\n        left: '0',\n        top: '0',\n        margin: '0',\n      },\n      arrow: {\n        position: 'absolute',\n      },\n      reference: {},\n    };\n\n    Object.assign(state.elements.popper.style, initialStyles.popper);\n    state.styles = initialStyles;\n\n    if (state.elements.arrow) {\n      Object.assign(state.elements.arrow.style, initialStyles.arrow);\n    }\n\n    // intentionally return no cleanup function\n    // return () => { ... }\n  },\n};\n\nconst createSingleton: CreateSingleton = (\n  tippyInstances,\n  optionalProps = {}\n) => {\n  /* istanbul ignore else */\n  if (__DEV__) {\n    errorWhen(\n      !Array.isArray(tippyInstances),\n      [\n        'The first argument passed to createSingleton() must be an array of',\n        'tippy instances. The passed value was',\n        String(tippyInstances),\n      ].join(' ')\n    );\n  }\n\n  let individualInstances = tippyInstances;\n  let references: Array<ReferenceElement> = [];\n  let triggerTargets: Array<Element> = [];\n  let currentTarget: Element | null;\n  let overrides = optionalProps.overrides;\n  let interceptSetPropsCleanups: Array<() => void> = [];\n  let shownOnCreate = false;\n\n  function setTriggerTargets(): void {\n    triggerTargets = individualInstances\n      .map((instance) =>\n        normalizeToArray(instance.props.triggerTarget || instance.reference)\n      )\n      .reduce((acc, item) => acc.concat(item), []);\n  }\n\n  function setReferences(): void {\n    references = individualInstances.map((instance) => instance.reference);\n  }\n\n  function enableInstances(isEnabled: boolean): void {\n    individualInstances.forEach((instance) => {\n      if (isEnabled) {\n        instance.enable();\n      } else {\n        instance.disable();\n      }\n    });\n  }\n\n  function interceptSetProps(singleton: Instance): Array<() => void> {\n    return individualInstances.map((instance) => {\n      const originalSetProps = instance.setProps;\n\n      instance.setProps = (props): void => {\n        originalSetProps(props);\n\n        if (instance.reference === currentTarget) {\n          singleton.setProps(props);\n        }\n      };\n\n      return (): void => {\n        instance.setProps = originalSetProps;\n      };\n    });\n  }\n\n  // have to pass singleton, as it maybe undefined on first call\n  function prepareInstance(\n    singleton: Instance,\n    target: ReferenceElement\n  ): void {\n    const index = triggerTargets.indexOf(target);\n\n    // bail-out\n    if (target === currentTarget) {\n      return;\n    }\n\n    currentTarget = target;\n\n    const overrideProps: Partial<Props> = (overrides || [])\n      .concat('content')\n      .reduce((acc, prop) => {\n        (acc as any)[prop] = individualInstances[index].props[prop];\n        return acc;\n      }, {});\n\n    singleton.setProps({\n      ...overrideProps,\n      getReferenceClientRect:\n        typeof overrideProps.getReferenceClientRect === 'function'\n          ? overrideProps.getReferenceClientRect\n          : (): ClientRect => references[index]?.getBoundingClientRect(),\n    });\n  }\n\n  enableInstances(false);\n  setReferences();\n  setTriggerTargets();\n\n  const plugin: Plugin = {\n    fn() {\n      return {\n        onDestroy(): void {\n          enableInstances(true);\n        },\n        onHidden(): void {\n          currentTarget = null;\n        },\n        onClickOutside(instance): void {\n          if (instance.props.showOnCreate && !shownOnCreate) {\n            shownOnCreate = true;\n            currentTarget = null;\n          }\n        },\n        onShow(instance): void {\n          if (instance.props.showOnCreate && !shownOnCreate) {\n            shownOnCreate = true;\n            prepareInstance(instance, references[0]);\n          }\n        },\n        onTrigger(instance, event): void {\n          prepareInstance(instance, event.currentTarget as Element);\n        },\n      };\n    },\n  };\n\n  const singleton = tippy(div(), {\n    ...removeProperties(optionalProps, ['overrides']),\n    plugins: [plugin, ...(optionalProps.plugins || [])],\n    triggerTarget: triggerTargets,\n    popperOptions: {\n      ...optionalProps.popperOptions,\n      modifiers: [\n        ...(optionalProps.popperOptions?.modifiers || []),\n        applyStylesModifier,\n      ],\n    },\n  }) as CreateSingletonInstance<CreateSingletonProps>;\n\n  const originalShow = singleton.show;\n\n  singleton.show = (target?: ReferenceElement | Instance | number): void => {\n    originalShow();\n\n    // first time, showOnCreate or programmatic call with no params\n    // default to showing first instance\n    if (!currentTarget && target == null) {\n      return prepareInstance(singleton, references[0]);\n    }\n\n    // triggered from event (do nothing as prepareInstance already called by onTrigger)\n    // programmatic call with no params when already visible (do nothing again)\n    if (currentTarget && target == null) {\n      return;\n    }\n\n    // target is index of instance\n    if (typeof target === 'number') {\n      return (\n        references[target] && prepareInstance(singleton, references[target])\n      );\n    }\n\n    // target is a child tippy instance\n    if (individualInstances.indexOf(target as Instance) >= 0) {\n      const ref = (target as Instance).reference;\n      return prepareInstance(singleton, ref);\n    }\n\n    // target is a ReferenceElement\n    if (references.indexOf(target as ReferenceElement) >= 0) {\n      return prepareInstance(singleton, target as ReferenceElement);\n    }\n  };\n\n  singleton.showNext = (): void => {\n    const first = references[0];\n    if (!currentTarget) {\n      return singleton.show(0);\n    }\n    const index = references.indexOf(currentTarget);\n    singleton.show(references[index + 1] || first);\n  };\n\n  singleton.showPrevious = (): void => {\n    const last = references[references.length - 1];\n    if (!currentTarget) {\n      return singleton.show(last);\n    }\n    const index = references.indexOf(currentTarget);\n    const target = references[index - 1] || last;\n    singleton.show(target);\n  };\n\n  const originalSetProps = singleton.setProps;\n\n  singleton.setProps = (props): void => {\n    overrides = props.overrides || overrides;\n    originalSetProps(props);\n  };\n\n  singleton.setInstances = (nextInstances): void => {\n    enableInstances(true);\n    interceptSetPropsCleanups.forEach((fn) => fn());\n\n    individualInstances = nextInstances;\n\n    enableInstances(false);\n    setReferences();\n    setTriggerTargets();\n    interceptSetPropsCleanups = interceptSetProps(singleton);\n\n    singleton.setProps({triggerTarget: triggerTargets});\n  };\n\n  interceptSetPropsCleanups = interceptSetProps(singleton);\n\n  return singleton;\n};\n\nexport default createSingleton;\n", "import tippy from '..';\nimport {TOUCH_OPTIONS} from '../constants';\nimport {defaultProps} from '../props';\nimport {Instance, Props, Targets} from '../types';\nimport {ListenerObject} from '../types-internal';\nimport {normalizeToArray, removeProperties} from '../utils';\nimport {errorWhen} from '../validation';\n\nconst BUBBLING_EVENTS_MAP = {\n  mouseover: 'mouseenter',\n  focusin: 'focus',\n  click: 'click',\n};\n\n/**\n * Creates a delegate instance that controls the creation of tippy instances\n * for child elements (`target` CSS selector).\n */\nfunction delegate(\n  targets: Targets,\n  props: Partial<Props> & {target: string}\n): Instance | Instance[] {\n  /* istanbul ignore else */\n  if (__DEV__) {\n    errorWhen(\n      !(props && props.target),\n      [\n        'You must specity a `target` prop indicating a CSS selector string matching',\n        'the target elements that should receive a tippy.',\n      ].join(' ')\n    );\n  }\n\n  let listeners: ListenerObject[] = [];\n  let childTippyInstances: Instance[] = [];\n  let disabled = false;\n\n  const {target} = props;\n\n  const nativeProps = removeProperties(props, ['target']);\n  const parentProps = {...nativeProps, trigger: 'manual', touch: false};\n  const childProps = {\n    touch: defaultProps.touch,\n    ...nativeProps,\n    showOnCreate: true,\n  };\n\n  const returnValue = tippy(targets, parentProps);\n  const normalizedReturnValue = normalizeToArray(returnValue);\n\n  function onTrigger(event: Event): void {\n    if (!event.target || disabled) {\n      return;\n    }\n\n    const targetNode = (event.target as Element).closest(target);\n\n    if (!targetNode) {\n      return;\n    }\n\n    // Get relevant trigger with fallbacks:\n    // 1. Check `data-tippy-trigger` attribute on target node\n    // 2. Fallback to `trigger` passed to `delegate()`\n    // 3. Fallback to `defaultProps.trigger`\n    const trigger =\n      targetNode.getAttribute('data-tippy-trigger') ||\n      props.trigger ||\n      defaultProps.trigger;\n\n    // @ts-ignore\n    if (targetNode._tippy) {\n      return;\n    }\n\n    if (event.type === 'touchstart' && typeof childProps.touch === 'boolean') {\n      return;\n    }\n\n    if (\n      event.type !== 'touchstart' &&\n      trigger.indexOf((BUBBLING_EVENTS_MAP as any)[event.type]) < 0\n    ) {\n      return;\n    }\n\n    const instance = tippy(targetNode, childProps);\n\n    if (instance) {\n      childTippyInstances = childTippyInstances.concat(instance);\n    }\n  }\n\n  function on(\n    node: Element,\n    eventType: string,\n    handler: EventListener,\n    options: boolean | Record<string, unknown> = false\n  ): void {\n    node.addEventListener(eventType, handler, options);\n    listeners.push({node, eventType, handler, options});\n  }\n\n  function addEventListeners(instance: Instance): void {\n    const {reference} = instance;\n\n    on(reference, 'touchstart', onTrigger, TOUCH_OPTIONS);\n    on(reference, 'mouseover', onTrigger);\n    on(reference, 'focusin', onTrigger);\n    on(reference, 'click', onTrigger);\n  }\n\n  function removeEventListeners(): void {\n    listeners.forEach(({node, eventType, handler, options}: ListenerObject) => {\n      node.removeEventListener(eventType, handler, options);\n    });\n    listeners = [];\n  }\n\n  function applyMutations(instance: Instance): void {\n    const originalDestroy = instance.destroy;\n    const originalEnable = instance.enable;\n    const originalDisable = instance.disable;\n\n    instance.destroy = (shouldDestroyChildInstances = true): void => {\n      if (shouldDestroyChildInstances) {\n        childTippyInstances.forEach((instance) => {\n          instance.destroy();\n        });\n      }\n\n      childTippyInstances = [];\n\n      removeEventListeners();\n      originalDestroy();\n    };\n\n    instance.enable = (): void => {\n      originalEnable();\n      childTippyInstances.forEach((instance) => instance.enable());\n      disabled = false;\n    };\n\n    instance.disable = (): void => {\n      originalDisable();\n      childTippyInstances.forEach((instance) => instance.disable());\n      disabled = true;\n    };\n\n    addEventListeners(instance);\n  }\n\n  normalizedReturnValue.forEach(applyMutations);\n\n  return returnValue;\n}\n\nexport default delegate;\n", "import {BACKDROP_CLASS} from '../constants';\nimport {div, setVisibilityState} from '../dom-utils';\nimport {getChildren} from '../template';\nimport {AnimateFill} from '../types';\nimport {errorWhen} from '../validation';\n\nconst animateFill: AnimateFill = {\n  name: 'animateFill',\n  defaultValue: false,\n  fn(instance) {\n    // @ts-ignore\n    if (!instance.props.render?.$$tippy) {\n      if (__DEV__) {\n        errorWhen(\n          instance.props.animateFill,\n          'The `animateFill` plugin requires the default render function.'\n        );\n      }\n\n      return {};\n    }\n\n    const {box, content} = getChildren(instance.popper);\n\n    const backdrop = instance.props.animateFill\n      ? createBackdropElement()\n      : null;\n\n    return {\n      onCreate(): void {\n        if (backdrop) {\n          box.insertBefore(backdrop, box.firstElementChild!);\n          box.setAttribute('data-animatefill', '');\n          box.style.overflow = 'hidden';\n\n          instance.setProps({arrow: false, animation: 'shift-away'});\n        }\n      },\n      onMount(): void {\n        if (backdrop) {\n          const {transitionDuration} = box.style;\n          const duration = Number(transitionDuration.replace('ms', ''));\n\n          // The content should fade in after the backdrop has mostly filled the\n          // tooltip element. `clip-path` is the other alternative but is not\n          // well-supported and is buggy on some devices.\n          content.style.transitionDelay = `${Math.round(duration / 10)}ms`;\n\n          backdrop.style.transitionDuration = transitionDuration;\n          setVisibilityState([backdrop], 'visible');\n        }\n      },\n      onShow(): void {\n        if (backdrop) {\n          backdrop.style.transitionDuration = '0ms';\n        }\n      },\n      onHide(): void {\n        if (backdrop) {\n          setVisibilityState([backdrop], 'hidden');\n        }\n      },\n    };\n  },\n};\n\nexport default animateFill;\n\nfunction createBackdropElement(): HTMLDivElement {\n  const backdrop = div();\n  backdrop.className = BACKDROP_CLASS;\n  setVisibilityState([backdrop], 'hidden');\n  return backdrop;\n}\n", "import {getOwnerDocument, isMouseEvent} from '../dom-utils';\nimport {FollowCursor, Instance} from '../types';\n\nlet mouseCoords = {clientX: 0, clientY: 0};\nlet activeInstances: Array<{instance: Instance; doc: Document}> = [];\n\nfunction storeMouseCoords({clientX, clientY}: MouseEvent): void {\n  mouseCoords = {clientX, clientY};\n}\n\nfunction addMouseCoordsListener(doc: Document): void {\n  doc.addEventListener('mousemove', storeMouseCoords);\n}\n\nfunction removeMouseCoordsListener(doc: Document): void {\n  doc.removeEventListener('mousemove', storeMouseCoords);\n}\n\nconst followCursor: FollowCursor = {\n  name: 'followCursor',\n  defaultValue: false,\n  fn(instance) {\n    const reference = instance.reference;\n    const doc = getOwnerDocument(instance.props.triggerTarget || reference);\n\n    let isInternalUpdate = false;\n    let wasFocusEvent = false;\n    let isUnmounted = true;\n    let prevProps = instance.props;\n\n    function getIsInitialBehavior(): boolean {\n      return (\n        instance.props.followCursor === 'initial' && instance.state.isVisible\n      );\n    }\n\n    function addListener(): void {\n      doc.addEventListener('mousemove', onMouseMove);\n    }\n\n    function removeListener(): void {\n      doc.removeEventListener('mousemove', onMouseMove);\n    }\n\n    function unsetGetReferenceClientRect(): void {\n      isInternalUpdate = true;\n      instance.setProps({getReferenceClientRect: null});\n      isInternalUpdate = false;\n    }\n\n    function onMouseMove(event: MouseEvent): void {\n      // If the instance is interactive, avoid updating the position unless it's\n      // over the reference element\n      const isCursorOverReference = event.target\n        ? reference.contains(event.target as Node)\n        : true;\n      const {followCursor} = instance.props;\n      const {clientX, clientY} = event;\n\n      const rect = reference.getBoundingClientRect();\n      const relativeX = clientX - rect.left;\n      const relativeY = clientY - rect.top;\n\n      if (isCursorOverReference || !instance.props.interactive) {\n        instance.setProps({\n          // @ts-ignore - unneeded DOMRect properties\n          getReferenceClientRect() {\n            const rect = reference.getBoundingClientRect();\n\n            let x = clientX;\n            let y = clientY;\n\n            if (followCursor === 'initial') {\n              x = rect.left + relativeX;\n              y = rect.top + relativeY;\n            }\n\n            const top = followCursor === 'horizontal' ? rect.top : y;\n            const right = followCursor === 'vertical' ? rect.right : x;\n            const bottom = followCursor === 'horizontal' ? rect.bottom : y;\n            const left = followCursor === 'vertical' ? rect.left : x;\n\n            return {\n              width: right - left,\n              height: bottom - top,\n              top,\n              right,\n              bottom,\n              left,\n            };\n          },\n        });\n      }\n    }\n\n    function create(): void {\n      if (instance.props.followCursor) {\n        activeInstances.push({instance, doc});\n        addMouseCoordsListener(doc);\n      }\n    }\n\n    function destroy(): void {\n      activeInstances = activeInstances.filter(\n        (data) => data.instance !== instance\n      );\n\n      if (activeInstances.filter((data) => data.doc === doc).length === 0) {\n        removeMouseCoordsListener(doc);\n      }\n    }\n\n    return {\n      onCreate: create,\n      onDestroy: destroy,\n      onBeforeUpdate(): void {\n        prevProps = instance.props;\n      },\n      onAfterUpdate(_, {followCursor}): void {\n        if (isInternalUpdate) {\n          return;\n        }\n\n        if (\n          followCursor !== undefined &&\n          prevProps.followCursor !== followCursor\n        ) {\n          destroy();\n\n          if (followCursor) {\n            create();\n\n            if (\n              instance.state.isMounted &&\n              !wasFocusEvent &&\n              !getIsInitialBehavior()\n            ) {\n              addListener();\n            }\n          } else {\n            removeListener();\n            unsetGetReferenceClientRect();\n          }\n        }\n      },\n      onMount(): void {\n        if (instance.props.followCursor && !wasFocusEvent) {\n          if (isUnmounted) {\n            onMouseMove(mouseCoords as MouseEvent);\n            isUnmounted = false;\n          }\n\n          if (!getIsInitialBehavior()) {\n            addListener();\n          }\n        }\n      },\n      onTrigger(_, event): void {\n        if (isMouseEvent(event)) {\n          mouseCoords = {clientX: event.clientX, clientY: event.clientY};\n        }\n        wasFocusEvent = event.type === 'focus';\n      },\n      onHidden(): void {\n        if (instance.props.followCursor) {\n          unsetGetReferenceClientRect();\n          removeListener();\n          isUnmounted = true;\n        }\n      },\n    };\n  },\n};\n\nexport default followCursor;\n", "import {Modifier, Placement} from '@popperjs/core';\nimport {isMouseEvent} from '../dom-utils';\nimport {BasePlacement, InlinePositioning, Props} from '../types';\nimport {arrayFrom, getBasePlacement} from '../utils';\n\nfunction getProps(props: Props, modifier: Modifier<any, any>): Partial<Props> {\n  return {\n    popperOptions: {\n      ...props.popperOptions,\n      modifiers: [\n        ...(props.popperOptions?.modifiers || []).filter(\n          ({name}) => name !== modifier.name\n        ),\n        modifier,\n      ],\n    },\n  };\n}\n\nconst inlinePositioning: InlinePositioning = {\n  name: 'inlinePositioning',\n  defaultValue: false,\n  fn(instance) {\n    const {reference} = instance;\n\n    function isEnabled(): boolean {\n      return !!instance.props.inlinePositioning;\n    }\n\n    let placement: Placement;\n    let cursorRectIndex = -1;\n    let isInternalUpdate = false;\n    let triedPlacements: Array<string> = [];\n\n    const modifier: Modifier<\n      'tippyInlinePositioning',\n      Record<string, unknown>\n    > = {\n      name: 'tippyInlinePositioning',\n      enabled: true,\n      phase: 'afterWrite',\n      fn({state}) {\n        if (isEnabled()) {\n          if (triedPlacements.indexOf(state.placement) !== -1) {\n            triedPlacements = [];\n          }\n\n          if (\n            placement !== state.placement &&\n            triedPlacements.indexOf(state.placement) === -1\n          ) {\n            triedPlacements.push(state.placement);\n            instance.setProps({\n              // @ts-ignore - unneeded DOMRect properties\n              getReferenceClientRect: () =>\n                getReferenceClientRect(state.placement),\n            });\n          }\n\n          placement = state.placement;\n        }\n      },\n    };\n\n    function getReferenceClientRect(placement: Placement): Partial<DOMRect> {\n      return getInlineBoundingClientRect(\n        getBasePlacement(placement),\n        reference.getBoundingClientRect(),\n        arrayFrom(reference.getClientRects()),\n        cursorRectIndex\n      );\n    }\n\n    function setInternalProps(partialProps: Partial<Props>): void {\n      isInternalUpdate = true;\n      instance.setProps(partialProps);\n      isInternalUpdate = false;\n    }\n\n    function addModifier(): void {\n      if (!isInternalUpdate) {\n        setInternalProps(getProps(instance.props, modifier));\n      }\n    }\n\n    return {\n      onCreate: addModifier,\n      onAfterUpdate: addModifier,\n      onTrigger(_, event): void {\n        if (isMouseEvent(event)) {\n          const rects = arrayFrom(instance.reference.getClientRects());\n          const cursorRect = rects.find(\n            (rect) =>\n              rect.left - 2 <= event.clientX &&\n              rect.right + 2 >= event.clientX &&\n              rect.top - 2 <= event.clientY &&\n              rect.bottom + 2 >= event.clientY\n          );\n          const index = rects.indexOf(cursorRect);\n          cursorRectIndex = index > -1 ? index : cursorRectIndex;\n        }\n      },\n      onHidden(): void {\n        cursorRectIndex = -1;\n      },\n    };\n  },\n};\n\nexport default inlinePositioning;\n\nexport function getInlineBoundingClientRect(\n  currentBasePlacement: BasePlacement | null,\n  boundingRect: DOMRect,\n  clientRects: DOMRect[],\n  cursorRectIndex: number\n): {\n  top: number;\n  bottom: number;\n  left: number;\n  right: number;\n  width: number;\n  height: number;\n} {\n  // Not an inline element, or placement is not yet known\n  if (clientRects.length < 2 || currentBasePlacement === null) {\n    return boundingRect;\n  }\n\n  // There are two rects and they are disjoined\n  if (\n    clientRects.length === 2 &&\n    cursorRectIndex >= 0 &&\n    clientRects[0].left > clientRects[1].right\n  ) {\n    return clientRects[cursorRectIndex] || boundingRect;\n  }\n\n  switch (currentBasePlacement) {\n    case 'top':\n    case 'bottom': {\n      const firstRect = clientRects[0];\n      const lastRect = clientRects[clientRects.length - 1];\n      const isTop = currentBasePlacement === 'top';\n\n      const top = firstRect.top;\n      const bottom = lastRect.bottom;\n      const left = isTop ? firstRect.left : lastRect.left;\n      const right = isTop ? firstRect.right : lastRect.right;\n      const width = right - left;\n      const height = bottom - top;\n\n      return {top, bottom, left, right, width, height};\n    }\n    case 'left':\n    case 'right': {\n      const minLeft = Math.min(...clientRects.map((rects) => rects.left));\n      const maxRight = Math.max(...clientRects.map((rects) => rects.right));\n      const measureRects = clientRects.filter((rect) =>\n        currentBasePlacement === 'left'\n          ? rect.left === minLeft\n          : rect.right === maxRight\n      );\n\n      const top = measureRects[0].top;\n      const bottom = measureRects[measureRects.length - 1].bottom;\n      const left = minLeft;\n      const right = maxRight;\n      const width = right - left;\n      const height = bottom - top;\n\n      return {top, bottom, left, right, width, height};\n    }\n    default: {\n      return boundingRect;\n    }\n  }\n}\n", "import {VirtualElement} from '@popperjs/core';\nimport {ReferenceElement, Sticky} from '../types';\n\nconst sticky: Sticky = {\n  name: 'sticky',\n  defaultValue: false,\n  fn(instance) {\n    const {reference, popper} = instance;\n\n    function getReference(): ReferenceElement | VirtualElement {\n      return instance.popperInstance\n        ? instance.popperInstance.state.elements.reference\n        : reference;\n    }\n\n    function shouldCheck(value: 'reference' | 'popper'): boolean {\n      return instance.props.sticky === true || instance.props.sticky === value;\n    }\n\n    let prevRefRect: ClientRect | null = null;\n    let prevPopRect: ClientRect | null = null;\n\n    function updatePosition(): void {\n      const currentRefRect = shouldCheck('reference')\n        ? getReference().getBoundingClientRect()\n        : null;\n      const currentPopRect = shouldCheck('popper')\n        ? popper.getBoundingClientRect()\n        : null;\n\n      if (\n        (currentRefRect && areRectsDifferent(prevRefRect, currentRefRect)) ||\n        (currentPopRect && areRectsDifferent(prevPopRect, currentPopRect))\n      ) {\n        if (instance.popperInstance) {\n          instance.popperInstance.update();\n        }\n      }\n\n      prevRefRect = currentRefRect;\n      prevPopRect = currentPopRect;\n\n      if (instance.state.isMounted) {\n        requestAnimationFrame(updatePosition);\n      }\n    }\n\n    return {\n      onMount(): void {\n        if (instance.props.sticky) {\n          updatePosition();\n        }\n      },\n    };\n  },\n};\n\nexport default sticky;\n\nfunction areRectsDifferent(\n  rectA: ClientRect | null,\n  rectB: ClientRect | null\n): boolean {\n  if (rectA && rectB) {\n    return (\n      rectA.top !== rectB.top ||\n      rectA.right !== rectB.right ||\n      rectA.bottom !== rectB.bottom ||\n      rectA.left !== rectB.left\n    );\n  }\n\n  return true;\n}\n", "import tippy from '../src';\n\nexport {hideAll} from '../src';\nexport {default as createSingleton} from '../src/addons/createSingleton';\nexport {default as delegate} from '../src/addons/delegate';\nexport {default as animateFill} from '../src/plugins/animateFill';\nexport {default as followCursor} from '../src/plugins/followCursor';\nexport {default as inlinePositioning} from '../src/plugins/inlinePositioning';\nexport {default as sticky} from '../src/plugins/sticky';\nexport {ROUND_ARROW as roundArrow} from '../src/constants';\n\ntippy.setDefaultProps({animation: false});\n\nexport default tippy;\n"], "names": ["ROUND_ARROW", "CONTENT_CLASS", "BACKDROP_CLASS", "ARROW_CLASS", "SVG_ARROW_CLASS", "TOUCH_OPTIONS", "passive", "capture", "TIPPY_DEFAULT_APPEND_TO", "document", "body", "hasOwnProperty", "obj", "key", "call", "getValueAtIndexOrReturn", "value", "index", "defaultValue", "Array", "isArray", "v", "isType", "type", "str", "toString", "indexOf", "invokeWithArgsOrReturn", "args", "debounce", "fn", "ms", "timeout", "arg", "clearTimeout", "setTimeout", "removeProperties", "keys", "clone", "for<PERSON>ach", "splitBySpaces", "split", "filter", "Boolean", "normalizeToArray", "concat", "pushIfUnique", "arr", "push", "unique", "item", "getBasePlacement", "placement", "arrayFrom", "slice", "removeUndefinedProps", "Object", "reduce", "acc", "undefined", "div", "createElement", "isElement", "some", "isNodeList", "isMouseEvent", "isReferenceElement", "_tippy", "reference", "getArrayOfElements", "querySelectorAll", "setTransitionDuration", "els", "el", "style", "transitionDuration", "setVisibilityState", "state", "setAttribute", "getOwnerDocument", "elementOrElements", "element", "ownerDocument", "isCursorOutsideInteractiveBorder", "popperTreeData", "event", "clientX", "clientY", "every", "popperRect", "popperState", "props", "interactiveBorder", "basePlacement", "offsetData", "modifiersData", "offset", "topDistance", "top", "y", "bottomDistance", "bottom", "leftDistance", "left", "x", "rightDistance", "right", "exceedsTop", "exceedsBottom", "exceedsLeft", "exceedsRight", "updateTransitionEndListener", "box", "action", "listener", "method", "actualContains", "parent", "child", "target", "contains", "getRootNode", "host", "currentInput", "is<PERSON><PERSON>ch", "lastMouseMoveTime", "onDocumentTouchStart", "window", "performance", "addEventListener", "onDocumentMouseMove", "now", "removeEventListener", "onWindowBlur", "activeElement", "instance", "blur", "isVisible", "bindGlobalEventListeners", "<PERSON><PERSON><PERSON><PERSON>", "isIE11", "msCrypto", "createMemoryLeakWarning", "txt", "join", "clean", "spacesAndTabs", "lineStartWithSpaces", "replace", "trim", "getDevMessage", "message", "getFormattedMessage", "visitedMessages", "resetVisitedMessages", "Set", "warn<PERSON><PERSON>", "condition", "has", "add", "console", "warn", "<PERSON><PERSON><PERSON>", "error", "validateTargets", "targets", "didPassFalsyValue", "didPassPlainObject", "prototype", "String", "pluginProps", "animateFill", "followCursor", "inlinePositioning", "sticky", "renderProps", "allowHTML", "animation", "arrow", "content", "inertia", "max<PERSON><PERSON><PERSON>", "role", "theme", "zIndex", "defaultProps", "appendTo", "aria", "expanded", "delay", "duration", "getReferenceClientRect", "hideOnClick", "ignoreAttributes", "interactive", "interactiveDebounce", "moveTransition", "onAfterUpdate", "onBeforeUpdate", "onCreate", "onDestroy", "onHidden", "onHide", "onMount", "onShow", "onShown", "onTrigger", "onUntrigger", "onClickOutside", "plugins", "popperOptions", "render", "showOnCreate", "touch", "trigger", "triggerTarget", "defaultKeys", "setDefaultProps", "partialProps", "validateProps", "getExtendedPassedProps", "passedProps", "plugin", "name", "getDataAttributeProps", "propKeys", "valueAsString", "getAttribute", "JSON", "parse", "e", "evaluateProps", "out", "prop", "nonPluginProps", "didPassUnknownProp", "length", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "popper", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "boxChildren", "children", "find", "node", "classList", "backdrop", "idCounter", "mouseMoveListeners", "mountedInstances", "createTippy", "showTimeout", "hideTimeout", "scheduleHideAnimationFrame", "isVisibleFromClick", "didHideDueToDocumentMouseDown", "didTouchMove", "ignoreOnFirstUpdate", "lastTriggerEvent", "currentTransitionEndListener", "onFirstUpdate", "listeners", "debouncedOnMouseMove", "onMouseMove", "currentTarget", "id", "popperInstance", "isEnabled", "isDestroyed", "isMounted", "isShown", "clearDelayTimeouts", "setProps", "<PERSON><PERSON><PERSON><PERSON>", "show", "hide", "hideWithInteractivity", "enable", "disable", "unmount", "destroy", "onUpdate", "pluginsHooks", "map", "hasAriaExpanded", "hasAttribute", "addListeners", "handleAriaExpandedAttribute", "handleStyles", "invokeHook", "scheduleShow", "getDocument", "getNormalizedTouchSettings", "getIsCustomTouchBehavior", "getIsDefaultRenderFn", "$$tippy", "getC<PERSON>rentTarget", "parentNode", "getDefaultTemplateChildren", "get<PERSON>elay", "isShow", "fromHide", "pointerEvents", "hook", "shouldInvokePropsHook", "pluginHooks", "handleAriaContentAttribute", "attr", "nodes", "currentValue", "nextValue", "removeAttribute", "cleanupInteractiveMouseListeners", "onDocumentPress", "actual<PERSON>arget", "<PERSON><PERSON><PERSON>", "removeDocumentPress", "onTouchMove", "onTouchStart", "addDocumentPress", "doc", "onTransitionedOut", "callback", "onTransitionEnd", "onTransitionedIn", "on", "eventType", "handler", "options", "onMouseLeave", "onBlurOrFocusOut", "removeListeners", "shouldScheduleClickHide", "isEventListenerStopped", "wasFocused", "scheduleHide", "isCursorOverReferenceOrPopper", "getNestedPopperTree", "getBoundingClientRect", "shouldBail", "relatedTarget", "createPopperInstance", "destroyPopperInstance", "computedReference", "contextElement", "tippyModifier", "enabled", "phase", "requires", "attributes", "modifiers", "padding", "adaptive", "createPopper", "mount", "append<PERSON><PERSON><PERSON>", "nextElement<PERSON><PERSON>ling", "touchValue", "touchDelay", "requestAnimationFrame", "cancelAnimationFrame", "prevProps", "nextProps", "nestedPopper", "forceUpdate", "isAlreadyVisible", "isDisabled", "isTouchAndTouchDisabled", "visibility", "transition", "offsetHeight", "isAlreadyHidden", "<PERSON><PERSON><PERSON><PERSON>", "i", "tippy", "optionalProps", "elements", "isSingleContentElement", "isMoreThanOneReferenceElement", "instances", "hide<PERSON>ll", "excludedReferenceOrInstance", "exclude", "isExcluded", "originalDuration", "applyStylesModifier", "applyStyles", "effect", "initialStyles", "position", "strategy", "margin", "assign", "styles", "createSingleton", "tippyInstances", "individualInstances", "references", "triggerTargets", "overrides", "interceptSetPropsCleanups", "shownOnCreate", "setTriggerTargets", "setReferences", "enableInstances", "interceptSetProps", "singleton", "originalSetProps", "prepareInstance", "overrideProps", "originalShow", "ref", "showNext", "first", "showPrevious", "last", "setInstances", "nextInstances", "BUBBLING_EVENTS_MAP", "mouseover", "focusin", "click", "delegate", "childTippyInstances", "disabled", "nativeProps", "parentProps", "childProps", "returnValue", "normalizedReturnValue", "targetNode", "closest", "addEventListeners", "removeEventListeners", "applyMutations", "original<PERSON><PERSON>roy", "originalEnable", "originalDisable", "shouldDestroyChildInstances", "createBackdropElement", "insertBefore", "overflow", "Number", "transitionDelay", "Math", "round", "className", "mouseCoords", "activeInstances", "storeMouseCoords", "addMouseCoordsListener", "removeMouseCoordsListener", "isInternalUpdate", "wasFocusEvent", "isUnmounted", "getIsInitialBehavior", "addListener", "removeListener", "unsetGetReferenceClientRect", "isCursorOverReference", "rect", "relativeX", "relativeY", "width", "height", "create", "data", "_", "getProps", "modifier", "cursorRectIndex", "triedPlacements", "getInlineBoundingClientRect", "getClientRects", "setInternalProps", "addModifier", "rects", "cursorRect", "currentBasePlacement", "boundingRect", "clientRects", "firstRect", "lastRect", "isTop", "minLeft", "min", "maxRight", "max", "measureRects", "getReference", "<PERSON><PERSON><PERSON><PERSON>", "prevRefRect", "prevPopRect", "updatePosition", "currentRefRect", "currentPopRect", "areRectsDifferent", "update", "rectA", "rectB"], "mappings": ";;;;;;;;;;;IAAaA,WAAW,GACtB;AAGK,IAAMC,aAAa,kBAAnB;AACA,IAAMC,cAAc,mBAApB;AACA,IAAMC,WAAW,gBAAjB;AACA,IAAMC,eAAe,oBAArB;AAEA,IAAMC,aAAa,GAAG;AAACC,EAAAA,OAAO,EAAE,IAAV;AAAgBC,EAAAA,OAAO,EAAE;AAAzB,CAAtB;AAEA,IAAMC,uBAAuB,GAAG,SAA1BA,uBAA0B;AAAA,SAAMC,QAAQ,CAACC,IAAf;AAAA,CAAhC;;ACTA,SAASC,cAAT,CACLC,GADK,EAELC,GAFK,EAGI;AACT,SAAO,GAAGF,cAAH,CAAkBG,IAAlB,CAAuBF,GAAvB,EAA4BC,GAA5B,CAAP;AACD;AAED,AAAO,SAASE,uBAAT,CACLC,KADK,EAELC,KAFK,EAGLC,YAHK,EAIF;AACH,MAAIC,KAAK,CAACC,OAAN,CAAcJ,KAAd,CAAJ,EAA0B;AACxB,QAAMK,CAAC,GAAGL,KAAK,CAACC,KAAD,CAAf;AACA,WAAOI,CAAC,IAAI,IAAL,GACHF,KAAK,CAACC,OAAN,CAAcF,YAAd,IACEA,YAAY,CAACD,KAAD,CADd,GAEEC,YAHC,GAIHG,CAJJ;AAKD;;AAED,SAAOL,KAAP;AACD;AAED,AAAO,SAASM,MAAT,CAAgBN,KAAhB,EAA4BO,IAA5B,EAAmD;AACxD,MAAMC,GAAG,GAAG,GAAGC,QAAH,CAAYX,IAAZ,CAAiBE,KAAjB,CAAZ;AACA,SAAOQ,GAAG,CAACE,OAAJ,CAAY,SAAZ,MAA2B,CAA3B,IAAgCF,GAAG,CAACE,OAAJ,CAAeH,IAAf,UAA0B,CAAC,CAAlE;AACD;AAED,AAAO,SAASI,sBAAT,CAAgCX,KAAhC,EAA4CY,IAA5C,EAA8D;AACnE,SAAO,OAAOZ,KAAP,KAAiB,UAAjB,GAA8BA,KAAK,MAAL,SAASY,IAAT,CAA9B,GAA+CZ,KAAtD;AACD;AAED,AAAO,SAASa,QAAT,CACLC,EADK,EAELC,EAFK,EAGa;AAClB;AACA,MAAIA,EAAE,KAAK,CAAX,EAAc;AACZ,WAAOD,EAAP;AACD;;AAED,MAAIE,OAAJ;AAEA,SAAO,UAACC,GAAD,EAAe;AACpBC,IAAAA,YAAY,CAACF,OAAD,CAAZ;AACAA,IAAAA,OAAO,GAAGG,UAAU,CAAC,YAAM;AACzBL,MAAAA,EAAE,CAACG,GAAD,CAAF;AACD,KAFmB,EAEjBF,EAFiB,CAApB;AAGD,GALD;AAMD;AAED,AAAO,SAASK,gBAAT,CAA6BxB,GAA7B,EAAqCyB,IAArC,EAAiE;AACtE,MAAMC,KAAK,qBAAO1B,GAAP,CAAX;AACAyB,EAAAA,IAAI,CAACE,OAAL,CAAa,UAAC1B,GAAD,EAAS;AACpB,WAAQyB,KAAD,CAAezB,GAAf,CAAP;AACD,GAFD;AAGA,SAAOyB,KAAP;AACD;AAED,AAAO,SAASE,aAAT,CAAuBxB,KAAvB,EAAgD;AACrD,SAAOA,KAAK,CAACyB,KAAN,CAAY,KAAZ,EAAmBC,MAAnB,CAA0BC,OAA1B,CAAP;AACD;AAED,AAAO,SAASC,gBAAT,CAA6B5B,KAA7B,EAAkD;AACvD,SAAQ,EAAD,CAAY6B,MAAZ,CAAmB7B,KAAnB,CAAP;AACD;AAED,AAAO,SAAS8B,YAAT,CAAyBC,GAAzB,EAAmC/B,KAAnC,EAAmD;AACxD,MAAI+B,GAAG,CAACrB,OAAJ,CAAYV,KAAZ,MAAuB,CAAC,CAA5B,EAA+B;AAC7B+B,IAAAA,GAAG,CAACC,IAAJ,CAAShC,KAAT;AACD;AACF;AAED,AAIO,SAASiC,MAAT,CAAmBF,GAAnB,EAAkC;AACvC,SAAOA,GAAG,CAACL,MAAJ,CAAW,UAACQ,IAAD,EAAOjC,KAAP;AAAA,WAAiB8B,GAAG,CAACrB,OAAJ,CAAYwB,IAAZ,MAAsBjC,KAAvC;AAAA,GAAX,CAAP;AACD;AAED,AAIO,SAASkC,gBAAT,CAA0BC,SAA1B,EAA+D;AACpE,SAAOA,SAAS,CAACX,KAAV,CAAgB,GAAhB,EAAqB,CAArB,CAAP;AACD;AAED,AAAO,SAASY,SAAT,CAAmBrC,KAAnB,EAAiD;AACtD,SAAO,GAAGsC,KAAH,CAASxC,IAAT,CAAcE,KAAd,CAAP;AACD;AAED,AAAO,SAASuC,oBAAT,CACL3C,GADK,EAE6B;AAClC,SAAO4C,MAAM,CAACnB,IAAP,CAAYzB,GAAZ,EAAiB6C,MAAjB,CAAwB,UAACC,GAAD,EAAM7C,GAAN,EAAc;AAC3C,QAAID,GAAG,CAACC,GAAD,CAAH,KAAa8C,SAAjB,EAA4B;AACzBD,MAAAA,GAAD,CAAa7C,GAAb,IAAoBD,GAAG,CAACC,GAAD,CAAvB;AACD;;AAED,WAAO6C,GAAP;AACD,GANM,EAMJ,EANI,CAAP;AAOD;;ACtGM,SAASE,GAAT,GAA+B;AACpC,SAAOnD,QAAQ,CAACoD,aAAT,CAAuB,KAAvB,CAAP;AACD;AAED,AAAO,SAASC,SAAT,CAAmB9C,KAAnB,EAAwE;AAC7E,SAAO,CAAC,SAAD,EAAY,UAAZ,EAAwB+C,IAAxB,CAA6B,UAACxC,IAAD;AAAA,WAAUD,MAAM,CAACN,KAAD,EAAQO,IAAR,CAAhB;AAAA,GAA7B,CAAP;AACD;AAED,AAAO,SAASyC,UAAT,CAAoBhD,KAApB,EAAuD;AAC5D,SAAOM,MAAM,CAACN,KAAD,EAAQ,UAAR,CAAb;AACD;AAED,AAAO,SAASiD,YAAT,CAAsBjD,KAAtB,EAA2D;AAChE,SAAOM,MAAM,CAACN,KAAD,EAAQ,YAAR,CAAb;AACD;AAED,AAAO,SAASkD,kBAAT,CAA4BlD,KAA5B,EAAmE;AACxE,SAAO,CAAC,EAAEA,KAAK,IAAIA,KAAK,CAACmD,MAAf,IAAyBnD,KAAK,CAACmD,MAAN,CAAaC,SAAb,KAA2BpD,KAAtD,CAAR;AACD;AAED,AAAO,SAASqD,kBAAT,CAA4BrD,KAA5B,EAAuD;AAC5D,MAAI8C,SAAS,CAAC9C,KAAD,CAAb,EAAsB;AACpB,WAAO,CAACA,KAAD,CAAP;AACD;;AAED,MAAIgD,UAAU,CAAChD,KAAD,CAAd,EAAuB;AACrB,WAAOqC,SAAS,CAACrC,KAAD,CAAhB;AACD;;AAED,MAAIG,KAAK,CAACC,OAAN,CAAcJ,KAAd,CAAJ,EAA0B;AACxB,WAAOA,KAAP;AACD;;AAED,SAAOqC,SAAS,CAAC5C,QAAQ,CAAC6D,gBAAT,CAA0BtD,KAA1B,CAAD,CAAhB;AACD;AAED,AAAO,SAASuD,qBAAT,CACLC,GADK,EAELxD,KAFK,EAGC;AACNwD,EAAAA,GAAG,CAACjC,OAAJ,CAAY,UAACkC,EAAD,EAAQ;AAClB,QAAIA,EAAJ,EAAQ;AACNA,MAAAA,EAAE,CAACC,KAAH,CAASC,kBAAT,GAAiC3D,KAAjC;AACD;AACF,GAJD;AAKD;AAED,AAAO,SAAS4D,kBAAT,CACLJ,GADK,EAELK,KAFK,EAGC;AACNL,EAAAA,GAAG,CAACjC,OAAJ,CAAY,UAACkC,EAAD,EAAQ;AAClB,QAAIA,EAAJ,EAAQ;AACNA,MAAAA,EAAE,CAACK,YAAH,CAAgB,YAAhB,EAA8BD,KAA9B;AACD;AACF,GAJD;AAKD;AAED,AAAO,SAASE,gBAAT,CACLC,iBADK,EAEK;AAAA;;AACV,0BAAkBpC,gBAAgB,CAACoC,iBAAD,CAAlC;AAAA,MAAOC,OAAP,wBADU;;;AAIV,SAAOA,OAAO,QAAP,6BAAAA,OAAO,CAAEC,aAAT,mCAAwBxE,IAAxB,GAA+BuE,OAAO,CAACC,aAAvC,GAAuDzE,QAA9D;AACD;AAED,AAAO,SAAS0E,gCAAT,CACLC,cADK,EAELC,KAFK,EAGI;AACT,MAAOC,OAAP,GAA2BD,KAA3B,CAAOC,OAAP;AAAA,MAAgBC,OAAhB,GAA2BF,KAA3B,CAAgBE,OAAhB;AAEA,SAAOH,cAAc,CAACI,KAAf,CAAqB,gBAAsC;AAAA,QAApCC,UAAoC,QAApCA,UAAoC;AAAA,QAAxBC,WAAwB,QAAxBA,WAAwB;AAAA,QAAXC,KAAW,QAAXA,KAAW;AAChE,QAAOC,iBAAP,GAA4BD,KAA5B,CAAOC,iBAAP;AACA,QAAMC,aAAa,GAAG1C,gBAAgB,CAACuC,WAAW,CAACtC,SAAb,CAAtC;AACA,QAAM0C,UAAU,GAAGJ,WAAW,CAACK,aAAZ,CAA0BC,MAA7C;;AAEA,QAAI,CAACF,UAAL,EAAiB;AACf,aAAO,IAAP;AACD;;AAED,QAAMG,WAAW,GAAGJ,aAAa,KAAK,QAAlB,GAA6BC,UAAU,CAACI,GAAX,CAAgBC,CAA7C,GAAiD,CAArE;AACA,QAAMC,cAAc,GAAGP,aAAa,KAAK,KAAlB,GAA0BC,UAAU,CAACO,MAAX,CAAmBF,CAA7C,GAAiD,CAAxE;AACA,QAAMG,YAAY,GAAGT,aAAa,KAAK,OAAlB,GAA4BC,UAAU,CAACS,IAAX,CAAiBC,CAA7C,GAAiD,CAAtE;AACA,QAAMC,aAAa,GAAGZ,aAAa,KAAK,MAAlB,GAA2BC,UAAU,CAACY,KAAX,CAAkBF,CAA7C,GAAiD,CAAvE;AAEA,QAAMG,UAAU,GACdlB,UAAU,CAACS,GAAX,GAAiBX,OAAjB,GAA2BU,WAA3B,GAAyCL,iBAD3C;AAEA,QAAMgB,aAAa,GACjBrB,OAAO,GAAGE,UAAU,CAACY,MAArB,GAA8BD,cAA9B,GAA+CR,iBADjD;AAEA,QAAMiB,WAAW,GACfpB,UAAU,CAACc,IAAX,GAAkBjB,OAAlB,GAA4BgB,YAA5B,GAA2CV,iBAD7C;AAEA,QAAMkB,YAAY,GAChBxB,OAAO,GAAGG,UAAU,CAACiB,KAArB,GAA6BD,aAA7B,GAA6Cb,iBAD/C;AAGA,WAAOe,UAAU,IAAIC,aAAd,IAA+BC,WAA/B,IAA8CC,YAArD;AACD,GAxBM,CAAP;AAyBD;AAED,AAAO,SAASC,2BAAT,CACLC,GADK,EAELC,MAFK,EAGLC,QAHK,EAIC;AACN,MAAMC,MAAM,GAAMF,MAAN,kBAAZ,CADM;AAMN;;AACA,GAAC,eAAD,EAAkB,qBAAlB,EAAyC1E,OAAzC,CAAiD,UAAC8C,KAAD,EAAW;AAC1D2B,IAAAA,GAAG,CAACG,MAAD,CAAH,CAAY9B,KAAZ,EAAmB6B,QAAnB;AACD,GAFD;AAGD;AAED;AACA;AACA;AACA;;AACA,AAAO,SAASE,cAAT,CAAwBC,MAAxB,EAAyCC,KAAzC,EAAkE;AACvE,MAAIC,MAAM,GAAGD,KAAb;;AACA,SAAOC,MAAP,EAAe;AAAA;;AACb,QAAIF,MAAM,CAACG,QAAP,CAAgBD,MAAhB,CAAJ,EAA6B;AAC3B,aAAO,IAAP;AACD;;AACDA,IAAAA,MAAM,GAAIA,MAAM,CAACE,WAAX,2CAAIF,MAAM,CAACE,WAAP,EAAJ,qBAAG,oBAAiCC,IAA1C;AACD;;AACD,SAAO,KAAP;AACD;;AClIM,IAAMC,YAAY,GAAG;AAACC,EAAAA,OAAO,EAAE;AAAV,CAArB;AACP,IAAIC,iBAAiB,GAAG,CAAxB;AAEA;AACA;AACA;AACA;AACA;AACA;;AACA,AAAO,SAASC,oBAAT,GAAsC;AAC3C,MAAIH,YAAY,CAACC,OAAjB,EAA0B;AACxB;AACD;;AAEDD,EAAAA,YAAY,CAACC,OAAb,GAAuB,IAAvB;;AAEA,MAAIG,MAAM,CAACC,WAAX,EAAwB;AACtBvH,IAAAA,QAAQ,CAACwH,gBAAT,CAA0B,WAA1B,EAAuCC,mBAAvC;AACD;AACF;AAED;AACA;AACA;AACA;AACA;;AACA,AAAO,SAASA,mBAAT,GAAqC;AAC1C,MAAMC,GAAG,GAAGH,WAAW,CAACG,GAAZ,EAAZ;;AAEA,MAAIA,GAAG,GAAGN,iBAAN,GAA0B,EAA9B,EAAkC;AAChCF,IAAAA,YAAY,CAACC,OAAb,GAAuB,KAAvB;AAEAnH,IAAAA,QAAQ,CAAC2H,mBAAT,CAA6B,WAA7B,EAA0CF,mBAA1C;AACD;;AAEDL,EAAAA,iBAAiB,GAAGM,GAApB;AACD;AAED;AACA;AACA;AACA;AACA;AACA;;AACA,AAAO,SAASE,YAAT,GAA8B;AACnC,MAAMC,aAAa,GAAG7H,QAAQ,CAAC6H,aAA/B;;AAEA,MAAIpE,kBAAkB,CAACoE,aAAD,CAAtB,EAAuC;AACrC,QAAMC,QAAQ,GAAGD,aAAa,CAACnE,MAA/B;;AAEA,QAAImE,aAAa,CAACE,IAAd,IAAsB,CAACD,QAAQ,CAAC1D,KAAT,CAAe4D,SAA1C,EAAqD;AACnDH,MAAAA,aAAa,CAACE,IAAd;AACD;AACF;AACF;AAED,AAAe,SAASE,wBAAT,GAA0C;AACvDjI,EAAAA,QAAQ,CAACwH,gBAAT,CAA0B,YAA1B,EAAwCH,oBAAxC,EAA8DzH,aAA9D;AACA0H,EAAAA,MAAM,CAACE,gBAAP,CAAwB,MAAxB,EAAgCI,YAAhC;AACD;;AC9DM,IAAMM,SAAS,GACpB,OAAOZ,MAAP,KAAkB,WAAlB,IAAiC,OAAOtH,QAAP,KAAoB,WADhD;AAGP,AAAO,IAAMmI,MAAM,GAAGD,SAAS;AAE3B,CAAC,CAACZ,MAAM,CAACc,QAFkB,GAG3B,KAHG;;ACDA,SAASC,uBAAT,CAAiC3B,MAAjC,EAAyD;AAC9D,MAAM4B,GAAG,GAAG5B,MAAM,KAAK,SAAX,GAAuB,YAAvB,GAAsC,GAAlD;AAEA,SAAO,CACFA,MADE,0BACyB4B,GADzB,8CAEL,oCAFK,EAGLC,IAHK,CAGA,GAHA,CAAP;AAID;AAED,AAAO,SAASC,KAAT,CAAejI,KAAf,EAAsC;AAC3C,MAAMkI,aAAa,GAAG,YAAtB;AACA,MAAMC,mBAAmB,GAAG,WAA5B;AAEA,SAAOnI,KAAK,CACToI,OADI,CACIF,aADJ,EACmB,GADnB,EAEJE,OAFI,CAEID,mBAFJ,EAEyB,EAFzB,EAGJE,IAHI,EAAP;AAID;;AAED,SAASC,aAAT,CAAuBC,OAAvB,EAAgD;AAC9C,SAAON,KAAK,4BAGRA,KAAK,CAACM,OAAD,CAHG,0GAAZ;AAOD;;AAED,AAAO,SAASC,mBAAT,CAA6BD,OAA7B,EAAwD;AAC7D,SAAO,CACLD,aAAa,CAACC,OAAD,CADR;AAGL,wDAHK;AAKL,oBALK;AAOL,mBAPK,CAAP;AASD;;AAGD,IAAIE,eAAJ;;AACA,2CAAa;AACXC,EAAAA,oBAAoB;AACrB;;AAED,AAAO,SAASA,oBAAT,GAAsC;AAC3CD,EAAAA,eAAe,GAAG,IAAIE,GAAJ,EAAlB;AACD;AAED,AAAO,SAASC,QAAT,CAAkBC,SAAlB,EAAsCN,OAAtC,EAA6D;AAClE,MAAIM,SAAS,IAAI,CAACJ,eAAe,CAACK,GAAhB,CAAoBP,OAApB,CAAlB,EAAgD;AAAA;;AAC9CE,IAAAA,eAAe,CAACM,GAAhB,CAAoBR,OAApB;;AACA,gBAAAS,OAAO,EAACC,IAAR,iBAAgBT,mBAAmB,CAACD,OAAD,CAAnC;AACD;AACF;AAED,AAAO,SAASW,SAAT,CAAmBL,SAAnB,EAAuCN,OAAvC,EAA8D;AACnE,MAAIM,SAAS,IAAI,CAACJ,eAAe,CAACK,GAAhB,CAAoBP,OAApB,CAAlB,EAAgD;AAAA;;AAC9CE,IAAAA,eAAe,CAACM,GAAhB,CAAoBR,OAApB;;AACA,iBAAAS,OAAO,EAACG,KAAR,kBAAiBX,mBAAmB,CAACD,OAAD,CAApC;AACD;AACF;AAED,AAAO,SAASa,eAAT,CAAyBC,OAAzB,EAAiD;AACtD,MAAMC,iBAAiB,GAAG,CAACD,OAA3B;AACA,MAAME,kBAAkB,GACtB/G,MAAM,CAACgH,SAAP,CAAiB/I,QAAjB,CAA0BX,IAA1B,CAA+BuJ,OAA/B,MAA4C,iBAA5C,IACA,CAAEA,OAAD,CAAiBpC,gBAFpB;AAIAiC,EAAAA,SAAS,CACPI,iBADO,EAEP,CACE,oBADF,EAEE,MAAMG,MAAM,CAACJ,OAAD,CAAZ,GAAwB,GAF1B,EAGE,oEAHF,EAIE,yBAJF,EAKErB,IALF,CAKO,GALP,CAFO,CAAT;AAUAkB,EAAAA,SAAS,CACPK,kBADO,EAEP,CACE,yEADF,EAEE,oEAFF,EAGEvB,IAHF,CAGO,GAHP,CAFO,CAAT;AAOD;;ACjFD,IAAM0B,WAAW,GAAG;AAClBC,EAAAA,WAAW,EAAE,KADK;AAElBC,EAAAA,YAAY,EAAE,KAFI;AAGlBC,EAAAA,iBAAiB,EAAE,KAHD;AAIlBC,EAAAA,MAAM,EAAE;AAJU,CAApB;AAOA,IAAMC,WAAW,GAAG;AAClBC,EAAAA,SAAS,EAAE,KADO;AAElBC,EAAAA,SAAS,EAAE,MAFO;AAGlBC,EAAAA,KAAK,EAAE,IAHW;AAIlBC,EAAAA,OAAO,EAAE,EAJS;AAKlBC,EAAAA,OAAO,EAAE,KALS;AAMlBC,EAAAA,QAAQ,EAAE,GANQ;AAOlBC,EAAAA,IAAI,EAAE,SAPY;AAQlBC,EAAAA,KAAK,EAAE,EARW;AASlBC,EAAAA,MAAM,EAAE;AATU,CAApB;AAYA,AAAO,IAAMC,YAA0B;AACrCC,EAAAA,QAAQ,EAAElL,uBAD2B;AAErCmL,EAAAA,IAAI,EAAE;AACJR,IAAAA,OAAO,EAAE,MADL;AAEJS,IAAAA,QAAQ,EAAE;AAFN,GAF+B;AAMrCC,EAAAA,KAAK,EAAE,CAN8B;AAOrCC,EAAAA,QAAQ,EAAE,CAAC,GAAD,EAAM,GAAN,CAP2B;AAQrCC,EAAAA,sBAAsB,EAAE,IARa;AASrCC,EAAAA,WAAW,EAAE,IATwB;AAUrCC,EAAAA,gBAAgB,EAAE,KAVmB;AAWrCC,EAAAA,WAAW,EAAE,KAXwB;AAYrCtG,EAAAA,iBAAiB,EAAE,CAZkB;AAarCuG,EAAAA,mBAAmB,EAAE,CAbgB;AAcrCC,EAAAA,cAAc,EAAE,EAdqB;AAerCpG,EAAAA,MAAM,EAAE,CAAC,CAAD,EAAI,EAAJ,CAf6B;AAgBrCqG,EAAAA,aAhBqC,2BAgBrB,EAhBqB;AAiBrCC,EAAAA,cAjBqC,4BAiBpB,EAjBoB;AAkBrCC,EAAAA,QAlBqC,sBAkB1B,EAlB0B;AAmBrCC,EAAAA,SAnBqC,uBAmBzB,EAnByB;AAoBrCC,EAAAA,QApBqC,sBAoB1B,EApB0B;AAqBrCC,EAAAA,MArBqC,oBAqB5B,EArB4B;AAsBrCC,EAAAA,OAtBqC,qBAsB3B,EAtB2B;AAuBrCC,EAAAA,MAvBqC,oBAuB5B,EAvB4B;AAwBrCC,EAAAA,OAxBqC,qBAwB3B,EAxB2B;AAyBrCC,EAAAA,SAzBqC,uBAyBzB,EAzByB;AA0BrCC,EAAAA,WA1BqC,yBA0BvB,EA1BuB;AA2BrCC,EAAAA,cA3BqC,4BA2BpB,EA3BoB;AA4BrC5J,EAAAA,SAAS,EAAE,KA5B0B;AA6BrC6J,EAAAA,OAAO,EAAE,EA7B4B;AA8BrCC,EAAAA,aAAa,EAAE,EA9BsB;AA+BrCC,EAAAA,MAAM,EAAE,IA/B6B;AAgCrCC,EAAAA,YAAY,EAAE,KAhCuB;AAiCrCC,EAAAA,KAAK,EAAE,IAjC8B;AAkCrCC,EAAAA,OAAO,EAAE,kBAlC4B;AAmCrCC,EAAAA,aAAa,EAAE;AAnCsB,GAoClC7C,WApCkC,EAqClCK,WArCkC,CAAhC;AAwCP,IAAMyC,WAAW,GAAGhK,MAAM,CAACnB,IAAP,CAAYoJ,YAAZ,CAApB;AAEA,AAAO,IAAMgC,eAAyC,GAAG,SAA5CA,eAA4C,CAACC,YAAD,EAAkB;AACzE;AACA,6CAAa;AACXC,IAAAA,aAAa,CAACD,YAAD,EAAe,EAAf,CAAb;AACD;;AAED,MAAMrL,IAAI,GAAGmB,MAAM,CAACnB,IAAP,CAAYqL,YAAZ,CAAb;AACArL,EAAAA,IAAI,CAACE,OAAL,CAAa,UAAC1B,GAAD,EAAS;AACnB4K,IAAAA,YAAD,CAAsB5K,GAAtB,IAA6B6M,YAAY,CAAC7M,GAAD,CAAzC;AACD,GAFD;AAGD,CAVM;AAYP,AAAO,SAAS+M,sBAAT,CACLC,WADK,EAEW;AAChB,MAAMZ,OAAO,GAAGY,WAAW,CAACZ,OAAZ,IAAuB,EAAvC;AACA,MAAMvC,WAAW,GAAGuC,OAAO,CAACxJ,MAAR,CAAwC,UAACC,GAAD,EAAMoK,MAAN,EAAiB;AAC3E,QAAOC,IAAP,GAA6BD,MAA7B,CAAOC,IAAP;AAAA,QAAa7M,YAAb,GAA6B4M,MAA7B,CAAa5M,YAAb;;AAEA,QAAI6M,IAAJ,EAAU;AAAA;;AACRrK,MAAAA,GAAG,CAACqK,IAAD,CAAH,GACEF,WAAW,CAACE,IAAD,CAAX,KAAsBpK,SAAtB,GACIkK,WAAW,CAACE,IAAD,CADf,YAEKtC,YAAD,CAAsBsC,IAAtB,CAFJ,oBAEmC7M,YAHrC;AAID;;AAED,WAAOwC,GAAP;AACD,GAXmB,EAWjB,EAXiB,CAApB;AAaA,2BACKmK,WADL,EAEKnD,WAFL;AAID;AAED,AAAO,SAASsD,qBAAT,CACL5J,SADK,EAEL6I,OAFK,EAGoB;AACzB,MAAMgB,QAAQ,GAAGhB,OAAO,GACpBzJ,MAAM,CAACnB,IAAP,CAAYuL,sBAAsB,mBAAKnC,YAAL;AAAmBwB,IAAAA,OAAO,EAAPA;AAAnB,KAAlC,CADoB,GAEpBO,WAFJ;AAIA,MAAM7H,KAAK,GAAGsI,QAAQ,CAACxK,MAAT,CACZ,UAACC,GAAD,EAAgD7C,GAAhD,EAAwD;AACtD,QAAMqN,aAAa,GAAG,CACpB9J,SAAS,CAAC+J,YAAV,iBAAqCtN,GAArC,KAA+C,EAD3B,EAEpBwI,IAFoB,EAAtB;;AAIA,QAAI,CAAC6E,aAAL,EAAoB;AAClB,aAAOxK,GAAP;AACD;;AAED,QAAI7C,GAAG,KAAK,SAAZ,EAAuB;AACrB6C,MAAAA,GAAG,CAAC7C,GAAD,CAAH,GAAWqN,aAAX;AACD,KAFD,MAEO;AACL,UAAI;AACFxK,QAAAA,GAAG,CAAC7C,GAAD,CAAH,GAAWuN,IAAI,CAACC,KAAL,CAAWH,aAAX,CAAX;AACD,OAFD,CAEE,OAAOI,CAAP,EAAU;AACV5K,QAAAA,GAAG,CAAC7C,GAAD,CAAH,GAAWqN,aAAX;AACD;AACF;;AAED,WAAOxK,GAAP;AACD,GArBW,EAsBZ,EAtBY,CAAd;AAyBA,SAAOiC,KAAP;AACD;AAED,AAAO,SAAS4I,aAAT,CACLnK,SADK,EAELuB,KAFK,EAGE;AACP,MAAM6I,GAAG,qBACJ7I,KADI;AAEPwF,IAAAA,OAAO,EAAExJ,sBAAsB,CAACgE,KAAK,CAACwF,OAAP,EAAgB,CAAC/G,SAAD,CAAhB;AAFxB,KAGHuB,KAAK,CAACsG,gBAAN,GACA,EADA,GAEA+B,qBAAqB,CAAC5J,SAAD,EAAYuB,KAAK,CAACsH,OAAlB,CALlB,CAAT;AAQAuB,EAAAA,GAAG,CAAC7C,IAAJ,qBACKF,YAAY,CAACE,IADlB,EAEK6C,GAAG,CAAC7C,IAFT;AAKA6C,EAAAA,GAAG,CAAC7C,IAAJ,GAAW;AACTC,IAAAA,QAAQ,EACN4C,GAAG,CAAC7C,IAAJ,CAASC,QAAT,KAAsB,MAAtB,GAA+BjG,KAAK,CAACuG,WAArC,GAAmDsC,GAAG,CAAC7C,IAAJ,CAASC,QAFrD;AAGTT,IAAAA,OAAO,EACLqD,GAAG,CAAC7C,IAAJ,CAASR,OAAT,KAAqB,MAArB,GACIxF,KAAK,CAACuG,WAAN,GACE,IADF,GAEE,aAHN,GAIIsC,GAAG,CAAC7C,IAAJ,CAASR;AARN,GAAX;AAWA,SAAOqD,GAAP;AACD;AAED,AAAO,SAASb,aAAT,CACLD,YADK,EAELT,OAFK,EAGC;AAAA,MAFNS,YAEM;AAFNA,IAAAA,YAEM,GAFyB,EAEzB;AAAA;;AAAA,MADNT,OACM;AADNA,IAAAA,OACM,GADc,EACd;AAAA;;AACN,MAAM5K,IAAI,GAAGmB,MAAM,CAACnB,IAAP,CAAYqL,YAAZ,CAAb;AACArL,EAAAA,IAAI,CAACE,OAAL,CAAa,UAACkM,IAAD,EAAU;AACrB,QAAMC,cAAc,GAAGtM,gBAAgB,CACrCqJ,YADqC,EAErCjI,MAAM,CAACnB,IAAP,CAAYqI,WAAZ,CAFqC,CAAvC;AAKA,QAAIiE,kBAAkB,GAAG,CAAChO,cAAc,CAAC+N,cAAD,EAAiBD,IAAjB,CAAxC,CANqB;;AASrB,QAAIE,kBAAJ,EAAwB;AACtBA,MAAAA,kBAAkB,GAChB1B,OAAO,CAACvK,MAAR,CAAe,UAACoL,MAAD;AAAA,eAAYA,MAAM,CAACC,IAAP,KAAgBU,IAA5B;AAAA,OAAf,EAAiDG,MAAjD,KAA4D,CAD9D;AAED;;AAEDhF,IAAAA,QAAQ,CACN+E,kBADM,EAEN,OACOF,IADP,QAEE,sEAFF,EAGE,2DAHF,EAIE,MAJF,EAKE,8DALF,EAME,wDANF,EAOEzF,IAPF,CAOO,GAPP,CAFM,CAAR;AAWD,GAzBD;AA0BD;;ACzJM,SAAS6F,WAAT,CAAqBC,MAArB,EAA4D;AACjE,MAAM9H,GAAG,GAAG8H,MAAM,CAACC,iBAAnB;AACA,MAAMC,WAAW,GAAG3L,SAAS,CAAC2D,GAAG,CAACiI,QAAL,CAA7B;AAEA,SAAO;AACLjI,IAAAA,GAAG,EAAHA,GADK;AAELmE,IAAAA,OAAO,EAAE6D,WAAW,CAACE,IAAZ,CAAiB,UAACC,IAAD;AAAA,aAAUA,IAAI,CAACC,SAAL,CAAe5H,QAAf,CAAwBvH,aAAxB,CAAV;AAAA,KAAjB,CAFJ;AAGLiL,IAAAA,KAAK,EAAE8D,WAAW,CAACE,IAAZ,CACL,UAACC,IAAD;AAAA,aACEA,IAAI,CAACC,SAAL,CAAe5H,QAAf,CAAwBrH,WAAxB,KACAgP,IAAI,CAACC,SAAL,CAAe5H,QAAf,CAAwBpH,eAAxB,CAFF;AAAA,KADK,CAHF;AAQLiP,IAAAA,QAAQ,EAAEL,WAAW,CAACE,IAAZ,CAAiB,UAACC,IAAD;AAAA,aACzBA,IAAI,CAACC,SAAL,CAAe5H,QAAf,CAAwBtH,cAAxB,CADyB;AAAA,KAAjB;AARL,GAAP;AAYD;;AC5BD,IAAIoP,SAAS,GAAG,CAAhB;AACA,IAAIC,kBAAmD,GAAG,EAA1D;;AAGA,AAAO,IAAIC,gBAA4B,GAAG,EAAnC;AAEP,AAAe,SAASC,WAAT,CACbrL,SADa,EAEbyJ,WAFa,EAGH;AACV,MAAMlI,KAAK,GAAG4I,aAAa,CAACnK,SAAD,oBACtBqH,YADsB,EAEtBmC,sBAAsB,CAACrK,oBAAoB,CAACsK,WAAD,CAArB,CAFA,EAA3B,CADU;AAOV;AACA;;AACA,MAAI6B,WAAJ;AACA,MAAIC,WAAJ;AACA,MAAIC,0BAAJ;AACA,MAAIC,kBAAkB,GAAG,KAAzB;AACA,MAAIC,6BAA6B,GAAG,KAApC;AACA,MAAIC,YAAY,GAAG,KAAnB;AACA,MAAIC,mBAAmB,GAAG,KAA1B;AACA,MAAIC,gBAAJ;AACA,MAAIC,4BAAJ;AACA,MAAIC,aAAJ;AACA,MAAIC,SAA2B,GAAG,EAAlC;AACA,MAAIC,oBAAoB,GAAGxO,QAAQ,CAACyO,WAAD,EAAc3K,KAAK,CAACwG,mBAApB,CAAnC;AACA,MAAIoE,aAAJ,CArBU;AAwBV;AACA;;AACA,MAAMC,EAAE,GAAGlB,SAAS,EAApB;AACA,MAAMmB,cAAc,GAAG,IAAvB;AACA,MAAMxD,OAAO,GAAGhK,MAAM,CAAC0C,KAAK,CAACsH,OAAP,CAAtB;AAEA,MAAMpI,KAAK,GAAG;AACZ;AACA6L,IAAAA,SAAS,EAAE,IAFC;AAGZ;AACAjI,IAAAA,SAAS,EAAE,KAJC;AAKZ;AACAkI,IAAAA,WAAW,EAAE,KAND;AAOZ;AACAC,IAAAA,SAAS,EAAE,KARC;AASZ;AACAC,IAAAA,OAAO,EAAE;AAVG,GAAd;AAaA,MAAMtI,QAAkB,GAAG;AACzB;AACAiI,IAAAA,EAAE,EAAFA,EAFyB;AAGzBpM,IAAAA,SAAS,EAATA,SAHyB;AAIzB0K,IAAAA,MAAM,EAAElL,GAAG,EAJc;AAKzB6M,IAAAA,cAAc,EAAdA,cALyB;AAMzB9K,IAAAA,KAAK,EAALA,KANyB;AAOzBd,IAAAA,KAAK,EAALA,KAPyB;AAQzBoI,IAAAA,OAAO,EAAPA,OARyB;AASzB;AACA6D,IAAAA,kBAAkB,EAAlBA,kBAVyB;AAWzBC,IAAAA,QAAQ,EAARA,QAXyB;AAYzBC,IAAAA,UAAU,EAAVA,UAZyB;AAazBC,IAAAA,IAAI,EAAJA,IAbyB;AAczBC,IAAAA,IAAI,EAAJA,IAdyB;AAezBC,IAAAA,qBAAqB,EAArBA,qBAfyB;AAgBzBC,IAAAA,MAAM,EAANA,MAhByB;AAiBzBC,IAAAA,OAAO,EAAPA,OAjByB;AAkBzBC,IAAAA,OAAO,EAAPA,OAlByB;AAmBzBC,IAAAA,OAAO,EAAPA;AAnByB,GAA3B,CA3CU;AAkEV;;AACA;;AACA,MAAI,CAAC5L,KAAK,CAACwH,MAAX,EAAmB;AACjB,+CAAa;AACXjD,MAAAA,SAAS,CAAC,IAAD,EAAO,0CAAP,CAAT;AACD;;AAED,WAAO3B,QAAP;AACD,GA1ES;AA6EV;AACA;;;AACA,sBAA2B5C,KAAK,CAACwH,MAAN,CAAa5E,QAAb,CAA3B;AAAA,MAAOuG,MAAP,iBAAOA,MAAP;AAAA,MAAe0C,QAAf,iBAAeA,QAAf;;AAEA1C,EAAAA,MAAM,CAAChK,YAAP,CAAoB,iBAApB,EAAsD,EAAtD;AACAgK,EAAAA,MAAM,CAAC0B,EAAP,cAAoCjI,QAAQ,CAACiI,EAA7C;AAEAjI,EAAAA,QAAQ,CAACuG,MAAT,GAAkBA,MAAlB;AACA1K,EAAAA,SAAS,CAACD,MAAV,GAAmBoE,QAAnB;AACAuG,EAAAA,MAAM,CAAC3K,MAAP,GAAgBoE,QAAhB;AAEA,MAAMkJ,YAAY,GAAGxE,OAAO,CAACyE,GAAR,CAAY,UAAC5D,MAAD;AAAA,WAAYA,MAAM,CAAChM,EAAP,CAAUyG,QAAV,CAAZ;AAAA,GAAZ,CAArB;AACA,MAAMoJ,eAAe,GAAGvN,SAAS,CAACwN,YAAV,CAAuB,eAAvB,CAAxB;AAEAC,EAAAA,YAAY;AACZC,EAAAA,2BAA2B;AAC3BC,EAAAA,YAAY;AAEZC,EAAAA,UAAU,CAAC,UAAD,EAAa,CAACzJ,QAAD,CAAb,CAAV;;AAEA,MAAI5C,KAAK,CAACyH,YAAV,EAAwB;AACtB6E,IAAAA,YAAY;AACb,GAnGS;AAsGV;;;AACAnD,EAAAA,MAAM,CAAC7G,gBAAP,CAAwB,YAAxB,EAAsC,YAAM;AAC1C,QAAIM,QAAQ,CAAC5C,KAAT,CAAeuG,WAAf,IAA8B3D,QAAQ,CAAC1D,KAAT,CAAe4D,SAAjD,EAA4D;AAC1DF,MAAAA,QAAQ,CAACuI,kBAAT;AACD;AACF,GAJD;AAMAhC,EAAAA,MAAM,CAAC7G,gBAAP,CAAwB,YAAxB,EAAsC,YAAM;AAC1C,QACEM,QAAQ,CAAC5C,KAAT,CAAeuG,WAAf,IACA3D,QAAQ,CAAC5C,KAAT,CAAe2H,OAAf,CAAuB5L,OAAvB,CAA+B,YAA/B,KAAgD,CAFlD,EAGE;AACAwQ,MAAAA,WAAW,GAAGjK,gBAAd,CAA+B,WAA/B,EAA4CoI,oBAA5C;AACD;AACF,GAPD;AASA,SAAO9H,QAAP,CAtHU;AAyHV;AACA;;AACA,WAAS4J,0BAAT,GAAkE;AAChE,QAAO9E,KAAP,GAAgB9E,QAAQ,CAAC5C,KAAzB,CAAO0H,KAAP;AACA,WAAOlM,KAAK,CAACC,OAAN,CAAciM,KAAd,IAAuBA,KAAvB,GAA+B,CAACA,KAAD,EAAQ,CAAR,CAAtC;AACD;;AAED,WAAS+E,wBAAT,GAA6C;AAC3C,WAAOD,0BAA0B,GAAG,CAAH,CAA1B,KAAoC,MAA3C;AACD;;AAED,WAASE,oBAAT,GAAyC;AAAA;;AACvC;AACA,WAAO,CAAC,2BAAC9J,QAAQ,CAAC5C,KAAT,CAAewH,MAAhB,aAAC,sBAAuBmF,OAAxB,CAAR;AACD;;AAED,WAASC,gBAAT,GAAqC;AACnC,WAAOhC,aAAa,IAAInM,SAAxB;AACD;;AAED,WAAS8N,WAAT,GAAiC;AAC/B,QAAM7K,MAAM,GAAGkL,gBAAgB,GAAGC,UAAlC;AACA,WAAOnL,MAAM,GAAGtC,gBAAgB,CAACsC,MAAD,CAAnB,GAA8B5G,QAA3C;AACD;;AAED,WAASgS,0BAAT,GAAsD;AACpD,WAAO5D,WAAW,CAACC,MAAD,CAAlB;AACD;;AAED,WAAS4D,QAAT,CAAkBC,MAAlB,EAA2C;AACzC;AACA;AACA;AACA,QACGpK,QAAQ,CAAC1D,KAAT,CAAe+L,SAAf,IAA4B,CAACrI,QAAQ,CAAC1D,KAAT,CAAe4D,SAA7C,IACAd,YAAY,CAACC,OADb,IAECqI,gBAAgB,IAAIA,gBAAgB,CAAC1O,IAAjB,KAA0B,OAHjD,EAIE;AACA,aAAO,CAAP;AACD;;AAED,WAAOR,uBAAuB,CAC5BwH,QAAQ,CAAC5C,KAAT,CAAekG,KADa,EAE5B8G,MAAM,GAAG,CAAH,GAAO,CAFe,EAG5BlH,YAAY,CAACI,KAHe,CAA9B;AAKD;;AAED,WAASkG,YAAT,CAAsBa,QAAtB,EAA8C;AAAA,QAAxBA,QAAwB;AAAxBA,MAAAA,QAAwB,GAAb,KAAa;AAAA;;AAC5C9D,IAAAA,MAAM,CAACpK,KAAP,CAAamO,aAAb,GACEtK,QAAQ,CAAC5C,KAAT,CAAeuG,WAAf,IAA8B,CAAC0G,QAA/B,GAA0C,EAA1C,GAA+C,MADjD;AAEA9D,IAAAA,MAAM,CAACpK,KAAP,CAAa8G,MAAb,QAAyBjD,QAAQ,CAAC5C,KAAT,CAAe6F,MAAxC;AACD;;AAED,WAASwG,UAAT,CACEc,IADF,EAEElR,IAFF,EAGEmR,qBAHF,EAIQ;AAAA,QADNA,qBACM;AADNA,MAAAA,qBACM,GADkB,IAClB;AAAA;;AACNtB,IAAAA,YAAY,CAAClP,OAAb,CAAqB,UAACyQ,WAAD,EAAiB;AACpC,UAAIA,WAAW,CAACF,IAAD,CAAf,EAAuB;AACrBE,QAAAA,WAAW,CAACF,IAAD,CAAX,OAAAE,WAAW,EAAWpR,IAAX,CAAX;AACD;AACF,KAJD;;AAMA,QAAImR,qBAAJ,EAA2B;AAAA;;AACzB,yBAAAxK,QAAQ,CAAC5C,KAAT,EAAemN,IAAf,yBAAwBlR,IAAxB;AACD;AACF;;AAED,WAASqR,0BAAT,GAA4C;AAC1C,QAAOtH,IAAP,GAAepD,QAAQ,CAAC5C,KAAxB,CAAOgG,IAAP;;AAEA,QAAI,CAACA,IAAI,CAACR,OAAV,EAAmB;AACjB;AACD;;AAED,QAAM+H,IAAI,aAAWvH,IAAI,CAACR,OAA1B;AACA,QAAMqF,EAAE,GAAG1B,MAAM,CAAC0B,EAAlB;AACA,QAAM2C,KAAK,GAAGvQ,gBAAgB,CAAC2F,QAAQ,CAAC5C,KAAT,CAAe4H,aAAf,IAAgCnJ,SAAjC,CAA9B;AAEA+O,IAAAA,KAAK,CAAC5Q,OAAN,CAAc,UAAC4M,IAAD,EAAU;AACtB,UAAMiE,YAAY,GAAGjE,IAAI,CAAChB,YAAL,CAAkB+E,IAAlB,CAArB;;AAEA,UAAI3K,QAAQ,CAAC1D,KAAT,CAAe4D,SAAnB,EAA8B;AAC5B0G,QAAAA,IAAI,CAACrK,YAAL,CAAkBoO,IAAlB,EAAwBE,YAAY,GAAMA,YAAN,SAAsB5C,EAAtB,GAA6BA,EAAjE;AACD,OAFD,MAEO;AACL,YAAM6C,SAAS,GAAGD,YAAY,IAAIA,YAAY,CAAChK,OAAb,CAAqBoH,EAArB,EAAyB,EAAzB,EAA6BnH,IAA7B,EAAlC;;AAEA,YAAIgK,SAAJ,EAAe;AACblE,UAAAA,IAAI,CAACrK,YAAL,CAAkBoO,IAAlB,EAAwBG,SAAxB;AACD,SAFD,MAEO;AACLlE,UAAAA,IAAI,CAACmE,eAAL,CAAqBJ,IAArB;AACD;AACF;AACF,KAdD;AAeD;;AAED,WAASpB,2BAAT,GAA6C;AAC3C,QAAIH,eAAe,IAAI,CAACpJ,QAAQ,CAAC5C,KAAT,CAAegG,IAAf,CAAoBC,QAA5C,EAAsD;AACpD;AACD;;AAED,QAAMuH,KAAK,GAAGvQ,gBAAgB,CAAC2F,QAAQ,CAAC5C,KAAT,CAAe4H,aAAf,IAAgCnJ,SAAjC,CAA9B;AAEA+O,IAAAA,KAAK,CAAC5Q,OAAN,CAAc,UAAC4M,IAAD,EAAU;AACtB,UAAI5G,QAAQ,CAAC5C,KAAT,CAAeuG,WAAnB,EAAgC;AAC9BiD,QAAAA,IAAI,CAACrK,YAAL,CACE,eADF,EAEEyD,QAAQ,CAAC1D,KAAT,CAAe4D,SAAf,IAA4B0G,IAAI,KAAKoD,gBAAgB,EAArD,GACI,MADJ,GAEI,OAJN;AAMD,OAPD,MAOO;AACLpD,QAAAA,IAAI,CAACmE,eAAL,CAAqB,eAArB;AACD;AACF,KAXD;AAYD;;AAED,WAASC,gCAAT,GAAkD;AAChDrB,IAAAA,WAAW,GAAG9J,mBAAd,CAAkC,WAAlC,EAA+CiI,oBAA/C;AACAd,IAAAA,kBAAkB,GAAGA,kBAAkB,CAAC7M,MAAnB,CACnB,UAACwE,QAAD;AAAA,aAAcA,QAAQ,KAAKmJ,oBAA3B;AAAA,KADmB,CAArB;AAGD;;AAED,WAASmD,eAAT,CAAyBnO,KAAzB,EAA+D;AAC7D;AACA,QAAIsC,YAAY,CAACC,OAAjB,EAA0B;AACxB,UAAImI,YAAY,IAAI1K,KAAK,CAAC9D,IAAN,KAAe,WAAnC,EAAgD;AAC9C;AACD;AACF;;AAED,QAAMkS,YAAY,GACfpO,KAAK,CAACqO,YAAN,IAAsBrO,KAAK,CAACqO,YAAN,GAAqB,CAArB,CAAvB,IAAmDrO,KAAK,CAACkC,MAD3D,CAR6D;;AAY7D,QACEgB,QAAQ,CAAC5C,KAAT,CAAeuG,WAAf,IACA9E,cAAc,CAAC0H,MAAD,EAAS2E,YAAT,CAFhB,EAGE;AACA;AACD,KAjB4D;;;AAoB7D,QACE7Q,gBAAgB,CAAC2F,QAAQ,CAAC5C,KAAT,CAAe4H,aAAf,IAAgCnJ,SAAjC,CAAhB,CAA4DL,IAA5D,CAAiE,UAACU,EAAD;AAAA,aAC/D2C,cAAc,CAAC3C,EAAD,EAAKgP,YAAL,CADiD;AAAA,KAAjE,CADF,EAIE;AACA,UAAI9L,YAAY,CAACC,OAAjB,EAA0B;AACxB;AACD;;AAED,UACEW,QAAQ,CAAC1D,KAAT,CAAe4D,SAAf,IACAF,QAAQ,CAAC5C,KAAT,CAAe2H,OAAf,CAAuB5L,OAAvB,CAA+B,OAA/B,KAA2C,CAF7C,EAGE;AACA;AACD;AACF,KAfD,MAeO;AACLsQ,MAAAA,UAAU,CAAC,gBAAD,EAAmB,CAACzJ,QAAD,EAAWlD,KAAX,CAAnB,CAAV;AACD;;AAED,QAAIkD,QAAQ,CAAC5C,KAAT,CAAeqG,WAAf,KAA+B,IAAnC,EAAyC;AACvCzD,MAAAA,QAAQ,CAACuI,kBAAT;AACAvI,MAAAA,QAAQ,CAAC2I,IAAT,GAFuC;AAKvC;AACA;;AACApB,MAAAA,6BAA6B,GAAG,IAAhC;AACA3N,MAAAA,UAAU,CAAC,YAAM;AACf2N,QAAAA,6BAA6B,GAAG,KAAhC;AACD,OAFS,CAAV,CARuC;AAavC;AACA;;AACA,UAAI,CAACvH,QAAQ,CAAC1D,KAAT,CAAe+L,SAApB,EAA+B;AAC7B+C,QAAAA,mBAAmB;AACpB;AACF;AACF;;AAED,WAASC,WAAT,GAA6B;AAC3B7D,IAAAA,YAAY,GAAG,IAAf;AACD;;AAED,WAAS8D,YAAT,GAA8B;AAC5B9D,IAAAA,YAAY,GAAG,KAAf;AACD;;AAED,WAAS+D,gBAAT,GAAkC;AAChC,QAAMC,GAAG,GAAG7B,WAAW,EAAvB;AACA6B,IAAAA,GAAG,CAAC9L,gBAAJ,CAAqB,WAArB,EAAkCuL,eAAlC,EAAmD,IAAnD;AACAO,IAAAA,GAAG,CAAC9L,gBAAJ,CAAqB,UAArB,EAAiCuL,eAAjC,EAAkDnT,aAAlD;AACA0T,IAAAA,GAAG,CAAC9L,gBAAJ,CAAqB,YAArB,EAAmC4L,YAAnC,EAAiDxT,aAAjD;AACA0T,IAAAA,GAAG,CAAC9L,gBAAJ,CAAqB,WAArB,EAAkC2L,WAAlC,EAA+CvT,aAA/C;AACD;;AAED,WAASsT,mBAAT,GAAqC;AACnC,QAAMI,GAAG,GAAG7B,WAAW,EAAvB;AACA6B,IAAAA,GAAG,CAAC3L,mBAAJ,CAAwB,WAAxB,EAAqCoL,eAArC,EAAsD,IAAtD;AACAO,IAAAA,GAAG,CAAC3L,mBAAJ,CAAwB,UAAxB,EAAoCoL,eAApC,EAAqDnT,aAArD;AACA0T,IAAAA,GAAG,CAAC3L,mBAAJ,CAAwB,YAAxB,EAAsCyL,YAAtC,EAAoDxT,aAApD;AACA0T,IAAAA,GAAG,CAAC3L,mBAAJ,CAAwB,WAAxB,EAAqCwL,WAArC,EAAkDvT,aAAlD;AACD;;AAED,WAAS2T,iBAAT,CAA2BlI,QAA3B,EAA6CmI,QAA7C,EAAyE;AACvEC,IAAAA,eAAe,CAACpI,QAAD,EAAW,YAAM;AAC9B,UACE,CAACvD,QAAQ,CAAC1D,KAAT,CAAe4D,SAAhB,IACAqG,MAAM,CAAC0D,UADP,IAEA1D,MAAM,CAAC0D,UAAP,CAAkBhL,QAAlB,CAA2BsH,MAA3B,CAHF,EAIE;AACAmF,QAAAA,QAAQ;AACT;AACF,KARc,CAAf;AASD;;AAED,WAASE,gBAAT,CAA0BrI,QAA1B,EAA4CmI,QAA5C,EAAwE;AACtEC,IAAAA,eAAe,CAACpI,QAAD,EAAWmI,QAAX,CAAf;AACD;;AAED,WAASC,eAAT,CAAyBpI,QAAzB,EAA2CmI,QAA3C,EAAuE;AACrE,QAAMjN,GAAG,GAAGyL,0BAA0B,GAAGzL,GAAzC;;AAEA,aAASE,QAAT,CAAkB7B,KAAlB,EAAgD;AAC9C,UAAIA,KAAK,CAACkC,MAAN,KAAiBP,GAArB,EAA0B;AACxBD,QAAAA,2BAA2B,CAACC,GAAD,EAAM,QAAN,EAAgBE,QAAhB,CAA3B;AACA+M,QAAAA,QAAQ;AACT;AACF,KARoE;AAWrE;;;AACA,QAAInI,QAAQ,KAAK,CAAjB,EAAoB;AAClB,aAAOmI,QAAQ,EAAf;AACD;;AAEDlN,IAAAA,2BAA2B,CAACC,GAAD,EAAM,QAAN,EAAgBkJ,4BAAhB,CAA3B;AACAnJ,IAAAA,2BAA2B,CAACC,GAAD,EAAM,KAAN,EAAaE,QAAb,CAA3B;AAEAgJ,IAAAA,4BAA4B,GAAGhJ,QAA/B;AACD;;AAED,WAASkN,EAAT,CACEC,SADF,EAEEC,OAFF,EAGEC,OAHF,EAIQ;AAAA,QADNA,OACM;AADNA,MAAAA,OACM,GADuC,KACvC;AAAA;;AACN,QAAMpB,KAAK,GAAGvQ,gBAAgB,CAAC2F,QAAQ,CAAC5C,KAAT,CAAe4H,aAAf,IAAgCnJ,SAAjC,CAA9B;AACA+O,IAAAA,KAAK,CAAC5Q,OAAN,CAAc,UAAC4M,IAAD,EAAU;AACtBA,MAAAA,IAAI,CAAClH,gBAAL,CAAsBoM,SAAtB,EAAiCC,OAAjC,EAA0CC,OAA1C;AACAnE,MAAAA,SAAS,CAACpN,IAAV,CAAe;AAACmM,QAAAA,IAAI,EAAJA,IAAD;AAAOkF,QAAAA,SAAS,EAATA,SAAP;AAAkBC,QAAAA,OAAO,EAAPA,OAAlB;AAA2BC,QAAAA,OAAO,EAAPA;AAA3B,OAAf;AACD,KAHD;AAID;;AAED,WAAS1C,YAAT,GAA8B;AAC5B,QAAIO,wBAAwB,EAA5B,EAAgC;AAC9BgC,MAAAA,EAAE,CAAC,YAAD,EAAetH,SAAf,EAA0B;AAACxM,QAAAA,OAAO,EAAE;AAAV,OAA1B,CAAF;AACA8T,MAAAA,EAAE,CAAC,UAAD,EAAaI,YAAb,EAA4C;AAAClU,QAAAA,OAAO,EAAE;AAAV,OAA5C,CAAF;AACD;;AAEDkC,IAAAA,aAAa,CAAC+F,QAAQ,CAAC5C,KAAT,CAAe2H,OAAhB,CAAb,CAAsC/K,OAAtC,CAA8C,UAAC8R,SAAD,EAAe;AAC3D,UAAIA,SAAS,KAAK,QAAlB,EAA4B;AAC1B;AACD;;AAEDD,MAAAA,EAAE,CAACC,SAAD,EAAYvH,SAAZ,CAAF;;AAEA,cAAQuH,SAAR;AACE,aAAK,YAAL;AACED,UAAAA,EAAE,CAAC,YAAD,EAAeI,YAAf,CAAF;AACA;;AACF,aAAK,OAAL;AACEJ,UAAAA,EAAE,CAACxL,MAAM,GAAG,UAAH,GAAgB,MAAvB,EAA+B6L,gBAA/B,CAAF;AACA;;AACF,aAAK,SAAL;AACEL,UAAAA,EAAE,CAAC,UAAD,EAAaK,gBAAb,CAAF;AACA;AATJ;AAWD,KAlBD;AAmBD;;AAED,WAASC,eAAT,GAAiC;AAC/BtE,IAAAA,SAAS,CAAC7N,OAAV,CAAkB,gBAAyD;AAAA,UAAvD4M,IAAuD,QAAvDA,IAAuD;AAAA,UAAjDkF,SAAiD,QAAjDA,SAAiD;AAAA,UAAtCC,OAAsC,QAAtCA,OAAsC;AAAA,UAA7BC,OAA6B,QAA7BA,OAA6B;AACzEpF,MAAAA,IAAI,CAAC/G,mBAAL,CAAyBiM,SAAzB,EAAoCC,OAApC,EAA6CC,OAA7C;AACD,KAFD;AAGAnE,IAAAA,SAAS,GAAG,EAAZ;AACD;;AAED,WAAStD,SAAT,CAAmBzH,KAAnB,EAAuC;AAAA;;AACrC,QAAIsP,uBAAuB,GAAG,KAA9B;;AAEA,QACE,CAACpM,QAAQ,CAAC1D,KAAT,CAAe6L,SAAhB,IACAkE,sBAAsB,CAACvP,KAAD,CADtB,IAEAyK,6BAHF,EAIE;AACA;AACD;;AAED,QAAM+E,UAAU,GAAG,sBAAA5E,gBAAgB,SAAhB,8BAAkB1O,IAAlB,MAA2B,OAA9C;AAEA0O,IAAAA,gBAAgB,GAAG5K,KAAnB;AACAkL,IAAAA,aAAa,GAAGlL,KAAK,CAACkL,aAAtB;AAEAuB,IAAAA,2BAA2B;;AAE3B,QAAI,CAACvJ,QAAQ,CAAC1D,KAAT,CAAe4D,SAAhB,IAA6BxE,YAAY,CAACoB,KAAD,CAA7C,EAAsD;AACpD;AACA;AACA;AACA;AACAkK,MAAAA,kBAAkB,CAAChN,OAAnB,CAA2B,UAAC2E,QAAD;AAAA,eAAcA,QAAQ,CAAC7B,KAAD,CAAtB;AAAA,OAA3B;AACD,KAxBoC;;;AA2BrC,QACEA,KAAK,CAAC9D,IAAN,KAAe,OAAf,KACCgH,QAAQ,CAAC5C,KAAT,CAAe2H,OAAf,CAAuB5L,OAAvB,CAA+B,YAA/B,IAA+C,CAA/C,IACCmO,kBAFF,KAGAtH,QAAQ,CAAC5C,KAAT,CAAeqG,WAAf,KAA+B,KAH/B,IAIAzD,QAAQ,CAAC1D,KAAT,CAAe4D,SALjB,EAME;AACAkM,MAAAA,uBAAuB,GAAG,IAA1B;AACD,KARD,MAQO;AACL1C,MAAAA,YAAY,CAAC5M,KAAD,CAAZ;AACD;;AAED,QAAIA,KAAK,CAAC9D,IAAN,KAAe,OAAnB,EAA4B;AAC1BsO,MAAAA,kBAAkB,GAAG,CAAC8E,uBAAtB;AACD;;AAED,QAAIA,uBAAuB,IAAI,CAACE,UAAhC,EAA4C;AAC1CC,MAAAA,YAAY,CAACzP,KAAD,CAAZ;AACD;AACF;;AAED,WAASiL,WAAT,CAAqBjL,KAArB,EAA8C;AAC5C,QAAMkC,MAAM,GAAGlC,KAAK,CAACkC,MAArB;AACA,QAAMwN,6BAA6B,GACjCxC,gBAAgB,GAAG/K,QAAnB,CAA4BD,MAA5B,KAAuCuH,MAAM,CAACtH,QAAP,CAAgBD,MAAhB,CADzC;;AAGA,QAAIlC,KAAK,CAAC9D,IAAN,KAAe,WAAf,IAA8BwT,6BAAlC,EAAiE;AAC/D;AACD;;AAED,QAAM3P,cAAc,GAAG4P,mBAAmB,GACvCnS,MADoB,CACbiM,MADa,EAEpB4C,GAFoB,CAEhB,UAAC5C,MAAD,EAAY;AAAA;;AACf,UAAMvG,QAAQ,GAAGuG,MAAM,CAAC3K,MAAxB;AACA,UAAMU,KAAK,4BAAG0D,QAAQ,CAACkI,cAAZ,qBAAG,sBAAyB5L,KAAvC;;AAEA,UAAIA,KAAJ,EAAW;AACT,eAAO;AACLY,UAAAA,UAAU,EAAEqJ,MAAM,CAACmG,qBAAP,EADP;AAELvP,UAAAA,WAAW,EAAEb,KAFR;AAGLc,UAAAA,KAAK,EAALA;AAHK,SAAP;AAKD;;AAED,aAAO,IAAP;AACD,KAfoB,EAgBpBjD,MAhBoB,CAgBbC,OAhBa,CAAvB;;AAkBA,QAAIwC,gCAAgC,CAACC,cAAD,EAAiBC,KAAjB,CAApC,EAA6D;AAC3DkO,MAAAA,gCAAgC;AAChCuB,MAAAA,YAAY,CAACzP,KAAD,CAAZ;AACD;AACF;;AAED,WAASmP,YAAT,CAAsBnP,KAAtB,EAA+C;AAC7C,QAAM6P,UAAU,GACdN,sBAAsB,CAACvP,KAAD,CAAtB,IACCkD,QAAQ,CAAC5C,KAAT,CAAe2H,OAAf,CAAuB5L,OAAvB,CAA+B,OAA/B,KAA2C,CAA3C,IAAgDmO,kBAFnD;;AAIA,QAAIqF,UAAJ,EAAgB;AACd;AACD;;AAED,QAAI3M,QAAQ,CAAC5C,KAAT,CAAeuG,WAAnB,EAAgC;AAC9B3D,MAAAA,QAAQ,CAAC4I,qBAAT,CAA+B9L,KAA/B;AACA;AACD;;AAEDyP,IAAAA,YAAY,CAACzP,KAAD,CAAZ;AACD;;AAED,WAASoP,gBAAT,CAA0BpP,KAA1B,EAAmD;AACjD,QACEkD,QAAQ,CAAC5C,KAAT,CAAe2H,OAAf,CAAuB5L,OAAvB,CAA+B,SAA/B,IAA4C,CAA5C,IACA2D,KAAK,CAACkC,MAAN,KAAiBgL,gBAAgB,EAFnC,EAGE;AACA;AACD,KANgD;;;AASjD,QACEhK,QAAQ,CAAC5C,KAAT,CAAeuG,WAAf,IACA7G,KAAK,CAAC8P,aADN,IAEArG,MAAM,CAACtH,QAAP,CAAgBnC,KAAK,CAAC8P,aAAtB,CAHF,EAIE;AACA;AACD;;AAEDL,IAAAA,YAAY,CAACzP,KAAD,CAAZ;AACD;;AAED,WAASuP,sBAAT,CAAgCvP,KAAhC,EAAuD;AACrD,WAAOsC,YAAY,CAACC,OAAb,GACHwK,wBAAwB,OAAO/M,KAAK,CAAC9D,IAAN,CAAWG,OAAX,CAAmB,OAAnB,KAA+B,CAD3D,GAEH,KAFJ;AAGD;;AAED,WAAS0T,oBAAT,GAAsC;AACpCC,IAAAA,qBAAqB;AAErB,2BAMI9M,QAAQ,CAAC5C,KANb;AAAA,QACEuH,aADF,oBACEA,aADF;AAAA,QAEE9J,SAFF,oBAEEA,SAFF;AAAA,QAGE4C,MAHF,oBAGEA,MAHF;AAAA,QAIE+F,sBAJF,oBAIEA,sBAJF;AAAA,QAKEK,cALF,oBAKEA,cALF;AAQA,QAAMlB,KAAK,GAAGmH,oBAAoB,KAAKxD,WAAW,CAACC,MAAD,CAAX,CAAoB5D,KAAzB,GAAiC,IAAnE;AAEA,QAAMoK,iBAAiB,GAAGvJ,sBAAsB,GAC5C;AACEkJ,MAAAA,qBAAqB,EAAElJ,sBADzB;AAEEwJ,MAAAA,cAAc,EACZxJ,sBAAsB,CAACwJ,cAAvB,IAAyChD,gBAAgB;AAH7D,KAD4C,GAM5CnO,SANJ;AAQA,QAAMoR,aAA2D,GAAG;AAClEzH,MAAAA,IAAI,EAAE,SAD4D;AAElE0H,MAAAA,OAAO,EAAE,IAFyD;AAGlEC,MAAAA,KAAK,EAAE,aAH2D;AAIlEC,MAAAA,QAAQ,EAAE,CAAC,eAAD,CAJwD;AAKlE7T,MAAAA,EALkE,qBAKtD;AAAA,YAAR+C,KAAQ,SAARA,KAAQ;;AACV,YAAIwN,oBAAoB,EAAxB,EAA4B;AAC1B,sCAAcI,0BAA0B,EAAxC;AAAA,cAAOzL,GAAP,yBAAOA,GAAP;;AAEA,WAAC,WAAD,EAAc,kBAAd,EAAkC,SAAlC,EAA6CzE,OAA7C,CAAqD,UAAC2Q,IAAD,EAAU;AAC7D,gBAAIA,IAAI,KAAK,WAAb,EAA0B;AACxBlM,cAAAA,GAAG,CAAClC,YAAJ,CAAiB,gBAAjB,EAAmCD,KAAK,CAACzB,SAAzC;AACD,aAFD,MAEO;AACL,kBAAIyB,KAAK,CAAC+Q,UAAN,CAAiB9G,MAAjB,kBAAuCoE,IAAvC,CAAJ,EAAoD;AAClDlM,gBAAAA,GAAG,CAAClC,YAAJ,WAAyBoO,IAAzB,EAAiC,EAAjC;AACD,eAFD,MAEO;AACLlM,gBAAAA,GAAG,CAACsM,eAAJ,WAA4BJ,IAA5B;AACD;AACF;AACF,WAVD;AAYArO,UAAAA,KAAK,CAAC+Q,UAAN,CAAiB9G,MAAjB,GAA0B,EAA1B;AACD;AACF;AAvBiE,KAApE;AA6BA,QAAM+G,SAAmC,GAAG,CAC1C;AACE9H,MAAAA,IAAI,EAAE,QADR;AAEEwG,MAAAA,OAAO,EAAE;AACPvO,QAAAA,MAAM,EAANA;AADO;AAFX,KAD0C,EAO1C;AACE+H,MAAAA,IAAI,EAAE,iBADR;AAEEwG,MAAAA,OAAO,EAAE;AACPuB,QAAAA,OAAO,EAAE;AACP5P,UAAAA,GAAG,EAAE,CADE;AAEPG,UAAAA,MAAM,EAAE,CAFD;AAGPE,UAAAA,IAAI,EAAE,CAHC;AAIPG,UAAAA,KAAK,EAAE;AAJA;AADF;AAFX,KAP0C,EAkB1C;AACEqH,MAAAA,IAAI,EAAE,MADR;AAEEwG,MAAAA,OAAO,EAAE;AACPuB,QAAAA,OAAO,EAAE;AADF;AAFX,KAlB0C,EAwB1C;AACE/H,MAAAA,IAAI,EAAE,eADR;AAEEwG,MAAAA,OAAO,EAAE;AACPwB,QAAAA,QAAQ,EAAE,CAAC3J;AADJ;AAFX,KAxB0C,EA8B1CoJ,aA9B0C,CAA5C;;AAiCA,QAAInD,oBAAoB,MAAMnH,KAA9B,EAAqC;AACnC2K,MAAAA,SAAS,CAAC7S,IAAV,CAAe;AACb+K,QAAAA,IAAI,EAAE,OADO;AAEbwG,QAAAA,OAAO,EAAE;AACPtP,UAAAA,OAAO,EAAEiG,KADF;AAEP4K,UAAAA,OAAO,EAAE;AAFF;AAFI,OAAf;AAOD;;AAEDD,IAAAA,SAAS,CAAC7S,IAAV,OAAA6S,SAAS,EAAU,CAAA3I,aAAa,QAAb,YAAAA,aAAa,CAAE2I,SAAf,KAA4B,EAAtC,CAAT;AAEAtN,IAAAA,QAAQ,CAACkI,cAAT,GAA0BuF,iBAAY,CACpCV,iBADoC,EAEpCxG,MAFoC,oBAI/B5B,aAJ+B;AAKlC9J,MAAAA,SAAS,EAATA,SALkC;AAMlC+M,MAAAA,aAAa,EAAbA,aANkC;AAOlC0F,MAAAA,SAAS,EAATA;AAPkC,OAAtC;AAUD;;AAED,WAASR,qBAAT,GAAuC;AACrC,QAAI9M,QAAQ,CAACkI,cAAb,EAA6B;AAC3BlI,MAAAA,QAAQ,CAACkI,cAAT,CAAwBc,OAAxB;AACAhJ,MAAAA,QAAQ,CAACkI,cAAT,GAA0B,IAA1B;AACD;AACF;;AAED,WAASwF,KAAT,GAAuB;AACrB,QAAOvK,QAAP,GAAmBnD,QAAQ,CAAC5C,KAA5B,CAAO+F,QAAP;AAEA,QAAI8G,UAAJ,CAHqB;AAMrB;AACA;AACA;AACA;;AACA,QAAMrD,IAAI,GAAGoD,gBAAgB,EAA7B;;AAEA,QACGhK,QAAQ,CAAC5C,KAAT,CAAeuG,WAAf,IAA8BR,QAAQ,KAAKlL,uBAA5C,IACAkL,QAAQ,KAAK,QAFf,EAGE;AACA8G,MAAAA,UAAU,GAAGrD,IAAI,CAACqD,UAAlB;AACD,KALD,MAKO;AACLA,MAAAA,UAAU,GAAG7Q,sBAAsB,CAAC+J,QAAD,EAAW,CAACyD,IAAD,CAAX,CAAnC;AACD,KAnBoB;AAsBrB;;;AACA,QAAI,CAACqD,UAAU,CAAChL,QAAX,CAAoBsH,MAApB,CAAL,EAAkC;AAChC0D,MAAAA,UAAU,CAAC0D,WAAX,CAAuBpH,MAAvB;AACD;;AAEDvG,IAAAA,QAAQ,CAAC1D,KAAT,CAAe+L,SAAf,GAA2B,IAA3B;AAEAwE,IAAAA,oBAAoB;AAEpB;;AACA,+CAAa;AACX;AACAxL,MAAAA,QAAQ,CACNrB,QAAQ,CAAC5C,KAAT,CAAeuG,WAAf,IACER,QAAQ,KAAKD,YAAY,CAACC,QAD5B,IAEEyD,IAAI,CAACgH,kBAAL,KAA4BrH,MAHxB,EAIN,CACE,8DADF,EAEE,mEAFF,EAGE,0BAHF,EAIE,MAJF,EAKE,kEALF,EAME,mDANF,EAOE,MAPF,EAQE,oEARF,EASE,6DATF,EAUE,sBAVF,EAWE,MAXF,EAYE,wEAZF,EAaE9F,IAbF,CAaO,GAbP,CAJM,CAAR;AAmBD;AACF;;AAED,WAASgM,mBAAT,GAAgD;AAC9C,WAAO3R,SAAS,CACdyL,MAAM,CAACxK,gBAAP,CAAwB,mBAAxB,CADc,CAAhB;AAGD;;AAED,WAAS2N,YAAT,CAAsB5M,KAAtB,EAA2C;AACzCkD,IAAAA,QAAQ,CAACuI,kBAAT;;AAEA,QAAIzL,KAAJ,EAAW;AACT2M,MAAAA,UAAU,CAAC,WAAD,EAAc,CAACzJ,QAAD,EAAWlD,KAAX,CAAd,CAAV;AACD;;AAEDyO,IAAAA,gBAAgB;AAEhB,QAAIjI,KAAK,GAAG6G,QAAQ,CAAC,IAAD,CAApB;;AACA,gCAAiCP,0BAA0B,EAA3D;AAAA,QAAOiE,UAAP;AAAA,QAAmBC,UAAnB;;AAEA,QAAI1O,YAAY,CAACC,OAAb,IAAwBwO,UAAU,KAAK,MAAvC,IAAiDC,UAArD,EAAiE;AAC/DxK,MAAAA,KAAK,GAAGwK,UAAR;AACD;;AAED,QAAIxK,KAAJ,EAAW;AACT6D,MAAAA,WAAW,GAAGvN,UAAU,CAAC,YAAM;AAC7BoG,QAAAA,QAAQ,CAAC0I,IAAT;AACD,OAFuB,EAErBpF,KAFqB,CAAxB;AAGD,KAJD,MAIO;AACLtD,MAAAA,QAAQ,CAAC0I,IAAT;AACD;AACF;;AAED,WAAS6D,YAAT,CAAsBzP,KAAtB,EAA0C;AACxCkD,IAAAA,QAAQ,CAACuI,kBAAT;AAEAkB,IAAAA,UAAU,CAAC,aAAD,EAAgB,CAACzJ,QAAD,EAAWlD,KAAX,CAAhB,CAAV;;AAEA,QAAI,CAACkD,QAAQ,CAAC1D,KAAT,CAAe4D,SAApB,EAA+B;AAC7BkL,MAAAA,mBAAmB;AAEnB;AACD,KATuC;AAYxC;AACA;AACA;;;AACA,QACEpL,QAAQ,CAAC5C,KAAT,CAAe2H,OAAf,CAAuB5L,OAAvB,CAA+B,YAA/B,KAAgD,CAAhD,IACA6G,QAAQ,CAAC5C,KAAT,CAAe2H,OAAf,CAAuB5L,OAAvB,CAA+B,OAA/B,KAA2C,CAD3C,IAEA,CAAC,YAAD,EAAe,WAAf,EAA4BA,OAA5B,CAAoC2D,KAAK,CAAC9D,IAA1C,KAAmD,CAFnD,IAGAsO,kBAJF,EAKE;AACA;AACD;;AAED,QAAMhE,KAAK,GAAG6G,QAAQ,CAAC,KAAD,CAAtB;;AAEA,QAAI7G,KAAJ,EAAW;AACT8D,MAAAA,WAAW,GAAGxN,UAAU,CAAC,YAAM;AAC7B,YAAIoG,QAAQ,CAAC1D,KAAT,CAAe4D,SAAnB,EAA8B;AAC5BF,UAAAA,QAAQ,CAAC2I,IAAT;AACD;AACF,OAJuB,EAIrBrF,KAJqB,CAAxB;AAKD,KAND,MAMO;AACL;AACA;AACA+D,MAAAA,0BAA0B,GAAG0G,qBAAqB,CAAC,YAAM;AACvD/N,QAAAA,QAAQ,CAAC2I,IAAT;AACD,OAFiD,CAAlD;AAGD;AACF,GA3wBS;AA8wBV;AACA;;;AACA,WAASE,MAAT,GAAwB;AACtB7I,IAAAA,QAAQ,CAAC1D,KAAT,CAAe6L,SAAf,GAA2B,IAA3B;AACD;;AAED,WAASW,OAAT,GAAyB;AACvB;AACA;AACA9I,IAAAA,QAAQ,CAAC2I,IAAT;AACA3I,IAAAA,QAAQ,CAAC1D,KAAT,CAAe6L,SAAf,GAA2B,KAA3B;AACD;;AAED,WAASI,kBAAT,GAAoC;AAClC5O,IAAAA,YAAY,CAACwN,WAAD,CAAZ;AACAxN,IAAAA,YAAY,CAACyN,WAAD,CAAZ;AACA4G,IAAAA,oBAAoB,CAAC3G,0BAAD,CAApB;AACD;;AAED,WAASmB,QAAT,CAAkBrD,YAAlB,EAAsD;AACpD;AACA,+CAAa;AACX9D,MAAAA,QAAQ,CAACrB,QAAQ,CAAC1D,KAAT,CAAe8L,WAAhB,EAA6B7H,uBAAuB,CAAC,UAAD,CAApD,CAAR;AACD;;AAED,QAAIP,QAAQ,CAAC1D,KAAT,CAAe8L,WAAnB,EAAgC;AAC9B;AACD;;AAEDqB,IAAAA,UAAU,CAAC,gBAAD,EAAmB,CAACzJ,QAAD,EAAWmF,YAAX,CAAnB,CAAV;AAEAgH,IAAAA,eAAe;AAEf,QAAM8B,SAAS,GAAGjO,QAAQ,CAAC5C,KAA3B;AACA,QAAM8Q,SAAS,GAAGlI,aAAa,CAACnK,SAAD,oBAC1BoS,SAD0B,EAE1BjT,oBAAoB,CAACmK,YAAD,CAFM;AAG7BzB,MAAAA,gBAAgB,EAAE;AAHW,OAA/B;AAMA1D,IAAAA,QAAQ,CAAC5C,KAAT,GAAiB8Q,SAAjB;AAEA5E,IAAAA,YAAY;;AAEZ,QAAI2E,SAAS,CAACrK,mBAAV,KAAkCsK,SAAS,CAACtK,mBAAhD,EAAqE;AACnEoH,MAAAA,gCAAgC;AAChClD,MAAAA,oBAAoB,GAAGxO,QAAQ,CAC7ByO,WAD6B,EAE7BmG,SAAS,CAACtK,mBAFmB,CAA/B;AAID,KA/BmD;;;AAkCpD,QAAIqK,SAAS,CAACjJ,aAAV,IAA2B,CAACkJ,SAAS,CAAClJ,aAA1C,EAAyD;AACvD3K,MAAAA,gBAAgB,CAAC4T,SAAS,CAACjJ,aAAX,CAAhB,CAA0ChL,OAA1C,CAAkD,UAAC4M,IAAD,EAAU;AAC1DA,QAAAA,IAAI,CAACmE,eAAL,CAAqB,eAArB;AACD,OAFD;AAGD,KAJD,MAIO,IAAImD,SAAS,CAAClJ,aAAd,EAA6B;AAClCnJ,MAAAA,SAAS,CAACkP,eAAV,CAA0B,eAA1B;AACD;;AAEDxB,IAAAA,2BAA2B;AAC3BC,IAAAA,YAAY;;AAEZ,QAAIP,QAAJ,EAAc;AACZA,MAAAA,QAAQ,CAACgF,SAAD,EAAYC,SAAZ,CAAR;AACD;;AAED,QAAIlO,QAAQ,CAACkI,cAAb,EAA6B;AAC3B2E,MAAAA,oBAAoB,GADO;AAI3B;AACA;AACA;;AACAJ,MAAAA,mBAAmB,GAAGzS,OAAtB,CAA8B,UAACmU,YAAD,EAAkB;AAC9C;AACA;AACAJ,QAAAA,qBAAqB,CAACI,YAAY,CAACvS,MAAb,CAAqBsM,cAArB,CAAqCkG,WAAtC,CAArB;AACD,OAJD;AAKD;;AAED3E,IAAAA,UAAU,CAAC,eAAD,EAAkB,CAACzJ,QAAD,EAAWmF,YAAX,CAAlB,CAAV;AACD;;AAED,WAASsD,UAAT,CAAoB7F,OAApB,EAA4C;AAC1C5C,IAAAA,QAAQ,CAACwI,QAAT,CAAkB;AAAC5F,MAAAA,OAAO,EAAPA;AAAD,KAAlB;AACD;;AAED,WAAS8F,IAAT,GAAsB;AACpB;AACA,+CAAa;AACXrH,MAAAA,QAAQ,CAACrB,QAAQ,CAAC1D,KAAT,CAAe8L,WAAhB,EAA6B7H,uBAAuB,CAAC,MAAD,CAApD,CAAR;AACD,KAJmB;;;AAOpB,QAAM8N,gBAAgB,GAAGrO,QAAQ,CAAC1D,KAAT,CAAe4D,SAAxC;AACA,QAAMkI,WAAW,GAAGpI,QAAQ,CAAC1D,KAAT,CAAe8L,WAAnC;AACA,QAAMkG,UAAU,GAAG,CAACtO,QAAQ,CAAC1D,KAAT,CAAe6L,SAAnC;AACA,QAAMoG,uBAAuB,GAC3BnP,YAAY,CAACC,OAAb,IAAwB,CAACW,QAAQ,CAAC5C,KAAT,CAAe0H,KAD1C;AAEA,QAAMvB,QAAQ,GAAG/K,uBAAuB,CACtCwH,QAAQ,CAAC5C,KAAT,CAAemG,QADuB,EAEtC,CAFsC,EAGtCL,YAAY,CAACK,QAHyB,CAAxC;;AAMA,QACE8K,gBAAgB,IAChBjG,WADA,IAEAkG,UAFA,IAGAC,uBAJF,EAKE;AACA;AACD,KAzBmB;AA4BpB;AACA;;;AACA,QAAIvE,gBAAgB,GAAGX,YAAnB,CAAgC,UAAhC,CAAJ,EAAiD;AAC/C;AACD;;AAEDI,IAAAA,UAAU,CAAC,QAAD,EAAW,CAACzJ,QAAD,CAAX,EAAuB,KAAvB,CAAV;;AACA,QAAIA,QAAQ,CAAC5C,KAAT,CAAeiH,MAAf,CAAsBrE,QAAtB,MAAoC,KAAxC,EAA+C;AAC7C;AACD;;AAEDA,IAAAA,QAAQ,CAAC1D,KAAT,CAAe4D,SAAf,GAA2B,IAA3B;;AAEA,QAAI4J,oBAAoB,EAAxB,EAA4B;AAC1BvD,MAAAA,MAAM,CAACpK,KAAP,CAAaqS,UAAb,GAA0B,SAA1B;AACD;;AAEDhF,IAAAA,YAAY;AACZ+B,IAAAA,gBAAgB;;AAEhB,QAAI,CAACvL,QAAQ,CAAC1D,KAAT,CAAe+L,SAApB,EAA+B;AAC7B9B,MAAAA,MAAM,CAACpK,KAAP,CAAasS,UAAb,GAA0B,MAA1B;AACD,KAlDmB;AAqDpB;;;AACA,QAAI3E,oBAAoB,EAAxB,EAA4B;AAC1B,mCAAuBI,0BAA0B,EAAjD;AAAA,UAAOzL,GAAP,0BAAOA,GAAP;AAAA,UAAYmE,OAAZ,0BAAYA,OAAZ;;AACA5G,MAAAA,qBAAqB,CAAC,CAACyC,GAAD,EAAMmE,OAAN,CAAD,EAAiB,CAAjB,CAArB;AACD;;AAEDgF,IAAAA,aAAa,GAAG,yBAAY;AAAA;;AAC1B,UAAI,CAAC5H,QAAQ,CAAC1D,KAAT,CAAe4D,SAAhB,IAA6BuH,mBAAjC,EAAsD;AACpD;AACD;;AAEDA,MAAAA,mBAAmB,GAAG,IAAtB,CAL0B;;AAQ1B,WAAKlB,MAAM,CAACmI,YAAZ;AAEAnI,MAAAA,MAAM,CAACpK,KAAP,CAAasS,UAAb,GAA0BzO,QAAQ,CAAC5C,KAAT,CAAeyG,cAAzC;;AAEA,UAAIiG,oBAAoB,MAAM9J,QAAQ,CAAC5C,KAAT,CAAesF,SAA7C,EAAwD;AACtD,qCAAuBwH,0BAA0B,EAAjD;AAAA,YAAOzL,IAAP,0BAAOA,GAAP;AAAA,YAAYmE,QAAZ,0BAAYA,OAAZ;;AACA5G,QAAAA,qBAAqB,CAAC,CAACyC,IAAD,EAAMmE,QAAN,CAAD,EAAiBW,QAAjB,CAArB;AACAlH,QAAAA,kBAAkB,CAAC,CAACoC,IAAD,EAAMmE,QAAN,CAAD,EAAiB,SAAjB,CAAlB;AACD;;AAED8H,MAAAA,0BAA0B;AAC1BnB,MAAAA,2BAA2B;AAE3BhP,MAAAA,YAAY,CAAC0M,gBAAD,EAAmBjH,QAAnB,CAAZ,CArB0B;AAwB1B;;AACA,gCAAAA,QAAQ,CAACkI,cAAT,4CAAyBkG,WAAzB;AAEA3E,MAAAA,UAAU,CAAC,SAAD,EAAY,CAACzJ,QAAD,CAAZ,CAAV;;AAEA,UAAIA,QAAQ,CAAC5C,KAAT,CAAesF,SAAf,IAA4BoH,oBAAoB,EAApD,EAAwD;AACtD8B,QAAAA,gBAAgB,CAACrI,QAAD,EAAW,YAAM;AAC/BvD,UAAAA,QAAQ,CAAC1D,KAAT,CAAegM,OAAf,GAAyB,IAAzB;AACAmB,UAAAA,UAAU,CAAC,SAAD,EAAY,CAACzJ,QAAD,CAAZ,CAAV;AACD,SAHe,CAAhB;AAID;AACF,KAnCD;;AAqCA0N,IAAAA,KAAK;AACN;;AAED,WAAS/E,IAAT,GAAsB;AACpB;AACA,+CAAa;AACXtH,MAAAA,QAAQ,CAACrB,QAAQ,CAAC1D,KAAT,CAAe8L,WAAhB,EAA6B7H,uBAAuB,CAAC,MAAD,CAApD,CAAR;AACD,KAJmB;;;AAOpB,QAAMoO,eAAe,GAAG,CAAC3O,QAAQ,CAAC1D,KAAT,CAAe4D,SAAxC;AACA,QAAMkI,WAAW,GAAGpI,QAAQ,CAAC1D,KAAT,CAAe8L,WAAnC;AACA,QAAMkG,UAAU,GAAG,CAACtO,QAAQ,CAAC1D,KAAT,CAAe6L,SAAnC;AACA,QAAM5E,QAAQ,GAAG/K,uBAAuB,CACtCwH,QAAQ,CAAC5C,KAAT,CAAemG,QADuB,EAEtC,CAFsC,EAGtCL,YAAY,CAACK,QAHyB,CAAxC;;AAMA,QAAIoL,eAAe,IAAIvG,WAAnB,IAAkCkG,UAAtC,EAAkD;AAChD;AACD;;AAED7E,IAAAA,UAAU,CAAC,QAAD,EAAW,CAACzJ,QAAD,CAAX,EAAuB,KAAvB,CAAV;;AACA,QAAIA,QAAQ,CAAC5C,KAAT,CAAe+G,MAAf,CAAsBnE,QAAtB,MAAoC,KAAxC,EAA+C;AAC7C;AACD;;AAEDA,IAAAA,QAAQ,CAAC1D,KAAT,CAAe4D,SAAf,GAA2B,KAA3B;AACAF,IAAAA,QAAQ,CAAC1D,KAAT,CAAegM,OAAf,GAAyB,KAAzB;AACAb,IAAAA,mBAAmB,GAAG,KAAtB;AACAH,IAAAA,kBAAkB,GAAG,KAArB;;AAEA,QAAIwC,oBAAoB,EAAxB,EAA4B;AAC1BvD,MAAAA,MAAM,CAACpK,KAAP,CAAaqS,UAAb,GAA0B,QAA1B;AACD;;AAEDxD,IAAAA,gCAAgC;AAChCI,IAAAA,mBAAmB;AACnB5B,IAAAA,YAAY,CAAC,IAAD,CAAZ;;AAEA,QAAIM,oBAAoB,EAAxB,EAA4B;AAC1B,mCAAuBI,0BAA0B,EAAjD;AAAA,UAAOzL,GAAP,0BAAOA,GAAP;AAAA,UAAYmE,OAAZ,0BAAYA,OAAZ;;AAEA,UAAI5C,QAAQ,CAAC5C,KAAT,CAAesF,SAAnB,EAA8B;AAC5B1G,QAAAA,qBAAqB,CAAC,CAACyC,GAAD,EAAMmE,OAAN,CAAD,EAAiBW,QAAjB,CAArB;AACAlH,QAAAA,kBAAkB,CAAC,CAACoC,GAAD,EAAMmE,OAAN,CAAD,EAAiB,QAAjB,CAAlB;AACD;AACF;;AAED8H,IAAAA,0BAA0B;AAC1BnB,IAAAA,2BAA2B;;AAE3B,QAAIvJ,QAAQ,CAAC5C,KAAT,CAAesF,SAAnB,EAA8B;AAC5B,UAAIoH,oBAAoB,EAAxB,EAA4B;AAC1B2B,QAAAA,iBAAiB,CAAClI,QAAD,EAAWvD,QAAQ,CAAC+I,OAApB,CAAjB;AACD;AACF,KAJD,MAIO;AACL/I,MAAAA,QAAQ,CAAC+I,OAAT;AACD;AACF;;AAED,WAASH,qBAAT,CAA+B9L,KAA/B,EAAwD;AACtD;AACA,+CAAa;AACXuE,MAAAA,QAAQ,CACNrB,QAAQ,CAAC1D,KAAT,CAAe8L,WADT,EAEN7H,uBAAuB,CAAC,uBAAD,CAFjB,CAAR;AAID;;AAEDoJ,IAAAA,WAAW,GAAGjK,gBAAd,CAA+B,WAA/B,EAA4CoI,oBAA5C;AACAvN,IAAAA,YAAY,CAACyM,kBAAD,EAAqBc,oBAArB,CAAZ;AACAA,IAAAA,oBAAoB,CAAChL,KAAD,CAApB;AACD;;AAED,WAASiM,OAAT,GAAyB;AACvB;AACA,+CAAa;AACX1H,MAAAA,QAAQ,CAACrB,QAAQ,CAAC1D,KAAT,CAAe8L,WAAhB,EAA6B7H,uBAAuB,CAAC,SAAD,CAApD,CAAR;AACD;;AAED,QAAIP,QAAQ,CAAC1D,KAAT,CAAe4D,SAAnB,EAA8B;AAC5BF,MAAAA,QAAQ,CAAC2I,IAAT;AACD;;AAED,QAAI,CAAC3I,QAAQ,CAAC1D,KAAT,CAAe+L,SAApB,EAA+B;AAC7B;AACD;;AAEDyE,IAAAA,qBAAqB,GAdE;AAiBvB;AACA;;AACAL,IAAAA,mBAAmB,GAAGzS,OAAtB,CAA8B,UAACmU,YAAD,EAAkB;AAC9CA,MAAAA,YAAY,CAACvS,MAAb,CAAqBmN,OAArB;AACD,KAFD;;AAIA,QAAIxC,MAAM,CAAC0D,UAAX,EAAuB;AACrB1D,MAAAA,MAAM,CAAC0D,UAAP,CAAkB2E,WAAlB,CAA8BrI,MAA9B;AACD;;AAEDU,IAAAA,gBAAgB,GAAGA,gBAAgB,CAAC9M,MAAjB,CAAwB,UAAC0U,CAAD;AAAA,aAAOA,CAAC,KAAK7O,QAAb;AAAA,KAAxB,CAAnB;AAEAA,IAAAA,QAAQ,CAAC1D,KAAT,CAAe+L,SAAf,GAA2B,KAA3B;AACAoB,IAAAA,UAAU,CAAC,UAAD,EAAa,CAACzJ,QAAD,CAAb,CAAV;AACD;;AAED,WAASgJ,OAAT,GAAyB;AACvB;AACA,+CAAa;AACX3H,MAAAA,QAAQ,CAACrB,QAAQ,CAAC1D,KAAT,CAAe8L,WAAhB,EAA6B7H,uBAAuB,CAAC,SAAD,CAApD,CAAR;AACD;;AAED,QAAIP,QAAQ,CAAC1D,KAAT,CAAe8L,WAAnB,EAAgC;AAC9B;AACD;;AAEDpI,IAAAA,QAAQ,CAACuI,kBAAT;AACAvI,IAAAA,QAAQ,CAAC+I,OAAT;AAEAoD,IAAAA,eAAe;AAEf,WAAOtQ,SAAS,CAACD,MAAjB;AAEAoE,IAAAA,QAAQ,CAAC1D,KAAT,CAAe8L,WAAf,GAA6B,IAA7B;AAEAqB,IAAAA,UAAU,CAAC,WAAD,EAAc,CAACzJ,QAAD,CAAd,CAAV;AACD;AACF;;AC/mCD,SAAS8O,KAAT,CACEhN,OADF,EAEEiN,aAFF,EAGyB;AAAA,MADvBA,aACuB;AADvBA,IAAAA,aACuB,GADS,EACT;AAAA;;AACvB,MAAMrK,OAAO,GAAGxB,YAAY,CAACwB,OAAb,CAAqBpK,MAArB,CAA4ByU,aAAa,CAACrK,OAAd,IAAyB,EAArD,CAAhB;AAEA;;AACA,6CAAa;AACX7C,IAAAA,eAAe,CAACC,OAAD,CAAf;AACAsD,IAAAA,aAAa,CAAC2J,aAAD,EAAgBrK,OAAhB,CAAb;AACD;;AAEDvE,EAAAA,wBAAwB;AAExB,MAAMmF,WAA2B,qBAAOyJ,aAAP;AAAsBrK,IAAAA,OAAO,EAAPA;AAAtB,IAAjC;AAEA,MAAMsK,QAAQ,GAAGlT,kBAAkB,CAACgG,OAAD,CAAnC;AAEA;;AACA,6CAAa;AACX,QAAMmN,sBAAsB,GAAG1T,SAAS,CAAC+J,WAAW,CAAC1C,OAAb,CAAxC;AACA,QAAMsM,6BAA6B,GAAGF,QAAQ,CAAC3I,MAAT,GAAkB,CAAxD;AACAhF,IAAAA,QAAQ,CACN4N,sBAAsB,IAAIC,6BADpB,EAEN,CACE,oEADF,EAEE,mEAFF,EAGE,mEAHF,EAIE,MAJF,EAKE,qEALF,EAME,kDANF,EAOE,MAPF,EAQE,iCARF,EASE,2CATF,EAUEzO,IAVF,CAUO,GAVP,CAFM,CAAR;AAcD;;AAED,MAAM0O,SAAS,GAAGH,QAAQ,CAAC9T,MAAT,CAChB,UAACC,GAAD,EAAMU,SAAN,EAAgC;AAC9B,QAAMmE,QAAQ,GAAGnE,SAAS,IAAIqL,WAAW,CAACrL,SAAD,EAAYyJ,WAAZ,CAAzC;;AAEA,QAAItF,QAAJ,EAAc;AACZ7E,MAAAA,GAAG,CAACV,IAAJ,CAASuF,QAAT;AACD;;AAED,WAAO7E,GAAP;AACD,GATe,EAUhB,EAVgB,CAAlB;AAaA,SAAOI,SAAS,CAACuG,OAAD,CAAT,GAAqBqN,SAAS,CAAC,CAAD,CAA9B,GAAoCA,SAA3C;AACD;;AAEDL,KAAK,CAAC5L,YAAN,GAAqBA,YAArB;AACA4L,KAAK,CAAC5J,eAAN,GAAwBA,eAAxB;AACA4J,KAAK,CAAC1P,YAAN,GAAqBA,YAArB;AAEA,IAEagQ,OAAgB,GAAG,SAAnBA,OAAmB,QAGL;AAAA,gCAAP,EAAO;AAAA,MAFhBC,2BAEgB,QAFzBC,OAEyB;AAAA,MADzB/L,QACyB,QADzBA,QACyB;;AACzB0D,EAAAA,gBAAgB,CAACjN,OAAjB,CAAyB,UAACgG,QAAD,EAAc;AACrC,QAAIuP,UAAU,GAAG,KAAjB;;AAEA,QAAIF,2BAAJ,EAAiC;AAC/BE,MAAAA,UAAU,GAAG5T,kBAAkB,CAAC0T,2BAAD,CAAlB,GACTrP,QAAQ,CAACnE,SAAT,KAAuBwT,2BADd,GAETrP,QAAQ,CAACuG,MAAT,KAAqB8I,2BAAD,CAA0C9I,MAFlE;AAGD;;AAED,QAAI,CAACgJ,UAAL,EAAiB;AACf,UAAMC,gBAAgB,GAAGxP,QAAQ,CAAC5C,KAAT,CAAemG,QAAxC;AAEAvD,MAAAA,QAAQ,CAACwI,QAAT,CAAkB;AAACjF,QAAAA,QAAQ,EAARA;AAAD,OAAlB;AACAvD,MAAAA,QAAQ,CAAC2I,IAAT;;AAEA,UAAI,CAAC3I,QAAQ,CAAC1D,KAAT,CAAe8L,WAApB,EAAiC;AAC/BpI,QAAAA,QAAQ,CAACwI,QAAT,CAAkB;AAACjF,UAAAA,QAAQ,EAAEiM;AAAX,SAAlB;AACD;AACF;AACF,GAnBD;AAoBD,CAxBM;;ACrDP;AACA;AACA;;AACA,IAAMC,mBAAqE,qBACtEC,gBADsE;AAEzEC,EAAAA,MAFyE,wBAEzD;AAAA,QAARrT,KAAQ,QAARA,KAAQ;AACd,QAAMsT,aAAa,GAAG;AACpBrJ,MAAAA,MAAM,EAAE;AACNsJ,QAAAA,QAAQ,EAAEvT,KAAK,CAAC0P,OAAN,CAAc8D,QADlB;AAEN9R,QAAAA,IAAI,EAAE,GAFA;AAGNL,QAAAA,GAAG,EAAE,GAHC;AAINoS,QAAAA,MAAM,EAAE;AAJF,OADY;AAOpBpN,MAAAA,KAAK,EAAE;AACLkN,QAAAA,QAAQ,EAAE;AADL,OAPa;AAUpBhU,MAAAA,SAAS,EAAE;AAVS,KAAtB;AAaAZ,IAAAA,MAAM,CAAC+U,MAAP,CAAc1T,KAAK,CAAC0S,QAAN,CAAezI,MAAf,CAAsBpK,KAApC,EAA2CyT,aAAa,CAACrJ,MAAzD;AACAjK,IAAAA,KAAK,CAAC2T,MAAN,GAAeL,aAAf;;AAEA,QAAItT,KAAK,CAAC0S,QAAN,CAAerM,KAAnB,EAA0B;AACxB1H,MAAAA,MAAM,CAAC+U,MAAP,CAAc1T,KAAK,CAAC0S,QAAN,CAAerM,KAAf,CAAqBxG,KAAnC,EAA0CyT,aAAa,CAACjN,KAAxD;AACD,KAnBa;AAsBd;;AACD;AAzBwE,EAA3E;;AA4BA,IAAMuN,eAAgC,GAAG,SAAnCA,eAAmC,CACvCC,cADuC,EAEvCpB,aAFuC,EAGpC;AAAA;;AAAA,MADHA,aACG;AADHA,IAAAA,aACG,GADa,EACb;AAAA;;AACH;AACA,6CAAa;AACXpN,IAAAA,SAAS,CACP,CAAC/I,KAAK,CAACC,OAAN,CAAcsX,cAAd,CADM,EAEP,CACE,oEADF,EAEE,uCAFF,EAGEjO,MAAM,CAACiO,cAAD,CAHR,EAIE1P,IAJF,CAIO,GAJP,CAFO,CAAT;AAQD;;AAED,MAAI2P,mBAAmB,GAAGD,cAA1B;AACA,MAAIE,UAAmC,GAAG,EAA1C;AACA,MAAIC,cAA8B,GAAG,EAArC;AACA,MAAItI,aAAJ;AACA,MAAIuI,SAAS,GAAGxB,aAAa,CAACwB,SAA9B;AACA,MAAIC,yBAA4C,GAAG,EAAnD;AACA,MAAIC,aAAa,GAAG,KAApB;;AAEA,WAASC,iBAAT,GAAmC;AACjCJ,IAAAA,cAAc,GAAGF,mBAAmB,CACjCjH,GADc,CACV,UAACnJ,QAAD;AAAA,aACH3F,gBAAgB,CAAC2F,QAAQ,CAAC5C,KAAT,CAAe4H,aAAf,IAAgChF,QAAQ,CAACnE,SAA1C,CADb;AAAA,KADU,EAIdX,MAJc,CAIP,UAACC,GAAD,EAAMR,IAAN;AAAA,aAAeQ,GAAG,CAACb,MAAJ,CAAWK,IAAX,CAAf;AAAA,KAJO,EAI0B,EAJ1B,CAAjB;AAKD;;AAED,WAASgW,aAAT,GAA+B;AAC7BN,IAAAA,UAAU,GAAGD,mBAAmB,CAACjH,GAApB,CAAwB,UAACnJ,QAAD;AAAA,aAAcA,QAAQ,CAACnE,SAAvB;AAAA,KAAxB,CAAb;AACD;;AAED,WAAS+U,eAAT,CAAyBzI,SAAzB,EAAmD;AACjDiI,IAAAA,mBAAmB,CAACpW,OAApB,CAA4B,UAACgG,QAAD,EAAc;AACxC,UAAImI,SAAJ,EAAe;AACbnI,QAAAA,QAAQ,CAAC6I,MAAT;AACD,OAFD,MAEO;AACL7I,QAAAA,QAAQ,CAAC8I,OAAT;AACD;AACF,KAND;AAOD;;AAED,WAAS+H,iBAAT,CAA2BC,SAA3B,EAAmE;AACjE,WAAOV,mBAAmB,CAACjH,GAApB,CAAwB,UAACnJ,QAAD,EAAc;AAC3C,UAAM+Q,gBAAgB,GAAG/Q,QAAQ,CAACwI,QAAlC;;AAEAxI,MAAAA,QAAQ,CAACwI,QAAT,GAAoB,UAACpL,KAAD,EAAiB;AACnC2T,QAAAA,gBAAgB,CAAC3T,KAAD,CAAhB;;AAEA,YAAI4C,QAAQ,CAACnE,SAAT,KAAuBmM,aAA3B,EAA0C;AACxC8I,UAAAA,SAAS,CAACtI,QAAV,CAAmBpL,KAAnB;AACD;AACF,OAND;;AAQA,aAAO,YAAY;AACjB4C,QAAAA,QAAQ,CAACwI,QAAT,GAAoBuI,gBAApB;AACD,OAFD;AAGD,KAdM,CAAP;AAeD,GA3DE;;;AA8DH,WAASC,eAAT,CACEF,SADF,EAEE9R,MAFF,EAGQ;AACN,QAAMtG,KAAK,GAAG4X,cAAc,CAACnX,OAAf,CAAuB6F,MAAvB,CAAd,CADM;;AAIN,QAAIA,MAAM,KAAKgJ,aAAf,EAA8B;AAC5B;AACD;;AAEDA,IAAAA,aAAa,GAAGhJ,MAAhB;AAEA,QAAMiS,aAA6B,GAAG,CAACV,SAAS,IAAI,EAAd,EACnCjW,MADmC,CAC5B,SAD4B,EAEnCY,MAFmC,CAE5B,UAACC,GAAD,EAAM+K,IAAN,EAAe;AACpB/K,MAAAA,GAAD,CAAa+K,IAAb,IAAqBkK,mBAAmB,CAAC1X,KAAD,CAAnB,CAA2B0E,KAA3B,CAAiC8I,IAAjC,CAArB;AACA,aAAO/K,GAAP;AACD,KALmC,EAKjC,EALiC,CAAtC;AAOA2V,IAAAA,SAAS,CAACtI,QAAV,mBACKyI,aADL;AAEEzN,MAAAA,sBAAsB,EACpB,OAAOyN,aAAa,CAACzN,sBAArB,KAAgD,UAAhD,GACIyN,aAAa,CAACzN,sBADlB,GAEI;AAAA;;AAAA,oCAAkB6M,UAAU,CAAC3X,KAAD,CAA5B,qBAAkB,kBAAmBgU,qBAAnB,EAAlB;AAAA;AALR;AAOD;;AAEDkE,EAAAA,eAAe,CAAC,KAAD,CAAf;AACAD,EAAAA,aAAa;AACbD,EAAAA,iBAAiB;AAEjB,MAAMnL,MAAc,GAAG;AACrBhM,IAAAA,EADqB,gBAChB;AACH,aAAO;AACL0K,QAAAA,SADK,uBACa;AAChB2M,UAAAA,eAAe,CAAC,IAAD,CAAf;AACD,SAHI;AAIL1M,QAAAA,QAJK,sBAIY;AACf8D,UAAAA,aAAa,GAAG,IAAhB;AACD,SANI;AAOLvD,QAAAA,cAPK,0BAOUzE,QAPV,EAO0B;AAC7B,cAAIA,QAAQ,CAAC5C,KAAT,CAAeyH,YAAf,IAA+B,CAAC4L,aAApC,EAAmD;AACjDA,YAAAA,aAAa,GAAG,IAAhB;AACAzI,YAAAA,aAAa,GAAG,IAAhB;AACD;AACF,SAZI;AAaL3D,QAAAA,MAbK,kBAaErE,QAbF,EAakB;AACrB,cAAIA,QAAQ,CAAC5C,KAAT,CAAeyH,YAAf,IAA+B,CAAC4L,aAApC,EAAmD;AACjDA,YAAAA,aAAa,GAAG,IAAhB;AACAO,YAAAA,eAAe,CAAChR,QAAD,EAAWqQ,UAAU,CAAC,CAAD,CAArB,CAAf;AACD;AACF,SAlBI;AAmBL9L,QAAAA,SAnBK,qBAmBKvE,QAnBL,EAmBelD,KAnBf,EAmB4B;AAC/BkU,UAAAA,eAAe,CAAChR,QAAD,EAAWlD,KAAK,CAACkL,aAAjB,CAAf;AACD;AArBI,OAAP;AAuBD;AAzBoB,GAAvB;AA4BA,MAAM8I,SAAS,GAAGhC,KAAK,CAACzT,GAAG,EAAJ,oBAClBxB,gBAAgB,CAACkV,aAAD,EAAgB,CAAC,WAAD,CAAhB,CADE;AAErBrK,IAAAA,OAAO,GAAGa,MAAH,SAAewJ,aAAa,CAACrK,OAAd,IAAyB,EAAxC,CAFc;AAGrBM,IAAAA,aAAa,EAAEsL,cAHM;AAIrB3L,IAAAA,aAAa,oBACRoK,aAAa,CAACpK,aADN;AAEX2I,MAAAA,SAAS,YACH,0BAAAyB,aAAa,CAACpK,aAAd,2CAA6B2I,SAA7B,KAA0C,EADvC,GAEPmC,mBAFO;AAFE;AAJQ,KAAvB;AAaA,MAAMyB,YAAY,GAAGJ,SAAS,CAACpI,IAA/B;;AAEAoI,EAAAA,SAAS,CAACpI,IAAV,GAAiB,UAAC1J,MAAD,EAAyD;AACxEkS,IAAAA,YAAY,GAD4D;AAIxE;;AACA,QAAI,CAAClJ,aAAD,IAAkBhJ,MAAM,IAAI,IAAhC,EAAsC;AACpC,aAAOgS,eAAe,CAACF,SAAD,EAAYT,UAAU,CAAC,CAAD,CAAtB,CAAtB;AACD,KAPuE;AAUxE;;;AACA,QAAIrI,aAAa,IAAIhJ,MAAM,IAAI,IAA/B,EAAqC;AACnC;AACD,KAbuE;;;AAgBxE,QAAI,OAAOA,MAAP,KAAkB,QAAtB,EAAgC;AAC9B,aACEqR,UAAU,CAACrR,MAAD,CAAV,IAAsBgS,eAAe,CAACF,SAAD,EAAYT,UAAU,CAACrR,MAAD,CAAtB,CADvC;AAGD,KApBuE;;;AAuBxE,QAAIoR,mBAAmB,CAACjX,OAApB,CAA4B6F,MAA5B,KAAmD,CAAvD,EAA0D;AACxD,UAAMmS,GAAG,GAAInS,MAAD,CAAqBnD,SAAjC;AACA,aAAOmV,eAAe,CAACF,SAAD,EAAYK,GAAZ,CAAtB;AACD,KA1BuE;;;AA6BxE,QAAId,UAAU,CAAClX,OAAX,CAAmB6F,MAAnB,KAAkD,CAAtD,EAAyD;AACvD,aAAOgS,eAAe,CAACF,SAAD,EAAY9R,MAAZ,CAAtB;AACD;AACF,GAhCD;;AAkCA8R,EAAAA,SAAS,CAACM,QAAV,GAAqB,YAAY;AAC/B,QAAMC,KAAK,GAAGhB,UAAU,CAAC,CAAD,CAAxB;;AACA,QAAI,CAACrI,aAAL,EAAoB;AAClB,aAAO8I,SAAS,CAACpI,IAAV,CAAe,CAAf,CAAP;AACD;;AACD,QAAMhQ,KAAK,GAAG2X,UAAU,CAAClX,OAAX,CAAmB6O,aAAnB,CAAd;AACA8I,IAAAA,SAAS,CAACpI,IAAV,CAAe2H,UAAU,CAAC3X,KAAK,GAAG,CAAT,CAAV,IAAyB2Y,KAAxC;AACD,GAPD;;AASAP,EAAAA,SAAS,CAACQ,YAAV,GAAyB,YAAY;AACnC,QAAMC,IAAI,GAAGlB,UAAU,CAACA,UAAU,CAAChK,MAAX,GAAoB,CAArB,CAAvB;;AACA,QAAI,CAAC2B,aAAL,EAAoB;AAClB,aAAO8I,SAAS,CAACpI,IAAV,CAAe6I,IAAf,CAAP;AACD;;AACD,QAAM7Y,KAAK,GAAG2X,UAAU,CAAClX,OAAX,CAAmB6O,aAAnB,CAAd;AACA,QAAMhJ,MAAM,GAAGqR,UAAU,CAAC3X,KAAK,GAAG,CAAT,CAAV,IAAyB6Y,IAAxC;AACAT,IAAAA,SAAS,CAACpI,IAAV,CAAe1J,MAAf;AACD,GARD;;AAUA,MAAM+R,gBAAgB,GAAGD,SAAS,CAACtI,QAAnC;;AAEAsI,EAAAA,SAAS,CAACtI,QAAV,GAAqB,UAACpL,KAAD,EAAiB;AACpCmT,IAAAA,SAAS,GAAGnT,KAAK,CAACmT,SAAN,IAAmBA,SAA/B;AACAQ,IAAAA,gBAAgB,CAAC3T,KAAD,CAAhB;AACD,GAHD;;AAKA0T,EAAAA,SAAS,CAACU,YAAV,GAAyB,UAACC,aAAD,EAAyB;AAChDb,IAAAA,eAAe,CAAC,IAAD,CAAf;AACAJ,IAAAA,yBAAyB,CAACxW,OAA1B,CAAkC,UAACT,EAAD;AAAA,aAAQA,EAAE,EAAV;AAAA,KAAlC;AAEA6W,IAAAA,mBAAmB,GAAGqB,aAAtB;AAEAb,IAAAA,eAAe,CAAC,KAAD,CAAf;AACAD,IAAAA,aAAa;AACbD,IAAAA,iBAAiB;AACjBF,IAAAA,yBAAyB,GAAGK,iBAAiB,CAACC,SAAD,CAA7C;AAEAA,IAAAA,SAAS,CAACtI,QAAV,CAAmB;AAACxD,MAAAA,aAAa,EAAEsL;AAAhB,KAAnB;AACD,GAZD;;AAcAE,EAAAA,yBAAyB,GAAGK,iBAAiB,CAACC,SAAD,CAA7C;AAEA,SAAOA,SAAP;AACD,CA1ND;;ACvCA,IAAMY,mBAAmB,GAAG;AAC1BC,EAAAA,SAAS,EAAE,YADe;AAE1BC,EAAAA,OAAO,EAAE,OAFiB;AAG1BC,EAAAA,KAAK,EAAE;AAHmB,CAA5B;AAMA;AACA;AACA;AACA;;AACA,SAASC,QAAT,CACEhQ,OADF,EAEE1E,KAFF,EAGyB;AACvB;AACA,6CAAa;AACXuE,IAAAA,SAAS,CACP,EAAEvE,KAAK,IAAIA,KAAK,CAAC4B,MAAjB,CADO,EAEP,CACE,4EADF,EAEE,kDAFF,EAGEyB,IAHF,CAGO,GAHP,CAFO,CAAT;AAOD;;AAED,MAAIoH,SAA2B,GAAG,EAAlC;AACA,MAAIkK,mBAA+B,GAAG,EAAtC;AACA,MAAIC,QAAQ,GAAG,KAAf;AAEA,MAAOhT,MAAP,GAAiB5B,KAAjB,CAAO4B,MAAP;AAEA,MAAMiT,WAAW,GAAGpY,gBAAgB,CAACuD,KAAD,EAAQ,CAAC,QAAD,CAAR,CAApC;AACA,MAAM8U,WAAW,qBAAOD,WAAP;AAAoBlN,IAAAA,OAAO,EAAE,QAA7B;AAAuCD,IAAAA,KAAK,EAAE;AAA9C,IAAjB;AACA,MAAMqN,UAAU;AACdrN,IAAAA,KAAK,EAAE5B,YAAY,CAAC4B;AADN,KAEXmN,WAFW;AAGdpN,IAAAA,YAAY,EAAE;AAHA,IAAhB;AAMA,MAAMuN,WAAW,GAAGtD,KAAK,CAAChN,OAAD,EAAUoQ,WAAV,CAAzB;AACA,MAAMG,qBAAqB,GAAGhY,gBAAgB,CAAC+X,WAAD,CAA9C;;AAEA,WAAS7N,SAAT,CAAmBzH,KAAnB,EAAuC;AACrC,QAAI,CAACA,KAAK,CAACkC,MAAP,IAAiBgT,QAArB,EAA+B;AAC7B;AACD;;AAED,QAAMM,UAAU,GAAIxV,KAAK,CAACkC,MAAP,CAA0BuT,OAA1B,CAAkCvT,MAAlC,CAAnB;;AAEA,QAAI,CAACsT,UAAL,EAAiB;AACf;AACD,KAToC;AAYrC;AACA;AACA;;;AACA,QAAMvN,OAAO,GACXuN,UAAU,CAAC1M,YAAX,CAAwB,oBAAxB,KACAxI,KAAK,CAAC2H,OADN,IAEA7B,YAAY,CAAC6B,OAHf,CAfqC;;AAqBrC,QAAIuN,UAAU,CAAC1W,MAAf,EAAuB;AACrB;AACD;;AAED,QAAIkB,KAAK,CAAC9D,IAAN,KAAe,YAAf,IAA+B,OAAOmZ,UAAU,CAACrN,KAAlB,KAA4B,SAA/D,EAA0E;AACxE;AACD;;AAED,QACEhI,KAAK,CAAC9D,IAAN,KAAe,YAAf,IACA+L,OAAO,CAAC5L,OAAR,CAAiBuY,mBAAD,CAA6B5U,KAAK,CAAC9D,IAAnC,CAAhB,IAA4D,CAF9D,EAGE;AACA;AACD;;AAED,QAAMgH,QAAQ,GAAG8O,KAAK,CAACwD,UAAD,EAAaH,UAAb,CAAtB;;AAEA,QAAInS,QAAJ,EAAc;AACZ+R,MAAAA,mBAAmB,GAAGA,mBAAmB,CAACzX,MAApB,CAA2B0F,QAA3B,CAAtB;AACD;AACF;;AAED,WAAS6L,EAAT,CACEjF,IADF,EAEEkF,SAFF,EAGEC,OAHF,EAIEC,OAJF,EAKQ;AAAA,QADNA,OACM;AADNA,MAAAA,OACM,GADuC,KACvC;AAAA;;AACNpF,IAAAA,IAAI,CAAClH,gBAAL,CAAsBoM,SAAtB,EAAiCC,OAAjC,EAA0CC,OAA1C;AACAnE,IAAAA,SAAS,CAACpN,IAAV,CAAe;AAACmM,MAAAA,IAAI,EAAJA,IAAD;AAAOkF,MAAAA,SAAS,EAATA,SAAP;AAAkBC,MAAAA,OAAO,EAAPA,OAAlB;AAA2BC,MAAAA,OAAO,EAAPA;AAA3B,KAAf;AACD;;AAED,WAASwG,iBAAT,CAA2BxS,QAA3B,EAAqD;AACnD,QAAOnE,SAAP,GAAoBmE,QAApB,CAAOnE,SAAP;AAEAgQ,IAAAA,EAAE,CAAChQ,SAAD,EAAY,YAAZ,EAA0B0I,SAA1B,EAAqCzM,aAArC,CAAF;AACA+T,IAAAA,EAAE,CAAChQ,SAAD,EAAY,WAAZ,EAAyB0I,SAAzB,CAAF;AACAsH,IAAAA,EAAE,CAAChQ,SAAD,EAAY,SAAZ,EAAuB0I,SAAvB,CAAF;AACAsH,IAAAA,EAAE,CAAChQ,SAAD,EAAY,OAAZ,EAAqB0I,SAArB,CAAF;AACD;;AAED,WAASkO,oBAAT,GAAsC;AACpC5K,IAAAA,SAAS,CAAC7N,OAAV,CAAkB,gBAAyD;AAAA,UAAvD4M,IAAuD,QAAvDA,IAAuD;AAAA,UAAjDkF,SAAiD,QAAjDA,SAAiD;AAAA,UAAtCC,OAAsC,QAAtCA,OAAsC;AAAA,UAA7BC,OAA6B,QAA7BA,OAA6B;AACzEpF,MAAAA,IAAI,CAAC/G,mBAAL,CAAyBiM,SAAzB,EAAoCC,OAApC,EAA6CC,OAA7C;AACD,KAFD;AAGAnE,IAAAA,SAAS,GAAG,EAAZ;AACD;;AAED,WAAS6K,cAAT,CAAwB1S,QAAxB,EAAkD;AAChD,QAAM2S,eAAe,GAAG3S,QAAQ,CAACgJ,OAAjC;AACA,QAAM4J,cAAc,GAAG5S,QAAQ,CAAC6I,MAAhC;AACA,QAAMgK,eAAe,GAAG7S,QAAQ,CAAC8I,OAAjC;;AAEA9I,IAAAA,QAAQ,CAACgJ,OAAT,GAAmB,UAAC8J,2BAAD,EAA8C;AAAA,UAA7CA,2BAA6C;AAA7CA,QAAAA,2BAA6C,GAAf,IAAe;AAAA;;AAC/D,UAAIA,2BAAJ,EAAiC;AAC/Bf,QAAAA,mBAAmB,CAAC/X,OAApB,CAA4B,UAACgG,QAAD,EAAc;AACxCA,UAAAA,QAAQ,CAACgJ,OAAT;AACD,SAFD;AAGD;;AAED+I,MAAAA,mBAAmB,GAAG,EAAtB;AAEAU,MAAAA,oBAAoB;AACpBE,MAAAA,eAAe;AAChB,KAXD;;AAaA3S,IAAAA,QAAQ,CAAC6I,MAAT,GAAkB,YAAY;AAC5B+J,MAAAA,cAAc;AACdb,MAAAA,mBAAmB,CAAC/X,OAApB,CAA4B,UAACgG,QAAD;AAAA,eAAcA,QAAQ,CAAC6I,MAAT,EAAd;AAAA,OAA5B;AACAmJ,MAAAA,QAAQ,GAAG,KAAX;AACD,KAJD;;AAMAhS,IAAAA,QAAQ,CAAC8I,OAAT,GAAmB,YAAY;AAC7B+J,MAAAA,eAAe;AACfd,MAAAA,mBAAmB,CAAC/X,OAApB,CAA4B,UAACgG,QAAD;AAAA,eAAcA,QAAQ,CAAC8I,OAAT,EAAd;AAAA,OAA5B;AACAkJ,MAAAA,QAAQ,GAAG,IAAX;AACD,KAJD;;AAMAQ,IAAAA,iBAAiB,CAACxS,QAAD,CAAjB;AACD;;AAEDqS,EAAAA,qBAAqB,CAACrY,OAAtB,CAA8B0Y,cAA9B;AAEA,SAAON,WAAP;AACD;;ACrJD,IAAMhQ,WAAwB,GAAG;AAC/BoD,EAAAA,IAAI,EAAE,aADyB;AAE/B7M,EAAAA,YAAY,EAAE,KAFiB;AAG/BY,EAAAA,EAH+B,cAG5ByG,QAH4B,EAGlB;AAAA;;AACX;AACA,QAAI,2BAACA,QAAQ,CAAC5C,KAAT,CAAewH,MAAhB,aAAC,sBAAuBmF,OAAxB,CAAJ,EAAqC;AACnC,iDAAa;AACXpI,QAAAA,SAAS,CACP3B,QAAQ,CAAC5C,KAAT,CAAegF,WADR,EAEP,gEAFO,CAAT;AAID;;AAED,aAAO,EAAP;AACD;;AAED,uBAAuBkE,WAAW,CAACtG,QAAQ,CAACuG,MAAV,CAAlC;AAAA,QAAO9H,GAAP,gBAAOA,GAAP;AAAA,QAAYmE,OAAZ,gBAAYA,OAAZ;;AAEA,QAAMkE,QAAQ,GAAG9G,QAAQ,CAAC5C,KAAT,CAAegF,WAAf,GACb2Q,qBAAqB,EADR,GAEb,IAFJ;AAIA,WAAO;AACL/O,MAAAA,QADK,sBACY;AACf,YAAI8C,QAAJ,EAAc;AACZrI,UAAAA,GAAG,CAACuU,YAAJ,CAAiBlM,QAAjB,EAA2BrI,GAAG,CAAC+H,iBAA/B;AACA/H,UAAAA,GAAG,CAAClC,YAAJ,CAAiB,kBAAjB,EAAqC,EAArC;AACAkC,UAAAA,GAAG,CAACtC,KAAJ,CAAU8W,QAAV,GAAqB,QAArB;AAEAjT,UAAAA,QAAQ,CAACwI,QAAT,CAAkB;AAAC7F,YAAAA,KAAK,EAAE,KAAR;AAAeD,YAAAA,SAAS,EAAE;AAA1B,WAAlB;AACD;AACF,OATI;AAUL0B,MAAAA,OAVK,qBAUW;AACd,YAAI0C,QAAJ,EAAc;AACZ,cAAO1K,kBAAP,GAA6BqC,GAAG,CAACtC,KAAjC,CAAOC,kBAAP;AACA,cAAMmH,QAAQ,GAAG2P,MAAM,CAAC9W,kBAAkB,CAACyE,OAAnB,CAA2B,IAA3B,EAAiC,EAAjC,CAAD,CAAvB,CAFY;AAKZ;AACA;;AACA+B,UAAAA,OAAO,CAACzG,KAAR,CAAcgX,eAAd,GAAmCC,IAAI,CAACC,KAAL,CAAW9P,QAAQ,GAAG,EAAtB,CAAnC;AAEAuD,UAAAA,QAAQ,CAAC3K,KAAT,CAAeC,kBAAf,GAAoCA,kBAApC;AACAC,UAAAA,kBAAkB,CAAC,CAACyK,QAAD,CAAD,EAAa,SAAb,CAAlB;AACD;AACF,OAvBI;AAwBLzC,MAAAA,MAxBK,oBAwBU;AACb,YAAIyC,QAAJ,EAAc;AACZA,UAAAA,QAAQ,CAAC3K,KAAT,CAAeC,kBAAf,GAAoC,KAApC;AACD;AACF,OA5BI;AA6BL+H,MAAAA,MA7BK,oBA6BU;AACb,YAAI2C,QAAJ,EAAc;AACZzK,UAAAA,kBAAkB,CAAC,CAACyK,QAAD,CAAD,EAAa,QAAb,CAAlB;AACD;AACF;AAjCI,KAAP;AAmCD;AAzD8B,CAAjC;AA4DA;AAEA,SAASiM,qBAAT,GAAiD;AAC/C,MAAMjM,QAAQ,GAAGzL,GAAG,EAApB;AACAyL,EAAAA,QAAQ,CAACwM,SAAT,GAAqB3b,cAArB;AACA0E,EAAAA,kBAAkB,CAAC,CAACyK,QAAD,CAAD,EAAa,QAAb,CAAlB;AACA,SAAOA,QAAP;AACD;;ACtED,IAAIyM,WAAW,GAAG;AAACxW,EAAAA,OAAO,EAAE,CAAV;AAAaC,EAAAA,OAAO,EAAE;AAAtB,CAAlB;AACA,IAAIwW,eAA2D,GAAG,EAAlE;;AAEA,SAASC,gBAAT,OAAgE;AAAA,MAArC1W,OAAqC,QAArCA,OAAqC;AAAA,MAA5BC,OAA4B,QAA5BA,OAA4B;AAC9DuW,EAAAA,WAAW,GAAG;AAACxW,IAAAA,OAAO,EAAPA,OAAD;AAAUC,IAAAA,OAAO,EAAPA;AAAV,GAAd;AACD;;AAED,SAAS0W,sBAAT,CAAgClI,GAAhC,EAAqD;AACnDA,EAAAA,GAAG,CAAC9L,gBAAJ,CAAqB,WAArB,EAAkC+T,gBAAlC;AACD;;AAED,SAASE,yBAAT,CAAmCnI,GAAnC,EAAwD;AACtDA,EAAAA,GAAG,CAAC3L,mBAAJ,CAAwB,WAAxB,EAAqC4T,gBAArC;AACD;;AAED,IAAMpR,YAA0B,GAAG;AACjCmD,EAAAA,IAAI,EAAE,cAD2B;AAEjC7M,EAAAA,YAAY,EAAE,KAFmB;AAGjCY,EAAAA,EAHiC,cAG9ByG,QAH8B,EAGpB;AACX,QAAMnE,SAAS,GAAGmE,QAAQ,CAACnE,SAA3B;AACA,QAAM2P,GAAG,GAAGhP,gBAAgB,CAACwD,QAAQ,CAAC5C,KAAT,CAAe4H,aAAf,IAAgCnJ,SAAjC,CAA5B;AAEA,QAAI+X,gBAAgB,GAAG,KAAvB;AACA,QAAIC,aAAa,GAAG,KAApB;AACA,QAAIC,WAAW,GAAG,IAAlB;AACA,QAAI7F,SAAS,GAAGjO,QAAQ,CAAC5C,KAAzB;;AAEA,aAAS2W,oBAAT,GAAyC;AACvC,aACE/T,QAAQ,CAAC5C,KAAT,CAAeiF,YAAf,KAAgC,SAAhC,IAA6CrC,QAAQ,CAAC1D,KAAT,CAAe4D,SAD9D;AAGD;;AAED,aAAS8T,WAAT,GAA6B;AAC3BxI,MAAAA,GAAG,CAAC9L,gBAAJ,CAAqB,WAArB,EAAkCqI,WAAlC;AACD;;AAED,aAASkM,cAAT,GAAgC;AAC9BzI,MAAAA,GAAG,CAAC3L,mBAAJ,CAAwB,WAAxB,EAAqCkI,WAArC;AACD;;AAED,aAASmM,2BAAT,GAA6C;AAC3CN,MAAAA,gBAAgB,GAAG,IAAnB;AACA5T,MAAAA,QAAQ,CAACwI,QAAT,CAAkB;AAAChF,QAAAA,sBAAsB,EAAE;AAAzB,OAAlB;AACAoQ,MAAAA,gBAAgB,GAAG,KAAnB;AACD;;AAED,aAAS7L,WAAT,CAAqBjL,KAArB,EAA8C;AAC5C;AACA;AACA,UAAMqX,qBAAqB,GAAGrX,KAAK,CAACkC,MAAN,GAC1BnD,SAAS,CAACoD,QAAV,CAAmBnC,KAAK,CAACkC,MAAzB,CAD0B,GAE1B,IAFJ;AAGA,UAAOqD,YAAP,GAAuBrC,QAAQ,CAAC5C,KAAhC,CAAOiF,YAAP;AACA,UAAOtF,OAAP,GAA2BD,KAA3B,CAAOC,OAAP;AAAA,UAAgBC,OAAhB,GAA2BF,KAA3B,CAAgBE,OAAhB;AAEA,UAAMoX,IAAI,GAAGvY,SAAS,CAAC6Q,qBAAV,EAAb;AACA,UAAM2H,SAAS,GAAGtX,OAAO,GAAGqX,IAAI,CAACpW,IAAjC;AACA,UAAMsW,SAAS,GAAGtX,OAAO,GAAGoX,IAAI,CAACzW,GAAjC;;AAEA,UAAIwW,qBAAqB,IAAI,CAACnU,QAAQ,CAAC5C,KAAT,CAAeuG,WAA7C,EAA0D;AACxD3D,QAAAA,QAAQ,CAACwI,QAAT,CAAkB;AAChB;AACAhF,UAAAA,sBAFgB,oCAES;AACvB,gBAAM4Q,IAAI,GAAGvY,SAAS,CAAC6Q,qBAAV,EAAb;AAEA,gBAAIzO,CAAC,GAAGlB,OAAR;AACA,gBAAIa,CAAC,GAAGZ,OAAR;;AAEA,gBAAIqF,YAAY,KAAK,SAArB,EAAgC;AAC9BpE,cAAAA,CAAC,GAAGmW,IAAI,CAACpW,IAAL,GAAYqW,SAAhB;AACAzW,cAAAA,CAAC,GAAGwW,IAAI,CAACzW,GAAL,GAAW2W,SAAf;AACD;;AAED,gBAAM3W,GAAG,GAAG0E,YAAY,KAAK,YAAjB,GAAgC+R,IAAI,CAACzW,GAArC,GAA2CC,CAAvD;AACA,gBAAMO,KAAK,GAAGkE,YAAY,KAAK,UAAjB,GAA8B+R,IAAI,CAACjW,KAAnC,GAA2CF,CAAzD;AACA,gBAAMH,MAAM,GAAGuE,YAAY,KAAK,YAAjB,GAAgC+R,IAAI,CAACtW,MAArC,GAA8CF,CAA7D;AACA,gBAAMI,IAAI,GAAGqE,YAAY,KAAK,UAAjB,GAA8B+R,IAAI,CAACpW,IAAnC,GAA0CC,CAAvD;AAEA,mBAAO;AACLsW,cAAAA,KAAK,EAAEpW,KAAK,GAAGH,IADV;AAELwW,cAAAA,MAAM,EAAE1W,MAAM,GAAGH,GAFZ;AAGLA,cAAAA,GAAG,EAAHA,GAHK;AAILQ,cAAAA,KAAK,EAALA,KAJK;AAKLL,cAAAA,MAAM,EAANA,MALK;AAMLE,cAAAA,IAAI,EAAJA;AANK,aAAP;AAQD;AA1Be,SAAlB;AA4BD;AACF;;AAED,aAASyW,MAAT,GAAwB;AACtB,UAAIzU,QAAQ,CAAC5C,KAAT,CAAeiF,YAAnB,EAAiC;AAC/BmR,QAAAA,eAAe,CAAC/Y,IAAhB,CAAqB;AAACuF,UAAAA,QAAQ,EAARA,QAAD;AAAWwL,UAAAA,GAAG,EAAHA;AAAX,SAArB;AACAkI,QAAAA,sBAAsB,CAAClI,GAAD,CAAtB;AACD;AACF;;AAED,aAASxC,OAAT,GAAyB;AACvBwK,MAAAA,eAAe,GAAGA,eAAe,CAACrZ,MAAhB,CAChB,UAACua,IAAD;AAAA,eAAUA,IAAI,CAAC1U,QAAL,KAAkBA,QAA5B;AAAA,OADgB,CAAlB;;AAIA,UAAIwT,eAAe,CAACrZ,MAAhB,CAAuB,UAACua,IAAD;AAAA,eAAUA,IAAI,CAAClJ,GAAL,KAAaA,GAAvB;AAAA,OAAvB,EAAmDnF,MAAnD,KAA8D,CAAlE,EAAqE;AACnEsN,QAAAA,yBAAyB,CAACnI,GAAD,CAAzB;AACD;AACF;;AAED,WAAO;AACLxH,MAAAA,QAAQ,EAAEyQ,MADL;AAELxQ,MAAAA,SAAS,EAAE+E,OAFN;AAGLjF,MAAAA,cAHK,4BAGkB;AACrBkK,QAAAA,SAAS,GAAGjO,QAAQ,CAAC5C,KAArB;AACD,OALI;AAML0G,MAAAA,aANK,yBAMS6Q,CANT,SAMkC;AAAA,YAArBtS,YAAqB,SAArBA,YAAqB;;AACrC,YAAIuR,gBAAJ,EAAsB;AACpB;AACD;;AAED,YACEvR,YAAY,KAAKjH,SAAjB,IACA6S,SAAS,CAAC5L,YAAV,KAA2BA,YAF7B,EAGE;AACA2G,UAAAA,OAAO;;AAEP,cAAI3G,YAAJ,EAAkB;AAChBoS,YAAAA,MAAM;;AAEN,gBACEzU,QAAQ,CAAC1D,KAAT,CAAe+L,SAAf,IACA,CAACwL,aADD,IAEA,CAACE,oBAAoB,EAHvB,EAIE;AACAC,cAAAA,WAAW;AACZ;AACF,WAVD,MAUO;AACLC,YAAAA,cAAc;AACdC,YAAAA,2BAA2B;AAC5B;AACF;AACF,OAhCI;AAiCL9P,MAAAA,OAjCK,qBAiCW;AACd,YAAIpE,QAAQ,CAAC5C,KAAT,CAAeiF,YAAf,IAA+B,CAACwR,aAApC,EAAmD;AACjD,cAAIC,WAAJ,EAAiB;AACf/L,YAAAA,WAAW,CAACwL,WAAD,CAAX;AACAO,YAAAA,WAAW,GAAG,KAAd;AACD;;AAED,cAAI,CAACC,oBAAoB,EAAzB,EAA6B;AAC3BC,YAAAA,WAAW;AACZ;AACF;AACF,OA5CI;AA6CLzP,MAAAA,SA7CK,qBA6CKoQ,CA7CL,EA6CQ7X,KA7CR,EA6CqB;AACxB,YAAIpB,YAAY,CAACoB,KAAD,CAAhB,EAAyB;AACvByW,UAAAA,WAAW,GAAG;AAACxW,YAAAA,OAAO,EAAED,KAAK,CAACC,OAAhB;AAAyBC,YAAAA,OAAO,EAAEF,KAAK,CAACE;AAAxC,WAAd;AACD;;AACD6W,QAAAA,aAAa,GAAG/W,KAAK,CAAC9D,IAAN,KAAe,OAA/B;AACD,OAlDI;AAmDLkL,MAAAA,QAnDK,sBAmDY;AACf,YAAIlE,QAAQ,CAAC5C,KAAT,CAAeiF,YAAnB,EAAiC;AAC/B6R,UAAAA,2BAA2B;AAC3BD,UAAAA,cAAc;AACdH,UAAAA,WAAW,GAAG,IAAd;AACD;AACF;AAzDI,KAAP;AA2DD;AAzJgC,CAAnC;;ACbA,SAASc,QAAT,CAAkBxX,KAAlB,EAAgCyX,QAAhC,EAA8E;AAAA;;AAC5E,SAAO;AACLlQ,IAAAA,aAAa,oBACRvH,KAAK,CAACuH,aADE;AAEX2I,MAAAA,SAAS,YACJ,CAAC,yBAAAlQ,KAAK,CAACuH,aAAN,0CAAqB2I,SAArB,KAAkC,EAAnC,EAAuCnT,MAAvC,CACD;AAAA,YAAEqL,IAAF,QAAEA,IAAF;AAAA,eAAYA,IAAI,KAAKqP,QAAQ,CAACrP,IAA9B;AAAA,OADC,CADI,GAIPqP,QAJO;AAFE;AADR,GAAP;AAWD;;AAED,IAAMvS,iBAAoC,GAAG;AAC3CkD,EAAAA,IAAI,EAAE,mBADqC;AAE3C7M,EAAAA,YAAY,EAAE,KAF6B;AAG3CY,EAAAA,EAH2C,cAGxCyG,QAHwC,EAG9B;AACX,QAAOnE,SAAP,GAAoBmE,QAApB,CAAOnE,SAAP;;AAEA,aAASsM,SAAT,GAA8B;AAC5B,aAAO,CAAC,CAACnI,QAAQ,CAAC5C,KAAT,CAAekF,iBAAxB;AACD;;AAED,QAAIzH,SAAJ;AACA,QAAIia,eAAe,GAAG,CAAC,CAAvB;AACA,QAAIlB,gBAAgB,GAAG,KAAvB;AACA,QAAImB,eAA8B,GAAG,EAArC;AAEA,QAAMF,QAGL,GAAG;AACFrP,MAAAA,IAAI,EAAE,wBADJ;AAEF0H,MAAAA,OAAO,EAAE,IAFP;AAGFC,MAAAA,KAAK,EAAE,YAHL;AAIF5T,MAAAA,EAJE,qBAIU;AAAA,YAAR+C,KAAQ,SAARA,KAAQ;;AACV,YAAI6L,SAAS,EAAb,EAAiB;AACf,cAAI4M,eAAe,CAAC5b,OAAhB,CAAwBmD,KAAK,CAACzB,SAA9B,MAA6C,CAAC,CAAlD,EAAqD;AACnDka,YAAAA,eAAe,GAAG,EAAlB;AACD;;AAED,cACEla,SAAS,KAAKyB,KAAK,CAACzB,SAApB,IACAka,eAAe,CAAC5b,OAAhB,CAAwBmD,KAAK,CAACzB,SAA9B,MAA6C,CAAC,CAFhD,EAGE;AACAka,YAAAA,eAAe,CAACta,IAAhB,CAAqB6B,KAAK,CAACzB,SAA3B;AACAmF,YAAAA,QAAQ,CAACwI,QAAT,CAAkB;AAChB;AACAhF,cAAAA,sBAAsB,EAAE;AAAA,uBACtBA,uBAAsB,CAAClH,KAAK,CAACzB,SAAP,CADA;AAAA;AAFR,aAAlB;AAKD;;AAEDA,UAAAA,SAAS,GAAGyB,KAAK,CAACzB,SAAlB;AACD;AACF;AAxBC,KAHJ;;AA8BA,aAAS2I,uBAAT,CAAgC3I,SAAhC,EAAwE;AACtE,aAAOma,2BAA2B,CAChCpa,gBAAgB,CAACC,SAAD,CADgB,EAEhCgB,SAAS,CAAC6Q,qBAAV,EAFgC,EAGhC5R,SAAS,CAACe,SAAS,CAACoZ,cAAV,EAAD,CAHuB,EAIhCH,eAJgC,CAAlC;AAMD;;AAED,aAASI,gBAAT,CAA0B/P,YAA1B,EAA8D;AAC5DyO,MAAAA,gBAAgB,GAAG,IAAnB;AACA5T,MAAAA,QAAQ,CAACwI,QAAT,CAAkBrD,YAAlB;AACAyO,MAAAA,gBAAgB,GAAG,KAAnB;AACD;;AAED,aAASuB,WAAT,GAA6B;AAC3B,UAAI,CAACvB,gBAAL,EAAuB;AACrBsB,QAAAA,gBAAgB,CAACN,QAAQ,CAAC5U,QAAQ,CAAC5C,KAAV,EAAiByX,QAAjB,CAAT,CAAhB;AACD;AACF;;AAED,WAAO;AACL7Q,MAAAA,QAAQ,EAAEmR,WADL;AAELrR,MAAAA,aAAa,EAAEqR,WAFV;AAGL5Q,MAAAA,SAHK,qBAGKoQ,CAHL,EAGQ7X,KAHR,EAGqB;AACxB,YAAIpB,YAAY,CAACoB,KAAD,CAAhB,EAAyB;AACvB,cAAMsY,KAAK,GAAGta,SAAS,CAACkF,QAAQ,CAACnE,SAAT,CAAmBoZ,cAAnB,EAAD,CAAvB;AACA,cAAMI,UAAU,GAAGD,KAAK,CAACzO,IAAN,CACjB,UAACyN,IAAD;AAAA,mBACEA,IAAI,CAACpW,IAAL,GAAY,CAAZ,IAAiBlB,KAAK,CAACC,OAAvB,IACAqX,IAAI,CAACjW,KAAL,GAAa,CAAb,IAAkBrB,KAAK,CAACC,OADxB,IAEAqX,IAAI,CAACzW,GAAL,GAAW,CAAX,IAAgBb,KAAK,CAACE,OAFtB,IAGAoX,IAAI,CAACtW,MAAL,GAAc,CAAd,IAAmBhB,KAAK,CAACE,OAJ3B;AAAA,WADiB,CAAnB;AAOA,cAAMtE,KAAK,GAAG0c,KAAK,CAACjc,OAAN,CAAckc,UAAd,CAAd;AACAP,UAAAA,eAAe,GAAGpc,KAAK,GAAG,CAAC,CAAT,GAAaA,KAAb,GAAqBoc,eAAvC;AACD;AACF,OAhBI;AAiBL5Q,MAAAA,QAjBK,sBAiBY;AACf4Q,QAAAA,eAAe,GAAG,CAAC,CAAnB;AACD;AAnBI,KAAP;AAqBD;AAvF0C,CAA7C;AA0FA,AAEO,SAASE,2BAAT,CACLM,oBADK,EAELC,YAFK,EAGLC,WAHK,EAILV,eAJK,EAYL;AACA;AACA,MAAIU,WAAW,CAACnP,MAAZ,GAAqB,CAArB,IAA0BiP,oBAAoB,KAAK,IAAvD,EAA6D;AAC3D,WAAOC,YAAP;AACD,GAJD;;;AAOA,MACEC,WAAW,CAACnP,MAAZ,KAAuB,CAAvB,IACAyO,eAAe,IAAI,CADnB,IAEAU,WAAW,CAAC,CAAD,CAAX,CAAexX,IAAf,GAAsBwX,WAAW,CAAC,CAAD,CAAX,CAAerX,KAHvC,EAIE;AACA,WAAOqX,WAAW,CAACV,eAAD,CAAX,IAAgCS,YAAvC;AACD;;AAED,UAAQD,oBAAR;AACE,SAAK,KAAL;AACA,SAAK,QAAL;AAAe;AACb,YAAMG,SAAS,GAAGD,WAAW,CAAC,CAAD,CAA7B;AACA,YAAME,QAAQ,GAAGF,WAAW,CAACA,WAAW,CAACnP,MAAZ,GAAqB,CAAtB,CAA5B;AACA,YAAMsP,KAAK,GAAGL,oBAAoB,KAAK,KAAvC;AAEA,YAAM3X,GAAG,GAAG8X,SAAS,CAAC9X,GAAtB;AACA,YAAMG,MAAM,GAAG4X,QAAQ,CAAC5X,MAAxB;AACA,YAAME,IAAI,GAAG2X,KAAK,GAAGF,SAAS,CAACzX,IAAb,GAAoB0X,QAAQ,CAAC1X,IAA/C;AACA,YAAMG,KAAK,GAAGwX,KAAK,GAAGF,SAAS,CAACtX,KAAb,GAAqBuX,QAAQ,CAACvX,KAAjD;AACA,YAAMoW,KAAK,GAAGpW,KAAK,GAAGH,IAAtB;AACA,YAAMwW,MAAM,GAAG1W,MAAM,GAAGH,GAAxB;AAEA,eAAO;AAACA,UAAAA,GAAG,EAAHA,GAAD;AAAMG,UAAAA,MAAM,EAANA,MAAN;AAAcE,UAAAA,IAAI,EAAJA,IAAd;AAAoBG,UAAAA,KAAK,EAALA,KAApB;AAA2BoW,UAAAA,KAAK,EAALA,KAA3B;AAAkCC,UAAAA,MAAM,EAANA;AAAlC,SAAP;AACD;;AACD,SAAK,MAAL;AACA,SAAK,OAAL;AAAc;AACZ,YAAMoB,OAAO,GAAGxC,IAAI,CAACyC,GAAL,OAAAzC,IAAI,EAAQoC,WAAW,CAACrM,GAAZ,CAAgB,UAACiM,KAAD;AAAA,iBAAWA,KAAK,CAACpX,IAAjB;AAAA,SAAhB,CAAR,CAApB;AACA,YAAM8X,QAAQ,GAAG1C,IAAI,CAAC2C,GAAL,OAAA3C,IAAI,EAAQoC,WAAW,CAACrM,GAAZ,CAAgB,UAACiM,KAAD;AAAA,iBAAWA,KAAK,CAACjX,KAAjB;AAAA,SAAhB,CAAR,CAArB;AACA,YAAM6X,YAAY,GAAGR,WAAW,CAACrb,MAAZ,CAAmB,UAACia,IAAD;AAAA,iBACtCkB,oBAAoB,KAAK,MAAzB,GACIlB,IAAI,CAACpW,IAAL,KAAc4X,OADlB,GAEIxB,IAAI,CAACjW,KAAL,KAAe2X,QAHmB;AAAA,SAAnB,CAArB;AAMA,YAAMnY,IAAG,GAAGqY,YAAY,CAAC,CAAD,CAAZ,CAAgBrY,GAA5B;AACA,YAAMG,OAAM,GAAGkY,YAAY,CAACA,YAAY,CAAC3P,MAAb,GAAsB,CAAvB,CAAZ,CAAsCvI,MAArD;AACA,YAAME,KAAI,GAAG4X,OAAb;AACA,YAAMzX,MAAK,GAAG2X,QAAd;;AACA,YAAMvB,MAAK,GAAGpW,MAAK,GAAGH,KAAtB;;AACA,YAAMwW,OAAM,GAAG1W,OAAM,GAAGH,IAAxB;;AAEA,eAAO;AAACA,UAAAA,GAAG,EAAHA,IAAD;AAAMG,UAAAA,MAAM,EAANA,OAAN;AAAcE,UAAAA,IAAI,EAAJA,KAAd;AAAoBG,UAAAA,KAAK,EAALA,MAApB;AAA2BoW,UAAAA,KAAK,EAALA,MAA3B;AAAkCC,UAAAA,MAAM,EAANA;AAAlC,SAAP;AACD;;AACD;AAAS;AACP,eAAOe,YAAP;AACD;AArCH;AAuCD;;AC9KD,IAAMhT,MAAc,GAAG;AACrBiD,EAAAA,IAAI,EAAE,QADe;AAErB7M,EAAAA,YAAY,EAAE,KAFO;AAGrBY,EAAAA,EAHqB,cAGlByG,QAHkB,EAGR;AACX,QAAOnE,SAAP,GAA4BmE,QAA5B,CAAOnE,SAAP;AAAA,QAAkB0K,MAAlB,GAA4BvG,QAA5B,CAAkBuG,MAAlB;;AAEA,aAAS0P,YAAT,GAA2D;AACzD,aAAOjW,QAAQ,CAACkI,cAAT,GACHlI,QAAQ,CAACkI,cAAT,CAAwB5L,KAAxB,CAA8B0S,QAA9B,CAAuCnT,SADpC,GAEHA,SAFJ;AAGD;;AAED,aAASqa,WAAT,CAAqBzd,KAArB,EAA6D;AAC3D,aAAOuH,QAAQ,CAAC5C,KAAT,CAAemF,MAAf,KAA0B,IAA1B,IAAkCvC,QAAQ,CAAC5C,KAAT,CAAemF,MAAf,KAA0B9J,KAAnE;AACD;;AAED,QAAI0d,WAA8B,GAAG,IAArC;AACA,QAAIC,WAA8B,GAAG,IAArC;;AAEA,aAASC,cAAT,GAAgC;AAC9B,UAAMC,cAAc,GAAGJ,WAAW,CAAC,WAAD,CAAX,GACnBD,YAAY,GAAGvJ,qBAAf,EADmB,GAEnB,IAFJ;AAGA,UAAM6J,cAAc,GAAGL,WAAW,CAAC,QAAD,CAAX,GACnB3P,MAAM,CAACmG,qBAAP,EADmB,GAEnB,IAFJ;;AAIA,UACG4J,cAAc,IAAIE,iBAAiB,CAACL,WAAD,EAAcG,cAAd,CAApC,IACCC,cAAc,IAAIC,iBAAiB,CAACJ,WAAD,EAAcG,cAAd,CAFtC,EAGE;AACA,YAAIvW,QAAQ,CAACkI,cAAb,EAA6B;AAC3BlI,UAAAA,QAAQ,CAACkI,cAAT,CAAwBuO,MAAxB;AACD;AACF;;AAEDN,MAAAA,WAAW,GAAGG,cAAd;AACAF,MAAAA,WAAW,GAAGG,cAAd;;AAEA,UAAIvW,QAAQ,CAAC1D,KAAT,CAAe+L,SAAnB,EAA8B;AAC5B0F,QAAAA,qBAAqB,CAACsI,cAAD,CAArB;AACD;AACF;;AAED,WAAO;AACLjS,MAAAA,OADK,qBACW;AACd,YAAIpE,QAAQ,CAAC5C,KAAT,CAAemF,MAAnB,EAA2B;AACzB8T,UAAAA,cAAc;AACf;AACF;AALI,KAAP;AAOD;AAnDoB,CAAvB;AAsDA;AAEA,SAASG,iBAAT,CACEE,KADF,EAEEC,KAFF,EAGW;AACT,MAAID,KAAK,IAAIC,KAAb,EAAoB;AAClB,WACED,KAAK,CAAC/Y,GAAN,KAAcgZ,KAAK,CAAChZ,GAApB,IACA+Y,KAAK,CAACvY,KAAN,KAAgBwY,KAAK,CAACxY,KADtB,IAEAuY,KAAK,CAAC5Y,MAAN,KAAiB6Y,KAAK,CAAC7Y,MAFvB,IAGA4Y,KAAK,CAAC1Y,IAAN,KAAe2Y,KAAK,CAAC3Y,IAJvB;AAMD;;AAED,SAAO,IAAP;AACD;;AC9DD8Q,KAAK,CAAC5J,eAAN,CAAsB;AAACxC,EAAAA,SAAS,EAAE;AAAZ,CAAtB;;;;;;;;;;;;"}