{"version": 3, "file": "index.cjs", "sources": ["../src/text.ts"], "sourcesContent": ["import { Node } from '@tiptap/core'\n\n/**\n * This extension allows you to create text nodes.\n * @see https://www.tiptap.dev/api/nodes/text\n */\nexport const Text = Node.create({\n  name: 'text',\n  group: 'inline',\n})\n"], "names": ["Node"], "mappings": ";;;;;;AAEA;;;AAGG;AACU,MAAA,IAAI,GAAGA,SAAI,CAAC,MAAM,CAAC;AAC9B,IAAA,IAAI,EAAE,MAAM;AACZ,IAAA,KAAK,EAAE,QAAQ;AAChB,CAAA;;;;;"}