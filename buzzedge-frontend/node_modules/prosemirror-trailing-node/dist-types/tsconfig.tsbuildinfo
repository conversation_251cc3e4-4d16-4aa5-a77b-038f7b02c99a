{"program": {"fileNames": ["../../../node_modules/.pnpm/typescript@5.0.4/node_modules/typescript/lib/lib.es5.d.ts", "../../../node_modules/.pnpm/typescript@5.0.4/node_modules/typescript/lib/lib.es2015.d.ts", "../../../node_modules/.pnpm/typescript@5.0.4/node_modules/typescript/lib/lib.es2016.d.ts", "../../../node_modules/.pnpm/typescript@5.0.4/node_modules/typescript/lib/lib.es2017.d.ts", "../../../node_modules/.pnpm/typescript@5.0.4/node_modules/typescript/lib/lib.es2018.d.ts", "../../../node_modules/.pnpm/typescript@5.0.4/node_modules/typescript/lib/lib.es2019.d.ts", "../../../node_modules/.pnpm/typescript@5.0.4/node_modules/typescript/lib/lib.es2020.d.ts", "../../../node_modules/.pnpm/typescript@5.0.4/node_modules/typescript/lib/lib.es2021.d.ts", "../../../node_modules/.pnpm/typescript@5.0.4/node_modules/typescript/lib/lib.es2022.d.ts", "../../../node_modules/.pnpm/typescript@5.0.4/node_modules/typescript/lib/lib.es2023.d.ts", "../../../node_modules/.pnpm/typescript@5.0.4/node_modules/typescript/lib/lib.esnext.d.ts", "../../../node_modules/.pnpm/typescript@5.0.4/node_modules/typescript/lib/lib.dom.d.ts", "../../../node_modules/.pnpm/typescript@5.0.4/node_modules/typescript/lib/lib.dom.iterable.d.ts", "../../../node_modules/.pnpm/typescript@5.0.4/node_modules/typescript/lib/lib.es2015.core.d.ts", "../../../node_modules/.pnpm/typescript@5.0.4/node_modules/typescript/lib/lib.es2015.collection.d.ts", "../../../node_modules/.pnpm/typescript@5.0.4/node_modules/typescript/lib/lib.es2015.generator.d.ts", "../../../node_modules/.pnpm/typescript@5.0.4/node_modules/typescript/lib/lib.es2015.iterable.d.ts", "../../../node_modules/.pnpm/typescript@5.0.4/node_modules/typescript/lib/lib.es2015.promise.d.ts", "../../../node_modules/.pnpm/typescript@5.0.4/node_modules/typescript/lib/lib.es2015.proxy.d.ts", "../../../node_modules/.pnpm/typescript@5.0.4/node_modules/typescript/lib/lib.es2015.reflect.d.ts", "../../../node_modules/.pnpm/typescript@5.0.4/node_modules/typescript/lib/lib.es2015.symbol.d.ts", "../../../node_modules/.pnpm/typescript@5.0.4/node_modules/typescript/lib/lib.es2015.symbol.wellknown.d.ts", "../../../node_modules/.pnpm/typescript@5.0.4/node_modules/typescript/lib/lib.es2016.array.include.d.ts", "../../../node_modules/.pnpm/typescript@5.0.4/node_modules/typescript/lib/lib.es2017.object.d.ts", "../../../node_modules/.pnpm/typescript@5.0.4/node_modules/typescript/lib/lib.es2017.sharedmemory.d.ts", "../../../node_modules/.pnpm/typescript@5.0.4/node_modules/typescript/lib/lib.es2017.string.d.ts", "../../../node_modules/.pnpm/typescript@5.0.4/node_modules/typescript/lib/lib.es2017.intl.d.ts", "../../../node_modules/.pnpm/typescript@5.0.4/node_modules/typescript/lib/lib.es2017.typedarrays.d.ts", "../../../node_modules/.pnpm/typescript@5.0.4/node_modules/typescript/lib/lib.es2018.asyncgenerator.d.ts", "../../../node_modules/.pnpm/typescript@5.0.4/node_modules/typescript/lib/lib.es2018.asynciterable.d.ts", "../../../node_modules/.pnpm/typescript@5.0.4/node_modules/typescript/lib/lib.es2018.intl.d.ts", "../../../node_modules/.pnpm/typescript@5.0.4/node_modules/typescript/lib/lib.es2018.promise.d.ts", "../../../node_modules/.pnpm/typescript@5.0.4/node_modules/typescript/lib/lib.es2018.regexp.d.ts", "../../../node_modules/.pnpm/typescript@5.0.4/node_modules/typescript/lib/lib.es2019.array.d.ts", "../../../node_modules/.pnpm/typescript@5.0.4/node_modules/typescript/lib/lib.es2019.object.d.ts", "../../../node_modules/.pnpm/typescript@5.0.4/node_modules/typescript/lib/lib.es2019.string.d.ts", "../../../node_modules/.pnpm/typescript@5.0.4/node_modules/typescript/lib/lib.es2019.symbol.d.ts", "../../../node_modules/.pnpm/typescript@5.0.4/node_modules/typescript/lib/lib.es2019.intl.d.ts", "../../../node_modules/.pnpm/typescript@5.0.4/node_modules/typescript/lib/lib.es2020.bigint.d.ts", "../../../node_modules/.pnpm/typescript@5.0.4/node_modules/typescript/lib/lib.es2020.date.d.ts", "../../../node_modules/.pnpm/typescript@5.0.4/node_modules/typescript/lib/lib.es2020.promise.d.ts", "../../../node_modules/.pnpm/typescript@5.0.4/node_modules/typescript/lib/lib.es2020.sharedmemory.d.ts", "../../../node_modules/.pnpm/typescript@5.0.4/node_modules/typescript/lib/lib.es2020.string.d.ts", "../../../node_modules/.pnpm/typescript@5.0.4/node_modules/typescript/lib/lib.es2020.symbol.wellknown.d.ts", "../../../node_modules/.pnpm/typescript@5.0.4/node_modules/typescript/lib/lib.es2020.intl.d.ts", "../../../node_modules/.pnpm/typescript@5.0.4/node_modules/typescript/lib/lib.es2020.number.d.ts", "../../../node_modules/.pnpm/typescript@5.0.4/node_modules/typescript/lib/lib.es2021.promise.d.ts", "../../../node_modules/.pnpm/typescript@5.0.4/node_modules/typescript/lib/lib.es2021.string.d.ts", "../../../node_modules/.pnpm/typescript@5.0.4/node_modules/typescript/lib/lib.es2021.weakref.d.ts", "../../../node_modules/.pnpm/typescript@5.0.4/node_modules/typescript/lib/lib.es2021.intl.d.ts", "../../../node_modules/.pnpm/typescript@5.0.4/node_modules/typescript/lib/lib.es2022.array.d.ts", "../../../node_modules/.pnpm/typescript@5.0.4/node_modules/typescript/lib/lib.es2022.error.d.ts", "../../../node_modules/.pnpm/typescript@5.0.4/node_modules/typescript/lib/lib.es2022.intl.d.ts", "../../../node_modules/.pnpm/typescript@5.0.4/node_modules/typescript/lib/lib.es2022.object.d.ts", "../../../node_modules/.pnpm/typescript@5.0.4/node_modules/typescript/lib/lib.es2022.sharedmemory.d.ts", "../../../node_modules/.pnpm/typescript@5.0.4/node_modules/typescript/lib/lib.es2022.string.d.ts", "../../../node_modules/.pnpm/typescript@5.0.4/node_modules/typescript/lib/lib.es2022.regexp.d.ts", "../../../node_modules/.pnpm/typescript@5.0.4/node_modules/typescript/lib/lib.es2023.array.d.ts", "../../../node_modules/.pnpm/typescript@5.0.4/node_modules/typescript/lib/lib.esnext.intl.d.ts", "../../../node_modules/.pnpm/typescript@5.0.4/node_modules/typescript/lib/lib.decorators.d.ts", "../../../node_modules/.pnpm/typescript@5.0.4/node_modules/typescript/lib/lib.decorators.legacy.d.ts", "../../../node_modules/.pnpm/orderedmap@2.0.0/node_modules/orderedmap/dist/index.d.ts", "../../../node_modules/.pnpm/prosemirror-model@1.22.1/node_modules/prosemirror-model/dist/index.d.ts", "../../../node_modules/.pnpm/prosemirror-transform@1.9.0/node_modules/prosemirror-transform/dist/index.d.ts", "../../../node_modules/.pnpm/prosemirror-view@1.33.8/node_modules/prosemirror-view/dist/index.d.ts", "../../../node_modules/.pnpm/prosemirror-state@1.4.3/node_modules/prosemirror-state/dist/index.d.ts", "../src/trailing-node-plugin.ts", "../src/index.ts"], "fileInfos": [{"version": "6a6b471e7e43e15ef6f8fe617a22ce4ecb0e34efa6c3dfcfe7cebd392bcca9d2", "affectsGlobalScope": true}, "45b7ab580deca34ae9729e97c13cfd999df04416a79116c3bfb483804f85ded4", "dc48272d7c333ccf58034c0026162576b7d50ea0e69c3b9292f803fc20720fd5", "27147504487dc1159369da4f4da8a26406364624fa9bc3db632f7d94a5bae2c3", "5e1c4c362065a6b95ff952c0eab010f04dcd2c3494e813b493ecfd4fcb9fc0d8", "68d73b4a11549f9c0b7d352d10e91e5dca8faa3322bfb77b661839c42b1ddec7", "5efce4fc3c29ea84e8928f97adec086e3dc876365e0982cc8479a07954a3efd4", "feecb1be483ed332fad555aff858affd90a48ab19ba7272ee084704eb7167569", "5514e54f17d6d74ecefedc73c504eadffdeda79c7ea205cf9febead32d45c4bc", "f4e736d6c8d69ae5b3ab0ddfcaa3dc365c3e76909d6660af5b4e979b3934ac20", "eeeb3aca31fbadef8b82502484499dfd1757204799a6f5b33116201c810676ec", {"version": "fcd3ecc9f764f06f4d5c467677f4f117f6abf49dee6716283aa204ff1162498b", "affectsGlobalScope": true}, {"version": "9a60b92bca4c1257db03b349d58e63e4868cfc0d1c8d0ba60c2dbc63f4e6c9f6", "affectsGlobalScope": true}, {"version": "f296963760430fb65b4e5d91f0ed770a91c6e77455bacf8fa23a1501654ede0e", "affectsGlobalScope": true}, {"version": "5114a95689b63f96b957e00216bc04baf9e1a1782aa4d8ee7e5e9acbf768e301", "affectsGlobalScope": true}, {"version": "4443e68b35f3332f753eacc66a04ac1d2053b8b035a0e0ac1d455392b5e243b3", "affectsGlobalScope": true}, {"version": "ab22100fdd0d24cfc2cc59d0a00fc8cf449830d9c4030dc54390a46bd562e929", "affectsGlobalScope": true}, {"version": "f7bd636ae3a4623c503359ada74510c4005df5b36de7f23e1db8a5c543fd176b", "affectsGlobalScope": true}, {"version": "ce691fb9e5c64efb9547083e4a34091bcbe5bdb41027e310ebba8f7d96a98671", "affectsGlobalScope": true}, {"version": "8d697a2a929a5fcb38b7a65594020fcef05ec1630804a33748829c5ff53640d0", "affectsGlobalScope": true}, {"version": "0c20f4d2358eb679e4ae8a4432bdd96c857a2960fd6800b21ec4008ec59d60ea", "affectsGlobalScope": true}, {"version": "36ae84ccc0633f7c0787bc6108386c8b773e95d3b052d9464a99cd9b8795fbec", "affectsGlobalScope": true}, {"version": "82d0d8e269b9eeac02c3bd1c9e884e85d483fcb2cd168bccd6bc54df663da031", "affectsGlobalScope": true}, {"version": "b8deab98702588840be73d67f02412a2d45a417a3c097b2e96f7f3a42ac483d1", "affectsGlobalScope": true}, {"version": "4738f2420687fd85629c9efb470793bb753709c2379e5f85bc1815d875ceadcd", "affectsGlobalScope": true}, {"version": "2f11ff796926e0832f9ae148008138ad583bd181899ab7dd768a2666700b1893", "affectsGlobalScope": true}, {"version": "376d554d042fb409cb55b5cbaf0b2b4b7e669619493c5d18d5fa8bd67273f82a", "affectsGlobalScope": true}, {"version": "9fc46429fbe091ac5ad2608c657201eb68b6f1b8341bd6d670047d32ed0a88fa", "affectsGlobalScope": true}, {"version": "61c37c1de663cf4171e1192466e52c7a382afa58da01b1dc75058f032ddf0839", "affectsGlobalScope": true}, {"version": "c4138a3dd7cd6cf1f363ca0f905554e8d81b45844feea17786cdf1626cb8ea06", "affectsGlobalScope": true}, {"version": "6ff3e2452b055d8f0ec026511c6582b55d935675af67cdb67dd1dc671e8065df", "affectsGlobalScope": true}, {"version": "03de17b810f426a2f47396b0b99b53a82c1b60e9cba7a7edda47f9bb077882f4", "affectsGlobalScope": true}, {"version": "8184c6ddf48f0c98429326b428478ecc6143c27f79b79e85740f17e6feb090f1", "affectsGlobalScope": true}, {"version": "261c4d2cf86ac5a89ad3fb3fafed74cbb6f2f7c1d139b0540933df567d64a6ca", "affectsGlobalScope": true}, {"version": "6af1425e9973f4924fca986636ac19a0cf9909a7e0d9d3009c349e6244e957b6", "affectsGlobalScope": true}, {"version": "576711e016cf4f1804676043e6a0a5414252560eb57de9faceee34d79798c850", "affectsGlobalScope": true}, {"version": "89c1b1281ba7b8a96efc676b11b264de7a8374c5ea1e6617f11880a13fc56dc6", "affectsGlobalScope": true}, {"version": "15a630d6817718a2ddd7088c4f83e4673fde19fa992d2eae2cf51132a302a5d3", "affectsGlobalScope": true}, {"version": "b7e9f95a7387e3f66be0ed6db43600c49cec33a3900437ce2fd350d9b7cb16f2", "affectsGlobalScope": true}, {"version": "01e0ee7e1f661acedb08b51f8a9b7d7f959e9cdb6441360f06522cc3aea1bf2e", "affectsGlobalScope": true}, {"version": "ac17a97f816d53d9dd79b0d235e1c0ed54a8cc6a0677e9a3d61efb480b2a3e4e", "affectsGlobalScope": true}, {"version": "bf14a426dbbf1022d11bd08d6b8e709a2e9d246f0c6c1032f3b2edb9a902adbe", "affectsGlobalScope": true}, {"version": "ec0104fee478075cb5171e5f4e3f23add8e02d845ae0165bfa3f1099241fa2aa", "affectsGlobalScope": true}, {"version": "2b72d528b2e2fe3c57889ca7baef5e13a56c957b946906d03767c642f386bbc3", "affectsGlobalScope": true}, {"version": "9cc66b0513ad41cb5f5372cca86ef83a0d37d1c1017580b7dace3ea5661836df", "affectsGlobalScope": true}, {"version": "368af93f74c9c932edd84c58883e736c9e3d53cec1fe24c0b0ff451f529ceab1", "affectsGlobalScope": true}, {"version": "709efdae0cb5df5f49376cde61daacc95cdd44ae4671da13a540da5088bf3f30", "affectsGlobalScope": true}, {"version": "995c005ab91a498455ea8dfb63aa9f83fa2ea793c3d8aa344be4a1678d06d399", "affectsGlobalScope": true}, {"version": "61ed9b6d07af959e745fb11f9593ecd743b279418cc8a99448ea3cd5f3b3eb22", "affectsGlobalScope": true}, {"version": "038a2f66a34ee7a9c2fbc3584c8ab43dff2995f8c68e3f566f4c300d2175e31e", "affectsGlobalScope": true}, {"version": "4fa6ed14e98aa80b91f61b9805c653ee82af3502dc21c9da5268d3857772ca05", "affectsGlobalScope": true}, {"version": "f5c92f2c27b06c1a41b88f6db8299205aee52c2a2943f7ed29bd585977f254e8", "affectsGlobalScope": true}, {"version": "b7feb7967c6c6003e11f49efa8f5de989484e0a6ba2e5a6c41b55f8b8bd85dba", "affectsGlobalScope": true}, {"version": "8444af78980e3b20b49324f4a16ba35024fef3ee069a0eb67616ea6ca821c47a", "affectsGlobalScope": true}, {"version": "b9ea5778ff8b50d7c04c9890170db34c26a5358cccba36844fe319f50a43a61a", "affectsGlobalScope": true}, {"version": "3287d9d085fbd618c3971944b65b4be57859f5415f495b33a6adc994edd2f004", "affectsGlobalScope": true}, {"version": "50d53ccd31f6667aff66e3d62adf948879a3a16f05d89882d1188084ee415bbc", "affectsGlobalScope": true}, {"version": "25de46552b782d43cb7284df22fe2a265de387cf0248b747a7a1b647d81861f6", "affectsGlobalScope": true}, {"version": "307c8b7ebbd7f23a92b73a4c6c0a697beca05b06b036c23a34553e5fe65e4fdc", "affectsGlobalScope": true}, {"version": "189c0703923150aa30673fa3de411346d727cc44a11c75d05d7cf9ef095daa22", "affectsGlobalScope": true}, {"version": "782dec38049b92d4e85c1585fbea5474a219c6984a35b004963b00beb1aab538", "affectsGlobalScope": true}, "3c616cbdf6f11c71a1cadba225121b0733b1c3da2a4855d7247a22bc64ee28cd", "a2112692a74a4ea317af69acc736f817205f352e9dae1ef2801c323a5bd776a3", "2a941965ca18ae8cf36f9d17638ea903a4263bcff7250e42e6c8fac5543df1d8", {"version": "3a2e4595137107331eb8abb0a09d0632bdbf74ea805bd1fffe552fd8d399022d", "affectsGlobalScope": true}, "e29c3246bccba476f4285c89ea0c026b6bfdf9e3d15b6edf2d50e7ea1a59ecfb", {"version": "faf097f40c9ed1387e0324547e4ec2bb5f80beeafd33867944afb563b6bb7bb6", "signature": "b2f9ecf121d9b5bfa024ae910bf1534ef51604fbb345774b178ba6838fc61e91"}, "0615d9f8c0ffb1770a5f761e46f531bbf18c5e067fa6cfe6d280ef005f63f405"], "root": [67, 68], "options": {"allowSyntheticDefaultImports": true, "allowUnreachableCode": false, "composite": true, "declaration": true, "emitDeclarationOnly": true, "esModuleInterop": true, "experimentalDecorators": false, "importsNotUsedAsValues": 0, "jsx": 2, "module": 99, "noImplicitReturns": true, "noUnusedLocals": true, "noUnusedParameters": true, "outDir": "./", "skipLibCheck": true, "sourceMap": true, "strict": true, "target": 99, "useDefineForClassFields": true}, "fileIdsList": [[62], [63, 64, 65], [63], [63, 64, 66], [67], [63, 66], [66]], "referencedMap": [[63, 1], [66, 2], [64, 3], [65, 4], [68, 5], [67, 6]], "exportedModulesMap": [[63, 1], [66, 2], [64, 3], [65, 4], [68, 5], [67, 7]], "semanticDiagnosticsPerFile": [62, 63, 66, 64, 65, 60, 61, 12, 13, 15, 14, 2, 16, 17, 18, 19, 20, 21, 22, 23, 3, 4, 27, 24, 25, 26, 28, 29, 30, 5, 31, 32, 33, 34, 6, 38, 35, 36, 37, 39, 7, 40, 45, 46, 41, 42, 43, 44, 8, 50, 47, 48, 49, 51, 9, 52, 53, 54, 57, 55, 56, 58, 10, 1, 11, 59, 68, 67], "latestChangedDtsFile": "./index.d.ts"}, "version": "5.0.4"}