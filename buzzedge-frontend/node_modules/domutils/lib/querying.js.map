{"version": 3, "file": "querying.js", "sourceRoot": "https://raw.githubusercontent.com/fb55/domutils/0ab8bcf1ecfc70dfc93291a4cb2496578ac25e9c/src/", "sources": ["querying.ts"], "names": [], "mappings": ";;AAYA,wBAOC;AAYD,oBA4CC;AAWD,oCAKC;AAWD,0BAkBC;AAUD,8BASC;AAYD,0BA+BC;AAtLD,yCAA8E;AAE9E;;;;;;;;;GASG;AACH,SAAgB,MAAM,CAClB,IAAgC,EAChC,IAAyB,EACzB,OAAc,EACd,KAAwB;IADxB,wBAAA,EAAA,cAAc;IACd,sBAAA,EAAA,gBAAwB;IAExB,OAAO,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;AAC3E,CAAC;AAED;;;;;;;;;GASG;AACH,SAAgB,IAAI,CAChB,IAAgC,EAChC,KAA6B,EAC7B,OAAgB,EAChB,KAAa;IAEb,IAAM,MAAM,GAAc,EAAE,CAAC;IAC7B,6CAA6C;IAC7C,IAAM,SAAS,GAAgB,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;IACxE,8CAA8C;IAC9C,IAAM,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC;IAEvB,SAAS,CAAC;QACN,sEAAsE;QACtE,IAAI,UAAU,CAAC,CAAC,CAAC,IAAI,SAAS,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC;YACvC,qDAAqD;YACrD,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAC1B,OAAO,MAAM,CAAC;YAClB,CAAC;YAED,sDAAsD;YACtD,SAAS,CAAC,KAAK,EAAE,CAAC;YAClB,UAAU,CAAC,KAAK,EAAE,CAAC;YAEnB,0DAA0D;YAC1D,SAAS;QACb,CAAC;QAED,IAAM,IAAI,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QAE3C,IAAI,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;YACb,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAClB,IAAI,EAAE,KAAK,IAAI,CAAC;gBAAE,OAAO,MAAM,CAAC;QACpC,CAAC;QAED,IAAI,OAAO,IAAI,IAAA,wBAAW,EAAC,IAAI,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC3D;;;eAGG;YACH,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;YACtB,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACrC,CAAC;IACL,CAAC;AACL,CAAC;AAED;;;;;;;;GAQG;AACH,SAAgB,YAAY,CACxB,IAA0B,EAC1B,KAAU;IAEV,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC5B,CAAC;AAED;;;;;;;;GAQG;AACH,SAAgB,OAAO,CACnB,IAAgC,EAChC,KAA6B,EAC7B,OAAc;IAAd,wBAAA,EAAA,cAAc;IAEd,IAAM,aAAa,GAAG,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;IAC7D,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,aAAa,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;QAC5C,IAAM,IAAI,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC;QAC9B,IAAI,IAAA,kBAAK,EAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;YAC5B,OAAO,IAAI,CAAC;QAChB,CAAC;QACD,IAAI,OAAO,IAAI,IAAA,wBAAW,EAAC,IAAI,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC3D,IAAM,KAAK,GAAG,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;YACjD,IAAI,KAAK;gBAAE,OAAO,KAAK,CAAC;QAC5B,CAAC;IACL,CAAC;IAED,OAAO,IAAI,CAAC;AAChB,CAAC;AAED;;;;;;;GAOG;AACH,SAAgB,SAAS,CACrB,IAAgC,EAChC,KAA6B;IAE7B,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAChD,UAAC,IAAI;QACD,OAAA,CAAC,IAAA,kBAAK,EAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC;YAC3B,CAAC,IAAA,wBAAW,EAAC,IAAI,CAAC,IAAI,SAAS,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;IADrD,CACqD,CAC5D,CAAC;AACN,CAAC;AAED;;;;;;;;;GASG;AACH,SAAgB,OAAO,CACnB,IAAgC,EAChC,KAA6B;IAE7B,IAAM,MAAM,GAAG,EAAE,CAAC;IAClB,IAAM,SAAS,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;IAC3D,IAAM,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC;IAEvB,SAAS,CAAC;QACN,IAAI,UAAU,CAAC,CAAC,CAAC,IAAI,SAAS,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC;YACvC,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACzB,OAAO,MAAM,CAAC;YAClB,CAAC;YAED,sDAAsD;YACtD,SAAS,CAAC,KAAK,EAAE,CAAC;YAClB,UAAU,CAAC,KAAK,EAAE,CAAC;YAEnB,0DAA0D;YAC1D,SAAS;QACb,CAAC;QAED,IAAM,IAAI,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QAE3C,IAAI,IAAA,kBAAK,EAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC;YAAE,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAEjD,IAAI,IAAA,wBAAW,EAAC,IAAI,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAChD,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;YACtB,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACrC,CAAC;IACL,CAAC;AACL,CAAC"}