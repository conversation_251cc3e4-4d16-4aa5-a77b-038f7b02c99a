{"version": 3, "file": "legacy.js", "sourceRoot": "https://raw.githubusercontent.com/fb55/domutils/0ab8bcf1ecfc70dfc93291a4cb2496578ac25e9c/src/", "sources": ["legacy.ts"], "names": [], "mappings": ";;AA+GA,kCAGC;AAYD,kCAQC;AAWD,wCAOC;AAYD,oDAYC;AAYD,wDAYC;AAYD,oDAOC;AA3ND,yCAA6D;AAE7D,6CAAgD;AAqBhD;;GAEG;AACH,IAAM,MAAM,GAGR;IACA,QAAQ,YAAC,IAAI;QACT,IAAI,OAAO,IAAI,KAAK,UAAU,EAAE,CAAC;YAC7B,OAAO,UAAC,IAAa,IAAK,OAAA,IAAA,kBAAK,EAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,EAA9B,CAA8B,CAAC;QAC7D,CAAC;aAAM,IAAI,IAAI,KAAK,GAAG,EAAE,CAAC;YACtB,OAAO,kBAAK,CAAC;QACjB,CAAC;QACD,OAAO,UAAC,IAAa,IAAK,OAAA,IAAA,kBAAK,EAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,KAAK,IAAI,EAAjC,CAAiC,CAAC;IAChE,CAAC;IACD,QAAQ,YAAC,IAAI;QACT,IAAI,OAAO,IAAI,KAAK,UAAU,EAAE,CAAC;YAC7B,OAAO,UAAC,IAAa,IAAK,OAAA,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,EAAf,CAAe,CAAC;QAC9C,CAAC;QACD,OAAO,UAAC,IAAa,IAAK,OAAA,IAAI,CAAC,IAAI,KAAK,IAAI,EAAlB,CAAkB,CAAC;IACjD,CAAC;IACD,YAAY,YAAC,IAAI;QACb,IAAI,OAAO,IAAI,KAAK,UAAU,EAAE,CAAC;YAC7B,OAAO,UAAC,IAAa,IAAK,OAAA,IAAA,mBAAM,EAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,EAA/B,CAA+B,CAAC;QAC9D,CAAC;QACD,OAAO,UAAC,IAAa,IAAK,OAAA,IAAA,mBAAM,EAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,KAAK,IAAI,EAAlC,CAAkC,CAAC;IACjE,CAAC;CACJ,CAAC;AAEF;;;;;;;;GAQG;AACH,SAAS,cAAc,CACnB,MAAc,EACd,KAAwD;IAExD,IAAI,OAAO,KAAK,KAAK,UAAU,EAAE,CAAC;QAC9B,OAAO,UAAC,IAAa,IAAK,OAAA,IAAA,kBAAK,EAAC,IAAI,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,EAA1C,CAA0C,CAAC;IACzE,CAAC;IACD,OAAO,UAAC,IAAa,IAAK,OAAA,IAAA,kBAAK,EAAC,IAAI,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,KAAK,EAA7C,CAA6C,CAAC;AAC5E,CAAC;AAED;;;;;;;;GAQG;AACH,SAAS,YAAY,CAAC,CAAW,EAAE,CAAW;IAC1C,OAAO,UAAC,IAAa,IAAK,OAAA,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,EAAlB,CAAkB,CAAC;AACjD,CAAC;AAED;;;;;;;GAOG;AACH,SAAS,WAAW,CAAC,OAAwB;IACzC,IAAM,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,UAAC,GAAG;QACvC,IAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC;QAC3B,OAAO,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC;YACpD,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC;YACpB,CAAC,CAAC,cAAc,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;IACrC,CAAC,CAAC,CAAC;IAEH,OAAO,KAAK,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;AAClE,CAAC;AAED;;;;;;;GAOG;AACH,SAAgB,WAAW,CAAC,OAAwB,EAAE,IAAa;IAC/D,IAAM,IAAI,GAAG,WAAW,CAAC,OAAO,CAAC,CAAC;IAClC,OAAO,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;AACpC,CAAC;AAED;;;;;;;;;GASG;AACH,SAAgB,WAAW,CACvB,OAAwB,EACxB,KAA0B,EAC1B,OAAgB,EAChB,KAAwB;IAAxB,sBAAA,EAAA,gBAAwB;IAExB,IAAM,IAAI,GAAG,WAAW,CAAC,OAAO,CAAC,CAAC;IAClC,OAAO,IAAI,CAAC,CAAC,CAAC,IAAA,oBAAM,EAAC,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;AAC3D,CAAC;AAED;;;;;;;;GAQG;AACH,SAAgB,cAAc,CAC1B,EAAsC,EACtC,KAA0B,EAC1B,OAAc;IAAd,wBAAA,EAAA,cAAc;IAEd,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC;QAAE,KAAK,GAAG,CAAC,KAAK,CAAC,CAAC;IAC3C,OAAO,IAAA,qBAAO,EAAC,cAAc,CAAC,IAAI,EAAE,EAAE,CAAC,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;AAC7D,CAAC;AAED;;;;;;;;;GASG;AACH,SAAgB,oBAAoB,CAChC,OAA6C,EAC7C,KAA0B,EAC1B,OAAc,EACd,KAAwB;IADxB,wBAAA,EAAA,cAAc;IACd,sBAAA,EAAA,gBAAwB;IAExB,OAAO,IAAA,oBAAM,EACT,MAAM,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,EAC3B,KAAK,EACL,OAAO,EACP,KAAK,CACK,CAAC;AACnB,CAAC;AAED;;;;;;;;;GASG;AACH,SAAgB,sBAAsB,CAClC,SAA+C,EAC/C,KAA0B,EAC1B,OAAc,EACd,KAAwB;IADxB,wBAAA,EAAA,cAAc;IACd,sBAAA,EAAA,gBAAwB;IAExB,OAAO,IAAA,oBAAM,EACT,cAAc,CAAC,OAAO,EAAE,SAAS,CAAC,EAClC,KAAK,EACL,OAAO,EACP,KAAK,CACK,CAAC;AACnB,CAAC;AAED;;;;;;;;;GASG;AACH,SAAgB,oBAAoB,CAChC,IAAoD,EACpD,KAA0B,EAC1B,OAAc,EACd,KAAwB;IADxB,wBAAA,EAAA,cAAc;IACd,sBAAA,EAAA,gBAAwB;IAExB,OAAO,IAAA,oBAAM,EAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAc,CAAC,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;AAC7E,CAAC"}