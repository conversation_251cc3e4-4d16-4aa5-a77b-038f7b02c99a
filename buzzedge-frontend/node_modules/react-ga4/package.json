{"name": "react-ga4", "version": "2.1.0", "description": "React Google Analytics 4", "main": "dist/index.js", "types": "types/index.d.ts", "scripts": {"build": "NODE_ENV=production babel src -d dist", "postbuild": "tsc src/index.js --declaration --allowJs --emitDeclarationOnly --outDir types", "prepublishOnly": "npm run build && npm test", "test": "NODE_ENV=test jest"}, "author": "<PERSON> (https://yap.nu)", "license": "MIT", "keywords": ["GA", "GTM", "Google Analytics", "Google Analytics 4", "Google Tag Manager"], "repository": {"type": "git", "url": "git+https://github.com/codler/react-ga4.git"}, "devDependencies": {"@babel/cli": "^7.20.7", "@babel/core": "^7.20.12", "@babel/preset-env": "^7.20.2", "babel-jest": "^29.3.1", "jest": "^29.3.1", "typescript": "^4.9.4"}, "jest": {"testPathIgnorePatterns": ["dist"]}}