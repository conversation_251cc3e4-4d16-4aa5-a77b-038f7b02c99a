{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Development/BuzzEdge/buzzedge-frontend/node_modules/highlight.js/es/core.js"], "sourcesContent": ["// https://nodejs.org/api/packages.html#packages_writing_dual_packages_while_avoiding_or_minimizing_hazards\nimport HighlightJS from '../lib/core.js';\nexport { HighlightJS };\nexport default HighlightJS;\n"], "names": [], "mappings": "AAAA,2GAA2G;;;;AAC3G;;;uCAEe,iJAAA,CAAA,UAAW", "ignoreList": [0], "debugId": null}}]}