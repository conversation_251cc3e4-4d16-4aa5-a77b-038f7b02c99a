{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Development/BuzzEdge/buzzedge-frontend/src/lib/api.ts"], "sourcesContent": ["// API Configuration\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || \"http://localhost:5001\";\n\nexport class ApiError extends Error {\n  constructor(message: string, public status: number, public response?: any) {\n    super(message);\n    this.name = \"ApiError\";\n  }\n}\n\nexport interface ApiResponse<T> {\n  success: boolean;\n  data?: T;\n  error?: string;\n  message?: string;\n}\n\nclass ApiClient {\n  private baseURL: string;\n  private cache: Map<string, { data: any; timestamp: number; ttl: number }>;\n\n  constructor(baseURL: string) {\n    this.baseURL = baseURL;\n    this.cache = new Map();\n  }\n\n  private getCacheKey(\n    endpoint: string,\n    params?: Record<string, string>\n  ): string {\n    const url = new URL(endpoint, this.baseURL);\n    if (params) {\n      Object.entries(params).forEach(([key, value]) => {\n        url.searchParams.append(key, value);\n      });\n    }\n    return url.pathname + url.search;\n  }\n\n  private isValidCache(cacheEntry: {\n    timestamp: number;\n    ttl: number;\n  }): boolean {\n    return Date.now() - cacheEntry.timestamp < cacheEntry.ttl * 1000;\n  }\n\n  private setCache(key: string, data: any, ttl: number = 300): void {\n    this.cache.set(key, {\n      data,\n      timestamp: Date.now(),\n      ttl,\n    });\n  }\n\n  private getCache(key: string): any | null {\n    const entry = this.cache.get(key);\n    if (entry && this.isValidCache(entry)) {\n      return entry.data;\n    }\n    if (entry) {\n      this.cache.delete(key); // Remove expired cache\n    }\n    return null;\n  }\n\n  private async request<T>(\n    endpoint: string,\n    options: RequestInit = {}\n  ): Promise<T> {\n    const url = `${this.baseURL}${endpoint}`;\n\n    // Get auth token if available\n    const getAuthToken = () => {\n      if (typeof window !== \"undefined\") {\n        return localStorage.getItem(\"buzzedge_auth_token\");\n      }\n      return null;\n    };\n\n    const token = getAuthToken();\n    const authHeaders: Record<string, string> = token\n      ? { Authorization: `Bearer ${token}` }\n      : {};\n\n    const config: RequestInit = {\n      headers: {\n        \"Content-Type\": \"application/json\",\n        ...authHeaders,\n        ...options.headers,\n      },\n      ...options,\n    };\n\n    try {\n      const response = await fetch(url, config);\n\n      if (!response.ok) {\n        const errorData = await response.json().catch(() => ({}));\n        throw new ApiError(\n          errorData.error || `HTTP ${response.status}: ${response.statusText}`,\n          response.status,\n          errorData\n        );\n      }\n\n      const data = await response.json();\n      return data;\n    } catch (error) {\n      if (error instanceof ApiError) {\n        throw error;\n      }\n\n      // Network or other errors\n      throw new ApiError(\n        error instanceof Error ? error.message : \"Network error occurred\",\n        0\n      );\n    }\n  }\n\n  async get<T>(\n    endpoint: string,\n    params?: Record<string, string>,\n    options: { cache?: boolean; ttl?: number } = {}\n  ): Promise<T> {\n    const { cache = true, ttl = 300 } = options;\n    const cacheKey = this.getCacheKey(endpoint, params);\n\n    // Check cache first\n    if (cache) {\n      const cachedData = this.getCache(cacheKey);\n      if (cachedData) {\n        return cachedData;\n      }\n    }\n\n    const url = new URL(endpoint, this.baseURL);\n    if (params) {\n      Object.entries(params).forEach(([key, value]) => {\n        url.searchParams.append(key, value);\n      });\n    }\n\n    const data = await this.request<T>(url.pathname + url.search);\n\n    // Cache the response\n    if (cache) {\n      this.setCache(cacheKey, data, ttl);\n    }\n\n    return data;\n  }\n\n  async post<T>(endpoint: string, data?: any): Promise<T> {\n    return this.request<T>(endpoint, {\n      method: \"POST\",\n      body: data ? JSON.stringify(data) : undefined,\n    });\n  }\n\n  async put<T>(endpoint: string, data?: any): Promise<T> {\n    return this.request<T>(endpoint, {\n      method: \"PUT\",\n      body: data ? JSON.stringify(data) : undefined,\n    });\n  }\n\n  async delete<T>(endpoint: string): Promise<T> {\n    return this.request<T>(endpoint, {\n      method: \"DELETE\",\n    });\n  }\n\n  // Cache management methods\n  clearCache(): void {\n    this.cache.clear();\n  }\n\n  clearCacheByPattern(pattern: string): void {\n    const regex = new RegExp(pattern);\n    for (const [key] of this.cache) {\n      if (regex.test(key)) {\n        this.cache.delete(key);\n      }\n    }\n  }\n\n  getCacheStats(): { size: number; keys: string[] } {\n    return {\n      size: this.cache.size,\n      keys: Array.from(this.cache.keys()),\n    };\n  }\n}\n\n// Create and export the API client instance\nexport const apiClient = new ApiClient(API_BASE_URL);\n\n// Health check function\nexport const checkApiHealth = async (): Promise<boolean> => {\n  try {\n    await apiClient.get(\"/health\");\n    return true;\n  } catch (error) {\n    console.error(\"API health check failed:\", error);\n    return false;\n  }\n};\n"], "names": [], "mappings": "AAAA,oBAAoB;;;;;;AACC;AAArB,MAAM,eAAe,6DAAmC;AAEjD,MAAM,iBAAiB;;;IAC5B,YAAY,OAAe,EAAE,AAAO,MAAc,EAAE,AAAO,QAAc,CAAE;QACzE,KAAK,CAAC,eAD4B,SAAA,aAAuB,WAAA;QAEzD,IAAI,CAAC,IAAI,GAAG;IACd;AACF;AASA,MAAM;IACI,QAAgB;IAChB,MAAkE;IAE1E,YAAY,OAAe,CAAE;QAC3B,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,KAAK,GAAG,IAAI;IACnB;IAEQ,YACN,QAAgB,EAChB,MAA+B,EACvB;QACR,MAAM,MAAM,IAAI,IAAI,UAAU,IAAI,CAAC,OAAO;QAC1C,IAAI,QAAQ;YACV,OAAO,OAAO,CAAC,QAAQ,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;gBAC1C,IAAI,YAAY,CAAC,MAAM,CAAC,KAAK;YAC/B;QACF;QACA,OAAO,IAAI,QAAQ,GAAG,IAAI,MAAM;IAClC;IAEQ,aAAa,UAGpB,EAAW;QACV,OAAO,KAAK,GAAG,KAAK,WAAW,SAAS,GAAG,WAAW,GAAG,GAAG;IAC9D;IAEQ,SAAS,GAAW,EAAE,IAAS,EAAE,MAAc,GAAG,EAAQ;QAChE,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK;YAClB;YACA,WAAW,KAAK,GAAG;YACnB;QACF;IACF;IAEQ,SAAS,GAAW,EAAc;QACxC,MAAM,QAAQ,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;QAC7B,IAAI,SAAS,IAAI,CAAC,YAAY,CAAC,QAAQ;YACrC,OAAO,MAAM,IAAI;QACnB;QACA,IAAI,OAAO;YACT,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,uBAAuB;QACjD;QACA,OAAO;IACT;IAEA,MAAc,QACZ,QAAgB,EAChB,UAAuB,CAAC,CAAC,EACb;QACZ,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,GAAG,UAAU;QAExC,8BAA8B;QAC9B,MAAM,eAAe;YACnB,wCAAmC;gBACjC,OAAO,aAAa,OAAO,CAAC;YAC9B;;QAEF;QAEA,MAAM,QAAQ;QACd,MAAM,cAAsC,QACxC;YAAE,eAAe,CAAC,OAAO,EAAE,OAAO;QAAC,IACnC,CAAC;QAEL,MAAM,SAAsB;YAC1B,SAAS;gBACP,gBAAgB;gBAChB,GAAG,WAAW;gBACd,GAAG,QAAQ,OAAO;YACpB;YACA,GAAG,OAAO;QACZ;QAEA,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,KAAK;YAElC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC,CAAC,CAAC;gBACvD,MAAM,IAAI,SACR,UAAU,KAAK,IAAI,CAAC,KAAK,EAAE,SAAS,MAAM,CAAC,EAAE,EAAE,SAAS,UAAU,EAAE,EACpE,SAAS,MAAM,EACf;YAEJ;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,OAAO;QACT,EAAE,OAAO,OAAO;YACd,IAAI,iBAAiB,UAAU;gBAC7B,MAAM;YACR;YAEA,0BAA0B;YAC1B,MAAM,IAAI,SACR,iBAAiB,QAAQ,MAAM,OAAO,GAAG,0BACzC;QAEJ;IACF;IAEA,MAAM,IACJ,QAAgB,EAChB,MAA+B,EAC/B,UAA6C,CAAC,CAAC,EACnC;QACZ,MAAM,EAAE,QAAQ,IAAI,EAAE,MAAM,GAAG,EAAE,GAAG;QACpC,MAAM,WAAW,IAAI,CAAC,WAAW,CAAC,UAAU;QAE5C,oBAAoB;QACpB,IAAI,OAAO;YACT,MAAM,aAAa,IAAI,CAAC,QAAQ,CAAC;YACjC,IAAI,YAAY;gBACd,OAAO;YACT;QACF;QAEA,MAAM,MAAM,IAAI,IAAI,UAAU,IAAI,CAAC,OAAO;QAC1C,IAAI,QAAQ;YACV,OAAO,OAAO,CAAC,QAAQ,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;gBAC1C,IAAI,YAAY,CAAC,MAAM,CAAC,KAAK;YAC/B;QACF;QAEA,MAAM,OAAO,MAAM,IAAI,CAAC,OAAO,CAAI,IAAI,QAAQ,GAAG,IAAI,MAAM;QAE5D,qBAAqB;QACrB,IAAI,OAAO;YACT,IAAI,CAAC,QAAQ,CAAC,UAAU,MAAM;QAChC;QAEA,OAAO;IACT;IAEA,MAAM,KAAQ,QAAgB,EAAE,IAAU,EAAc;QACtD,OAAO,IAAI,CAAC,OAAO,CAAI,UAAU;YAC/B,QAAQ;YACR,MAAM,OAAO,KAAK,SAAS,CAAC,QAAQ;QACtC;IACF;IAEA,MAAM,IAAO,QAAgB,EAAE,IAAU,EAAc;QACrD,OAAO,IAAI,CAAC,OAAO,CAAI,UAAU;YAC/B,QAAQ;YACR,MAAM,OAAO,KAAK,SAAS,CAAC,QAAQ;QACtC;IACF;IAEA,MAAM,OAAU,QAAgB,EAAc;QAC5C,OAAO,IAAI,CAAC,OAAO,CAAI,UAAU;YAC/B,QAAQ;QACV;IACF;IAEA,2BAA2B;IAC3B,aAAmB;QACjB,IAAI,CAAC,KAAK,CAAC,KAAK;IAClB;IAEA,oBAAoB,OAAe,EAAQ;QACzC,MAAM,QAAQ,IAAI,OAAO;QACzB,KAAK,MAAM,CAAC,IAAI,IAAI,IAAI,CAAC,KAAK,CAAE;YAC9B,IAAI,MAAM,IAAI,CAAC,MAAM;gBACnB,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;YACpB;QACF;IACF;IAEA,gBAAkD;QAChD,OAAO;YACL,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI;YACrB,MAAM,MAAM,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI;QAClC;IACF;AACF;AAGO,MAAM,YAAY,IAAI,UAAU;AAGhC,MAAM,iBAAiB;IAC5B,IAAI;QACF,MAAM,UAAU,GAAG,CAAC;QACpB,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 174, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Development/BuzzEdge/buzzedge-frontend/src/components/PerformanceMonitor.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useEffect, useState } from \"react\";\nimport { apiClient } from \"@/lib/api\";\n\ninterface PerformanceMetrics {\n  pageLoadTime: number;\n  apiResponseTime: number;\n  cacheHitRate: number;\n  totalRequests: number;\n  cachedRequests: number;\n}\n\ninterface PerformanceMonitorProps {\n  enabled?: boolean;\n  showStats?: boolean;\n}\n\nexport function PerformanceMonitor({\n  enabled = process.env.NODE_ENV === \"development\",\n  showStats = false,\n}: PerformanceMonitorProps) {\n  const [metrics, setMetrics] = useState<PerformanceMetrics>({\n    pageLoadTime: 0,\n    apiResponseTime: 0,\n    cacheHitRate: 0,\n    totalRequests: 0,\n    cachedRequests: 0,\n  });\n  const [isVisible, setIsVisible] = useState(false);\n\n  useEffect(() => {\n    if (!enabled) return;\n\n    // Measure page load time\n    const measurePageLoad = () => {\n      if (typeof window !== \"undefined\" && window.performance) {\n        const navigation = performance.getEntriesByType(\n          \"navigation\"\n        )[0] as PerformanceNavigationTiming;\n        if (navigation) {\n          const loadTime = navigation.loadEventEnd - navigation.fetchStart;\n          setMetrics((prev) => ({ ...prev, pageLoadTime: loadTime }));\n        }\n      }\n    };\n\n    // Measure API performance\n    const measureApiPerformance = () => {\n      const cacheStats = apiClient.getCacheStats();\n      setMetrics((prev) => ({\n        ...prev,\n        totalRequests: cacheStats.size,\n        cachedRequests: cacheStats.size,\n        cacheHitRate:\n          cacheStats.size > 0\n            ? (cacheStats.size / (cacheStats.size + 1)) * 100\n            : 0,\n      }));\n    };\n\n    // Initial measurements\n    setTimeout(measurePageLoad, 1000);\n    measureApiPerformance();\n\n    // Update metrics periodically\n    const interval = setInterval(measureApiPerformance, 5000);\n\n    return () => clearInterval(interval);\n  }, [enabled]);\n\n  if (!enabled || !showStats) return null;\n\n  return (\n    <>\n      {/* Performance Stats Toggle */}\n      <button\n        onClick={() => setIsVisible(!isVisible)}\n        className=\"fixed bottom-4 right-4 z-50 bg-gray-800 text-white p-2 rounded-full shadow-lg hover:bg-gray-700 transition-colors\"\n        title=\"Performance Stats\"\n      >\n        📊\n      </button>\n\n      {/* Performance Stats Panel */}\n      {isVisible && (\n        <div className=\"fixed bottom-16 right-4 z-50 bg-white border border-gray-200 rounded-lg shadow-lg p-4 w-80\">\n          <div className=\"flex items-center justify-between mb-3\">\n            <h3 className=\"font-semibold text-gray-900\">Performance Stats</h3>\n            <button\n              onClick={() => setIsVisible(false)}\n              className=\"text-gray-400 hover:text-gray-600\"\n            >\n              ✕\n            </button>\n          </div>\n\n          <div className=\"space-y-3 text-sm\">\n            <div className=\"flex justify-between\">\n              <span className=\"text-gray-600\">Page Load Time:</span>\n              <span className=\"font-mono\">\n                {metrics.pageLoadTime > 0\n                  ? `${Math.round(metrics.pageLoadTime)}ms`\n                  : \"Measuring...\"}\n              </span>\n            </div>\n\n            <div className=\"flex justify-between\">\n              <span className=\"text-gray-600\">Cache Hit Rate:</span>\n              <span className=\"font-mono\">\n                {metrics.cacheHitRate.toFixed(1)}%\n              </span>\n            </div>\n\n            <div className=\"flex justify-between\">\n              <span className=\"text-gray-600\">Cached Requests:</span>\n              <span className=\"font-mono\">{metrics.cachedRequests}</span>\n            </div>\n\n            <div className=\"border-t pt-2\">\n              <button\n                onClick={() => {\n                  apiClient.clearCache();\n                  setMetrics((prev) => ({\n                    ...prev,\n                    cachedRequests: 0,\n                    cacheHitRate: 0,\n                  }));\n                }}\n                className=\"w-full text-xs bg-red-50 text-red-600 py-1 px-2 rounded hover:bg-red-100 transition-colors\"\n              >\n                Clear Cache\n              </button>\n            </div>\n          </div>\n        </div>\n      )}\n    </>\n  );\n}\n\n// Web Vitals monitoring\nexport function WebVitalsMonitor() {\n  useEffect(() => {\n    if (typeof window === \"undefined\") return;\n\n    // Monitor Core Web Vitals\n    const observer = new PerformanceObserver((list) => {\n      for (const entry of list.getEntries()) {\n        const metric = entry.name;\n        const value =\n          (entry as any).value ||\n          (entry as any).processingStart ||\n          entry.duration;\n\n        // Log metrics in development\n        if (process.env.NODE_ENV === \"development\") {\n          console.log(`${metric}:`, value);\n        }\n\n        // Send to analytics in production\n        if (process.env.NODE_ENV === \"production\") {\n          // Example: Send to Google Analytics or other analytics service\n          // gtag('event', metric, { value: Math.round(value) });\n        }\n      }\n    });\n\n    // Observe different metric types\n    try {\n      observer.observe({ entryTypes: [\"measure\", \"navigation\", \"paint\"] });\n    } catch (error) {\n      console.warn(\"Performance Observer not supported:\", error);\n    }\n\n    return () => observer.disconnect();\n  }, []);\n\n  return null;\n}\n\n// Image loading optimization\nexport function ImageLoadMonitor() {\n  useEffect(() => {\n    if (typeof window === \"undefined\") return;\n\n    const images = document.querySelectorAll(\"img[data-src]\");\n\n    const imageObserver = new IntersectionObserver((entries) => {\n      entries.forEach((entry) => {\n        if (entry.isIntersecting) {\n          const img = entry.target as HTMLImageElement;\n          const src = img.getAttribute(\"data-src\");\n          if (src) {\n            img.src = src;\n            img.removeAttribute(\"data-src\");\n            imageObserver.unobserve(img);\n          }\n        }\n      });\n    });\n\n    images.forEach((img) => imageObserver.observe(img));\n\n    return () => imageObserver.disconnect();\n  }, []);\n\n  return null;\n}\n\n// Performance utilities\nexport const performanceUtils = {\n  // Measure function execution time\n  measureTime: async function <T>(\n    fn: () => Promise<T>,\n    label: string\n  ): Promise<T> {\n    const start = performance.now();\n    const result = await fn();\n    const end = performance.now();\n\n    if (process.env.NODE_ENV === \"development\") {\n      console.log(`${label}: ${Math.round(end - start)}ms`);\n    }\n\n    return result;\n  },\n\n  // Debounce function for performance\n  debounce: function <T extends (...args: any[]) => any>(\n    func: T,\n    wait: number\n  ): (...args: Parameters<T>) => void {\n    let timeout: NodeJS.Timeout;\n    return (...args: Parameters<T>) => {\n      clearTimeout(timeout);\n      timeout = setTimeout(() => func(...args), wait);\n    };\n  },\n\n  // Throttle function for performance\n  throttle: function <T extends (...args: any[]) => any>(\n    func: T,\n    limit: number\n  ): (...args: Parameters<T>) => void {\n    let inThrottle: boolean;\n    return (...args: Parameters<T>) => {\n      if (!inThrottle) {\n        func(...args);\n        inThrottle = true;\n        setTimeout(() => (inThrottle = false), limit);\n      }\n    };\n  },\n};\n"], "names": [], "mappings": ";;;;;;AA6NQ;;AA3NR;AACA;;;AAHA;;;AAkBO,SAAS,mBAAmB,EACjC,UAAU,oDAAyB,aAAa,EAChD,YAAY,KAAK,EACO;;IACxB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsB;QACzD,cAAc;QACd,iBAAiB;QACjB,cAAc;QACd,eAAe;QACf,gBAAgB;IAClB;IACA,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR,IAAI,CAAC,SAAS;YAEd,yBAAyB;YACzB,MAAM;gEAAkB;oBACtB,IAAI,aAAkB,eAAe,OAAO,WAAW,EAAE;wBACvD,MAAM,aAAa,YAAY,gBAAgB,CAC7C,aACD,CAAC,EAAE;wBACJ,IAAI,YAAY;4BACd,MAAM,WAAW,WAAW,YAAY,GAAG,WAAW,UAAU;4BAChE;gFAAW,CAAC,OAAS,CAAC;wCAAE,GAAG,IAAI;wCAAE,cAAc;oCAAS,CAAC;;wBAC3D;oBACF;gBACF;;YAEA,0BAA0B;YAC1B,MAAM;sEAAwB;oBAC5B,MAAM,aAAa,oHAAA,CAAA,YAAS,CAAC,aAAa;oBAC1C;8EAAW,CAAC,OAAS,CAAC;gCACpB,GAAG,IAAI;gCACP,eAAe,WAAW,IAAI;gCAC9B,gBAAgB,WAAW,IAAI;gCAC/B,cACE,WAAW,IAAI,GAAG,IACd,AAAC,WAAW,IAAI,GAAG,CAAC,WAAW,IAAI,GAAG,CAAC,IAAK,MAC5C;4BACR,CAAC;;gBACH;;YAEA,uBAAuB;YACvB,WAAW,iBAAiB;YAC5B;YAEA,8BAA8B;YAC9B,MAAM,WAAW,YAAY,uBAAuB;YAEpD;gDAAO,IAAM,cAAc;;QAC7B;uCAAG;QAAC;KAAQ;IAEZ,IAAI,CAAC,WAAW,CAAC,WAAW,OAAO;IAEnC,qBACE;;0BAEE,6LAAC;gBACC,SAAS,IAAM,aAAa,CAAC;gBAC7B,WAAU;gBACV,OAAM;0BACP;;;;;;YAKA,2BACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA8B;;;;;;0CAC5C,6LAAC;gCACC,SAAS,IAAM,aAAa;gCAC5B,WAAU;0CACX;;;;;;;;;;;;kCAKH,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAAgB;;;;;;kDAChC,6LAAC;wCAAK,WAAU;kDACb,QAAQ,YAAY,GAAG,IACpB,GAAG,KAAK,KAAK,CAAC,QAAQ,YAAY,EAAE,EAAE,CAAC,GACvC;;;;;;;;;;;;0CAIR,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAAgB;;;;;;kDAChC,6LAAC;wCAAK,WAAU;;4CACb,QAAQ,YAAY,CAAC,OAAO,CAAC;4CAAG;;;;;;;;;;;;;0CAIrC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAAgB;;;;;;kDAChC,6LAAC;wCAAK,WAAU;kDAAa,QAAQ,cAAc;;;;;;;;;;;;0CAGrD,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCACC,SAAS;wCACP,oHAAA,CAAA,YAAS,CAAC,UAAU;wCACpB,WAAW,CAAC,OAAS,CAAC;gDACpB,GAAG,IAAI;gDACP,gBAAgB;gDAChB,cAAc;4CAChB,CAAC;oCACH;oCACA,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;AASf;GAzHgB;KAAA;AA4HT,SAAS;;IACd,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,uCAAmC;;YAAM;YAEzC,0BAA0B;YAC1B,MAAM,WAAW,IAAI;8CAAoB,CAAC;oBACxC,KAAK,MAAM,SAAS,KAAK,UAAU,GAAI;wBACrC,MAAM,SAAS,MAAM,IAAI;wBACzB,MAAM,QACJ,AAAC,MAAc,KAAK,IACpB,AAAC,MAAc,eAAe,IAC9B,MAAM,QAAQ;wBAEhB,6BAA6B;wBAC7B,wCAA4C;4BAC1C,QAAQ,GAAG,CAAC,GAAG,OAAO,CAAC,CAAC,EAAE;wBAC5B;wBAEA,kCAAkC;wBAClC,IAAI,oDAAyB,cAAc;wBACzC,+DAA+D;wBAC/D,uDAAuD;wBACzD;oBACF;gBACF;;YAEA,iCAAiC;YACjC,IAAI;gBACF,SAAS,OAAO,CAAC;oBAAE,YAAY;wBAAC;wBAAW;wBAAc;qBAAQ;gBAAC;YACpE,EAAE,OAAO,OAAO;gBACd,QAAQ,IAAI,CAAC,uCAAuC;YACtD;YAEA;8CAAO,IAAM,SAAS,UAAU;;QAClC;qCAAG,EAAE;IAEL,OAAO;AACT;IArCgB;MAAA;AAwCT,SAAS;;IACd,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,uCAAmC;;YAAM;YAEzC,MAAM,SAAS,SAAS,gBAAgB,CAAC;YAEzC,MAAM,gBAAgB,IAAI;8CAAqB,CAAC;oBAC9C,QAAQ,OAAO;sDAAC,CAAC;4BACf,IAAI,MAAM,cAAc,EAAE;gCACxB,MAAM,MAAM,MAAM,MAAM;gCACxB,MAAM,MAAM,IAAI,YAAY,CAAC;gCAC7B,IAAI,KAAK;oCACP,IAAI,GAAG,GAAG;oCACV,IAAI,eAAe,CAAC;oCACpB,cAAc,SAAS,CAAC;gCAC1B;4BACF;wBACF;;gBACF;;YAEA,OAAO,OAAO;8CAAC,CAAC,MAAQ,cAAc,OAAO,CAAC;;YAE9C;8CAAO,IAAM,cAAc,UAAU;;QACvC;qCAAG,EAAE;IAEL,OAAO;AACT;IA1BgB;MAAA;AA6BT,MAAM,mBAAmB;IAC9B,kCAAkC;IAClC,aAAa,eACX,EAAoB,EACpB,KAAa;QAEb,MAAM,QAAQ,YAAY,GAAG;QAC7B,MAAM,SAAS,MAAM;QACrB,MAAM,MAAM,YAAY,GAAG;QAE3B,wCAA4C;YAC1C,QAAQ,GAAG,CAAC,GAAG,MAAM,EAAE,EAAE,KAAK,KAAK,CAAC,MAAM,OAAO,EAAE,CAAC;QACtD;QAEA,OAAO;IACT;IAEA,oCAAoC;IACpC,UAAU,SACR,IAAO,EACP,IAAY;QAEZ,IAAI;QACJ,OAAO,CAAC,GAAG;YACT,aAAa;YACb,UAAU,WAAW,IAAM,QAAQ,OAAO;QAC5C;IACF;IAEA,oCAAoC;IACpC,UAAU,SACR,IAAO,EACP,KAAa;QAEb,IAAI;QACJ,OAAO,CAAC,GAAG;YACT,IAAI,CAAC,YAAY;gBACf,QAAQ;gBACR,aAAa;gBACb,WAAW,IAAO,aAAa,OAAQ;YACzC;QACF;IACF;AACF", "debugId": null}}, {"offset": {"line": 535, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Development/BuzzEdge/buzzedge-frontend/src/lib/auth.tsx"], "sourcesContent": ["\"use client\";\n\nimport {\n  create<PERSON>ontext,\n  useContext,\n  useEffect,\n  useState,\n  ReactNode,\n} from \"react\";\nimport { apiClient } from \"./api\";\n\nexport interface User {\n  _id: string;\n  email: string;\n  name: string;\n  role: \"admin\" | \"editor\" | \"author\";\n  createdAt: string;\n}\n\nexport interface AuthResponse {\n  success: boolean;\n  data: {\n    user: User;\n    token: string;\n  };\n  message?: string;\n}\n\nexport interface LoginCredentials {\n  email: string;\n  password: string;\n}\n\nclass AuthService {\n  private static TOKEN_KEY = \"buzzedge_auth_token\";\n  private static USER_KEY = \"buzzedge_user\";\n\n  // Login\n  static async login(credentials: LoginCredentials): Promise<AuthResponse> {\n    try {\n      const response = await apiClient.post<AuthResponse>(\n        \"/api/auth/login\",\n        credentials\n      );\n\n      if (response.success && response.data.token) {\n        this.setToken(response.data.token);\n        this.setUser(response.data.user);\n      }\n\n      return response;\n    } catch (error) {\n      throw new Error(\"Login failed\");\n    }\n  }\n\n  // Logout\n  static logout(): void {\n    if (typeof window !== \"undefined\") {\n      localStorage.removeItem(this.TOKEN_KEY);\n      localStorage.removeItem(this.USER_KEY);\n    }\n  }\n\n  // Get stored token\n  static getToken(): string | null {\n    if (typeof window !== \"undefined\") {\n      return localStorage.getItem(this.TOKEN_KEY);\n    }\n    return null;\n  }\n\n  // Set token\n  static setToken(token: string): void {\n    if (typeof window !== \"undefined\") {\n      localStorage.setItem(this.TOKEN_KEY, token);\n    }\n  }\n\n  // Get stored user\n  static getUser(): User | null {\n    if (typeof window !== \"undefined\") {\n      const userStr = localStorage.getItem(this.USER_KEY);\n      return userStr ? JSON.parse(userStr) : null;\n    }\n    return null;\n  }\n\n  // Set user\n  static setUser(user: User): void {\n    if (typeof window !== \"undefined\") {\n      localStorage.setItem(this.USER_KEY, JSON.stringify(user));\n    }\n  }\n\n  // Check if user is authenticated\n  static isAuthenticated(): boolean {\n    return !!this.getToken();\n  }\n\n  // Check if user has admin role\n  static isAdmin(): boolean {\n    const user = this.getUser();\n    return user?.role === \"admin\";\n  }\n\n  // Check if user can edit content\n  static canEdit(): boolean {\n    const user = this.getUser();\n    return user?.role === \"admin\" || user?.role === \"editor\";\n  }\n\n  // Verify token with server\n  static async verifyToken(): Promise<boolean> {\n    try {\n      const token = this.getToken();\n      if (!token) return false;\n\n      const response = await apiClient.get<{\n        success: boolean;\n        data: { user: User };\n      }>(\"/api/auth/verify\");\n\n      if (response.success) {\n        this.setUser(response.data.user);\n        return true;\n      } else {\n        this.logout();\n        return false;\n      }\n    } catch (error) {\n      this.logout();\n      return false;\n    }\n  }\n\n  // Get authorization header\n  static getAuthHeader(): Record<string, string> {\n    const token = this.getToken();\n    return token ? { Authorization: `Bearer ${token}` } : {};\n  }\n}\n\n// Auth context hook\n\ninterface AuthContextType {\n  user: User | null;\n  isAuthenticated: boolean;\n  isLoading: boolean;\n  login: (credentials: LoginCredentials) => Promise<AuthResponse>;\n  logout: () => void;\n  verifyAuth: () => Promise<void>;\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined);\n\nexport function AuthProvider({ children }: { children: ReactNode }) {\n  const [user, setUser] = useState<User | null>(null);\n  const [isLoading, setIsLoading] = useState(true);\n\n  const verifyAuth = async () => {\n    setIsLoading(true);\n    try {\n      const isValid = await AuthService.verifyToken();\n      if (isValid) {\n        setUser(AuthService.getUser());\n      } else {\n        setUser(null);\n      }\n    } catch (error) {\n      setUser(null);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const login = async (\n    credentials: LoginCredentials\n  ): Promise<AuthResponse> => {\n    const response = await AuthService.login(credentials);\n    if (response.success) {\n      setUser(response.data.user);\n    }\n    return response;\n  };\n\n  const logout = () => {\n    AuthService.logout();\n    setUser(null);\n  };\n\n  useEffect(() => {\n    verifyAuth();\n  }, []);\n\n  const value = {\n    user,\n    isAuthenticated: !!user,\n    isLoading,\n    login,\n    logout,\n    verifyAuth,\n  };\n\n  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;\n}\n\nexport function useAuth() {\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error(\"useAuth must be used within an AuthProvider\");\n  }\n  return context;\n}\n\nexport { AuthService };\n"], "names": [], "mappings": ";;;;;;AAEA;AAOA;;;AATA;;;AAiCA,MAAM;IACJ,OAAe,YAAY,sBAAsB;IACjD,OAAe,WAAW,gBAAgB;IAE1C,QAAQ;IACR,aAAa,MAAM,WAA6B,EAAyB;QACvE,IAAI;YACF,MAAM,WAAW,MAAM,oHAAA,CAAA,YAAS,CAAC,IAAI,CACnC,mBACA;YAGF,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,CAAC,KAAK,EAAE;gBAC3C,IAAI,CAAC,QAAQ,CAAC,SAAS,IAAI,CAAC,KAAK;gBACjC,IAAI,CAAC,OAAO,CAAC,SAAS,IAAI,CAAC,IAAI;YACjC;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,MAAM,IAAI,MAAM;QAClB;IACF;IAEA,SAAS;IACT,OAAO,SAAe;QACpB,wCAAmC;YACjC,aAAa,UAAU,CAAC,IAAI,CAAC,SAAS;YACtC,aAAa,UAAU,CAAC,IAAI,CAAC,QAAQ;QACvC;IACF;IAEA,mBAAmB;IACnB,OAAO,WAA0B;QAC/B,wCAAmC;YACjC,OAAO,aAAa,OAAO,CAAC,IAAI,CAAC,SAAS;QAC5C;;IAEF;IAEA,YAAY;IACZ,OAAO,SAAS,KAAa,EAAQ;QACnC,wCAAmC;YACjC,aAAa,OAAO,CAAC,IAAI,CAAC,SAAS,EAAE;QACvC;IACF;IAEA,kBAAkB;IAClB,OAAO,UAAuB;QAC5B,wCAAmC;YACjC,MAAM,UAAU,aAAa,OAAO,CAAC,IAAI,CAAC,QAAQ;YAClD,OAAO,UAAU,KAAK,KAAK,CAAC,WAAW;QACzC;;IAEF;IAEA,WAAW;IACX,OAAO,QAAQ,IAAU,EAAQ;QAC/B,wCAAmC;YACjC,aAAa,OAAO,CAAC,IAAI,CAAC,QAAQ,EAAE,KAAK,SAAS,CAAC;QACrD;IACF;IAEA,iCAAiC;IACjC,OAAO,kBAA2B;QAChC,OAAO,CAAC,CAAC,IAAI,CAAC,QAAQ;IACxB;IAEA,+BAA+B;IAC/B,OAAO,UAAmB;QACxB,MAAM,OAAO,IAAI,CAAC,OAAO;QACzB,OAAO,MAAM,SAAS;IACxB;IAEA,iCAAiC;IACjC,OAAO,UAAmB;QACxB,MAAM,OAAO,IAAI,CAAC,OAAO;QACzB,OAAO,MAAM,SAAS,WAAW,MAAM,SAAS;IAClD;IAEA,2BAA2B;IAC3B,aAAa,cAAgC;QAC3C,IAAI;YACF,MAAM,QAAQ,IAAI,CAAC,QAAQ;YAC3B,IAAI,CAAC,OAAO,OAAO;YAEnB,MAAM,WAAW,MAAM,oHAAA,CAAA,YAAS,CAAC,GAAG,CAGjC;YAEH,IAAI,SAAS,OAAO,EAAE;gBACpB,IAAI,CAAC,OAAO,CAAC,SAAS,IAAI,CAAC,IAAI;gBAC/B,OAAO;YACT,OAAO;gBACL,IAAI,CAAC,MAAM;gBACX,OAAO;YACT;QACF,EAAE,OAAO,OAAO;YACd,IAAI,CAAC,MAAM;YACX,OAAO;QACT;IACF;IAEA,2BAA2B;IAC3B,OAAO,gBAAwC;QAC7C,MAAM,QAAQ,IAAI,CAAC,QAAQ;QAC3B,OAAO,QAAQ;YAAE,eAAe,CAAC,OAAO,EAAE,OAAO;QAAC,IAAI,CAAC;IACzD;AACF;AAaA,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,SAAS,aAAa,EAAE,QAAQ,EAA2B;;IAChE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,aAAa;QACjB,aAAa;QACb,IAAI;YACF,MAAM,UAAU,MAAM,YAAY,WAAW;YAC7C,IAAI,SAAS;gBACX,QAAQ,YAAY,OAAO;YAC7B,OAAO;gBACL,QAAQ;YACV;QACF,EAAE,OAAO,OAAO;YACd,QAAQ;QACV,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,QAAQ,OACZ;QAEA,MAAM,WAAW,MAAM,YAAY,KAAK,CAAC;QACzC,IAAI,SAAS,OAAO,EAAE;YACpB,QAAQ,SAAS,IAAI,CAAC,IAAI;QAC5B;QACA,OAAO;IACT;IAEA,MAAM,SAAS;QACb,YAAY,MAAM;QAClB,QAAQ;IACV;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR;QACF;iCAAG,EAAE;IAEL,MAAM,QAAQ;QACZ;QACA,iBAAiB,CAAC,CAAC;QACnB;QACA;QACA;QACA;IACF;IAEA,qBAAO,6LAAC,YAAY,QAAQ;QAAC,OAAO;kBAAQ;;;;;;AAC9C;GAjDgB;KAAA;AAmDT,SAAS;;IACd,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANgB", "debugId": null}}, {"offset": {"line": 713, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Development/BuzzEdge/buzzedge-frontend/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js"], "sourcesContent": ["/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\n\"production\" !== process.env.NODE_ENV &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return (type.displayName || \"Context\") + \".Provider\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = require(\"next/dist/compiled/react\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\");\n    Symbol.for(\"react.provider\");\n    var REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      \"react-stack-bottom-frame\": function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React[\"react-stack-bottom-frame\"].bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n"], "names": [], "mappings": "AAAA;;;;;;;;CAQC,GAGgB;AADjB;AACA,oEACE,AAAC;IACC,SAAS,yBAAyB,IAAI;QACpC,IAAI,QAAQ,MAAM,OAAO;QACzB,IAAI,eAAe,OAAO,MACxB,OAAO,KAAK,QAAQ,KAAK,yBACrB,OACA,KAAK,WAAW,IAAI,KAAK,IAAI,IAAI;QACvC,IAAI,aAAa,OAAO,MAAM,OAAO;QACrC,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;QACX;QACA,IAAI,aAAa,OAAO,MACtB,OACG,aAAa,OAAO,KAAK,GAAG,IAC3B,QAAQ,KAAK,CACX,sHAEJ,KAAK,QAAQ;YAEb,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO,CAAC,KAAK,WAAW,IAAI,SAAS,IAAI;YAC3C,KAAK;gBACH,OAAO,CAAC,KAAK,QAAQ,CAAC,WAAW,IAAI,SAAS,IAAI;YACpD,KAAK;gBACH,IAAI,YAAY,KAAK,MAAM;gBAC3B,OAAO,KAAK,WAAW;gBACvB,QACE,CAAC,AAAC,OAAO,UAAU,WAAW,IAAI,UAAU,IAAI,IAAI,IACnD,OAAO,OAAO,OAAO,gBAAgB,OAAO,MAAM,YAAa;gBAClE,OAAO;YACT,KAAK;gBACH,OACE,AAAC,YAAY,KAAK,WAAW,IAAI,MACjC,SAAS,YACL,YACA,yBAAyB,KAAK,IAAI,KAAK;YAE/C,KAAK;gBACH,YAAY,KAAK,QAAQ;gBACzB,OAAO,KAAK,KAAK;gBACjB,IAAI;oBACF,OAAO,yBAAyB,KAAK;gBACvC,EAAE,OAAO,GAAG,CAAC;QACjB;QACF,OAAO;IACT;IACA,SAAS,mBAAmB,KAAK;QAC/B,OAAO,KAAK;IACd;IACA,SAAS,uBAAuB,KAAK;QACnC,IAAI;YACF,mBAAmB;YACnB,IAAI,2BAA2B,CAAC;QAClC,EAAE,OAAO,GAAG;YACV,2BAA2B,CAAC;QAC9B;QACA,IAAI,0BAA0B;YAC5B,2BAA2B;YAC3B,IAAI,wBAAwB,yBAAyB,KAAK;YAC1D,IAAI,oCACF,AAAC,eAAe,OAAO,UACrB,OAAO,WAAW,IAClB,KAAK,CAAC,OAAO,WAAW,CAAC,IAC3B,MAAM,WAAW,CAAC,IAAI,IACtB;YACF,sBAAsB,IAAI,CACxB,0BACA,4GACA;YAEF,OAAO,mBAAmB;QAC5B;IACF;IACA,SAAS,YAAY,IAAI;QACvB,IAAI,SAAS,qBAAqB,OAAO;QACzC,IACE,aAAa,OAAO,QACpB,SAAS,QACT,KAAK,QAAQ,KAAK,iBAElB,OAAO;QACT,IAAI;YACF,IAAI,OAAO,yBAAyB;YACpC,OAAO,OAAO,MAAM,OAAO,MAAM;QACnC,EAAE,OAAO,GAAG;YACV,OAAO;QACT;IACF;IACA,SAAS;QACP,IAAI,aAAa,qBAAqB,CAAC;QACvC,OAAO,SAAS,aAAa,OAAO,WAAW,QAAQ;IACzD;IACA,SAAS;QACP,OAAO,MAAM;IACf;IACA,SAAS,YAAY,MAAM;QACzB,IAAI,eAAe,IAAI,CAAC,QAAQ,QAAQ;YACtC,IAAI,SAAS,OAAO,wBAAwB,CAAC,QAAQ,OAAO,GAAG;YAC/D,IAAI,UAAU,OAAO,cAAc,EAAE,OAAO,CAAC;QAC/C;QACA,OAAO,KAAK,MAAM,OAAO,GAAG;IAC9B;IACA,SAAS,2BAA2B,KAAK,EAAE,WAAW;QACpD,SAAS;YACP,8BACE,CAAC,AAAC,6BAA6B,CAAC,GAChC,QAAQ,KAAK,CACX,2OACA,YACD;QACL;QACA,sBAAsB,cAAc,GAAG,CAAC;QACxC,OAAO,cAAc,CAAC,OAAO,OAAO;YAClC,KAAK;YACL,cAAc,CAAC;QACjB;IACF;IACA,SAAS;QACP,IAAI,gBAAgB,yBAAyB,IAAI,CAAC,IAAI;QACtD,sBAAsB,CAAC,cAAc,IACnC,CAAC,AAAC,sBAAsB,CAAC,cAAc,GAAG,CAAC,GAC3C,QAAQ,KAAK,CACX,8IACD;QACH,gBAAgB,IAAI,CAAC,KAAK,CAAC,GAAG;QAC9B,OAAO,KAAK,MAAM,gBAAgB,gBAAgB;IACpD;IACA,SAAS,aACP,IAAI,EACJ,GAAG,EACH,IAAI,EACJ,MAAM,EACN,KAAK,EACL,KAAK,EACL,UAAU,EACV,SAAS;QAET,OAAO,MAAM,GAAG;QAChB,OAAO;YACL,UAAU;YACV,MAAM;YACN,KAAK;YACL,OAAO;YACP,QAAQ;QACV;QACA,SAAS,CAAC,KAAK,MAAM,OAAO,OAAO,IAAI,IACnC,OAAO,cAAc,CAAC,MAAM,OAAO;YACjC,YAAY,CAAC;YACb,KAAK;QACP,KACA,OAAO,cAAc,CAAC,MAAM,OAAO;YAAE,YAAY,CAAC;YAAG,OAAO;QAAK;QACrE,KAAK,MAAM,GAAG,CAAC;QACf,OAAO,cAAc,CAAC,KAAK,MAAM,EAAE,aAAa;YAC9C,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,cAAc;YACxC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,eAAe;YACzC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,cAAc;YACxC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,MAAM,IAAI,CAAC,OAAO,MAAM,CAAC,KAAK,KAAK,GAAG,OAAO,MAAM,CAAC,KAAK;QAChE,OAAO;IACT;IACA,SAAS,WACP,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,gBAAgB,EAChB,MAAM,EACN,IAAI,EACJ,UAAU,EACV,SAAS;QAET,IAAI,WAAW,OAAO,QAAQ;QAC9B,IAAI,KAAK,MAAM,UACb,IAAI,kBACF,IAAI,YAAY,WAAW;YACzB,IACE,mBAAmB,GACnB,mBAAmB,SAAS,MAAM,EAClC,mBAEA,kBAAkB,QAAQ,CAAC,iBAAiB;YAC9C,OAAO,MAAM,IAAI,OAAO,MAAM,CAAC;QACjC,OACE,QAAQ,KAAK,CACX;aAED,kBAAkB;QACzB,IAAI,eAAe,IAAI,CAAC,QAAQ,QAAQ;YACtC,WAAW,yBAAyB;YACpC,IAAI,OAAO,OAAO,IAAI,CAAC,QAAQ,MAAM,CAAC,SAAU,CAAC;gBAC/C,OAAO,UAAU;YACnB;YACA,mBACE,IAAI,KAAK,MAAM,GACX,oBAAoB,KAAK,IAAI,CAAC,aAAa,WAC3C;YACN,qBAAqB,CAAC,WAAW,iBAAiB,IAChD,CAAC,AAAC,OACA,IAAI,KAAK,MAAM,GAAG,MAAM,KAAK,IAAI,CAAC,aAAa,WAAW,MAC5D,QAAQ,KAAK,CACX,mOACA,kBACA,UACA,MACA,WAED,qBAAqB,CAAC,WAAW,iBAAiB,GAAG,CAAC,CAAE;QAC7D;QACA,WAAW;QACX,KAAK,MAAM,YACT,CAAC,uBAAuB,WAAY,WAAW,KAAK,QAAS;QAC/D,YAAY,WACV,CAAC,uBAAuB,OAAO,GAAG,GAAI,WAAW,KAAK,OAAO,GAAG,AAAC;QACnE,IAAI,SAAS,QAAQ;YACnB,WAAW,CAAC;YACZ,IAAK,IAAI,YAAY,OACnB,UAAU,YAAY,CAAC,QAAQ,CAAC,SAAS,GAAG,MAAM,CAAC,SAAS;QAChE,OAAO,WAAW;QAClB,YACE,2BACE,UACA,eAAe,OAAO,OAClB,KAAK,WAAW,IAAI,KAAK,IAAI,IAAI,YACjC;QAER,OAAO,aACL,MACA,UACA,MACA,QACA,YACA,UACA,YACA;IAEJ;IACA,SAAS,kBAAkB,IAAI;QAC7B,aAAa,OAAO,QAClB,SAAS,QACT,KAAK,QAAQ,KAAK,sBAClB,KAAK,MAAM,IACX,CAAC,KAAK,MAAM,CAAC,SAAS,GAAG,CAAC;IAC9B;IACA,IAAI,uHACF,qBAAqB,OAAO,GAAG,CAAC,+BAChC,oBAAoB,OAAO,GAAG,CAAC,iBAC/B,sBAAsB,OAAO,GAAG,CAAC,mBACjC,yBAAyB,OAAO,GAAG,CAAC,sBACpC,sBAAsB,OAAO,GAAG,CAAC;IACnC,OAAO,GAAG,CAAC;IACX,IAAI,sBAAsB,OAAO,GAAG,CAAC,mBACnC,qBAAqB,OAAO,GAAG,CAAC,kBAChC,yBAAyB,OAAO,GAAG,CAAC,sBACpC,sBAAsB,OAAO,GAAG,CAAC,mBACjC,2BAA2B,OAAO,GAAG,CAAC,wBACtC,kBAAkB,OAAO,GAAG,CAAC,eAC7B,kBAAkB,OAAO,GAAG,CAAC,eAC7B,sBAAsB,OAAO,GAAG,CAAC,mBACjC,yBAAyB,OAAO,GAAG,CAAC,2BACpC,uBACE,MAAM,+DAA+D,EACvE,iBAAiB,OAAO,SAAS,CAAC,cAAc,EAChD,cAAc,MAAM,OAAO,EAC3B,aAAa,QAAQ,UAAU,GAC3B,QAAQ,UAAU,GAClB;QACE,OAAO;IACT;IACN,QAAQ;QACN,4BAA4B,SAAU,iBAAiB;YACrD,OAAO;QACT;IACF;IACA,IAAI;IACJ,IAAI,yBAAyB,CAAC;IAC9B,IAAI,yBAAyB,KAAK,CAAC,2BAA2B,CAAC,IAAI,CACjE,OACA;IAEF,IAAI,wBAAwB,WAAW,YAAY;IACnD,IAAI,wBAAwB,CAAC;IAC7B,QAAQ,QAAQ,GAAG;IACnB,QAAQ,MAAM,GAAG,SACf,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,gBAAgB,EAChB,MAAM,EACN,IAAI;QAEJ,IAAI,mBACF,MAAM,qBAAqB,0BAA0B;QACvD,OAAO,WACL,MACA,QACA,UACA,kBACA,QACA,MACA,mBACI,MAAM,2BACN,wBACJ,mBAAmB,WAAW,YAAY,SAAS;IAEvD;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 921, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Development/BuzzEdge/buzzedge-frontend/node_modules/next/dist/compiled/react/jsx-dev-runtime.js"], "sourcesContent": ["'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/react-jsx-dev-runtime.production.js');\n} else {\n  module.exports = require('./cjs/react-jsx-dev-runtime.development.js');\n}\n"], "names": [], "mappings": "AAEI;AAFJ;AAEA,uCAA2C;;AAE3C,OAAO;IACL,OAAO,OAAO;AAChB", "ignoreList": [0], "debugId": null}}]}