{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Development/BuzzEdge/buzzedge-frontend/src/components/RichTextEditor.tsx"], "sourcesContent": ["'use client';\n\nimport { useEditor, EditorContent } from '@tiptap/react';\nimport StarterKit from '@tiptap/starter-kit';\nimport Image from '@tiptap/extension-image';\nimport Link from '@tiptap/extension-link';\nimport CodeBlockLowlight from '@tiptap/extension-code-block-lowlight';\nimport Table from '@tiptap/extension-table';\nimport TableRow from '@tiptap/extension-table-row';\nimport TableCell from '@tiptap/extension-table-cell';\nimport TableHeader from '@tiptap/extension-table-header';\nimport { lowlight } from 'lowlight';\nimport { useCallback } from 'react';\n\ninterface RichTextEditorProps {\n  content: string;\n  onChange: (content: string) => void;\n  placeholder?: string;\n}\n\nexport function RichTextEditor({ content, onChange, placeholder = 'Start writing...' }: RichTextEditorProps) {\n  const editor = useEditor({\n    extensions: [\n      StarterKit,\n      Image.configure({\n        HTMLAttributes: {\n          class: 'max-w-full h-auto rounded-lg',\n        },\n      }),\n      Link.configure({\n        openOnClick: false,\n        HTMLAttributes: {\n          class: 'text-blue-600 hover:text-blue-800 underline',\n        },\n      }),\n      CodeBlockLowlight.configure({\n        lowlight,\n        HTMLAttributes: {\n          class: 'bg-gray-100 rounded-md p-4 font-mono text-sm',\n        },\n      }),\n      Table.configure({\n        resizable: true,\n      }),\n      TableRow,\n      TableHeader,\n      TableCell,\n    ],\n    content,\n    onUpdate: ({ editor }) => {\n      onChange(editor.getHTML());\n    },\n    editorProps: {\n      attributes: {\n        class: 'prose prose-sm sm:prose lg:prose-lg xl:prose-2xl mx-auto focus:outline-none min-h-[300px] p-4',\n      },\n    },\n  });\n\n  const addImage = useCallback(() => {\n    const url = window.prompt('Enter image URL:');\n    if (url && editor) {\n      editor.chain().focus().setImage({ src: url }).run();\n    }\n  }, [editor]);\n\n  const setLink = useCallback(() => {\n    const previousUrl = editor?.getAttributes('link').href;\n    const url = window.prompt('Enter URL:', previousUrl);\n\n    if (url === null) {\n      return;\n    }\n\n    if (url === '') {\n      editor?.chain().focus().extendMarkRange('link').unsetLink().run();\n      return;\n    }\n\n    editor?.chain().focus().extendMarkRange('link').setLink({ href: url }).run();\n  }, [editor]);\n\n  if (!editor) {\n    return null;\n  }\n\n  return (\n    <div className=\"border border-gray-300 rounded-lg overflow-hidden\">\n      {/* Toolbar */}\n      <div className=\"border-b border-gray-300 p-2 bg-gray-50 flex flex-wrap gap-1\">\n        {/* Text formatting */}\n        <button\n          onClick={() => editor.chain().focus().toggleBold().run()}\n          className={`px-3 py-1 rounded text-sm font-medium ${\n            editor.isActive('bold') ? 'bg-blue-500 text-white' : 'bg-white text-gray-700 hover:bg-gray-100'\n          }`}\n        >\n          Bold\n        </button>\n        <button\n          onClick={() => editor.chain().focus().toggleItalic().run()}\n          className={`px-3 py-1 rounded text-sm font-medium ${\n            editor.isActive('italic') ? 'bg-blue-500 text-white' : 'bg-white text-gray-700 hover:bg-gray-100'\n          }`}\n        >\n          Italic\n        </button>\n        <button\n          onClick={() => editor.chain().focus().toggleStrike().run()}\n          className={`px-3 py-1 rounded text-sm font-medium ${\n            editor.isActive('strike') ? 'bg-blue-500 text-white' : 'bg-white text-gray-700 hover:bg-gray-100'\n          }`}\n        >\n          Strike\n        </button>\n\n        <div className=\"w-px h-6 bg-gray-300 mx-1\"></div>\n\n        {/* Headings */}\n        <button\n          onClick={() => editor.chain().focus().toggleHeading({ level: 1 }).run()}\n          className={`px-3 py-1 rounded text-sm font-medium ${\n            editor.isActive('heading', { level: 1 }) ? 'bg-blue-500 text-white' : 'bg-white text-gray-700 hover:bg-gray-100'\n          }`}\n        >\n          H1\n        </button>\n        <button\n          onClick={() => editor.chain().focus().toggleHeading({ level: 2 }).run()}\n          className={`px-3 py-1 rounded text-sm font-medium ${\n            editor.isActive('heading', { level: 2 }) ? 'bg-blue-500 text-white' : 'bg-white text-gray-700 hover:bg-gray-100'\n          }`}\n        >\n          H2\n        </button>\n        <button\n          onClick={() => editor.chain().focus().toggleHeading({ level: 3 }).run()}\n          className={`px-3 py-1 rounded text-sm font-medium ${\n            editor.isActive('heading', { level: 3 }) ? 'bg-blue-500 text-white' : 'bg-white text-gray-700 hover:bg-gray-100'\n          }`}\n        >\n          H3\n        </button>\n\n        <div className=\"w-px h-6 bg-gray-300 mx-1\"></div>\n\n        {/* Lists */}\n        <button\n          onClick={() => editor.chain().focus().toggleBulletList().run()}\n          className={`px-3 py-1 rounded text-sm font-medium ${\n            editor.isActive('bulletList') ? 'bg-blue-500 text-white' : 'bg-white text-gray-700 hover:bg-gray-100'\n          }`}\n        >\n          • List\n        </button>\n        <button\n          onClick={() => editor.chain().focus().toggleOrderedList().run()}\n          className={`px-3 py-1 rounded text-sm font-medium ${\n            editor.isActive('orderedList') ? 'bg-blue-500 text-white' : 'bg-white text-gray-700 hover:bg-gray-100'\n          }`}\n        >\n          1. List\n        </button>\n\n        <div className=\"w-px h-6 bg-gray-300 mx-1\"></div>\n\n        {/* Media & Links */}\n        <button\n          onClick={addImage}\n          className=\"px-3 py-1 rounded text-sm font-medium bg-white text-gray-700 hover:bg-gray-100\"\n        >\n          📷 Image\n        </button>\n        <button\n          onClick={setLink}\n          className={`px-3 py-1 rounded text-sm font-medium ${\n            editor.isActive('link') ? 'bg-blue-500 text-white' : 'bg-white text-gray-700 hover:bg-gray-100'\n          }`}\n        >\n          🔗 Link\n        </button>\n\n        <div className=\"w-px h-6 bg-gray-300 mx-1\"></div>\n\n        {/* Code & Quote */}\n        <button\n          onClick={() => editor.chain().focus().toggleCodeBlock().run()}\n          className={`px-3 py-1 rounded text-sm font-medium ${\n            editor.isActive('codeBlock') ? 'bg-blue-500 text-white' : 'bg-white text-gray-700 hover:bg-gray-100'\n          }`}\n        >\n          &lt;/&gt; Code\n        </button>\n        <button\n          onClick={() => editor.chain().focus().toggleBlockquote().run()}\n          className={`px-3 py-1 rounded text-sm font-medium ${\n            editor.isActive('blockquote') ? 'bg-blue-500 text-white' : 'bg-white text-gray-700 hover:bg-gray-100'\n          }`}\n        >\n          \" Quote\n        </button>\n\n        <div className=\"w-px h-6 bg-gray-300 mx-1\"></div>\n\n        {/* Undo/Redo */}\n        <button\n          onClick={() => editor.chain().focus().undo().run()}\n          disabled={!editor.can().undo()}\n          className=\"px-3 py-1 rounded text-sm font-medium bg-white text-gray-700 hover:bg-gray-100 disabled:opacity-50\"\n        >\n          ↶ Undo\n        </button>\n        <button\n          onClick={() => editor.chain().focus().redo().run()}\n          disabled={!editor.can().redo()}\n          className=\"px-3 py-1 rounded text-sm font-medium bg-white text-gray-700 hover:bg-gray-100 disabled:opacity-50\"\n        >\n          ↷ Redo\n        </button>\n      </div>\n\n      {/* Editor */}\n      <div className=\"bg-white\">\n        <EditorContent editor={editor} />\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAZA;;;;;;;;;;;;AAoBO,SAAS,eAAe,EAAE,OAAO,EAAE,QAAQ,EAAE,cAAc,kBAAkB,EAAuB;;IACzG,MAAM,SAAS,CAAA,GAAA,qKAAA,CAAA,YAAS,AAAD,EAAE;QACvB,YAAY;YACV,8JAAA,CAAA,UAAU;YACV,kKAAA,CAAA,UAAK,CAAC,SAAS,CAAC;gBACd,gBAAgB;oBACd,OAAO;gBACT;YACF;YACA,iKAAA,CAAA,UAAI,CAAC,SAAS,CAAC;gBACb,aAAa;gBACb,gBAAgB;oBACd,OAAO;gBACT;YACF;YACA,sLAAA,CAAA,UAAiB,CAAC,SAAS,CAAC;gBAC1B,UAAA,qJAAA,CAAA,WAAQ;gBACR,gBAAgB;oBACd,OAAO;gBACT;YACF;YACA,kKAAA,CAAA,UAAK,CAAC,SAAS,CAAC;gBACd,WAAW;YACb;YACA,yKAAA,CAAA,UAAQ;YACR,4KAAA,CAAA,UAAW;YACX,0KAAA,CAAA,UAAS;SACV;QACD;QACA,QAAQ;gDAAE,CAAC,EAAE,MAAM,EAAE;gBACnB,SAAS,OAAO,OAAO;YACzB;;QACA,aAAa;YACX,YAAY;gBACV,OAAO;YACT;QACF;IACF;IAEA,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;gDAAE;YAC3B,MAAM,MAAM,OAAO,MAAM,CAAC;YAC1B,IAAI,OAAO,QAAQ;gBACjB,OAAO,KAAK,GAAG,KAAK,GAAG,QAAQ,CAAC;oBAAE,KAAK;gBAAI,GAAG,GAAG;YACnD;QACF;+CAAG;QAAC;KAAO;IAEX,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;+CAAE;YAC1B,MAAM,cAAc,QAAQ,cAAc,QAAQ;YAClD,MAAM,MAAM,OAAO,MAAM,CAAC,cAAc;YAExC,IAAI,QAAQ,MAAM;gBAChB;YACF;YAEA,IAAI,QAAQ,IAAI;gBACd,QAAQ,QAAQ,QAAQ,gBAAgB,QAAQ,YAAY;gBAC5D;YACF;YAEA,QAAQ,QAAQ,QAAQ,gBAAgB,QAAQ,QAAQ;gBAAE,MAAM;YAAI,GAAG;QACzE;8CAAG;QAAC;KAAO;IAEX,IAAI,CAAC,QAAQ;QACX,OAAO;IACT;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBACC,SAAS,IAAM,OAAO,KAAK,GAAG,KAAK,GAAG,UAAU,GAAG,GAAG;wBACtD,WAAW,CAAC,sCAAsC,EAChD,OAAO,QAAQ,CAAC,UAAU,2BAA2B,4CACrD;kCACH;;;;;;kCAGD,6LAAC;wBACC,SAAS,IAAM,OAAO,KAAK,GAAG,KAAK,GAAG,YAAY,GAAG,GAAG;wBACxD,WAAW,CAAC,sCAAsC,EAChD,OAAO,QAAQ,CAAC,YAAY,2BAA2B,4CACvD;kCACH;;;;;;kCAGD,6LAAC;wBACC,SAAS,IAAM,OAAO,KAAK,GAAG,KAAK,GAAG,YAAY,GAAG,GAAG;wBACxD,WAAW,CAAC,sCAAsC,EAChD,OAAO,QAAQ,CAAC,YAAY,2BAA2B,4CACvD;kCACH;;;;;;kCAID,6LAAC;wBAAI,WAAU;;;;;;kCAGf,6LAAC;wBACC,SAAS,IAAM,OAAO,KAAK,GAAG,KAAK,GAAG,aAAa,CAAC;gCAAE,OAAO;4BAAE,GAAG,GAAG;wBACrE,WAAW,CAAC,sCAAsC,EAChD,OAAO,QAAQ,CAAC,WAAW;4BAAE,OAAO;wBAAE,KAAK,2BAA2B,4CACtE;kCACH;;;;;;kCAGD,6LAAC;wBACC,SAAS,IAAM,OAAO,KAAK,GAAG,KAAK,GAAG,aAAa,CAAC;gCAAE,OAAO;4BAAE,GAAG,GAAG;wBACrE,WAAW,CAAC,sCAAsC,EAChD,OAAO,QAAQ,CAAC,WAAW;4BAAE,OAAO;wBAAE,KAAK,2BAA2B,4CACtE;kCACH;;;;;;kCAGD,6LAAC;wBACC,SAAS,IAAM,OAAO,KAAK,GAAG,KAAK,GAAG,aAAa,CAAC;gCAAE,OAAO;4BAAE,GAAG,GAAG;wBACrE,WAAW,CAAC,sCAAsC,EAChD,OAAO,QAAQ,CAAC,WAAW;4BAAE,OAAO;wBAAE,KAAK,2BAA2B,4CACtE;kCACH;;;;;;kCAID,6LAAC;wBAAI,WAAU;;;;;;kCAGf,6LAAC;wBACC,SAAS,IAAM,OAAO,KAAK,GAAG,KAAK,GAAG,gBAAgB,GAAG,GAAG;wBAC5D,WAAW,CAAC,sCAAsC,EAChD,OAAO,QAAQ,CAAC,gBAAgB,2BAA2B,4CAC3D;kCACH;;;;;;kCAGD,6LAAC;wBACC,SAAS,IAAM,OAAO,KAAK,GAAG,KAAK,GAAG,iBAAiB,GAAG,GAAG;wBAC7D,WAAW,CAAC,sCAAsC,EAChD,OAAO,QAAQ,CAAC,iBAAiB,2BAA2B,4CAC5D;kCACH;;;;;;kCAID,6LAAC;wBAAI,WAAU;;;;;;kCAGf,6LAAC;wBACC,SAAS;wBACT,WAAU;kCACX;;;;;;kCAGD,6LAAC;wBACC,SAAS;wBACT,WAAW,CAAC,sCAAsC,EAChD,OAAO,QAAQ,CAAC,UAAU,2BAA2B,4CACrD;kCACH;;;;;;kCAID,6LAAC;wBAAI,WAAU;;;;;;kCAGf,6LAAC;wBACC,SAAS,IAAM,OAAO,KAAK,GAAG,KAAK,GAAG,eAAe,GAAG,GAAG;wBAC3D,WAAW,CAAC,sCAAsC,EAChD,OAAO,QAAQ,CAAC,eAAe,2BAA2B,4CAC1D;kCACH;;;;;;kCAGD,6LAAC;wBACC,SAAS,IAAM,OAAO,KAAK,GAAG,KAAK,GAAG,gBAAgB,GAAG,GAAG;wBAC5D,WAAW,CAAC,sCAAsC,EAChD,OAAO,QAAQ,CAAC,gBAAgB,2BAA2B,4CAC3D;kCACH;;;;;;kCAID,6LAAC;wBAAI,WAAU;;;;;;kCAGf,6LAAC;wBACC,SAAS,IAAM,OAAO,KAAK,GAAG,KAAK,GAAG,IAAI,GAAG,GAAG;wBAChD,UAAU,CAAC,OAAO,GAAG,GAAG,IAAI;wBAC5B,WAAU;kCACX;;;;;;kCAGD,6LAAC;wBACC,SAAS,IAAM,OAAO,KAAK,GAAG,KAAK,GAAG,IAAI,GAAG,GAAG;wBAChD,UAAU,CAAC,OAAO,GAAG,GAAG,IAAI;wBAC5B,WAAU;kCACX;;;;;;;;;;;;0BAMH,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,qKAAA,CAAA,gBAAa;oBAAC,QAAQ;;;;;;;;;;;;;;;;;AAI/B;GA/MgB;;QACC,qKAAA,CAAA,YAAS;;;KADV", "debugId": null}}, {"offset": {"line": 335, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Development/BuzzEdge/buzzedge-frontend/src/components/ImageUpload.tsx"], "sourcesContent": ["'use client';\n\nimport { useCallback, useState } from 'react';\nimport { useDropzone } from 'react-dropzone';\nimport Image from 'next/image';\n\ninterface ImageUploadProps {\n  value?: string;\n  onChange: (url: string) => void;\n  onRemove?: () => void;\n  disabled?: boolean;\n}\n\nexport function ImageUpload({ value, onChange, onRemove, disabled }: ImageUploadProps) {\n  const [isUploading, setIsUploading] = useState(false);\n\n  const onDrop = useCallback(async (acceptedFiles: File[]) => {\n    if (acceptedFiles.length === 0) return;\n\n    const file = acceptedFiles[0];\n    setIsUploading(true);\n\n    try {\n      // For demo purposes, we'll use a placeholder service\n      // In production, you'd upload to your own server or cloud storage\n      const formData = new FormData();\n      formData.append('file', file);\n\n      // Simulate upload delay\n      await new Promise(resolve => setTimeout(resolve, 1000));\n\n      // For demo, we'll use a placeholder image URL\n      // In production, this would be the actual uploaded image URL\n      const demoUrl = `https://images.unsplash.com/photo-1486312338219-ce68d2c6f44d?w=800&h=600&fit=crop&crop=center`;\n      onChange(demoUrl);\n    } catch (error) {\n      console.error('Upload failed:', error);\n      alert('Upload failed. Please try again.');\n    } finally {\n      setIsUploading(false);\n    }\n  }, [onChange]);\n\n  const { getRootProps, getInputProps, isDragActive } = useDropzone({\n    onDrop,\n    accept: {\n      'image/*': ['.jpeg', '.jpg', '.png', '.gif', '.webp']\n    },\n    maxFiles: 1,\n    disabled: disabled || isUploading,\n  });\n\n  if (value) {\n    return (\n      <div className=\"relative\">\n        <div className=\"relative w-full h-64 rounded-lg overflow-hidden border border-gray-300\">\n          <Image\n            src={value}\n            alt=\"Uploaded image\"\n            fill\n            className=\"object-cover\"\n          />\n        </div>\n        {onRemove && (\n          <button\n            onClick={onRemove}\n            disabled={disabled}\n            className=\"absolute top-2 right-2 bg-red-500 text-white rounded-full w-8 h-8 flex items-center justify-center hover:bg-red-600 disabled:opacity-50\"\n          >\n            ✕\n          </button>\n        )}\n        <div className=\"mt-2 text-sm text-gray-500\">\n          Click to change image\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div\n      {...getRootProps()}\n      className={`\n        border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors\n        ${isDragActive ? 'border-blue-500 bg-blue-50' : 'border-gray-300 hover:border-gray-400'}\n        ${disabled || isUploading ? 'opacity-50 cursor-not-allowed' : ''}\n      `}\n    >\n      <input {...getInputProps()} />\n      \n      {isUploading ? (\n        <div className=\"flex flex-col items-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mb-4\"></div>\n          <p className=\"text-gray-600\">Uploading image...</p>\n        </div>\n      ) : (\n        <div className=\"flex flex-col items-center\">\n          <div className=\"text-4xl mb-4\">📷</div>\n          {isDragActive ? (\n            <p className=\"text-blue-600 font-medium\">Drop the image here...</p>\n          ) : (\n            <div>\n              <p className=\"text-gray-600 font-medium mb-2\">\n                Drag & drop an image here, or click to select\n              </p>\n              <p className=\"text-sm text-gray-500\">\n                Supports: JPEG, PNG, GIF, WebP (max 10MB)\n              </p>\n            </div>\n          )}\n        </div>\n      )}\n    </div>\n  );\n}\n\n// URL Input Alternative\ninterface ImageUrlInputProps {\n  value?: string;\n  onChange: (url: string) => void;\n  placeholder?: string;\n}\n\nexport function ImageUrlInput({ value, onChange, placeholder = 'Enter image URL...' }: ImageUrlInputProps) {\n  const [url, setUrl] = useState(value || '');\n  const [isValidating, setIsValidating] = useState(false);\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    if (!url.trim()) return;\n\n    setIsValidating(true);\n    \n    // Validate image URL\n    try {\n      const img = new window.Image();\n      img.onload = () => {\n        onChange(url);\n        setIsValidating(false);\n      };\n      img.onerror = () => {\n        alert('Invalid image URL. Please check the URL and try again.');\n        setIsValidating(false);\n      };\n      img.src = url;\n    } catch (error) {\n      alert('Invalid image URL. Please check the URL and try again.');\n      setIsValidating(false);\n    }\n  };\n\n  return (\n    <form onSubmit={handleSubmit} className=\"space-y-3\">\n      <div className=\"flex space-x-2\">\n        <input\n          type=\"url\"\n          value={url}\n          onChange={(e) => setUrl(e.target.value)}\n          placeholder={placeholder}\n          className=\"flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n        />\n        <button\n          type=\"submit\"\n          disabled={!url.trim() || isValidating}\n          className=\"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed\"\n        >\n          {isValidating ? 'Validating...' : 'Add'}\n        </button>\n      </div>\n      \n      {value && (\n        <div className=\"relative w-full h-32 rounded-lg overflow-hidden border border-gray-300\">\n          <Image\n            src={value}\n            alt=\"Preview\"\n            fill\n            className=\"object-cover\"\n          />\n        </div>\n      )}\n    </form>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;AACA;;;AAJA;;;;AAaO,SAAS,YAAY,EAAE,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAoB;;IACnF,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,SAAS,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;2CAAE,OAAO;YAChC,IAAI,cAAc,MAAM,KAAK,GAAG;YAEhC,MAAM,OAAO,aAAa,CAAC,EAAE;YAC7B,eAAe;YAEf,IAAI;gBACF,qDAAqD;gBACrD,kEAAkE;gBAClE,MAAM,WAAW,IAAI;gBACrB,SAAS,MAAM,CAAC,QAAQ;gBAExB,wBAAwB;gBACxB,MAAM,IAAI;uDAAQ,CAAA,UAAW,WAAW,SAAS;;gBAEjD,8CAA8C;gBAC9C,6DAA6D;gBAC7D,MAAM,UAAU,CAAC,6FAA6F,CAAC;gBAC/G,SAAS;YACX,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,kBAAkB;gBAChC,MAAM;YACR,SAAU;gBACR,eAAe;YACjB;QACF;0CAAG;QAAC;KAAS;IAEb,MAAM,EAAE,YAAY,EAAE,aAAa,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,2KAAA,CAAA,cAAW,AAAD,EAAE;QAChE;QACA,QAAQ;YACN,WAAW;gBAAC;gBAAS;gBAAQ;gBAAQ;gBAAQ;aAAQ;QACvD;QACA,UAAU;QACV,UAAU,YAAY;IACxB;IAEA,IAAI,OAAO;QACT,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;wBACJ,KAAK;wBACL,KAAI;wBACJ,IAAI;wBACJ,WAAU;;;;;;;;;;;gBAGb,0BACC,6LAAC;oBACC,SAAS;oBACT,UAAU;oBACV,WAAU;8BACX;;;;;;8BAIH,6LAAC;oBAAI,WAAU;8BAA6B;;;;;;;;;;;;IAKlD;IAEA,qBACE,6LAAC;QACE,GAAG,cAAc;QAClB,WAAW,CAAC;;QAEV,EAAE,eAAe,+BAA+B,wCAAwC;QACxF,EAAE,YAAY,cAAc,kCAAkC,GAAG;MACnE,CAAC;;0BAED,6LAAC;gBAAO,GAAG,eAAe;;;;;;YAEzB,4BACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;qCAG/B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCAAgB;;;;;;oBAC9B,6BACC,6LAAC;wBAAE,WAAU;kCAA4B;;;;;6CAEzC,6LAAC;;0CACC,6LAAC;gCAAE,WAAU;0CAAiC;;;;;;0CAG9C,6LAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;;;;;;;;;;;;;AASnD;GArGgB;;QA8BwC,2KAAA,CAAA,cAAW;;;KA9BnD;AA8GT,SAAS,cAAc,EAAE,KAAK,EAAE,QAAQ,EAAE,cAAc,oBAAoB,EAAsB;;IACvG,MAAM,CAAC,KAAK,OAAO,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,SAAS;IACxC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,IAAI,CAAC,IAAI,IAAI,IAAI;QAEjB,gBAAgB;QAEhB,qBAAqB;QACrB,IAAI;YACF,MAAM,MAAM,IAAI,OAAO,KAAK;YAC5B,IAAI,MAAM,GAAG;gBACX,SAAS;gBACT,gBAAgB;YAClB;YACA,IAAI,OAAO,GAAG;gBACZ,MAAM;gBACN,gBAAgB;YAClB;YACA,IAAI,GAAG,GAAG;QACZ,EAAE,OAAO,OAAO;YACd,MAAM;YACN,gBAAgB;QAClB;IACF;IAEA,qBACE,6LAAC;QAAK,UAAU;QAAc,WAAU;;0BACtC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,MAAK;wBACL,OAAO;wBACP,UAAU,CAAC,IAAM,OAAO,EAAE,MAAM,CAAC,KAAK;wBACtC,aAAa;wBACb,WAAU;;;;;;kCAEZ,6LAAC;wBACC,MAAK;wBACL,UAAU,CAAC,IAAI,IAAI,MAAM;wBACzB,WAAU;kCAET,eAAe,kBAAkB;;;;;;;;;;;;YAIrC,uBACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;oBACJ,KAAK;oBACL,KAAI;oBACJ,IAAI;oBACJ,WAAU;;;;;;;;;;;;;;;;;AAMtB;IA3DgB;MAAA", "debugId": null}}, {"offset": {"line": 637, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Development/BuzzEdge/buzzedge-frontend/src/components/BlogPostForm.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { RichTextEditor } from './RichTextEditor';\nimport { ImageUpload, ImageUrlInput } from './ImageUpload';\n\ninterface BlogPostFormData {\n  title: string;\n  slug: string;\n  excerpt: string;\n  content: string;\n  category: string;\n  tags: string[];\n  featuredImage: {\n    url: string;\n    alt: string;\n  };\n  seo: {\n    metaTitle: string;\n    metaDescription: string;\n    keywords: string[];\n  };\n  status: 'draft' | 'published';\n}\n\ninterface BlogPostFormProps {\n  initialData?: Partial<BlogPostFormData>;\n  onSubmit: (data: BlogPostFormData) => Promise<void>;\n  onSave?: (data: BlogPostFormData) => Promise<void>;\n  isLoading?: boolean;\n}\n\nconst CATEGORIES = [\n  'AI Tools',\n  'Productivity',\n  'Design Tools',\n  'Developer Tools',\n  'Marketing Tools',\n  'Business Tools',\n  'Education',\n  'Entertainment',\n];\n\nexport function BlogPostForm({ initialData, onSubmit, onSave, isLoading }: BlogPostFormProps) {\n  const [formData, setFormData] = useState<BlogPostFormData>({\n    title: '',\n    slug: '',\n    excerpt: '',\n    content: '',\n    category: '',\n    tags: [],\n    featuredImage: {\n      url: '',\n      alt: '',\n    },\n    seo: {\n      metaTitle: '',\n      metaDescription: '',\n      keywords: [],\n    },\n    status: 'draft',\n    ...initialData,\n  });\n\n  const [tagInput, setTagInput] = useState('');\n  const [keywordInput, setKeywordInput] = useState('');\n  const [activeTab, setActiveTab] = useState<'content' | 'seo' | 'settings'>('content');\n\n  // Auto-generate slug from title\n  useEffect(() => {\n    if (formData.title && !initialData?.slug) {\n      const slug = formData.title\n        .toLowerCase()\n        .replace(/[^a-z0-9\\s-]/g, '')\n        .replace(/\\s+/g, '-')\n        .replace(/-+/g, '-')\n        .trim();\n      setFormData(prev => ({ ...prev, slug }));\n    }\n  }, [formData.title, initialData?.slug]);\n\n  // Auto-generate meta title from title\n  useEffect(() => {\n    if (formData.title && !formData.seo.metaTitle) {\n      setFormData(prev => ({\n        ...prev,\n        seo: {\n          ...prev.seo,\n          metaTitle: formData.title,\n        },\n      }));\n    }\n  }, [formData.title, formData.seo.metaTitle]);\n\n  const handleSubmit = async (e: React.FormEvent, status: 'draft' | 'published') => {\n    e.preventDefault();\n    const dataToSubmit = { ...formData, status };\n    await onSubmit(dataToSubmit);\n  };\n\n  const handleSave = async () => {\n    if (onSave) {\n      await onSave(formData);\n    }\n  };\n\n  const addTag = () => {\n    if (tagInput.trim() && !formData.tags.includes(tagInput.trim())) {\n      setFormData(prev => ({\n        ...prev,\n        tags: [...prev.tags, tagInput.trim()],\n      }));\n      setTagInput('');\n    }\n  };\n\n  const removeTag = (tagToRemove: string) => {\n    setFormData(prev => ({\n      ...prev,\n      tags: prev.tags.filter(tag => tag !== tagToRemove),\n    }));\n  };\n\n  const addKeyword = () => {\n    if (keywordInput.trim() && !formData.seo.keywords.includes(keywordInput.trim())) {\n      setFormData(prev => ({\n        ...prev,\n        seo: {\n          ...prev.seo,\n          keywords: [...prev.seo.keywords, keywordInput.trim()],\n        },\n      }));\n      setKeywordInput('');\n    }\n  };\n\n  const removeKeyword = (keywordToRemove: string) => {\n    setFormData(prev => ({\n      ...prev,\n      seo: {\n        ...prev.seo,\n        keywords: prev.seo.keywords.filter(keyword => keyword !== keywordToRemove),\n      },\n    }));\n  };\n\n  return (\n    <div className=\"max-w-4xl mx-auto\">\n      {/* Header */}\n      <div className=\"mb-6 flex justify-between items-center\">\n        <h1 className=\"text-2xl font-bold text-gray-900\">\n          {initialData ? 'Edit Post' : 'Create New Post'}\n        </h1>\n        <div className=\"flex space-x-3\">\n          {onSave && (\n            <button\n              onClick={handleSave}\n              disabled={isLoading}\n              className=\"px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 disabled:opacity-50\"\n            >\n              Save Draft\n            </button>\n          )}\n          <button\n            onClick={(e) => handleSubmit(e, 'published')}\n            disabled={isLoading}\n            className=\"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50\"\n          >\n            {isLoading ? 'Publishing...' : 'Publish'}\n          </button>\n        </div>\n      </div>\n\n      {/* Tabs */}\n      <div className=\"border-b border-gray-200 mb-6\">\n        <nav className=\"-mb-px flex space-x-8\">\n          {[\n            { id: 'content', label: 'Content' },\n            { id: 'seo', label: 'SEO' },\n            { id: 'settings', label: 'Settings' },\n          ].map((tab) => (\n            <button\n              key={tab.id}\n              onClick={() => setActiveTab(tab.id as any)}\n              className={`py-2 px-1 border-b-2 font-medium text-sm ${\n                activeTab === tab.id\n                  ? 'border-blue-500 text-blue-600'\n                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n              }`}\n            >\n              {tab.label}\n            </button>\n          ))}\n        </nav>\n      </div>\n\n      <form className=\"space-y-6\">\n        {/* Content Tab */}\n        {activeTab === 'content' && (\n          <div className=\"space-y-6\">\n            {/* Title */}\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Title *\n              </label>\n              <input\n                type=\"text\"\n                value={formData.title}\n                onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                placeholder=\"Enter post title...\"\n                required\n              />\n            </div>\n\n            {/* Slug */}\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                URL Slug *\n              </label>\n              <input\n                type=\"text\"\n                value={formData.slug}\n                onChange={(e) => setFormData(prev => ({ ...prev, slug: e.target.value }))}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                placeholder=\"url-slug\"\n                required\n              />\n              <p className=\"mt-1 text-sm text-gray-500\">\n                URL: /blog/{formData.slug}\n              </p>\n            </div>\n\n            {/* Excerpt */}\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Excerpt *\n              </label>\n              <textarea\n                value={formData.excerpt}\n                onChange={(e) => setFormData(prev => ({ ...prev, excerpt: e.target.value }))}\n                rows={3}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                placeholder=\"Brief description of the post...\"\n                required\n              />\n            </div>\n\n            {/* Featured Image */}\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Featured Image *\n              </label>\n              <div className=\"space-y-4\">\n                <ImageUrlInput\n                  value={formData.featuredImage.url}\n                  onChange={(url) => setFormData(prev => ({\n                    ...prev,\n                    featuredImage: { ...prev.featuredImage, url }\n                  }))}\n                />\n                {formData.featuredImage.url && (\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      Alt Text\n                    </label>\n                    <input\n                      type=\"text\"\n                      value={formData.featuredImage.alt}\n                      onChange={(e) => setFormData(prev => ({\n                        ...prev,\n                        featuredImage: { ...prev.featuredImage, alt: e.target.value }\n                      }))}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                      placeholder=\"Describe the image for accessibility...\"\n                    />\n                  </div>\n                )}\n              </div>\n            </div>\n\n            {/* Content Editor */}\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Content *\n              </label>\n              <RichTextEditor\n                content={formData.content}\n                onChange={(content) => setFormData(prev => ({ ...prev, content }))}\n                placeholder=\"Start writing your post...\"\n              />\n            </div>\n          </div>\n        )}\n\n        {/* SEO Tab */}\n        {activeTab === 'seo' && (\n          <div className=\"space-y-6\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Meta Title\n              </label>\n              <input\n                type=\"text\"\n                value={formData.seo.metaTitle}\n                onChange={(e) => setFormData(prev => ({\n                  ...prev,\n                  seo: { ...prev.seo, metaTitle: e.target.value }\n                }))}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                placeholder=\"SEO title for search engines...\"\n                maxLength={60}\n              />\n              <p className=\"mt-1 text-sm text-gray-500\">\n                {formData.seo.metaTitle.length}/60 characters\n              </p>\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Meta Description\n              </label>\n              <textarea\n                value={formData.seo.metaDescription}\n                onChange={(e) => setFormData(prev => ({\n                  ...prev,\n                  seo: { ...prev.seo, metaDescription: e.target.value }\n                }))}\n                rows={3}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                placeholder=\"SEO description for search engines...\"\n                maxLength={160}\n              />\n              <p className=\"mt-1 text-sm text-gray-500\">\n                {formData.seo.metaDescription.length}/160 characters\n              </p>\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Keywords\n              </label>\n              <div className=\"flex space-x-2 mb-2\">\n                <input\n                  type=\"text\"\n                  value={keywordInput}\n                  onChange={(e) => setKeywordInput(e.target.value)}\n                  onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addKeyword())}\n                  className=\"flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                  placeholder=\"Add keyword...\"\n                />\n                <button\n                  type=\"button\"\n                  onClick={addKeyword}\n                  className=\"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700\"\n                >\n                  Add\n                </button>\n              </div>\n              <div className=\"flex flex-wrap gap-2\">\n                {formData.seo.keywords.map((keyword) => (\n                  <span\n                    key={keyword}\n                    className=\"inline-flex items-center px-3 py-1 rounded-full text-sm bg-blue-100 text-blue-800\"\n                  >\n                    {keyword}\n                    <button\n                      type=\"button\"\n                      onClick={() => removeKeyword(keyword)}\n                      className=\"ml-2 text-blue-600 hover:text-blue-800\"\n                    >\n                      ×\n                    </button>\n                  </span>\n                ))}\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Settings Tab */}\n        {activeTab === 'settings' && (\n          <div className=\"space-y-6\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Category *\n              </label>\n              <select\n                value={formData.category}\n                onChange={(e) => setFormData(prev => ({ ...prev, category: e.target.value }))}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                required\n              >\n                <option value=\"\">Select a category</option>\n                {CATEGORIES.map((category) => (\n                  <option key={category} value={category}>\n                    {category}\n                  </option>\n                ))}\n              </select>\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Tags\n              </label>\n              <div className=\"flex space-x-2 mb-2\">\n                <input\n                  type=\"text\"\n                  value={tagInput}\n                  onChange={(e) => setTagInput(e.target.value)}\n                  onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addTag())}\n                  className=\"flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                  placeholder=\"Add tag...\"\n                />\n                <button\n                  type=\"button\"\n                  onClick={addTag}\n                  className=\"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700\"\n                >\n                  Add\n                </button>\n              </div>\n              <div className=\"flex flex-wrap gap-2\">\n                {formData.tags.map((tag) => (\n                  <span\n                    key={tag}\n                    className=\"inline-flex items-center px-3 py-1 rounded-full text-sm bg-gray-100 text-gray-800\"\n                  >\n                    {tag}\n                    <button\n                      type=\"button\"\n                      onClick={() => removeTag(tag)}\n                      className=\"ml-2 text-gray-600 hover:text-gray-800\"\n                    >\n                      ×\n                    </button>\n                  </span>\n                ))}\n              </div>\n            </div>\n          </div>\n        )}\n      </form>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAgCA,MAAM,aAAa;IACjB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAEM,SAAS,aAAa,EAAE,WAAW,EAAE,QAAQ,EAAE,MAAM,EAAE,SAAS,EAAqB;;IAC1F,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB;QACzD,OAAO;QACP,MAAM;QACN,SAAS;QACT,SAAS;QACT,UAAU;QACV,MAAM,EAAE;QACR,eAAe;YACb,KAAK;YACL,KAAK;QACP;QACA,KAAK;YACH,WAAW;YACX,iBAAiB;YACjB,UAAU,EAAE;QACd;QACA,QAAQ;QACR,GAAG,WAAW;IAChB;IAEA,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkC;IAE3E,gCAAgC;IAChC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,SAAS,KAAK,IAAI,CAAC,aAAa,MAAM;gBACxC,MAAM,OAAO,SAAS,KAAK,CACxB,WAAW,GACX,OAAO,CAAC,iBAAiB,IACzB,OAAO,CAAC,QAAQ,KAChB,OAAO,CAAC,OAAO,KACf,IAAI;gBACP;8CAAY,CAAA,OAAQ,CAAC;4BAAE,GAAG,IAAI;4BAAE;wBAAK,CAAC;;YACxC;QACF;iCAAG;QAAC,SAAS,KAAK;QAAE,aAAa;KAAK;IAEtC,sCAAsC;IACtC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,SAAS,KAAK,IAAI,CAAC,SAAS,GAAG,CAAC,SAAS,EAAE;gBAC7C;8CAAY,CAAA,OAAQ,CAAC;4BACnB,GAAG,IAAI;4BACP,KAAK;gCACH,GAAG,KAAK,GAAG;gCACX,WAAW,SAAS,KAAK;4BAC3B;wBACF,CAAC;;YACH;QACF;iCAAG;QAAC,SAAS,KAAK;QAAE,SAAS,GAAG,CAAC,SAAS;KAAC;IAE3C,MAAM,eAAe,OAAO,GAAoB;QAC9C,EAAE,cAAc;QAChB,MAAM,eAAe;YAAE,GAAG,QAAQ;YAAE;QAAO;QAC3C,MAAM,SAAS;IACjB;IAEA,MAAM,aAAa;QACjB,IAAI,QAAQ;YACV,MAAM,OAAO;QACf;IACF;IAEA,MAAM,SAAS;QACb,IAAI,SAAS,IAAI,MAAM,CAAC,SAAS,IAAI,CAAC,QAAQ,CAAC,SAAS,IAAI,KAAK;YAC/D,YAAY,CAAA,OAAQ,CAAC;oBACnB,GAAG,IAAI;oBACP,MAAM;2BAAI,KAAK,IAAI;wBAAE,SAAS,IAAI;qBAAG;gBACvC,CAAC;YACD,YAAY;QACd;IACF;IAEA,MAAM,YAAY,CAAC;QACjB,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,MAAM,KAAK,IAAI,CAAC,MAAM,CAAC,CAAA,MAAO,QAAQ;YACxC,CAAC;IACH;IAEA,MAAM,aAAa;QACjB,IAAI,aAAa,IAAI,MAAM,CAAC,SAAS,GAAG,CAAC,QAAQ,CAAC,QAAQ,CAAC,aAAa,IAAI,KAAK;YAC/E,YAAY,CAAA,OAAQ,CAAC;oBACnB,GAAG,IAAI;oBACP,KAAK;wBACH,GAAG,KAAK,GAAG;wBACX,UAAU;+BAAI,KAAK,GAAG,CAAC,QAAQ;4BAAE,aAAa,IAAI;yBAAG;oBACvD;gBACF,CAAC;YACD,gBAAgB;QAClB;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,KAAK;oBACH,GAAG,KAAK,GAAG;oBACX,UAAU,KAAK,GAAG,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAA,UAAW,YAAY;gBAC5D;YACF,CAAC;IACH;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCACX,cAAc,cAAc;;;;;;kCAE/B,6LAAC;wBAAI,WAAU;;4BACZ,wBACC,6LAAC;gCACC,SAAS;gCACT,UAAU;gCACV,WAAU;0CACX;;;;;;0CAIH,6LAAC;gCACC,SAAS,CAAC,IAAM,aAAa,GAAG;gCAChC,UAAU;gCACV,WAAU;0CAET,YAAY,kBAAkB;;;;;;;;;;;;;;;;;;0BAMrC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACZ;wBACC;4BAAE,IAAI;4BAAW,OAAO;wBAAU;wBAClC;4BAAE,IAAI;4BAAO,OAAO;wBAAM;wBAC1B;4BAAE,IAAI;4BAAY,OAAO;wBAAW;qBACrC,CAAC,GAAG,CAAC,CAAC,oBACL,6LAAC;4BAEC,SAAS,IAAM,aAAa,IAAI,EAAE;4BAClC,WAAW,CAAC,yCAAyC,EACnD,cAAc,IAAI,EAAE,GAChB,kCACA,8EACJ;sCAED,IAAI,KAAK;2BARL,IAAI,EAAE;;;;;;;;;;;;;;;0BAcnB,6LAAC;gBAAK,WAAU;;oBAEb,cAAc,2BACb,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAGhE,6LAAC;wCACC,MAAK;wCACL,OAAO,SAAS,KAAK;wCACrB,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;oDAAE,GAAG,IAAI;oDAAE,OAAO,EAAE,MAAM,CAAC,KAAK;gDAAC,CAAC;wCACxE,WAAU;wCACV,aAAY;wCACZ,QAAQ;;;;;;;;;;;;0CAKZ,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAGhE,6LAAC;wCACC,MAAK;wCACL,OAAO,SAAS,IAAI;wCACpB,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;oDAAE,GAAG,IAAI;oDAAE,MAAM,EAAE,MAAM,CAAC,KAAK;gDAAC,CAAC;wCACvE,WAAU;wCACV,aAAY;wCACZ,QAAQ;;;;;;kDAEV,6LAAC;wCAAE,WAAU;;4CAA6B;4CAC5B,SAAS,IAAI;;;;;;;;;;;;;0CAK7B,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAGhE,6LAAC;wCACC,OAAO,SAAS,OAAO;wCACvB,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;oDAAE,GAAG,IAAI;oDAAE,SAAS,EAAE,MAAM,CAAC,KAAK;gDAAC,CAAC;wCAC1E,MAAM;wCACN,WAAU;wCACV,aAAY;wCACZ,QAAQ;;;;;;;;;;;;0CAKZ,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAGhE,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,oIAAA,CAAA,gBAAa;gDACZ,OAAO,SAAS,aAAa,CAAC,GAAG;gDACjC,UAAU,CAAC,MAAQ,YAAY,CAAA,OAAQ,CAAC;4DACtC,GAAG,IAAI;4DACP,eAAe;gEAAE,GAAG,KAAK,aAAa;gEAAE;4DAAI;wDAC9C,CAAC;;;;;;4CAEF,SAAS,aAAa,CAAC,GAAG,kBACzB,6LAAC;;kEACC,6LAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAGhE,6LAAC;wDACC,MAAK;wDACL,OAAO,SAAS,aAAa,CAAC,GAAG;wDACjC,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;oEACpC,GAAG,IAAI;oEACP,eAAe;wEAAE,GAAG,KAAK,aAAa;wEAAE,KAAK,EAAE,MAAM,CAAC,KAAK;oEAAC;gEAC9D,CAAC;wDACD,WAAU;wDACV,aAAY;;;;;;;;;;;;;;;;;;;;;;;;0CAQtB,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAGhE,6LAAC,uIAAA,CAAA,iBAAc;wCACb,SAAS,SAAS,OAAO;wCACzB,UAAU,CAAC,UAAY,YAAY,CAAA,OAAQ,CAAC;oDAAE,GAAG,IAAI;oDAAE;gDAAQ,CAAC;wCAChE,aAAY;;;;;;;;;;;;;;;;;;oBAOnB,cAAc,uBACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAGhE,6LAAC;wCACC,MAAK;wCACL,OAAO,SAAS,GAAG,CAAC,SAAS;wCAC7B,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;oDACpC,GAAG,IAAI;oDACP,KAAK;wDAAE,GAAG,KAAK,GAAG;wDAAE,WAAW,EAAE,MAAM,CAAC,KAAK;oDAAC;gDAChD,CAAC;wCACD,WAAU;wCACV,aAAY;wCACZ,WAAW;;;;;;kDAEb,6LAAC;wCAAE,WAAU;;4CACV,SAAS,GAAG,CAAC,SAAS,CAAC,MAAM;4CAAC;;;;;;;;;;;;;0CAInC,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAGhE,6LAAC;wCACC,OAAO,SAAS,GAAG,CAAC,eAAe;wCACnC,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;oDACpC,GAAG,IAAI;oDACP,KAAK;wDAAE,GAAG,KAAK,GAAG;wDAAE,iBAAiB,EAAE,MAAM,CAAC,KAAK;oDAAC;gDACtD,CAAC;wCACD,MAAM;wCACN,WAAU;wCACV,aAAY;wCACZ,WAAW;;;;;;kDAEb,6LAAC;wCAAE,WAAU;;4CACV,SAAS,GAAG,CAAC,eAAe,CAAC,MAAM;4CAAC;;;;;;;;;;;;;0CAIzC,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAGhE,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,MAAK;gDACL,OAAO;gDACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;gDAC/C,YAAY,CAAC,IAAM,EAAE,GAAG,KAAK,WAAW,CAAC,EAAE,cAAc,IAAI,YAAY;gDACzE,WAAU;gDACV,aAAY;;;;;;0DAEd,6LAAC;gDACC,MAAK;gDACL,SAAS;gDACT,WAAU;0DACX;;;;;;;;;;;;kDAIH,6LAAC;wCAAI,WAAU;kDACZ,SAAS,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,wBAC1B,6LAAC;gDAEC,WAAU;;oDAET;kEACD,6LAAC;wDACC,MAAK;wDACL,SAAS,IAAM,cAAc;wDAC7B,WAAU;kEACX;;;;;;;+CARI;;;;;;;;;;;;;;;;;;;;;;oBAmBhB,cAAc,4BACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAGhE,6LAAC;wCACC,OAAO,SAAS,QAAQ;wCACxB,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;oDAAE,GAAG,IAAI;oDAAE,UAAU,EAAE,MAAM,CAAC,KAAK;gDAAC,CAAC;wCAC3E,WAAU;wCACV,QAAQ;;0DAER,6LAAC;gDAAO,OAAM;0DAAG;;;;;;4CAChB,WAAW,GAAG,CAAC,CAAC,yBACf,6LAAC;oDAAsB,OAAO;8DAC3B;mDADU;;;;;;;;;;;;;;;;;0CAOnB,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAGhE,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,MAAK;gDACL,OAAO;gDACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;gDAC3C,YAAY,CAAC,IAAM,EAAE,GAAG,KAAK,WAAW,CAAC,EAAE,cAAc,IAAI,QAAQ;gDACrE,WAAU;gDACV,aAAY;;;;;;0DAEd,6LAAC;gDACC,MAAK;gDACL,SAAS;gDACT,WAAU;0DACX;;;;;;;;;;;;kDAIH,6LAAC;wCAAI,WAAU;kDACZ,SAAS,IAAI,CAAC,GAAG,CAAC,CAAC,oBAClB,6LAAC;gDAEC,WAAU;;oDAET;kEACD,6LAAC;wDACC,MAAK;wDACL,SAAS,IAAM,UAAU;wDACzB,WAAU;kEACX;;;;;;;+CARI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoBzB;GAnZgB;KAAA", "debugId": null}}, {"offset": {"line": 1405, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Development/BuzzEdge/buzzedge-frontend/src/app/admin/posts/new/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport Link from 'next/link';\nimport { BlogPostForm } from '@/components/BlogPostForm';\n\ninterface BlogPostFormData {\n  title: string;\n  slug: string;\n  excerpt: string;\n  content: string;\n  category: string;\n  tags: string[];\n  featuredImage: {\n    url: string;\n    alt: string;\n  };\n  seo: {\n    metaTitle: string;\n    metaDescription: string;\n    keywords: string[];\n  };\n  status: 'draft' | 'published';\n}\n\nexport default function NewPostPage() {\n  const [isLoading, setIsLoading] = useState(false);\n  const router = useRouter();\n\n  useEffect(() => {\n    // Check if user is authenticated\n    const isAdmin = localStorage.getItem('buzzedge_admin');\n    if (!isAdmin) {\n      router.push('/admin/login');\n      return;\n    }\n  }, [router]);\n\n  const handleSubmit = async (data: BlogPostFormData) => {\n    setIsLoading(true);\n    try {\n      // In a real app, this would make an API call to create the post\n      console.log('Creating post:', data);\n      \n      // Simulate API call\n      await new Promise(resolve => setTimeout(resolve, 2000));\n      \n      // Show success message\n      alert(`Post \"${data.title}\" has been ${data.status === 'published' ? 'published' : 'saved as draft'} successfully!`);\n      \n      // Redirect to posts list\n      router.push('/admin/posts');\n    } catch (error) {\n      console.error('Failed to create post:', error);\n      alert('Failed to create post. Please try again.');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleSave = async (data: BlogPostFormData) => {\n    setIsLoading(true);\n    try {\n      // In a real app, this would make an API call to save the draft\n      console.log('Saving draft:', data);\n      \n      // Simulate API call\n      await new Promise(resolve => setTimeout(resolve, 1000));\n      \n      alert('Draft saved successfully!');\n    } catch (error) {\n      console.error('Failed to save draft:', error);\n      alert('Failed to save draft. Please try again.');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleLogout = () => {\n    localStorage.removeItem('buzzedge_admin');\n    router.push('/admin/login');\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Admin Header */}\n      <div className=\"bg-white shadow-sm border-b border-gray-200\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center h-16\">\n            <div className=\"flex items-center\">\n              <Link href=\"/admin/dashboard\" className=\"flex items-center\">\n                <div className=\"w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center text-white font-bold text-lg mr-2\">\n                  B\n                </div>\n                <span className=\"text-xl font-bold text-gray-900\">BuzzEdge Admin</span>\n              </Link>\n              <nav className=\"ml-8 flex space-x-4\">\n                <Link\n                  href=\"/admin/dashboard\"\n                  className=\"text-gray-500 hover:text-gray-700 px-3 py-2 rounded-md text-sm font-medium\"\n                >\n                  Dashboard\n                </Link>\n                <Link\n                  href=\"/admin/posts\"\n                  className=\"text-gray-500 hover:text-gray-700 px-3 py-2 rounded-md text-sm font-medium\"\n                >\n                  Posts\n                </Link>\n                <span className=\"text-blue-600 px-3 py-2 rounded-md text-sm font-medium\">\n                  New Post\n                </span>\n              </nav>\n            </div>\n            <div className=\"flex items-center space-x-4\">\n              <Link\n                href=\"/\"\n                target=\"_blank\"\n                className=\"text-gray-500 hover:text-gray-700\"\n              >\n                View Site\n              </Link>\n              <button\n                onClick={handleLogout}\n                className=\"bg-red-600 text-white px-4 py-2 rounded-md text-sm hover:bg-red-700\"\n              >\n                Logout\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Breadcrumb */}\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4\">\n        <nav className=\"flex\" aria-label=\"Breadcrumb\">\n          <ol className=\"flex items-center space-x-4\">\n            <li>\n              <Link href=\"/admin/dashboard\" className=\"text-gray-400 hover:text-gray-500\">\n                Dashboard\n              </Link>\n            </li>\n            <li>\n              <div className=\"flex items-center\">\n                <svg className=\"flex-shrink-0 h-5 w-5 text-gray-300\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                  <path fillRule=\"evenodd\" d=\"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z\" clipRule=\"evenodd\" />\n                </svg>\n                <Link href=\"/admin/posts\" className=\"ml-4 text-gray-400 hover:text-gray-500\">\n                  Posts\n                </Link>\n              </div>\n            </li>\n            <li>\n              <div className=\"flex items-center\">\n                <svg className=\"flex-shrink-0 h-5 w-5 text-gray-300\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                  <path fillRule=\"evenodd\" d=\"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z\" clipRule=\"evenodd\" />\n                </svg>\n                <span className=\"ml-4 text-gray-500\">New Post</span>\n              </div>\n            </li>\n          </ol>\n        </nav>\n      </div>\n\n      {/* Main Content */}\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-12\">\n        <BlogPostForm\n          onSubmit={handleSubmit}\n          onSave={handleSave}\n          isLoading={isLoading}\n        />\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AA0Be,SAAS;;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,iCAAiC;YACjC,MAAM,UAAU,aAAa,OAAO,CAAC;YACrC,IAAI,CAAC,SAAS;gBACZ,OAAO,IAAI,CAAC;gBACZ;YACF;QACF;gCAAG;QAAC;KAAO;IAEX,MAAM,eAAe,OAAO;QAC1B,aAAa;QACb,IAAI;YACF,gEAAgE;YAChE,QAAQ,GAAG,CAAC,kBAAkB;YAE9B,oBAAoB;YACpB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,uBAAuB;YACvB,MAAM,CAAC,MAAM,EAAE,KAAK,KAAK,CAAC,WAAW,EAAE,KAAK,MAAM,KAAK,cAAc,cAAc,iBAAiB,cAAc,CAAC;YAEnH,yBAAyB;YACzB,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,MAAM;QACR,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,aAAa,OAAO;QACxB,aAAa;QACb,IAAI;YACF,+DAA+D;YAC/D,QAAQ,GAAG,CAAC,iBAAiB;YAE7B,oBAAoB;YACpB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,MAAM;QACR,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,MAAM;QACR,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,eAAe;QACnB,aAAa,UAAU,CAAC;QACxB,OAAO,IAAI,CAAC;IACd;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAmB,WAAU;;0DACtC,6LAAC;gDAAI,WAAU;0DAAqI;;;;;;0DAGpJ,6LAAC;gDAAK,WAAU;0DAAkC;;;;;;;;;;;;kDAEpD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;0DACX;;;;;;0DAGD,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;0DACX;;;;;;0DAGD,6LAAC;gDAAK,WAAU;0DAAyD;;;;;;;;;;;;;;;;;;0CAK7E,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,QAAO;wCACP,WAAU;kDACX;;;;;;kDAGD,6LAAC;wCACC,SAAS;wCACT,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAST,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;oBAAO,cAAW;8BAC/B,cAAA,6LAAC;wBAAG,WAAU;;0CACZ,6LAAC;0CACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAmB,WAAU;8CAAoC;;;;;;;;;;;0CAI9E,6LAAC;0CACC,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;4CAAsC,MAAK;4CAAe,SAAQ;sDAC/E,cAAA,6LAAC;gDAAK,UAAS;gDAAU,GAAE;gDAAqH,UAAS;;;;;;;;;;;sDAE3J,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAe,WAAU;sDAAyC;;;;;;;;;;;;;;;;;0CAKjF,6LAAC;0CACC,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;4CAAsC,MAAK;4CAAe,SAAQ;sDAC/E,cAAA,6LAAC;gDAAK,UAAS;gDAAU,GAAE;gDAAqH,UAAS;;;;;;;;;;;sDAE3J,6LAAC;4CAAK,WAAU;sDAAqB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ/C,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,qIAAA,CAAA,eAAY;oBACX,UAAU;oBACV,QAAQ;oBACR,WAAW;;;;;;;;;;;;;;;;;AAKrB;GArJwB;;QAEP,qIAAA,CAAA,YAAS;;;KAFF", "debugId": null}}]}