{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Development/BuzzEdge/buzzedge-frontend/src/lib/api.ts"], "sourcesContent": ["// API Configuration\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5001';\n\nexport class ApiError extends Error {\n  constructor(\n    message: string,\n    public status: number,\n    public response?: any\n  ) {\n    super(message);\n    this.name = 'ApiError';\n  }\n}\n\nexport interface ApiResponse<T> {\n  success: boolean;\n  data?: T;\n  error?: string;\n  message?: string;\n}\n\nclass ApiClient {\n  private baseURL: string;\n\n  constructor(baseURL: string) {\n    this.baseURL = baseURL;\n  }\n\n  private async request<T>(\n    endpoint: string,\n    options: RequestInit = {}\n  ): Promise<T> {\n    const url = `${this.baseURL}${endpoint}`;\n    \n    const config: RequestInit = {\n      headers: {\n        'Content-Type': 'application/json',\n        ...options.headers,\n      },\n      ...options,\n    };\n\n    try {\n      const response = await fetch(url, config);\n      \n      if (!response.ok) {\n        const errorData = await response.json().catch(() => ({}));\n        throw new ApiError(\n          errorData.error || `HTTP ${response.status}: ${response.statusText}`,\n          response.status,\n          errorData\n        );\n      }\n\n      const data = await response.json();\n      return data;\n    } catch (error) {\n      if (error instanceof ApiError) {\n        throw error;\n      }\n      \n      // Network or other errors\n      throw new ApiError(\n        error instanceof Error ? error.message : 'Network error occurred',\n        0\n      );\n    }\n  }\n\n  async get<T>(endpoint: string, params?: Record<string, string>): Promise<T> {\n    const url = new URL(endpoint, this.baseURL);\n    if (params) {\n      Object.entries(params).forEach(([key, value]) => {\n        url.searchParams.append(key, value);\n      });\n    }\n    \n    return this.request<T>(url.pathname + url.search);\n  }\n\n  async post<T>(endpoint: string, data?: any): Promise<T> {\n    return this.request<T>(endpoint, {\n      method: 'POST',\n      body: data ? JSON.stringify(data) : undefined,\n    });\n  }\n\n  async put<T>(endpoint: string, data?: any): Promise<T> {\n    return this.request<T>(endpoint, {\n      method: 'PUT',\n      body: data ? JSON.stringify(data) : undefined,\n    });\n  }\n\n  async delete<T>(endpoint: string): Promise<T> {\n    return this.request<T>(endpoint, {\n      method: 'DELETE',\n    });\n  }\n}\n\n// Create and export the API client instance\nexport const apiClient = new ApiClient(API_BASE_URL);\n\n// Health check function\nexport const checkApiHealth = async (): Promise<boolean> => {\n  try {\n    await apiClient.get('/health');\n    return true;\n  } catch (error) {\n    console.error('API health check failed:', error);\n    return false;\n  }\n};\n"], "names": [], "mappings": "AAAA,oBAAoB;;;;;;AACC;AAArB,MAAM,eAAe,6DAAmC;AAEjD,MAAM,iBAAiB;;;IAC5B,YACE,OAAe,EACf,AAAO,MAAc,EACrB,AAAO,QAAc,CACrB;QACA,KAAK,CAAC,eAHC,SAAA,aACA,WAAA;QAGP,IAAI,CAAC,IAAI,GAAG;IACd;AACF;AASA,MAAM;IACI,QAAgB;IAExB,YAAY,OAAe,CAAE;QAC3B,IAAI,CAAC,OAAO,GAAG;IACjB;IAEA,MAAc,QACZ,QAAgB,EAChB,UAAuB,CAAC,CAAC,EACb;QACZ,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,GAAG,UAAU;QAExC,MAAM,SAAsB;YAC1B,SAAS;gBACP,gBAAgB;gBAChB,GAAG,QAAQ,OAAO;YACpB;YACA,GAAG,OAAO;QACZ;QAEA,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,KAAK;YAElC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC,CAAC,CAAC;gBACvD,MAAM,IAAI,SACR,UAAU,KAAK,IAAI,CAAC,KAAK,EAAE,SAAS,MAAM,CAAC,EAAE,EAAE,SAAS,UAAU,EAAE,EACpE,SAAS,MAAM,EACf;YAEJ;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,OAAO;QACT,EAAE,OAAO,OAAO;YACd,IAAI,iBAAiB,UAAU;gBAC7B,MAAM;YACR;YAEA,0BAA0B;YAC1B,MAAM,IAAI,SACR,iBAAiB,QAAQ,MAAM,OAAO,GAAG,0BACzC;QAEJ;IACF;IAEA,MAAM,IAAO,QAAgB,EAAE,MAA+B,EAAc;QAC1E,MAAM,MAAM,IAAI,IAAI,UAAU,IAAI,CAAC,OAAO;QAC1C,IAAI,QAAQ;YACV,OAAO,OAAO,CAAC,QAAQ,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;gBAC1C,IAAI,YAAY,CAAC,MAAM,CAAC,KAAK;YAC/B;QACF;QAEA,OAAO,IAAI,CAAC,OAAO,CAAI,IAAI,QAAQ,GAAG,IAAI,MAAM;IAClD;IAEA,MAAM,KAAQ,QAAgB,EAAE,IAAU,EAAc;QACtD,OAAO,IAAI,CAAC,OAAO,CAAI,UAAU;YAC/B,QAAQ;YACR,MAAM,OAAO,KAAK,SAAS,CAAC,QAAQ;QACtC;IACF;IAEA,MAAM,IAAO,QAAgB,EAAE,IAAU,EAAc;QACrD,OAAO,IAAI,CAAC,OAAO,CAAI,UAAU;YAC/B,QAAQ;YACR,MAAM,OAAO,KAAK,SAAS,CAAC,QAAQ;QACtC;IACF;IAEA,MAAM,OAAU,QAAgB,EAAc;QAC5C,OAAO,IAAI,CAAC,OAAO,CAAI,UAAU;YAC/B,QAAQ;QACV;IACF;AACF;AAGO,MAAM,YAAY,IAAI,UAAU;AAGhC,MAAM,iBAAiB;IAC5B,IAAI;QACF,MAAM,UAAU,GAAG,CAAC;QACpB,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 99, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Development/BuzzEdge/buzzedge-frontend/src/services/blogService.ts"], "sourcesContent": ["import { apiClient } from '@/lib/api';\nimport {\n  BlogListResponse,\n  BlogPostResponse,\n  SearchResponse,\n  CategoryResponse,\n  BlogPostSummary,\n} from '@/types/blog';\n\nexport class BlogService {\n  /**\n   * Get all published blog posts with pagination\n   */\n  static async getAllPosts(\n    page: number = 1,\n    limit: number = 10\n  ): Promise<BlogListResponse> {\n    return apiClient.get<BlogListResponse>('/api/blogs', {\n      page: page.toString(),\n      limit: limit.toString(),\n    });\n  }\n\n  /**\n   * Get a single blog post by slug\n   */\n  static async getPostBySlug(slug: string): Promise<BlogPostResponse> {\n    return apiClient.get<BlogPostResponse>(`/api/blogs/${slug}`);\n  }\n\n  /**\n   * Search blog posts\n   */\n  static async searchPosts(\n    query: string,\n    page: number = 1,\n    limit: number = 10\n  ): Promise<SearchResponse> {\n    return apiClient.get<SearchResponse>('/api/blogs/search', {\n      q: query,\n      page: page.toString(),\n      limit: limit.toString(),\n    });\n  }\n\n  /**\n   * Get posts by category\n   */\n  static async getPostsByCategory(\n    category: string,\n    page: number = 1,\n    limit: number = 10\n  ): Promise<CategoryResponse> {\n    return apiClient.get<CategoryResponse>(`/api/blogs/category/${category}`, {\n      page: page.toString(),\n      limit: limit.toString(),\n    });\n  }\n\n  /**\n   * Increment post view count\n   */\n  static async incrementPostViews(slug: string): Promise<{ views: number }> {\n    return apiClient.post(`/api/blogs/${slug}/view`);\n  }\n\n  /**\n   * Get featured posts (most viewed)\n   */\n  static async getFeaturedPosts(limit: number = 5): Promise<BlogPostSummary[]> {\n    const response = await this.getAllPosts(1, limit);\n    return response.data.posts;\n  }\n\n  /**\n   * Get recent posts\n   */\n  static async getRecentPosts(limit: number = 5): Promise<BlogPostSummary[]> {\n    const response = await this.getAllPosts(1, limit);\n    return response.data.posts;\n  }\n}\n\n// Utility functions for client-side data processing\nexport const blogUtils = {\n  /**\n   * Format date for display\n   */\n  formatDate: (dateString: string): string => {\n    const date = new Date(dateString);\n    return date.toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric',\n    });\n  },\n\n  /**\n   * Calculate reading time based on content length\n   */\n  calculateReadingTime: (content: string): number => {\n    const wordsPerMinute = 200;\n    const wordCount = content.split(/\\s+/).length;\n    return Math.ceil(wordCount / wordsPerMinute);\n  },\n\n  /**\n   * Get category color class\n   */\n  getCategoryColor: (category: string): string => {\n    switch (category.toLowerCase()) {\n      case 'ai tools':\n        return 'bg-blue-100 text-blue-800';\n      case 'productivity':\n        return 'bg-green-100 text-green-800';\n      case 'developer tools':\n        return 'bg-purple-100 text-purple-800';\n      case 'design tools':\n        return 'bg-pink-100 text-pink-800';\n      default:\n        return 'bg-gray-100 text-gray-800';\n    }\n  },\n\n  /**\n   * Generate SEO-friendly URL slug\n   */\n  generateSlug: (title: string): string => {\n    return title\n      .toLowerCase()\n      .replace(/[^a-z0-9]+/g, '-')\n      .replace(/(^-|-$)/g, '');\n  },\n\n  /**\n   * Truncate text to specified length\n   */\n  truncateText: (text: string, maxLength: number): string => {\n    if (text.length <= maxLength) return text;\n    return text.substring(0, maxLength).replace(/\\s+\\S*$/, '') + '...';\n  },\n};\n"], "names": [], "mappings": ";;;;AAAA;;AASO,MAAM;IACX;;GAEC,GACD,aAAa,YACX,OAAe,CAAC,EAChB,QAAgB,EAAE,EACS;QAC3B,OAAO,oHAAA,CAAA,YAAS,CAAC,GAAG,CAAmB,cAAc;YACnD,MAAM,KAAK,QAAQ;YACnB,OAAO,MAAM,QAAQ;QACvB;IACF;IAEA;;GAEC,GACD,aAAa,cAAc,IAAY,EAA6B;QAClE,OAAO,oHAAA,CAAA,YAAS,CAAC,GAAG,CAAmB,CAAC,WAAW,EAAE,MAAM;IAC7D;IAEA;;GAEC,GACD,aAAa,YACX,KAAa,EACb,OAAe,CAAC,EAChB,QAAgB,EAAE,EACO;QACzB,OAAO,oHAAA,CAAA,YAAS,CAAC,GAAG,CAAiB,qBAAqB;YACxD,GAAG;YACH,MAAM,KAAK,QAAQ;YACnB,OAAO,MAAM,QAAQ;QACvB;IACF;IAEA;;GAEC,GACD,aAAa,mBACX,QAAgB,EAChB,OAAe,CAAC,EAChB,QAAgB,EAAE,EACS;QAC3B,OAAO,oHAAA,CAAA,YAAS,CAAC,GAAG,CAAmB,CAAC,oBAAoB,EAAE,UAAU,EAAE;YACxE,MAAM,KAAK,QAAQ;YACnB,OAAO,MAAM,QAAQ;QACvB;IACF;IAEA;;GAEC,GACD,aAAa,mBAAmB,IAAY,EAA8B;QACxE,OAAO,oHAAA,CAAA,YAAS,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE,KAAK,KAAK,CAAC;IACjD;IAEA;;GAEC,GACD,aAAa,iBAAiB,QAAgB,CAAC,EAA8B;QAC3E,MAAM,WAAW,MAAM,IAAI,CAAC,WAAW,CAAC,GAAG;QAC3C,OAAO,SAAS,IAAI,CAAC,KAAK;IAC5B;IAEA;;GAEC,GACD,aAAa,eAAe,QAAgB,CAAC,EAA8B;QACzE,MAAM,WAAW,MAAM,IAAI,CAAC,WAAW,CAAC,GAAG;QAC3C,OAAO,SAAS,IAAI,CAAC,KAAK;IAC5B;AACF;AAGO,MAAM,YAAY;IACvB;;GAEC,GACD,YAAY,CAAC;QACX,MAAM,OAAO,IAAI,KAAK;QACtB,OAAO,KAAK,kBAAkB,CAAC,SAAS;YACtC,MAAM;YACN,OAAO;YACP,KAAK;QACP;IACF;IAEA;;GAEC,GACD,sBAAsB,CAAC;QACrB,MAAM,iBAAiB;QACvB,MAAM,YAAY,QAAQ,KAAK,CAAC,OAAO,MAAM;QAC7C,OAAO,KAAK,IAAI,CAAC,YAAY;IAC/B;IAEA;;GAEC,GACD,kBAAkB,CAAC;QACjB,OAAQ,SAAS,WAAW;YAC1B,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA;;GAEC,GACD,cAAc,CAAC;QACb,OAAO,MACJ,WAAW,GACX,OAAO,CAAC,eAAe,KACvB,OAAO,CAAC,YAAY;IACzB;IAEA;;GAEC,GACD,cAAc,CAAC,MAAc;QAC3B,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;QACrC,OAAO,KAAK,SAAS,CAAC,GAAG,WAAW,OAAO,CAAC,WAAW,MAAM;IAC/D;AACF", "debugId": null}}, {"offset": {"line": 209, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Development/BuzzEdge/buzzedge-frontend/src/components/SearchSuggestions.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect, useRef } from 'react';\nimport Link from 'next/link';\nimport { BlogService } from '@/services/blogService';\nimport { BlogPostSummary } from '@/types/blog';\n\ninterface SearchSuggestionsProps {\n  query: string;\n  onSelect: () => void;\n}\n\nexport function SearchSuggestions({ query, onSelect }: SearchSuggestionsProps) {\n  const [suggestions, setSuggestions] = useState<BlogPostSummary[]>([]);\n  const [isLoading, setIsLoading] = useState(false);\n  const [isVisible, setIsVisible] = useState(false);\n  const timeoutRef = useRef<NodeJS.Timeout>();\n\n  useEffect(() => {\n    // Clear previous timeout\n    if (timeoutRef.current) {\n      clearTimeout(timeoutRef.current);\n    }\n\n    // Don't search for very short queries\n    if (query.trim().length < 2) {\n      setSuggestions([]);\n      setIsVisible(false);\n      return;\n    }\n\n    // Debounce the search\n    timeoutRef.current = setTimeout(async () => {\n      setIsLoading(true);\n      try {\n        const response = await BlogService.searchPosts(query.trim(), 1, 5);\n        setSuggestions(response.data.posts);\n        setIsVisible(response.data.posts.length > 0);\n      } catch (error) {\n        console.error('Failed to fetch search suggestions:', error);\n        setSuggestions([]);\n        setIsVisible(false);\n      } finally {\n        setIsLoading(false);\n      }\n    }, 300); // 300ms debounce\n\n    return () => {\n      if (timeoutRef.current) {\n        clearTimeout(timeoutRef.current);\n      }\n    };\n  }, [query]);\n\n  if (!isVisible && !isLoading) {\n    return null;\n  }\n\n  return (\n    <div className=\"absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-md shadow-lg z-50 max-h-96 overflow-y-auto\">\n      {isLoading ? (\n        <div className=\"p-4 text-center\">\n          <div className=\"inline-flex items-center\">\n            <svg className=\"animate-spin -ml-1 mr-3 h-4 w-4 text-gray-400\" fill=\"none\" viewBox=\"0 0 24 24\">\n              <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\" />\n              <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\" />\n            </svg>\n            <span className=\"text-sm text-gray-500\">Searching...</span>\n          </div>\n        </div>\n      ) : suggestions.length > 0 ? (\n        <>\n          <div className=\"p-2 border-b border-gray-100\">\n            <p className=\"text-xs text-gray-500 font-medium uppercase tracking-wide\">\n              Suggestions\n            </p>\n          </div>\n          <div className=\"py-1\">\n            {suggestions.map((post) => (\n              <Link\n                key={post._id}\n                href={`/blog/${post.slug}`}\n                onClick={onSelect}\n                className=\"block px-4 py-3 hover:bg-gray-50 transition-colors\"\n              >\n                <div className=\"flex items-start space-x-3\">\n                  <div className=\"flex-shrink-0\">\n                    <img\n                      src={post.featuredImage.url}\n                      alt={post.featuredImage.alt}\n                      className=\"w-12 h-12 rounded object-cover\"\n                    />\n                  </div>\n                  <div className=\"flex-1 min-w-0\">\n                    <p className=\"text-sm font-medium text-gray-900 line-clamp-1\">\n                      {post.title}\n                    </p>\n                    <p className=\"text-xs text-gray-500 line-clamp-2 mt-1\">\n                      {post.excerpt}\n                    </p>\n                    <div className=\"flex items-center mt-2 space-x-2\">\n                      <span className=\"inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800\">\n                        {post.category}\n                      </span>\n                      <span className=\"text-xs text-gray-400\">\n                        {post.analytics.views} views\n                      </span>\n                    </div>\n                  </div>\n                </div>\n              </Link>\n            ))}\n          </div>\n          <div className=\"p-2 border-t border-gray-100\">\n            <Link\n              href={`/search?q=${encodeURIComponent(query)}`}\n              onClick={onSelect}\n              className=\"block w-full text-center py-2 text-sm text-blue-600 hover:text-blue-800 font-medium\"\n            >\n              View all results for \"{query}\"\n            </Link>\n          </div>\n        </>\n      ) : null}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAYO,SAAS,kBAAkB,EAAE,KAAK,EAAE,QAAQ,EAA0B;;IAC3E,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAqB,EAAE;IACpE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD;IAExB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,yBAAyB;YACzB,IAAI,WAAW,OAAO,EAAE;gBACtB,aAAa,WAAW,OAAO;YACjC;YAEA,sCAAsC;YACtC,IAAI,MAAM,IAAI,GAAG,MAAM,GAAG,GAAG;gBAC3B,eAAe,EAAE;gBACjB,aAAa;gBACb;YACF;YAEA,sBAAsB;YACtB,WAAW,OAAO,GAAG;+CAAW;oBAC9B,aAAa;oBACb,IAAI;wBACF,MAAM,WAAW,MAAM,iIAAA,CAAA,cAAW,CAAC,WAAW,CAAC,MAAM,IAAI,IAAI,GAAG;wBAChE,eAAe,SAAS,IAAI,CAAC,KAAK;wBAClC,aAAa,SAAS,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG;oBAC5C,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,uCAAuC;wBACrD,eAAe,EAAE;wBACjB,aAAa;oBACf,SAAU;wBACR,aAAa;oBACf;gBACF;8CAAG,MAAM,iBAAiB;YAE1B;+CAAO;oBACL,IAAI,WAAW,OAAO,EAAE;wBACtB,aAAa,WAAW,OAAO;oBACjC;gBACF;;QACF;sCAAG;QAAC;KAAM;IAEV,IAAI,CAAC,aAAa,CAAC,WAAW;QAC5B,OAAO;IACT;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACZ,0BACC,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;wBAAgD,MAAK;wBAAO,SAAQ;;0CACjF,6LAAC;gCAAO,WAAU;gCAAa,IAAG;gCAAK,IAAG;gCAAK,GAAE;gCAAK,QAAO;gCAAe,aAAY;;;;;;0CACxF,6LAAC;gCAAK,WAAU;gCAAa,MAAK;gCAAe,GAAE;;;;;;;;;;;;kCAErD,6LAAC;wBAAK,WAAU;kCAAwB;;;;;;;;;;;;;;;;mBAG1C,YAAY,MAAM,GAAG,kBACvB;;8BACE,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAE,WAAU;kCAA4D;;;;;;;;;;;8BAI3E,6LAAC;oBAAI,WAAU;8BACZ,YAAY,GAAG,CAAC,CAAC,qBAChB,6LAAC,+JAAA,CAAA,UAAI;4BAEH,MAAM,CAAC,MAAM,EAAE,KAAK,IAAI,EAAE;4BAC1B,SAAS;4BACT,WAAU;sCAEV,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CACC,KAAK,KAAK,aAAa,CAAC,GAAG;4CAC3B,KAAK,KAAK,aAAa,CAAC,GAAG;4CAC3B,WAAU;;;;;;;;;;;kDAGd,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;0DACV,KAAK,KAAK;;;;;;0DAEb,6LAAC;gDAAE,WAAU;0DACV,KAAK,OAAO;;;;;;0DAEf,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEACb,KAAK,QAAQ;;;;;;kEAEhB,6LAAC;wDAAK,WAAU;;4DACb,KAAK,SAAS,CAAC,KAAK;4DAAC;;;;;;;;;;;;;;;;;;;;;;;;;2BAzBzB,KAAK,GAAG;;;;;;;;;;8BAiCnB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;wBACH,MAAM,CAAC,UAAU,EAAE,mBAAmB,QAAQ;wBAC9C,SAAS;wBACT,WAAU;;4BACX;4BACwB;4BAAM;;;;;;;;;;;;;2BAIjC;;;;;;AAGV;GAlHgB;KAAA", "debugId": null}}, {"offset": {"line": 481, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Development/BuzzEdge/buzzedge-frontend/src/components/Header.tsx"], "sourcesContent": ["\"use client\";\n\nimport Link from \"next/link\";\nimport { useState, useRef, useEffect } from \"react\";\nimport { useRouter } from \"next/navigation\";\nimport { SearchSuggestions } from \"./SearchSuggestions\";\n\nexport function Header() {\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const [searchQuery, setSearchQuery] = useState(\"\");\n  const [showSuggestions, setShowSuggestions] = useState(false);\n  const router = useRouter();\n  const searchRef = useRef<HTMLDivElement>(null);\n\n  const navigation = [\n    { name: \"Home\", href: \"/\" },\n    { name: \"AI Tools\", href: \"/category/ai-tools\" },\n    { name: \"Productivity\", href: \"/category/productivity\" },\n    { name: \"Developer Tools\", href: \"/category/developer-tools\" },\n    { name: \"Design Tools\", href: \"/category/design-tools\" },\n  ];\n\n  const handleSearch = (e: React.FormEvent) => {\n    e.preventDefault();\n    if (searchQuery.trim()) {\n      router.push(`/search?q=${encodeURIComponent(searchQuery.trim())}`);\n      setSearchQuery(\"\");\n      setShowSuggestions(false);\n    }\n  };\n\n  const handleSearchInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const value = e.target.value;\n    setSearchQuery(value);\n    setShowSuggestions(value.trim().length > 0);\n  };\n\n  const handleSuggestionSelect = () => {\n    setSearchQuery(\"\");\n    setShowSuggestions(false);\n  };\n\n  // Close suggestions when clicking outside\n  useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      if (\n        searchRef.current &&\n        !searchRef.current.contains(event.target as Node)\n      ) {\n        setShowSuggestions(false);\n      }\n    };\n\n    document.addEventListener(\"mousedown\", handleClickOutside);\n    return () => {\n      document.removeEventListener(\"mousedown\", handleClickOutside);\n    };\n  }, []);\n\n  return (\n    <header className=\"bg-white shadow-sm border-b border-gray-200\">\n      <nav className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between items-center h-16\">\n          {/* Logo */}\n          <div className=\"flex-shrink-0\">\n            <Link href=\"/\" className=\"flex items-center\">\n              <div className=\"w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center\">\n                <span className=\"text-white font-bold text-lg\">B</span>\n              </div>\n              <span className=\"ml-2 text-xl font-bold text-gray-900\">\n                BuzzEdge\n              </span>\n            </Link>\n          </div>\n\n          {/* Desktop Navigation */}\n          <div className=\"hidden md:block\">\n            <div className=\"ml-10 flex items-baseline space-x-4\">\n              {navigation.map((item) => (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  className=\"text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium transition-colors\"\n                >\n                  {item.name}\n                </Link>\n              ))}\n            </div>\n          </div>\n\n          {/* Search Bar */}\n          <div className=\"hidden md:block flex-1 max-w-lg mx-8\" ref={searchRef}>\n            <form onSubmit={handleSearch}>\n              <div className=\"relative\">\n                <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                  <svg\n                    className=\"h-5 w-5 text-gray-400\"\n                    fill=\"none\"\n                    stroke=\"currentColor\"\n                    viewBox=\"0 0 24 24\"\n                  >\n                    <path\n                      strokeLinecap=\"round\"\n                      strokeLinejoin=\"round\"\n                      strokeWidth={2}\n                      d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"\n                    />\n                  </svg>\n                </div>\n                <input\n                  type=\"search\"\n                  placeholder=\"Search reviews...\"\n                  value={searchQuery}\n                  onChange={handleSearchInputChange}\n                  onFocus={() =>\n                    searchQuery.trim().length > 0 && setShowSuggestions(true)\n                  }\n                  className=\"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n                />\n                {showSuggestions && (\n                  <SearchSuggestions\n                    query={searchQuery}\n                    onSelect={handleSuggestionSelect}\n                  />\n                )}\n              </div>\n            </form>\n          </div>\n\n          {/* Mobile menu button */}\n          <div className=\"md:hidden\">\n            <button\n              onClick={() => setIsMenuOpen(!isMenuOpen)}\n              className=\"inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500\"\n            >\n              <span className=\"sr-only\">Open main menu</span>\n              {!isMenuOpen ? (\n                <svg\n                  className=\"block h-6 w-6\"\n                  fill=\"none\"\n                  stroke=\"currentColor\"\n                  viewBox=\"0 0 24 24\"\n                >\n                  <path\n                    strokeLinecap=\"round\"\n                    strokeLinejoin=\"round\"\n                    strokeWidth={2}\n                    d=\"M4 6h16M4 12h16M4 18h16\"\n                  />\n                </svg>\n              ) : (\n                <svg\n                  className=\"block h-6 w-6\"\n                  fill=\"none\"\n                  stroke=\"currentColor\"\n                  viewBox=\"0 0 24 24\"\n                >\n                  <path\n                    strokeLinecap=\"round\"\n                    strokeLinejoin=\"round\"\n                    strokeWidth={2}\n                    d=\"M6 18L18 6M6 6l12 12\"\n                  />\n                </svg>\n              )}\n            </button>\n          </div>\n        </div>\n\n        {/* Mobile Navigation */}\n        {isMenuOpen && (\n          <div className=\"md:hidden\">\n            <div className=\"px-2 pt-2 pb-3 space-y-1 sm:px-3 border-t border-gray-200\">\n              {navigation.map((item) => (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  className=\"text-gray-600 hover:text-gray-900 block px-3 py-2 rounded-md text-base font-medium transition-colors\"\n                  onClick={() => setIsMenuOpen(false)}\n                >\n                  {item.name}\n                </Link>\n              ))}\n\n              {/* Mobile Search */}\n              <div className=\"px-3 py-2\">\n                <div className=\"relative\">\n                  <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                    <svg\n                      className=\"h-5 w-5 text-gray-400\"\n                      fill=\"none\"\n                      stroke=\"currentColor\"\n                      viewBox=\"0 0 24 24\"\n                    >\n                      <path\n                        strokeLinecap=\"round\"\n                        strokeLinejoin=\"round\"\n                        strokeWidth={2}\n                        d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"\n                      />\n                    </svg>\n                  </div>\n                  <input\n                    type=\"search\"\n                    placeholder=\"Search reviews...\"\n                    className=\"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n                  />\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n      </nav>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAOO,SAAS;;IACd,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAEzC,MAAM,aAAa;QACjB;YAAE,MAAM;YAAQ,MAAM;QAAI;QAC1B;YAAE,MAAM;YAAY,MAAM;QAAqB;QAC/C;YAAE,MAAM;YAAgB,MAAM;QAAyB;QACvD;YAAE,MAAM;YAAmB,MAAM;QAA4B;QAC7D;YAAE,MAAM;YAAgB,MAAM;QAAyB;KACxD;IAED,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,IAAI,YAAY,IAAI,IAAI;YACtB,OAAO,IAAI,CAAC,CAAC,UAAU,EAAE,mBAAmB,YAAY,IAAI,KAAK;YACjE,eAAe;YACf,mBAAmB;QACrB;IACF;IAEA,MAAM,0BAA0B,CAAC;QAC/B,MAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;QAC5B,eAAe;QACf,mBAAmB,MAAM,IAAI,GAAG,MAAM,GAAG;IAC3C;IAEA,MAAM,yBAAyB;QAC7B,eAAe;QACf,mBAAmB;IACrB;IAEA,0CAA0C;IAC1C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR,MAAM;uDAAqB,CAAC;oBAC1B,IACE,UAAU,OAAO,IACjB,CAAC,UAAU,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GACxC;wBACA,mBAAmB;oBACrB;gBACF;;YAEA,SAAS,gBAAgB,CAAC,aAAa;YACvC;oCAAO;oBACL,SAAS,mBAAmB,CAAC,aAAa;gBAC5C;;QACF;2BAAG,EAAE;IAEL,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;;kDACvB,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAK,WAAU;sDAA+B;;;;;;;;;;;kDAEjD,6LAAC;wCAAK,WAAU;kDAAuC;;;;;;;;;;;;;;;;;sCAO3D,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACZ,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC,+JAAA,CAAA,UAAI;wCAEH,MAAM,KAAK,IAAI;wCACf,WAAU;kDAET,KAAK,IAAI;uCAJL,KAAK,IAAI;;;;;;;;;;;;;;;sCAWtB,6LAAC;4BAAI,WAAU;4BAAuC,KAAK;sCACzD,cAAA,6LAAC;gCAAK,UAAU;0CACd,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDACC,WAAU;gDACV,MAAK;gDACL,QAAO;gDACP,SAAQ;0DAER,cAAA,6LAAC;oDACC,eAAc;oDACd,gBAAe;oDACf,aAAa;oDACb,GAAE;;;;;;;;;;;;;;;;sDAIR,6LAAC;4CACC,MAAK;4CACL,aAAY;4CACZ,OAAO;4CACP,UAAU;4CACV,SAAS,IACP,YAAY,IAAI,GAAG,MAAM,GAAG,KAAK,mBAAmB;4CAEtD,WAAU;;;;;;wCAEX,iCACC,6LAAC,0IAAA,CAAA,oBAAiB;4CAChB,OAAO;4CACP,UAAU;;;;;;;;;;;;;;;;;;;;;;sCAQpB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,SAAS,IAAM,cAAc,CAAC;gCAC9B,WAAU;;kDAEV,6LAAC;wCAAK,WAAU;kDAAU;;;;;;oCACzB,CAAC,2BACA,6LAAC;wCACC,WAAU;wCACV,MAAK;wCACL,QAAO;wCACP,SAAQ;kDAER,cAAA,6LAAC;4CACC,eAAc;4CACd,gBAAe;4CACf,aAAa;4CACb,GAAE;;;;;;;;;;6DAIN,6LAAC;wCACC,WAAU;wCACV,MAAK;wCACL,QAAO;wCACP,SAAQ;kDAER,cAAA,6LAAC;4CACC,eAAc;4CACd,gBAAe;4CACf,aAAa;4CACb,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBASb,4BACC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;4BACZ,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC,+JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAU;oCACV,SAAS,IAAM,cAAc;8CAE5B,KAAK,IAAI;mCALL,KAAK,IAAI;;;;;0CAUlB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDACC,WAAU;gDACV,MAAK;gDACL,QAAO;gDACP,SAAQ;0DAER,cAAA,6LAAC;oDACC,eAAc;oDACd,gBAAe;oDACf,aAAa;oDACb,GAAE;;;;;;;;;;;;;;;;sDAIR,6LAAC;4CACC,MAAK;4CACL,aAAY;4CACZ,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU9B;GAhNgB;;QAIC,qIAAA,CAAA,YAAS;;;KAJV", "debugId": null}}]}