{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next/src/experimental/testmode/context.ts"], "sourcesContent": ["import { AsyncLocalStorage } from 'node:async_hooks'\n\nexport interface TestReqInfo {\n  url: string\n  proxyPort: number\n  testData: string\n}\n\nexport interface TestRequestReader<R> {\n  url(req: R): string\n  header(req: R, name: string): string | null\n}\n\nconst testStorage = new AsyncLocalStorage<TestReqInfo>()\n\nfunction extractTestInfoFromRequest<R>(\n  req: R,\n  reader: TestRequestReader<R>\n): TestReqInfo | undefined {\n  const proxyPortHeader = reader.header(req, 'next-test-proxy-port')\n  if (!proxyPortHeader) {\n    return undefined\n  }\n  const url = reader.url(req)\n  const proxyPort = Number(proxyPortHeader)\n  const testData = reader.header(req, 'next-test-data') || ''\n  return { url, proxyPort, testData }\n}\n\nexport function withRequest<R, T>(\n  req: R,\n  reader: TestRequestReader<R>,\n  fn: () => T\n): T {\n  const testReqInfo = extractTestInfoFromRequest(req, reader)\n  if (!testReqInfo) {\n    return fn()\n  }\n  return testStorage.run(testReqInfo, fn)\n}\n\nexport function getTestReqInfo<R>(\n  req?: R,\n  reader?: TestRequestReader<R>\n): TestReqInfo | undefined {\n  const testReqInfo = testStorage.getStore()\n  if (testReqInfo) {\n    return testReqInfo\n  }\n  if (req && reader) {\n    return extractTestInfoFromRequest(req, reader)\n  }\n  return undefined\n}\n"], "names": ["getTestReqInfo", "withRequest", "testStorage", "AsyncLocalStorage", "extractTestInfoFromRequest", "req", "reader", "proxyPortHeader", "header", "undefined", "url", "proxyPort", "Number", "testData", "fn", "testReqInfo", "run", "getStore"], "mappings": ";;;;;;;;;;;;;;;IAyCgBA,cAAc,EAAA;eAAdA;;IAZAC,WAAW,EAAA;eAAXA;;;iCA7BkB;AAalC,MAAMC,cAAc,IAAIC,iBAAAA,iBAAiB;AAEzC,SAASC,2BACPC,GAAM,EACNC,MAA4B;IAE5B,MAAMC,kBAAkBD,OAAOE,MAAM,CAACH,KAAK;IAC3C,IAAI,CAACE,iBAAiB;QACpB,OAAOE;IACT;IACA,MAAMC,MAAMJ,OAAOI,GAAG,CAACL;IACvB,MAAMM,YAAYC,OAAOL;IACzB,MAAMM,WAAWP,OAAOE,MAAM,CAACH,KAAK,qBAAqB;IACzD,OAAO;QAAEK;QAAKC;QAAWE;IAAS;AACpC;AAEO,SAASZ,YACdI,GAAM,EACNC,MAA4B,EAC5BQ,EAAW;IAEX,MAAMC,cAAcX,2BAA2BC,KAAKC;IACpD,IAAI,CAACS,aAAa;QAChB,OAAOD;IACT;IACA,OAAOZ,YAAYc,GAAG,CAACD,aAAaD;AACtC;AAEO,SAASd,eACdK,GAAO,EACPC,MAA6B;IAE7B,MAAMS,cAAcb,YAAYe,QAAQ;IACxC,IAAIF,aAAa;QACf,OAAOA;IACT;IACA,IAAIV,OAAOC,QAAQ;QACjB,OAAOF,2BAA2BC,KAAKC;IACzC;IACA,OAAOG;AACT", "ignoreList": [0]}}, {"offset": {"line": 66, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next/src/experimental/testmode/fetch.ts"], "sourcesContent": ["import type {\n  ProxyFetchRequest,\n  ProxyFetchResponse,\n  ProxyResponse,\n} from './proxy'\nimport { getTestReqInfo, type TestRequestReader } from './context'\n\ntype Fetch = typeof fetch\ntype FetchInputArg = Parameters<Fetch>[0]\ntype FetchInitArg = Parameters<Fetch>[1]\n\nexport const reader: TestRequestReader<Request> = {\n  url(req) {\n    return req.url\n  },\n  header(req, name) {\n    return req.headers.get(name)\n  },\n}\n\nfunction getTestStack(): string {\n  let stack = (new Error().stack ?? '').split('\\n')\n  // Skip the first line and find first non-empty line.\n  for (let i = 1; i < stack.length; i++) {\n    if (stack[i].length > 0) {\n      stack = stack.slice(i)\n      break\n    }\n  }\n  // Filter out franmework lines.\n  stack = stack.filter((f) => !f.includes('/next/dist/'))\n  // At most 5 lines.\n  stack = stack.slice(0, 5)\n  // Cleanup some internal info and trim.\n  stack = stack.map((s) => s.replace('webpack-internal:///(rsc)/', '').trim())\n  return stack.join('    ')\n}\n\nasync function buildProxyRequest(\n  testData: string,\n  request: Request\n): Promise<ProxyFetchRequest> {\n  const {\n    url,\n    method,\n    headers,\n    body,\n    cache,\n    credentials,\n    integrity,\n    mode,\n    redirect,\n    referrer,\n    referrerPolicy,\n  } = request\n  return {\n    testData,\n    api: 'fetch',\n    request: {\n      url,\n      method,\n      headers: [...Array.from(headers), ['next-test-stack', getTestStack()]],\n      body: body\n        ? Buffer.from(await request.arrayBuffer()).toString('base64')\n        : null,\n      cache,\n      credentials,\n      integrity,\n      mode,\n      redirect,\n      referrer,\n      referrerPolicy,\n    },\n  }\n}\n\nfunction buildResponse(proxyResponse: ProxyFetchResponse): Response {\n  const { status, headers, body } = proxyResponse.response\n  return new Response(body ? Buffer.from(body, 'base64') : null, {\n    status,\n    headers: new Headers(headers),\n  })\n}\n\nexport async function handleFetch(\n  originalFetch: Fetch,\n  request: Request\n): Promise<Response> {\n  const testInfo = getTestReqInfo(request, reader)\n  if (!testInfo) {\n    // Passthrough non-test requests.\n    return originalFetch(request)\n  }\n\n  const { testData, proxyPort } = testInfo\n  const proxyRequest = await buildProxyRequest(testData, request)\n\n  const resp = await originalFetch(`http://localhost:${proxyPort}`, {\n    method: 'POST',\n    body: JSON.stringify(proxyRequest),\n    next: {\n      // @ts-ignore\n      internal: true,\n    },\n  })\n  if (!resp.ok) {\n    throw new Error(`Proxy request failed: ${resp.status}`)\n  }\n\n  const proxyResponse = (await resp.json()) as ProxyResponse\n  const { api } = proxyResponse\n  switch (api) {\n    case 'continue':\n      return originalFetch(request)\n    case 'abort':\n    case 'unhandled':\n      throw new Error(\n        `Proxy request aborted [${request.method} ${request.url}]`\n      )\n    default:\n      break\n  }\n  return buildResponse(proxyResponse)\n}\n\nexport function interceptFetch(originalFetch: Fetch) {\n  global.fetch = function testFetch(\n    input: FetchInputArg,\n    init?: FetchInitArg\n  ): Promise<Response> {\n    // Passthrough internal requests.\n    // @ts-ignore\n    if (init?.next?.internal) {\n      return originalFetch(input, init)\n    }\n    return handleFetch(originalFetch, new Request(input, init))\n  }\n  return () => {\n    global.fetch = originalFetch\n  }\n}\n"], "names": ["handleFetch", "interceptFetch", "reader", "url", "req", "header", "name", "headers", "get", "getTestStack", "stack", "Error", "split", "i", "length", "slice", "filter", "f", "includes", "map", "s", "replace", "trim", "join", "buildProxyRequest", "testData", "request", "method", "body", "cache", "credentials", "integrity", "mode", "redirect", "referrer", "referrerPolicy", "api", "Array", "from", "<PERSON><PERSON><PERSON>", "arrayBuffer", "toString", "buildResponse", "proxyResponse", "status", "response", "Response", "Headers", "originalFetch", "testInfo", "getTestReqInfo", "proxyPort", "proxyRequest", "resp", "JSON", "stringify", "next", "internal", "ok", "json", "global", "fetch", "testFetch", "input", "init", "Request"], "mappings": "AA+DUuC;;;;;;;;;;;;;;;;;IAqBYvC,WAAW,EAAA;eAAXA;;IAyCNC,cAAc,EAAA;eAAdA;;IAlHHC,MAAM,EAAA;eAANA;;;yBAN0C;AAMhD,MAAMA,SAAqC;IAChDC,KAAIC,GAAG;QACL,OAAOA,IAAID,GAAG;IAChB;IACAE,QAAOD,GAAG,EAAEE,IAAI;QACd,OAAOF,IAAIG,OAAO,CAACC,GAAG,CAACF;IACzB;AACF;AAEA,SAASG;IACP,IAAIC,QAAS,CAAA,IAAIC,QAAQD,KAAK,IAAI,EAAC,EAAGE,KAAK,CAAC;IAC5C,qDAAqD;IACrD,IAAK,IAAIC,IAAI,GAAGA,IAAIH,MAAMI,MAAM,EAAED,IAAK;QACrC,IAAIH,KAAK,CAACG,EAAE,CAACC,MAAM,GAAG,GAAG;YACvBJ,QAAQA,MAAMK,KAAK,CAACF;YACpB;QACF;IACF;IACA,+BAA+B;IAC/BH,QAAQA,MAAMM,MAAM,CAAC,CAACC,IAAM,CAACA,EAAEC,QAAQ,CAAC;IACxC,mBAAmB;IACnBR,QAAQA,MAAMK,KAAK,CAAC,GAAG;IACvB,uCAAuC;IACvCL,QAAQA,MAAMS,GAAG,CAAC,CAACC,IAAMA,EAAEC,OAAO,CAAC,8BAA8B,IAAIC,IAAI;IACzE,OAAOZ,MAAMa,IAAI,CAAC;AACpB;AAEA,eAAeC,kBACbC,QAAgB,EAChBC,OAAgB;IAEhB,MAAM,EACJvB,GAAG,EACHwB,MAAM,EACNpB,OAAO,EACPqB,IAAI,EACJC,KAAK,EACLC,WAAW,EACXC,SAAS,EACTC,IAAI,EACJC,QAAQ,EACRC,QAAQ,EACRC,cAAc,EACf,GAAGT;IACJ,OAAO;QACLD;QACAW,KAAK;QACLV,SAAS;YACPvB;YACAwB;YACApB,SAAS;mBAAI8B,MAAMC,IAAI,CAAC/B;gBAAU;oBAAC;oBAAmBE;iBAAe;aAAC;YACtEmB,MAAMA,sIACFW,CAAOD,IAAI,CAAC,MAAMZ,QAAQc,WAAW,IAAIC,QAAQ,CAAC,YAClD;YACJZ;YACAC;YACAC;YACAC;YACAC;YACAC;YACAC;QACF;IACF;AACF;AAEA,SAASO,cAAcC,aAAiC;IACtD,MAAM,EAAEC,MAAM,EAAErC,OAAO,EAAEqB,IAAI,EAAE,GAAGe,cAAcE,QAAQ;IACxD,OAAO,IAAIC,SAASlB,OAAOW,+HAAAA,CAAOD,IAAI,CAACV,MAAM,YAAY,MAAM;QAC7DgB;QACArC,SAAS,IAAIwC,QAAQxC;IACvB;AACF;AAEO,eAAeP,YACpBgD,aAAoB,EACpBtB,OAAgB;IAEhB,MAAMuB,WAAWC,CAAAA,GAAAA,SAAAA,cAAc,EAACxB,SAASxB;IACzC,IAAI,CAAC+C,UAAU;QACb,iCAAiC;QACjC,OAAOD,cAActB;IACvB;IAEA,MAAM,EAAED,QAAQ,EAAE0B,SAAS,EAAE,GAAGF;IAChC,MAAMG,eAAe,MAAM5B,kBAAkBC,UAAUC;IAEvD,MAAM2B,OAAO,MAAML,cAAc,CAAC,iBAAiB,EAAEG,WAAW,EAAE;QAChExB,QAAQ;QACRC,MAAM0B,KAAKC,SAAS,CAACH;QACrBI,MAAM;YACJ,aAAa;YACbC,UAAU;QACZ;IACF;IACA,IAAI,CAACJ,KAAKK,EAAE,EAAE;QACZ,MAAM,OAAA,cAAiD,CAAjD,IAAI/C,MAAM,CAAC,sBAAsB,EAAE0C,KAAKT,MAAM,EAAE,GAAhD,qBAAA;mBAAA;wBAAA;0BAAA;QAAgD;IACxD;IAEA,MAAMD,gBAAiB,MAAMU,KAAKM,IAAI;IACtC,MAAM,EAAEvB,GAAG,EAAE,GAAGO;IAChB,OAAQP;QACN,KAAK;YACH,OAAOY,cAActB;QACvB,KAAK;QACL,KAAK;YACH,MAAM,OAAA,cAEL,CAFK,IAAIf,MACR,CAAC,uBAAuB,EAAEe,QAAQC,MAAM,CAAC,CAAC,EAAED,QAAQvB,GAAG,CAAC,CAAC,CAAC,GADtD,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;YACE;IACJ;IACA,OAAOuC,cAAcC;AACvB;AAEO,SAAS1C,eAAe+C,aAAoB;IACjDY,OAAOC,KAAK,GAAG,SAASC,UACtBC,KAAoB,EACpBC,IAAmB;YAIfA;QAFJ,iCAAiC;QACjC,aAAa;QACb,IAAIA,QAAAA,OAAAA,KAAAA,IAAAA,CAAAA,aAAAA,KAAMR,IAAI,KAAA,OAAA,KAAA,IAAVQ,WAAYP,QAAQ,EAAE;YACxB,OAAOT,cAAce,OAAOC;QAC9B;QACA,OAAOhE,YAAYgD,eAAe,IAAIiB,QAAQF,OAAOC;IACvD;IACA,OAAO;QACLJ,OAAOC,KAAK,GAAGb;IACjB;AACF", "ignoreList": [0]}}, {"offset": {"line": 211, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next/src/experimental/testmode/server-edge.ts"], "sourcesContent": ["import { withRequest as withRequestContext } from './context'\nimport { interceptFetch, reader } from './fetch'\n\nexport function interceptTestApis(): () => void {\n  return interceptFetch(global.fetch)\n}\n\nexport function wrapRequestHandler<T>(\n  handler: (req: Request, fn: () => T) => T\n): (req: Request, fn: () => T) => T {\n  return (req, fn) => withRequestContext(req, reader, () => handler(req, fn))\n}\n"], "names": ["interceptTestApis", "wrapRequestHandler", "interceptFetch", "global", "fetch", "handler", "req", "fn", "withRequestContext", "reader"], "mappings": ";;;;;;;;;;;;;;;IAGgBA,iBAAiB,EAAA;eAAjBA;;IAIAC,kBAAkB,EAAA;eAAlBA;;;yBAPkC;uBACX;AAEhC,SAASD;IACd,OAAOE,CAAAA,GAAAA,OAAAA,cAAc,EAACC,OAAOC,KAAK;AACpC;AAEO,SAASH,mBACdI,OAAyC;IAEzC,OAAO,CAACC,KAAKC,KAAOC,CAAAA,GAAAA,SAAAA,WAAkB,EAACF,KAAKG,OAAAA,MAAM,EAAE,IAAMJ,QAAQC,KAAKC;AACzE", "ignoreList": [0]}}, {"offset": {"line": 246, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next/dist/shared/lib/app-router-context.shared-runtime.js/proxy.cjs"], "sourcesContent": ["const { createClientModuleProxy } = require(\"react-server-dom-turbopack/server.edge\");\n\n__turbopack_context__.n(createClientModuleProxy(\"[project]/node_modules/next/dist/shared/lib/app-router-context.shared-runtime.js <module evaluation>\"));\n"], "names": [], "mappings": "AAAA,MAAM,EAAE,uBAAuB,EAAE;AAEjC,sBAAsB,CAAC,CAAC,wBAAwB", "ignoreList": [0]}}, {"offset": {"line": 253, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next/dist/shared/lib/app-router-context.shared-runtime.js/proxy.cjs"], "sourcesContent": ["const { createClientModuleProxy } = require(\"react-server-dom-turbopack/server.edge\");\n\n__turbopack_context__.n(createClientModuleProxy(\"[project]/node_modules/next/dist/shared/lib/app-router-context.shared-runtime.js\"));\n"], "names": [], "mappings": "AAAA,MAAM,EAAE,uBAAuB,EAAE;AAEjC,sBAAsB,CAAC,CAAC,wBAAwB", "ignoreList": [0]}}, {"offset": {"line": 261, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next/src/shared/lib/app-router-context.shared-runtime.ts"], "sourcesContent": ["'use client'\n\nimport type { FetchServerResponseResult } from '../../client/components/router-reducer/fetch-server-response'\nimport type {\n  FocusAndScrollRef,\n  PrefetchKind,\n} from '../../client/components/router-reducer/router-reducer-types'\nimport type {\n  FlightRouterState,\n  FlightSegmentPath,\n} from '../../server/app-render/types'\nimport React from 'react'\n\nexport type ChildSegmentMap = Map<string, CacheNode>\n\n/**\n * Cache node used in app-router / layout-router.\n */\nexport type CacheNode = ReadyCacheNode | LazyCacheNode\n\nexport type LoadingModuleData =\n  | [React.JSX.Element, React.ReactNode, React.ReactNode]\n  | null\n\n/** viewport metadata node */\nexport type HeadData = React.ReactNode\n\nexport type LazyCacheNode = {\n  /**\n   * When rsc is null, this is a lazily-initialized cache node.\n   *\n   * If the app attempts to render it, it triggers a lazy data fetch,\n   * postpones the render, and schedules an update to a new tree.\n   *\n   * TODO: This mechanism should not be used when PPR is enabled, though it\n   * currently is in some cases until we've implemented partial\n   * segment fetching.\n   */\n  rsc: null\n\n  /**\n   * A prefetched version of the segment data. See explanation in corresponding\n   * field of ReadyCacheNode (below).\n   *\n   * Since LazyCacheNode mostly only exists in the non-PPR implementation, this\n   * will usually be null, but it could have been cloned from a previous\n   * CacheNode that was created by the PPR implementation. Eventually we want\n   * to migrate everything away from LazyCacheNode entirely.\n   */\n  prefetchRsc: React.ReactNode\n\n  /**\n   * A pending response for the lazy data fetch. If this is not present\n   * during render, it is lazily created.\n   */\n  lazyData: Promise<FetchServerResponseResult> | null\n\n  prefetchHead: HeadData | null\n\n  head: HeadData\n\n  loading: LoadingModuleData | Promise<LoadingModuleData>\n\n  /**\n   * Child parallel routes.\n   */\n  parallelRoutes: Map<string, ChildSegmentMap>\n\n  /**\n   * The timestamp of the navigation that last updated the CacheNode's data. If\n   * a CacheNode is reused from a previous navigation, this value is not\n   * updated. Used to track the staleness of the data.\n   */\n  navigatedAt: number\n}\n\nexport type ReadyCacheNode = {\n  /**\n   * When rsc is not null, it represents the RSC data for the\n   * corresponding segment.\n   *\n   * `null` is a valid React Node but because segment data is always a\n   * <LayoutRouter> component, we can use `null` to represent empty.\n   *\n   * TODO: For additional type safety, update this type to\n   * Exclude<React.ReactNode, null>. Need to update createEmptyCacheNode to\n   * accept rsc as an argument, or just inline the callers.\n   */\n  rsc: React.ReactNode\n\n  /**\n   * Represents a static version of the segment that can be shown immediately,\n   * and may or may not contain dynamic holes. It's prefetched before a\n   * navigation occurs.\n   *\n   * During rendering, we will choose whether to render `rsc` or `prefetchRsc`\n   * with `useDeferredValue`. As with the `rsc` field, a value of `null` means\n   * no value was provided. In this case, the LayoutRouter will go straight to\n   * rendering the `rsc` value; if that one is also missing, it will suspend and\n   * trigger a lazy fetch.\n   */\n  prefetchRsc: React.ReactNode\n\n  /**\n   * There should never be a lazy data request in this case.\n   */\n  lazyData: null\n  prefetchHead: HeadData | null\n\n  head: HeadData\n\n  loading: LoadingModuleData | Promise<LoadingModuleData>\n\n  parallelRoutes: Map<string, ChildSegmentMap>\n\n  navigatedAt: number\n}\n\nexport interface NavigateOptions {\n  scroll?: boolean\n}\n\nexport interface PrefetchOptions {\n  kind: PrefetchKind\n}\n\nexport interface AppRouterInstance {\n  /**\n   * Navigate to the previous history entry.\n   */\n  back(): void\n  /**\n   * Navigate to the next history entry.\n   */\n  forward(): void\n  /**\n   * Refresh the current page.\n   */\n  refresh(): void\n  /**\n   * Refresh the current page. Use in development only.\n   * @internal\n   */\n  hmrRefresh(): void\n  /**\n   * Navigate to the provided href.\n   * Pushes a new history entry.\n   */\n  push(href: string, options?: NavigateOptions): void\n  /**\n   * Navigate to the provided href.\n   * Replaces the current history entry.\n   */\n  replace(href: string, options?: NavigateOptions): void\n  /**\n   * Prefetch the provided href.\n   */\n  prefetch(href: string, options?: PrefetchOptions): void\n}\n\nexport const AppRouterContext = React.createContext<AppRouterInstance | null>(\n  null\n)\nexport const LayoutRouterContext = React.createContext<{\n  parentTree: FlightRouterState\n  parentCacheNode: CacheNode\n  parentSegmentPath: FlightSegmentPath | null\n  url: string\n} | null>(null)\n\nexport const GlobalLayoutRouterContext = React.createContext<{\n  tree: FlightRouterState\n  focusAndScrollRef: FocusAndScrollRef\n  nextUrl: string | null\n}>(null as any)\n\nexport const TemplateContext = React.createContext<React.ReactNode>(null as any)\n\nif (process.env.NODE_ENV !== 'production') {\n  AppRouterContext.displayName = 'AppRouterContext'\n  LayoutRouterContext.displayName = 'LayoutRouterContext'\n  GlobalLayoutRouterContext.displayName = 'GlobalLayoutRouterContext'\n  TemplateContext.displayName = 'TemplateContext'\n}\n\nexport const MissingSlotContext = React.createContext<Set<string>>(new Set())\n"], "names": ["AppRouterContext", "GlobalLayoutRouterContext", "LayoutRouterContext", "MissingSlotContext", "TemplateContext", "React", "createContext", "process", "env", "NODE_ENV", "displayName", "Set"], "mappings": "", "ignoreList": [0]}}]}