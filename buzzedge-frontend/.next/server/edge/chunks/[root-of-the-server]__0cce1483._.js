(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push(["chunks/[root-of-the-server]__0cce1483._.js", {

"[externals]/node:buffer [external] (node:buffer, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:buffer", () => require("node:buffer"));

module.exports = mod;
}}),
"[externals]/node:async_hooks [external] (node:async_hooks, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:async_hooks", () => require("node:async_hooks"));

module.exports = mod;
}}),
"[project]/src/app/icon.tsx [app-edge-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "contentType": (()=>contentType),
    "default": (()=>Icon),
    "runtime": (()=>runtime),
    "size": (()=>size)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$react$2d$server$2e$js__$5b$app$2d$edge$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.react-server.js [app-edge-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$api$2f$og$2e$js__$5b$app$2d$edge$2d$rsc$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/next/dist/esm/api/og.js [app-edge-rsc] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$og$2f$image$2d$response$2e$js__$5b$app$2d$edge$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/esm/server/og/image-response.js [app-edge-rsc] (ecmascript)");
;
;
const runtime = 'edge';
const size = {
    width: 32,
    height: 32
};
const contentType = 'image/png';
function Icon() {
    return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$og$2f$image$2d$response$2e$js__$5b$app$2d$edge$2d$rsc$5d$__$28$ecmascript$29$__["ImageResponse"](/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$react$2d$server$2e$js__$5b$app$2d$edge$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        style: {
            fontSize: 24,
            background: 'linear-gradient(90deg, #3B82F6 0%, #8B5CF6 100%)',
            width: '100%',
            height: '100%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            color: 'white',
            fontWeight: 'bold',
            borderRadius: '6px'
        },
        children: "B"
    }, void 0, false, {
        fileName: "[project]/src/app/icon.tsx",
        lineNumber: 17,
        columnNumber: 7
    }, this), {
        ...size
    });
}
}}),
"[project]/src/app/icon--route-entry.js [app-edge-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "GET": (()=>GET)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$api$2f$server$2e$js__$5b$app$2d$edge$2d$rsc$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/next/dist/esm/api/server.js [app-edge-rsc] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$app$2d$edge$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/esm/server/web/spec-extension/response.js [app-edge-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$icon$2e$tsx__$5b$app$2d$edge$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/app/icon.tsx [app-edge-rsc] (ecmascript)");
;
;
const imageModule = {
    ...__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$icon$2e$tsx__$5b$app$2d$edge$2d$rsc$5d$__$28$ecmascript$29$__
};
const handler = imageModule.default;
const generateImageMetadata = imageModule.generateImageMetadata;
if (typeof handler !== 'function') {
    throw new Error('Default export is missing in "./icon.tsx"');
}
async function GET(_, ctx) {
    const params = await ctx.params;
    const { __metadata_id__, ...rest } = params || {};
    const restParams = params ? rest : undefined;
    const targetId = __metadata_id__;
    let id = undefined;
    if (generateImageMetadata) {
        const imageMetadata = await generateImageMetadata({
            params: restParams
        });
        id = imageMetadata.find((item)=>{
            if ("TURBOPACK compile-time truthy", 1) {
                if (item?.id == null) {
                    throw new Error('id property is required for every item returned from generateImageMetadata');
                }
            }
            return item.id.toString() === targetId;
        })?.id;
        if (id == null) {
            return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$app$2d$edge$2d$rsc$5d$__$28$ecmascript$29$__["NextResponse"]('Not Found', {
                status: 404
            });
        }
    }
    return handler({
        params: restParams,
        id
    });
}
}}),
}]);

//# sourceMappingURL=%5Broot-of-the-server%5D__0cce1483._.js.map