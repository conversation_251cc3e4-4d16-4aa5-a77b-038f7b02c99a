module.exports = {

"[project]/.next-internal/server/app/sitemap.xml/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[project]/src/lib/api.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// API Configuration
__turbopack_context__.s({
    "ApiError": (()=>ApiError),
    "apiClient": (()=>apiClient),
    "checkApiHealth": (()=>checkApiHealth)
});
const API_BASE_URL = ("TURBOPACK compile-time value", "http://localhost:5001") || "http://localhost:5001";
class ApiError extends Error {
    status;
    response;
    constructor(message, status, response){
        super(message), this.status = status, this.response = response;
        this.name = "ApiError";
    }
}
class ApiClient {
    baseURL;
    cache;
    constructor(baseURL){
        this.baseURL = baseURL;
        this.cache = new Map();
    }
    getCacheKey(endpoint, params) {
        const url = new URL(endpoint, this.baseURL);
        if (params) {
            Object.entries(params).forEach(([key, value])=>{
                url.searchParams.append(key, value);
            });
        }
        return url.pathname + url.search;
    }
    isValidCache(cacheEntry) {
        return Date.now() - cacheEntry.timestamp < cacheEntry.ttl * 1000;
    }
    setCache(key, data, ttl = 300) {
        this.cache.set(key, {
            data,
            timestamp: Date.now(),
            ttl
        });
    }
    getCache(key) {
        const entry = this.cache.get(key);
        if (entry && this.isValidCache(entry)) {
            return entry.data;
        }
        if (entry) {
            this.cache.delete(key); // Remove expired cache
        }
        return null;
    }
    async request(endpoint, options = {}) {
        const url = `${this.baseURL}${endpoint}`;
        const config = {
            headers: {
                "Content-Type": "application/json",
                ...options.headers
            },
            ...options
        };
        try {
            const response = await fetch(url, config);
            if (!response.ok) {
                const errorData = await response.json().catch(()=>({}));
                throw new ApiError(errorData.error || `HTTP ${response.status}: ${response.statusText}`, response.status, errorData);
            }
            const data = await response.json();
            return data;
        } catch (error) {
            if (error instanceof ApiError) {
                throw error;
            }
            // Network or other errors
            throw new ApiError(error instanceof Error ? error.message : "Network error occurred", 0);
        }
    }
    async get(endpoint, params, options = {}) {
        const { cache = true, ttl = 300 } = options;
        const cacheKey = this.getCacheKey(endpoint, params);
        // Check cache first
        if (cache) {
            const cachedData = this.getCache(cacheKey);
            if (cachedData) {
                return cachedData;
            }
        }
        const url = new URL(endpoint, this.baseURL);
        if (params) {
            Object.entries(params).forEach(([key, value])=>{
                url.searchParams.append(key, value);
            });
        }
        const data = await this.request(url.pathname + url.search);
        // Cache the response
        if (cache) {
            this.setCache(cacheKey, data, ttl);
        }
        return data;
    }
    async post(endpoint, data) {
        return this.request(endpoint, {
            method: "POST",
            body: data ? JSON.stringify(data) : undefined
        });
    }
    async put(endpoint, data) {
        return this.request(endpoint, {
            method: "PUT",
            body: data ? JSON.stringify(data) : undefined
        });
    }
    async delete(endpoint) {
        return this.request(endpoint, {
            method: "DELETE"
        });
    }
    // Cache management methods
    clearCache() {
        this.cache.clear();
    }
    clearCacheByPattern(pattern) {
        const regex = new RegExp(pattern);
        for (const [key] of this.cache){
            if (regex.test(key)) {
                this.cache.delete(key);
            }
        }
    }
    getCacheStats() {
        return {
            size: this.cache.size,
            keys: Array.from(this.cache.keys())
        };
    }
}
const apiClient = new ApiClient(API_BASE_URL);
const checkApiHealth = async ()=>{
    try {
        await apiClient.get("/health");
        return true;
    } catch (error) {
        console.error("API health check failed:", error);
        return false;
    }
};
}}),
"[project]/src/services/blogService.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "BlogService": (()=>BlogService),
    "blogUtils": (()=>blogUtils)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api.ts [app-rsc] (ecmascript)");
;
class BlogService {
    /**
   * Get all published blog posts with pagination
   */ static async getAllPosts(page = 1, limit = 10) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["apiClient"].get("/api/blogs", {
            page: page.toString(),
            limit: limit.toString()
        }, {
            cache: true,
            ttl: 300
        } // 5 minutes cache
        );
    }
    /**
   * Get a single blog post by slug
   */ static async getPostBySlug(slug) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["apiClient"].get(`/api/blogs/${slug}`, undefined, {
            cache: true,
            ttl: 600
        } // 10 minutes cache
        );
    }
    /**
   * Search blog posts
   */ static async searchPosts(query, page = 1, limit = 10) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["apiClient"].get("/api/blogs/search", {
            q: query,
            page: page.toString(),
            limit: limit.toString()
        }, {
            cache: true,
            ttl: 60
        } // 1 minute cache for search
        );
    }
    /**
   * Get posts by category
   */ static async getPostsByCategory(category, page = 1, limit = 10) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["apiClient"].get(`/api/blogs/category/${category}`, {
            page: page.toString(),
            limit: limit.toString()
        }, {
            cache: true,
            ttl: 300
        } // 5 minutes cache
        );
    }
    /**
   * Increment post view count
   */ static async incrementPostViews(slug) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["apiClient"].post(`/api/blogs/${slug}/view`);
    }
    /**
   * Get featured posts (most viewed)
   */ static async getFeaturedPosts(limit = 5) {
        const response = await this.getAllPosts(1, limit);
        return response.data.posts;
    }
    /**
   * Get recent posts
   */ static async getRecentPosts(limit = 5) {
        const response = await this.getAllPosts(1, limit);
        return response.data.posts;
    }
}
const blogUtils = {
    /**
   * Format date for display
   */ formatDate: (dateString)=>{
        const date = new Date(dateString);
        return date.toLocaleDateString("en-US", {
            year: "numeric",
            month: "long",
            day: "numeric"
        });
    },
    /**
   * Calculate reading time based on content length
   */ calculateReadingTime: (content)=>{
        const wordsPerMinute = 200;
        const wordCount = content.split(/\s+/).length;
        return Math.ceil(wordCount / wordsPerMinute);
    },
    /**
   * Get category color class
   */ getCategoryColor: (category)=>{
        switch(category.toLowerCase()){
            case "ai tools":
                return "bg-blue-100 text-blue-800";
            case "productivity":
                return "bg-green-100 text-green-800";
            case "developer tools":
                return "bg-purple-100 text-purple-800";
            case "design tools":
                return "bg-pink-100 text-pink-800";
            default:
                return "bg-gray-100 text-gray-800";
        }
    },
    /**
   * Generate SEO-friendly URL slug
   */ generateSlug: (title)=>{
        return title.toLowerCase().replace(/[^a-z0-9]+/g, "-").replace(/(^-|-$)/g, "");
    },
    /**
   * Truncate text to specified length
   */ truncateText: (text, maxLength)=>{
        if (text.length <= maxLength) return text;
        return text.substring(0, maxLength).replace(/\s+\S*$/, "") + "...";
    }
};
}}),
"[project]/src/app/sitemap.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>sitemap)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$blogService$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/blogService.ts [app-rsc] (ecmascript)");
;
async function sitemap() {
    const baseUrl = ("TURBOPACK compile-time value", "http://localhost:3002") || 'http://localhost:3002';
    // Static pages
    const staticPages = [
        {
            url: baseUrl,
            lastModified: new Date(),
            changeFrequency: 'daily',
            priority: 1
        },
        {
            url: `${baseUrl}/search`,
            lastModified: new Date(),
            changeFrequency: 'weekly',
            priority: 0.5
        }
    ];
    // Category pages
    const categories = [
        'ai-tools',
        'productivity',
        'developer-tools',
        'design-tools'
    ];
    const categoryPages = categories.map((category)=>({
            url: `${baseUrl}/category/${category}`,
            lastModified: new Date(),
            changeFrequency: 'weekly',
            priority: 0.8
        }));
    // Blog posts
    let blogPages = [];
    try {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$blogService$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["BlogService"].getAllPosts(1, 1000); // Get all posts
        blogPages = response.data.posts.map((post)=>({
                url: `${baseUrl}/blog/${post.slug}`,
                lastModified: new Date(post.publishedAt || post.createdAt),
                changeFrequency: 'monthly',
                priority: 0.9
            }));
    } catch (error) {
        console.error('Failed to fetch posts for sitemap:', error);
    }
    return [
        ...staticPages,
        ...categoryPages,
        ...blogPages
    ];
}
}}),
"[project]/src/app/sitemap--route-entry.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "GET": (()=>GET)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$sitemap$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/app/sitemap.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$metadata$2f$resolve$2d$route$2d$data$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/metadata/resolve-route-data.js [app-rsc] (ecmascript)");
;
;
;
const sitemapModule = {
    ...__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$sitemap$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__
};
const handler = sitemapModule.default;
const generateSitemaps = sitemapModule.generateSitemaps;
const contentType = "application/xml";
const cacheControl = "public, max-age=0, must-revalidate";
const fileType = "sitemap";
if (typeof handler !== 'function') {
    throw new Error('Default export is missing in "./sitemap.ts"');
}
async function GET(_, ctx) {
    const { __metadata_id__: id, ...params } = await ctx.params || {};
    const hasXmlExtension = id ? id.endsWith('.xml') : false;
    if (id && !hasXmlExtension) {
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["NextResponse"]('Not Found', {
            status: 404
        });
    }
    if (("TURBOPACK compile-time value", "development") !== 'production' && sitemapModule.generateSitemaps) {
        const sitemaps = await sitemapModule.generateSitemaps();
        for (const item of sitemaps){
            if (item?.id == null) {
                throw new Error('id property is required for every item returned from generateSitemaps');
            }
        }
    }
    const targetId = id && hasXmlExtension ? id.slice(0, -4) : undefined;
    const data = await handler({
        id: targetId
    });
    const content = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$metadata$2f$resolve$2d$route$2d$data$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["resolveRouteData"])(data, fileType);
    return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["NextResponse"](content, {
        headers: {
            'Content-Type': contentType,
            'Cache-Control': cacheControl
        }
    });
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__8a6e73e1._.js.map