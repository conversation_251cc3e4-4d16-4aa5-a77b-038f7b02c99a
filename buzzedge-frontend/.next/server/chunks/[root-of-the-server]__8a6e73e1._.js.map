{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Development/BuzzEdge/buzzedge-frontend/src/lib/api.ts"], "sourcesContent": ["// API Configuration\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || \"http://localhost:5001\";\n\nexport class ApiError extends Error {\n  constructor(message: string, public status: number, public response?: any) {\n    super(message);\n    this.name = \"ApiError\";\n  }\n}\n\nexport interface ApiResponse<T> {\n  success: boolean;\n  data?: T;\n  error?: string;\n  message?: string;\n}\n\nclass ApiClient {\n  private baseURL: string;\n  private cache: Map<string, { data: any; timestamp: number; ttl: number }>;\n\n  constructor(baseURL: string) {\n    this.baseURL = baseURL;\n    this.cache = new Map();\n  }\n\n  private getCacheKey(\n    endpoint: string,\n    params?: Record<string, string>\n  ): string {\n    const url = new URL(endpoint, this.baseURL);\n    if (params) {\n      Object.entries(params).forEach(([key, value]) => {\n        url.searchParams.append(key, value);\n      });\n    }\n    return url.pathname + url.search;\n  }\n\n  private isValidCache(cacheEntry: {\n    timestamp: number;\n    ttl: number;\n  }): boolean {\n    return Date.now() - cacheEntry.timestamp < cacheEntry.ttl * 1000;\n  }\n\n  private setCache(key: string, data: any, ttl: number = 300): void {\n    this.cache.set(key, {\n      data,\n      timestamp: Date.now(),\n      ttl,\n    });\n  }\n\n  private getCache(key: string): any | null {\n    const entry = this.cache.get(key);\n    if (entry && this.isValidCache(entry)) {\n      return entry.data;\n    }\n    if (entry) {\n      this.cache.delete(key); // Remove expired cache\n    }\n    return null;\n  }\n\n  private async request<T>(\n    endpoint: string,\n    options: RequestInit = {}\n  ): Promise<T> {\n    const url = `${this.baseURL}${endpoint}`;\n\n    const config: RequestInit = {\n      headers: {\n        \"Content-Type\": \"application/json\",\n        ...options.headers,\n      },\n      ...options,\n    };\n\n    try {\n      const response = await fetch(url, config);\n\n      if (!response.ok) {\n        const errorData = await response.json().catch(() => ({}));\n        throw new ApiError(\n          errorData.error || `HTTP ${response.status}: ${response.statusText}`,\n          response.status,\n          errorData\n        );\n      }\n\n      const data = await response.json();\n      return data;\n    } catch (error) {\n      if (error instanceof ApiError) {\n        throw error;\n      }\n\n      // Network or other errors\n      throw new ApiError(\n        error instanceof Error ? error.message : \"Network error occurred\",\n        0\n      );\n    }\n  }\n\n  async get<T>(\n    endpoint: string,\n    params?: Record<string, string>,\n    options: { cache?: boolean; ttl?: number } = {}\n  ): Promise<T> {\n    const { cache = true, ttl = 300 } = options;\n    const cacheKey = this.getCacheKey(endpoint, params);\n\n    // Check cache first\n    if (cache) {\n      const cachedData = this.getCache(cacheKey);\n      if (cachedData) {\n        return cachedData;\n      }\n    }\n\n    const url = new URL(endpoint, this.baseURL);\n    if (params) {\n      Object.entries(params).forEach(([key, value]) => {\n        url.searchParams.append(key, value);\n      });\n    }\n\n    const data = await this.request<T>(url.pathname + url.search);\n\n    // Cache the response\n    if (cache) {\n      this.setCache(cacheKey, data, ttl);\n    }\n\n    return data;\n  }\n\n  async post<T>(endpoint: string, data?: any): Promise<T> {\n    return this.request<T>(endpoint, {\n      method: \"POST\",\n      body: data ? JSON.stringify(data) : undefined,\n    });\n  }\n\n  async put<T>(endpoint: string, data?: any): Promise<T> {\n    return this.request<T>(endpoint, {\n      method: \"PUT\",\n      body: data ? JSON.stringify(data) : undefined,\n    });\n  }\n\n  async delete<T>(endpoint: string): Promise<T> {\n    return this.request<T>(endpoint, {\n      method: \"DELETE\",\n    });\n  }\n\n  // Cache management methods\n  clearCache(): void {\n    this.cache.clear();\n  }\n\n  clearCacheByPattern(pattern: string): void {\n    const regex = new RegExp(pattern);\n    for (const [key] of this.cache) {\n      if (regex.test(key)) {\n        this.cache.delete(key);\n      }\n    }\n  }\n\n  getCacheStats(): { size: number; keys: string[] } {\n    return {\n      size: this.cache.size,\n      keys: Array.from(this.cache.keys()),\n    };\n  }\n}\n\n// Create and export the API client instance\nexport const apiClient = new ApiClient(API_BASE_URL);\n\n// Health check function\nexport const checkApiHealth = async (): Promise<boolean> => {\n  try {\n    await apiClient.get(\"/health\");\n    return true;\n  } catch (error) {\n    console.error(\"API health check failed:\", error);\n    return false;\n  }\n};\n"], "names": [], "mappings": "AAAA,oBAAoB;;;;;;AACpB,MAAM,eAAe,6DAAmC;AAEjD,MAAM,iBAAiB;;;IAC5B,YAAY,OAAe,EAAE,AAAO,MAAc,EAAE,AAAO,QAAc,CAAE;QACzE,KAAK,CAAC,eAD4B,SAAA,aAAuB,WAAA;QAEzD,IAAI,CAAC,IAAI,GAAG;IACd;AACF;AASA,MAAM;IACI,QAAgB;IAChB,MAAkE;IAE1E,YAAY,OAAe,CAAE;QAC3B,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,KAAK,GAAG,IAAI;IACnB;IAEQ,YACN,QAAgB,EAChB,MAA+B,EACvB;QACR,MAAM,MAAM,IAAI,IAAI,UAAU,IAAI,CAAC,OAAO;QAC1C,IAAI,QAAQ;YACV,OAAO,OAAO,CAAC,QAAQ,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;gBAC1C,IAAI,YAAY,CAAC,MAAM,CAAC,KAAK;YAC/B;QACF;QACA,OAAO,IAAI,QAAQ,GAAG,IAAI,MAAM;IAClC;IAEQ,aAAa,UAGpB,EAAW;QACV,OAAO,KAAK,GAAG,KAAK,WAAW,SAAS,GAAG,WAAW,GAAG,GAAG;IAC9D;IAEQ,SAAS,GAAW,EAAE,IAAS,EAAE,MAAc,GAAG,EAAQ;QAChE,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK;YAClB;YACA,WAAW,KAAK,GAAG;YACnB;QACF;IACF;IAEQ,SAAS,GAAW,EAAc;QACxC,MAAM,QAAQ,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;QAC7B,IAAI,SAAS,IAAI,CAAC,YAAY,CAAC,QAAQ;YACrC,OAAO,MAAM,IAAI;QACnB;QACA,IAAI,OAAO;YACT,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,uBAAuB;QACjD;QACA,OAAO;IACT;IAEA,MAAc,QACZ,QAAgB,EAChB,UAAuB,CAAC,CAAC,EACb;QACZ,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,GAAG,UAAU;QAExC,MAAM,SAAsB;YAC1B,SAAS;gBACP,gBAAgB;gBAChB,GAAG,QAAQ,OAAO;YACpB;YACA,GAAG,OAAO;QACZ;QAEA,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,KAAK;YAElC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC,CAAC,CAAC;gBACvD,MAAM,IAAI,SACR,UAAU,KAAK,IAAI,CAAC,KAAK,EAAE,SAAS,MAAM,CAAC,EAAE,EAAE,SAAS,UAAU,EAAE,EACpE,SAAS,MAAM,EACf;YAEJ;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,OAAO;QACT,EAAE,OAAO,OAAO;YACd,IAAI,iBAAiB,UAAU;gBAC7B,MAAM;YACR;YAEA,0BAA0B;YAC1B,MAAM,IAAI,SACR,iBAAiB,QAAQ,MAAM,OAAO,GAAG,0BACzC;QAEJ;IACF;IAEA,MAAM,IACJ,QAAgB,EAChB,MAA+B,EAC/B,UAA6C,CAAC,CAAC,EACnC;QACZ,MAAM,EAAE,QAAQ,IAAI,EAAE,MAAM,GAAG,EAAE,GAAG;QACpC,MAAM,WAAW,IAAI,CAAC,WAAW,CAAC,UAAU;QAE5C,oBAAoB;QACpB,IAAI,OAAO;YACT,MAAM,aAAa,IAAI,CAAC,QAAQ,CAAC;YACjC,IAAI,YAAY;gBACd,OAAO;YACT;QACF;QAEA,MAAM,MAAM,IAAI,IAAI,UAAU,IAAI,CAAC,OAAO;QAC1C,IAAI,QAAQ;YACV,OAAO,OAAO,CAAC,QAAQ,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;gBAC1C,IAAI,YAAY,CAAC,MAAM,CAAC,KAAK;YAC/B;QACF;QAEA,MAAM,OAAO,MAAM,IAAI,CAAC,OAAO,CAAI,IAAI,QAAQ,GAAG,IAAI,MAAM;QAE5D,qBAAqB;QACrB,IAAI,OAAO;YACT,IAAI,CAAC,QAAQ,CAAC,UAAU,MAAM;QAChC;QAEA,OAAO;IACT;IAEA,MAAM,KAAQ,QAAgB,EAAE,IAAU,EAAc;QACtD,OAAO,IAAI,CAAC,OAAO,CAAI,UAAU;YAC/B,QAAQ;YACR,MAAM,OAAO,KAAK,SAAS,CAAC,QAAQ;QACtC;IACF;IAEA,MAAM,IAAO,QAAgB,EAAE,IAAU,EAAc;QACrD,OAAO,IAAI,CAAC,OAAO,CAAI,UAAU;YAC/B,QAAQ;YACR,MAAM,OAAO,KAAK,SAAS,CAAC,QAAQ;QACtC;IACF;IAEA,MAAM,OAAU,QAAgB,EAAc;QAC5C,OAAO,IAAI,CAAC,OAAO,CAAI,UAAU;YAC/B,QAAQ;QACV;IACF;IAEA,2BAA2B;IAC3B,aAAmB;QACjB,IAAI,CAAC,KAAK,CAAC,KAAK;IAClB;IAEA,oBAAoB,OAAe,EAAQ;QACzC,MAAM,QAAQ,IAAI,OAAO;QACzB,KAAK,MAAM,CAAC,IAAI,IAAI,IAAI,CAAC,KAAK,CAAE;YAC9B,IAAI,MAAM,IAAI,CAAC,MAAM;gBACnB,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;YACpB;QACF;IACF;IAEA,gBAAkD;QAChD,OAAO;YACL,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI;YACrB,MAAM,MAAM,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI;QAClC;IACF;AACF;AAGO,MAAM,YAAY,IAAI,UAAU;AAGhC,MAAM,iBAAiB;IAC5B,IAAI;QACF,MAAM,UAAU,GAAG,CAAC;QACpB,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 211, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Development/BuzzEdge/buzzedge-frontend/src/services/blogService.ts"], "sourcesContent": ["import { apiClient } from \"@/lib/api\";\nimport {\n  BlogListResponse,\n  BlogPostResponse,\n  SearchResponse,\n  CategoryResponse,\n  BlogPostSummary,\n} from \"@/types/blog\";\n\nexport class BlogService {\n  /**\n   * Get all published blog posts with pagination\n   */\n  static async getAllPosts(\n    page: number = 1,\n    limit: number = 10\n  ): Promise<BlogListResponse> {\n    return apiClient.get<BlogListResponse>(\n      \"/api/blogs\",\n      {\n        page: page.toString(),\n        limit: limit.toString(),\n      },\n      { cache: true, ttl: 300 } // 5 minutes cache\n    );\n  }\n\n  /**\n   * Get a single blog post by slug\n   */\n  static async getPostBySlug(slug: string): Promise<BlogPostResponse> {\n    return apiClient.get<BlogPostResponse>(\n      `/api/blogs/${slug}`,\n      undefined,\n      { cache: true, ttl: 600 } // 10 minutes cache\n    );\n  }\n\n  /**\n   * Search blog posts\n   */\n  static async searchPosts(\n    query: string,\n    page: number = 1,\n    limit: number = 10\n  ): Promise<SearchResponse> {\n    return apiClient.get<SearchResponse>(\n      \"/api/blogs/search\",\n      {\n        q: query,\n        page: page.toString(),\n        limit: limit.toString(),\n      },\n      { cache: true, ttl: 60 } // 1 minute cache for search\n    );\n  }\n\n  /**\n   * Get posts by category\n   */\n  static async getPostsByCategory(\n    category: string,\n    page: number = 1,\n    limit: number = 10\n  ): Promise<CategoryResponse> {\n    return apiClient.get<CategoryResponse>(\n      `/api/blogs/category/${category}`,\n      {\n        page: page.toString(),\n        limit: limit.toString(),\n      },\n      { cache: true, ttl: 300 } // 5 minutes cache\n    );\n  }\n\n  /**\n   * Increment post view count\n   */\n  static async incrementPostViews(slug: string): Promise<{ views: number }> {\n    return apiClient.post(`/api/blogs/${slug}/view`);\n  }\n\n  /**\n   * Get featured posts (most viewed)\n   */\n  static async getFeaturedPosts(limit: number = 5): Promise<BlogPostSummary[]> {\n    const response = await this.getAllPosts(1, limit);\n    return response.data.posts;\n  }\n\n  /**\n   * Get recent posts\n   */\n  static async getRecentPosts(limit: number = 5): Promise<BlogPostSummary[]> {\n    const response = await this.getAllPosts(1, limit);\n    return response.data.posts;\n  }\n}\n\n// Utility functions for client-side data processing\nexport const blogUtils = {\n  /**\n   * Format date for display\n   */\n  formatDate: (dateString: string): string => {\n    const date = new Date(dateString);\n    return date.toLocaleDateString(\"en-US\", {\n      year: \"numeric\",\n      month: \"long\",\n      day: \"numeric\",\n    });\n  },\n\n  /**\n   * Calculate reading time based on content length\n   */\n  calculateReadingTime: (content: string): number => {\n    const wordsPerMinute = 200;\n    const wordCount = content.split(/\\s+/).length;\n    return Math.ceil(wordCount / wordsPerMinute);\n  },\n\n  /**\n   * Get category color class\n   */\n  getCategoryColor: (category: string): string => {\n    switch (category.toLowerCase()) {\n      case \"ai tools\":\n        return \"bg-blue-100 text-blue-800\";\n      case \"productivity\":\n        return \"bg-green-100 text-green-800\";\n      case \"developer tools\":\n        return \"bg-purple-100 text-purple-800\";\n      case \"design tools\":\n        return \"bg-pink-100 text-pink-800\";\n      default:\n        return \"bg-gray-100 text-gray-800\";\n    }\n  },\n\n  /**\n   * Generate SEO-friendly URL slug\n   */\n  generateSlug: (title: string): string => {\n    return title\n      .toLowerCase()\n      .replace(/[^a-z0-9]+/g, \"-\")\n      .replace(/(^-|-$)/g, \"\");\n  },\n\n  /**\n   * Truncate text to specified length\n   */\n  truncateText: (text: string, maxLength: number): string => {\n    if (text.length <= maxLength) return text;\n    return text.substring(0, maxLength).replace(/\\s+\\S*$/, \"\") + \"...\";\n  },\n};\n"], "names": [], "mappings": ";;;;AAAA;;AASO,MAAM;IACX;;GAEC,GACD,aAAa,YACX,OAAe,CAAC,EAChB,QAAgB,EAAE,EACS;QAC3B,OAAO,iHAAA,CAAA,YAAS,CAAC,GAAG,CAClB,cACA;YACE,MAAM,KAAK,QAAQ;YACnB,OAAO,MAAM,QAAQ;QACvB,GACA;YAAE,OAAO;YAAM,KAAK;QAAI,EAAE,kBAAkB;;IAEhD;IAEA;;GAEC,GACD,aAAa,cAAc,IAAY,EAA6B;QAClE,OAAO,iHAAA,CAAA,YAAS,CAAC,GAAG,CAClB,CAAC,WAAW,EAAE,MAAM,EACpB,WACA;YAAE,OAAO;YAAM,KAAK;QAAI,EAAE,mBAAmB;;IAEjD;IAEA;;GAEC,GACD,aAAa,YACX,KAAa,EACb,OAAe,CAAC,EAChB,QAAgB,EAAE,EACO;QACzB,OAAO,iHAAA,CAAA,YAAS,CAAC,GAAG,CAClB,qBACA;YACE,GAAG;YACH,MAAM,KAAK,QAAQ;YACnB,OAAO,MAAM,QAAQ;QACvB,GACA;YAAE,OAAO;YAAM,KAAK;QAAG,EAAE,4BAA4B;;IAEzD;IAEA;;GAEC,GACD,aAAa,mBACX,QAAgB,EAChB,OAAe,CAAC,EAChB,QAAgB,EAAE,EACS;QAC3B,OAAO,iHAAA,CAAA,YAAS,CAAC,GAAG,CAClB,CAAC,oBAAoB,EAAE,UAAU,EACjC;YACE,MAAM,KAAK,QAAQ;YACnB,OAAO,MAAM,QAAQ;QACvB,GACA;YAAE,OAAO;YAAM,KAAK;QAAI,EAAE,kBAAkB;;IAEhD;IAEA;;GAEC,GACD,aAAa,mBAAmB,IAAY,EAA8B;QACxE,OAAO,iHAAA,CAAA,YAAS,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE,KAAK,KAAK,CAAC;IACjD;IAEA;;GAEC,GACD,aAAa,iBAAiB,QAAgB,CAAC,EAA8B;QAC3E,MAAM,WAAW,MAAM,IAAI,CAAC,WAAW,CAAC,GAAG;QAC3C,OAAO,SAAS,IAAI,CAAC,KAAK;IAC5B;IAEA;;GAEC,GACD,aAAa,eAAe,QAAgB,CAAC,EAA8B;QACzE,MAAM,WAAW,MAAM,IAAI,CAAC,WAAW,CAAC,GAAG;QAC3C,OAAO,SAAS,IAAI,CAAC,KAAK;IAC5B;AACF;AAGO,MAAM,YAAY;IACvB;;GAEC,GACD,YAAY,CAAC;QACX,MAAM,OAAO,IAAI,KAAK;QACtB,OAAO,KAAK,kBAAkB,CAAC,SAAS;YACtC,MAAM;YACN,OAAO;YACP,KAAK;QACP;IACF;IAEA;;GAEC,GACD,sBAAsB,CAAC;QACrB,MAAM,iBAAiB;QACvB,MAAM,YAAY,QAAQ,KAAK,CAAC,OAAO,MAAM;QAC7C,OAAO,KAAK,IAAI,CAAC,YAAY;IAC/B;IAEA;;GAEC,GACD,kBAAkB,CAAC;QACjB,OAAQ,SAAS,WAAW;YAC1B,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA;;GAEC,GACD,cAAc,CAAC;QACb,OAAO,MACJ,WAAW,GACX,OAAO,CAAC,eAAe,KACvB,OAAO,CAAC,YAAY;IACzB;IAEA;;GAEC,GACD,cAAc,CAAC,MAAc;QAC3B,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;QACrC,OAAO,KAAK,SAAS,CAAC,GAAG,WAAW,OAAO,CAAC,WAAW,MAAM;IAC/D;AACF", "debugId": null}}, {"offset": {"line": 334, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Development/BuzzEdge/buzzedge-frontend/src/app/sitemap.ts"], "sourcesContent": ["import { MetadataRoute } from 'next';\nimport { BlogService } from '@/services/blogService';\n\nexport default async function sitemap(): Promise<MetadataRoute.Sitemap> {\n  const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3002';\n\n  // Static pages\n  const staticPages = [\n    {\n      url: baseUrl,\n      lastModified: new Date(),\n      changeFrequency: 'daily' as const,\n      priority: 1,\n    },\n    {\n      url: `${baseUrl}/search`,\n      lastModified: new Date(),\n      changeFrequency: 'weekly' as const,\n      priority: 0.5,\n    },\n  ];\n\n  // Category pages\n  const categories = ['ai-tools', 'productivity', 'developer-tools', 'design-tools'];\n  const categoryPages = categories.map((category) => ({\n    url: `${baseUrl}/category/${category}`,\n    lastModified: new Date(),\n    changeFrequency: 'weekly' as const,\n    priority: 0.8,\n  }));\n\n  // Blog posts\n  let blogPages: MetadataRoute.Sitemap = [];\n  try {\n    const response = await BlogService.getAllPosts(1, 1000); // Get all posts\n    blogPages = response.data.posts.map((post) => ({\n      url: `${baseUrl}/blog/${post.slug}`,\n      lastModified: new Date(post.publishedAt || post.createdAt),\n      changeFrequency: 'monthly' as const,\n      priority: 0.9,\n    }));\n  } catch (error) {\n    console.error('Failed to fetch posts for sitemap:', error);\n  }\n\n  return [...staticPages, ...categoryPages, ...blogPages];\n}\n"], "names": [], "mappings": ";;;AACA;;AAEe,eAAe;IAC5B,MAAM,UAAU,6DAAoC;IAEpD,eAAe;IACf,MAAM,cAAc;QAClB;YACE,KAAK;YACL,cAAc,IAAI;YAClB,iBAAiB;YACjB,UAAU;QACZ;QACA;YACE,KAAK,GAAG,QAAQ,OAAO,CAAC;YACxB,cAAc,IAAI;YAClB,iBAAiB;YACjB,UAAU;QACZ;KACD;IAED,iBAAiB;IACjB,MAAM,aAAa;QAAC;QAAY;QAAgB;QAAmB;KAAe;IAClF,MAAM,gBAAgB,WAAW,GAAG,CAAC,CAAC,WAAa,CAAC;YAClD,KAAK,GAAG,QAAQ,UAAU,EAAE,UAAU;YACtC,cAAc,IAAI;YAClB,iBAAiB;YACjB,UAAU;QACZ,CAAC;IAED,aAAa;IACb,IAAI,YAAmC,EAAE;IACzC,IAAI;QACF,MAAM,WAAW,MAAM,8HAAA,CAAA,cAAW,CAAC,WAAW,CAAC,GAAG,OAAO,gBAAgB;QACzE,YAAY,SAAS,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,OAAS,CAAC;gBAC7C,KAAK,GAAG,QAAQ,MAAM,EAAE,KAAK,IAAI,EAAE;gBACnC,cAAc,IAAI,KAAK,KAAK,WAAW,IAAI,KAAK,SAAS;gBACzD,iBAAiB;gBACjB,UAAU;YACZ,CAAC;IACH,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sCAAsC;IACtD;IAEA,OAAO;WAAI;WAAgB;WAAkB;KAAU;AACzD", "debugId": null}}, {"offset": {"line": 394, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Development/BuzzEdge/buzzedge-frontend/src/app/sitemap--route-entry.js"], "sourcesContent": ["import { NextResponse } from 'next/server'\nimport * as _sitemapModule from \"./sitemap.ts\"\nimport { resolveRouteData } from 'next/dist/build/webpack/loaders/metadata/resolve-route-data'\n\nconst sitemapModule = { ..._sitemapModule }\nconst handler = sitemapModule.default\nconst generateSitemaps = sitemapModule.generateSitemaps\nconst contentType = \"application/xml\"\nconst cacheControl = \"public, max-age=0, must-revalidate\"\nconst fileType = \"sitemap\"\n\nif (typeof handler !== 'function') {\n    throw new Error('Default export is missing in \"./sitemap.ts\"')\n}\n\nexport async function GET(_, ctx) {\n    const { __metadata_id__: id, ...params } = await ctx.params || {}\n    const hasXmlExtension = id ? id.endsWith('.xml') : false\n    if (id && !hasXmlExtension) {\n        return new NextResponse('Not Found', {\n            status: 404,\n        })\n    }\n\n    if (process.env.NODE_ENV !== 'production' && sitemapModule.generateSitemaps) {\n        const sitemaps = await sitemapModule.generateSitemaps()\n        for (const item of sitemaps) {\n            if (item?.id == null) {\n                throw new Error('id property is required for every item returned from generateSitemaps')\n            }\n        }\n    }\n    \n    const targetId = id && hasXmlExtension ? id.slice(0, -4) : undefined\n    const data = await handler({ id: targetId })\n    const content = resolveRouteData(data, fileType)\n\n    return new NextResponse(content, {\n        headers: {\n            'Content-Type': contentType,\n            'Cache-Control': cacheControl,\n        },\n    })\n}\n\n\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEA,MAAM,gBAAgB;IAAE,GAAG,qHAAc;AAAC;AAC1C,MAAM,UAAU,cAAc,OAAO;AACrC,MAAM,mBAAmB,cAAc,gBAAgB;AACvD,MAAM,cAAc;AACpB,MAAM,eAAe;AACrB,MAAM,WAAW;AAEjB,IAAI,OAAO,YAAY,YAAY;IAC/B,MAAM,IAAI,MAAM;AACpB;AAEO,eAAe,IAAI,CAAC,EAAE,GAAG;IAC5B,MAAM,EAAE,iBAAiB,EAAE,EAAE,GAAG,QAAQ,GAAG,MAAM,IAAI,MAAM,IAAI,CAAC;IAChE,MAAM,kBAAkB,KAAK,GAAG,QAAQ,CAAC,UAAU;IACnD,IAAI,MAAM,CAAC,iBAAiB;QACxB,OAAO,IAAI,8HAAA,CAAA,eAAY,CAAC,aAAa;YACjC,QAAQ;QACZ;IACJ;IAEA,IAAI,oDAAyB,gBAAgB,cAAc,gBAAgB,EAAE;QACzE,MAAM,WAAW,MAAM,cAAc,gBAAgB;QACrD,KAAK,MAAM,QAAQ,SAAU;YACzB,IAAI,MAAM,MAAM,MAAM;gBAClB,MAAM,IAAI,MAAM;YACpB;QACJ;IACJ;IAEA,MAAM,WAAW,MAAM,kBAAkB,GAAG,KAAK,CAAC,GAAG,CAAC,KAAK;IAC3D,MAAM,OAAO,MAAM,QAAQ;QAAE,IAAI;IAAS;IAC1C,MAAM,UAAU,CAAA,GAAA,mMAAA,CAAA,mBAAgB,AAAD,EAAE,MAAM;IAEvC,OAAO,IAAI,8HAAA,CAAA,eAAY,CAAC,SAAS;QAC7B,SAAS;YACL,gBAAgB;YAChB,iBAAiB;QACrB;IACJ;AACJ", "debugId": null}}]}