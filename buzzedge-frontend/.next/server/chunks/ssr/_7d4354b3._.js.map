{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 30, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Development/BuzzEdge/buzzedge-frontend/src/components/Header.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Header = registerClientReference(\n    function() { throw new Error(\"Attempted to call Head<PERSON>() from the server but <PERSON><PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Header.tsx <module evaluation>\",\n    \"Header\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,SAAS,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,2DACA", "debugId": null}}, {"offset": {"line": 44, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Development/BuzzEdge/buzzedge-frontend/src/components/Header.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Header = registerClientReference(\n    function() { throw new Error(\"Attempted to call Header() from the server but <PERSON><PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Header.tsx\",\n    \"Header\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,SAAS,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,uCACA", "debugId": null}}, {"offset": {"line": 58, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Development/BuzzEdge/buzzedge-frontend/src/components/Footer.tsx"], "sourcesContent": ["import Link from 'next/link';\n\nexport function Footer() {\n  const currentYear = new Date().getFullYear();\n\n  const footerLinks = {\n    categories: [\n      { name: 'AI Tools', href: '/category/ai-tools' },\n      { name: 'Productivity', href: '/category/productivity' },\n      { name: 'Developer Tools', href: '/category/developer-tools' },\n      { name: 'Design Tools', href: '/category/design-tools' },\n    ],\n    company: [\n      { name: 'About', href: '/about' },\n      { name: 'Contact', href: '/contact' },\n      { name: 'Privacy Policy', href: '/privacy' },\n      { name: 'Terms of Service', href: '/terms' },\n    ],\n    social: [\n      { name: 'Twitter', href: '#', icon: 'twitter' },\n      { name: 'LinkedIn', href: '#', icon: 'linkedin' },\n      { name: 'GitH<PERSON>', href: '#', icon: 'github' },\n    ],\n  };\n\n  const SocialIcon = ({ icon }: { icon: string }) => {\n    switch (icon) {\n      case 'twitter':\n        return (\n          <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path d=\"M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84\" />\n          </svg>\n        );\n      case 'linkedin':\n        return (\n          <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path d=\"M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z\" />\n          </svg>\n        );\n      case 'github':\n        return (\n          <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path d=\"M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z\" />\n          </svg>\n        );\n      default:\n        return null;\n    }\n  };\n\n  return (\n    <footer className=\"bg-gray-900 text-white\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-8\">\n          {/* Brand */}\n          <div className=\"col-span-1 md:col-span-2\">\n            <div className=\"flex items-center mb-4\">\n              <div className=\"w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center\">\n                <span className=\"text-white font-bold text-lg\">B</span>\n              </div>\n              <span className=\"ml-2 text-xl font-bold\">BuzzEdge</span>\n            </div>\n            <p className=\"text-gray-400 mb-6 max-w-md\">\n              Your ultimate source for tech tool reviews, AI comparisons, and productivity insights. \n              Stay ahead with our comprehensive analysis of the latest tools and platforms.\n            </p>\n            <div className=\"flex space-x-4\">\n              {footerLinks.social.map((item) => (\n                <a\n                  key={item.name}\n                  href={item.href}\n                  className=\"text-gray-400 hover:text-white transition-colors\"\n                  aria-label={item.name}\n                >\n                  <SocialIcon icon={item.icon} />\n                </a>\n              ))}\n            </div>\n          </div>\n\n          {/* Categories */}\n          <div>\n            <h3 className=\"text-sm font-semibold text-gray-300 tracking-wider uppercase mb-4\">\n              Categories\n            </h3>\n            <ul className=\"space-y-3\">\n              {footerLinks.categories.map((item) => (\n                <li key={item.name}>\n                  <Link\n                    href={item.href}\n                    className=\"text-gray-400 hover:text-white transition-colors\"\n                  >\n                    {item.name}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </div>\n\n          {/* Company */}\n          <div>\n            <h3 className=\"text-sm font-semibold text-gray-300 tracking-wider uppercase mb-4\">\n              Company\n            </h3>\n            <ul className=\"space-y-3\">\n              {footerLinks.company.map((item) => (\n                <li key={item.name}>\n                  <Link\n                    href={item.href}\n                    className=\"text-gray-400 hover:text-white transition-colors\"\n                  >\n                    {item.name}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </div>\n        </div>\n\n        {/* Bottom */}\n        <div className=\"mt-8 pt-8 border-t border-gray-800\">\n          <div className=\"flex flex-col md:flex-row justify-between items-center\">\n            <p className=\"text-gray-400 text-sm\">\n              © {currentYear} BuzzEdge. All rights reserved.\n            </p>\n            <p className=\"text-gray-400 text-sm mt-2 md:mt-0\">\n              Made with ❤️ for the tech community\n            </p>\n          </div>\n        </div>\n      </div>\n    </footer>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;;AAEO,SAAS;IACd,MAAM,cAAc,IAAI,OAAO,WAAW;IAE1C,MAAM,cAAc;QAClB,YAAY;YACV;gBAAE,MAAM;gBAAY,MAAM;YAAqB;YAC/C;gBAAE,MAAM;gBAAgB,MAAM;YAAyB;YACvD;gBAAE,MAAM;gBAAmB,MAAM;YAA4B;YAC7D;gBAAE,MAAM;gBAAgB,MAAM;YAAyB;SACxD;QACD,SAAS;YACP;gBAAE,MAAM;gBAAS,MAAM;YAAS;YAChC;gBAAE,MAAM;gBAAW,MAAM;YAAW;YACpC;gBAAE,MAAM;gBAAkB,MAAM;YAAW;YAC3C;gBAAE,MAAM;gBAAoB,MAAM;YAAS;SAC5C;QACD,QAAQ;YACN;gBAAE,MAAM;gBAAW,MAAM;gBAAK,MAAM;YAAU;YAC9C;gBAAE,MAAM;gBAAY,MAAM;gBAAK,MAAM;YAAW;YAChD;gBAAE,MAAM;gBAAU,MAAM;gBAAK,MAAM;YAAS;SAC7C;IACH;IAEA,MAAM,aAAa,CAAC,EAAE,IAAI,EAAoB;QAC5C,OAAQ;YACN,KAAK;gBACH,qBACE,8OAAC;oBAAI,WAAU;oBAAU,MAAK;oBAAe,SAAQ;8BACnD,cAAA,8OAAC;wBAAK,GAAE;;;;;;;;;;;YAGd,KAAK;gBACH,qBACE,8OAAC;oBAAI,WAAU;oBAAU,MAAK;oBAAe,SAAQ;8BACnD,cAAA,8OAAC;wBAAK,GAAE;;;;;;;;;;;YAGd,KAAK;gBACH,qBACE,8OAAC;oBAAI,WAAU;oBAAU,MAAK;oBAAe,SAAQ;8BACnD,cAAA,8OAAC;wBAAK,GAAE;;;;;;;;;;;YAGd;gBACE,OAAO;QACX;IACF;IAEA,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAA+B;;;;;;;;;;;sDAEjD,8OAAC;4CAAK,WAAU;sDAAyB;;;;;;;;;;;;8CAE3C,8OAAC;oCAAE,WAAU;8CAA8B;;;;;;8CAI3C,8OAAC;oCAAI,WAAU;8CACZ,YAAY,MAAM,CAAC,GAAG,CAAC,CAAC,qBACvB,8OAAC;4CAEC,MAAM,KAAK,IAAI;4CACf,WAAU;4CACV,cAAY,KAAK,IAAI;sDAErB,cAAA,8OAAC;gDAAW,MAAM,KAAK,IAAI;;;;;;2CALtB,KAAK,IAAI;;;;;;;;;;;;;;;;sCAYtB,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAoE;;;;;;8CAGlF,8OAAC;oCAAG,WAAU;8CACX,YAAY,UAAU,CAAC,GAAG,CAAC,CAAC,qBAC3B,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,IAAI;;;;;;2CALL,KAAK,IAAI;;;;;;;;;;;;;;;;sCAaxB,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAoE;;;;;;8CAGlF,8OAAC;oCAAG,WAAU;8CACX,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,qBACxB,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,IAAI;;;;;;2CALL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;8BAc1B,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,WAAU;;oCAAwB;oCAChC;oCAAY;;;;;;;0CAEjB,8OAAC;gCAAE,WAAU;0CAAqC;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ9D", "debugId": null}}, {"offset": {"line": 405, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Development/BuzzEdge/buzzedge-frontend/src/lib/api.ts"], "sourcesContent": ["// API Configuration\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5001';\n\nexport class ApiError extends Error {\n  constructor(\n    message: string,\n    public status: number,\n    public response?: any\n  ) {\n    super(message);\n    this.name = 'ApiError';\n  }\n}\n\nexport interface ApiResponse<T> {\n  success: boolean;\n  data?: T;\n  error?: string;\n  message?: string;\n}\n\nclass ApiClient {\n  private baseURL: string;\n\n  constructor(baseURL: string) {\n    this.baseURL = baseURL;\n  }\n\n  private async request<T>(\n    endpoint: string,\n    options: RequestInit = {}\n  ): Promise<T> {\n    const url = `${this.baseURL}${endpoint}`;\n    \n    const config: RequestInit = {\n      headers: {\n        'Content-Type': 'application/json',\n        ...options.headers,\n      },\n      ...options,\n    };\n\n    try {\n      const response = await fetch(url, config);\n      \n      if (!response.ok) {\n        const errorData = await response.json().catch(() => ({}));\n        throw new ApiError(\n          errorData.error || `HTTP ${response.status}: ${response.statusText}`,\n          response.status,\n          errorData\n        );\n      }\n\n      const data = await response.json();\n      return data;\n    } catch (error) {\n      if (error instanceof ApiError) {\n        throw error;\n      }\n      \n      // Network or other errors\n      throw new ApiError(\n        error instanceof Error ? error.message : 'Network error occurred',\n        0\n      );\n    }\n  }\n\n  async get<T>(endpoint: string, params?: Record<string, string>): Promise<T> {\n    const url = new URL(endpoint, this.baseURL);\n    if (params) {\n      Object.entries(params).forEach(([key, value]) => {\n        url.searchParams.append(key, value);\n      });\n    }\n    \n    return this.request<T>(url.pathname + url.search);\n  }\n\n  async post<T>(endpoint: string, data?: any): Promise<T> {\n    return this.request<T>(endpoint, {\n      method: 'POST',\n      body: data ? JSON.stringify(data) : undefined,\n    });\n  }\n\n  async put<T>(endpoint: string, data?: any): Promise<T> {\n    return this.request<T>(endpoint, {\n      method: 'PUT',\n      body: data ? JSON.stringify(data) : undefined,\n    });\n  }\n\n  async delete<T>(endpoint: string): Promise<T> {\n    return this.request<T>(endpoint, {\n      method: 'DELETE',\n    });\n  }\n}\n\n// Create and export the API client instance\nexport const apiClient = new ApiClient(API_BASE_URL);\n\n// Health check function\nexport const checkApiHealth = async (): Promise<boolean> => {\n  try {\n    await apiClient.get('/health');\n    return true;\n  } catch (error) {\n    console.error('API health check failed:', error);\n    return false;\n  }\n};\n"], "names": [], "mappings": "AAAA,oBAAoB;;;;;;AACpB,MAAM,eAAe,6DAAmC;AAEjD,MAAM,iBAAiB;;;IAC5B,YACE,OAAe,EACf,AAAO,MAAc,EACrB,AAAO,QAAc,CACrB;QACA,KAAK,CAAC,eAHC,SAAA,aACA,WAAA;QAGP,IAAI,CAAC,IAAI,GAAG;IACd;AACF;AASA,MAAM;IACI,QAAgB;IAExB,YAAY,OAAe,CAAE;QAC3B,IAAI,CAAC,OAAO,GAAG;IACjB;IAEA,MAAc,QACZ,QAAgB,EAChB,UAAuB,CAAC,CAAC,EACb;QACZ,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,GAAG,UAAU;QAExC,MAAM,SAAsB;YAC1B,SAAS;gBACP,gBAAgB;gBAChB,GAAG,QAAQ,OAAO;YACpB;YACA,GAAG,OAAO;QACZ;QAEA,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,KAAK;YAElC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC,CAAC,CAAC;gBACvD,MAAM,IAAI,SACR,UAAU,KAAK,IAAI,CAAC,KAAK,EAAE,SAAS,MAAM,CAAC,EAAE,EAAE,SAAS,UAAU,EAAE,EACpE,SAAS,MAAM,EACf;YAEJ;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,OAAO;QACT,EAAE,OAAO,OAAO;YACd,IAAI,iBAAiB,UAAU;gBAC7B,MAAM;YACR;YAEA,0BAA0B;YAC1B,MAAM,IAAI,SACR,iBAAiB,QAAQ,MAAM,OAAO,GAAG,0BACzC;QAEJ;IACF;IAEA,MAAM,IAAO,QAAgB,EAAE,MAA+B,EAAc;QAC1E,MAAM,MAAM,IAAI,IAAI,UAAU,IAAI,CAAC,OAAO;QAC1C,IAAI,QAAQ;YACV,OAAO,OAAO,CAAC,QAAQ,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;gBAC1C,IAAI,YAAY,CAAC,MAAM,CAAC,KAAK;YAC/B;QACF;QAEA,OAAO,IAAI,CAAC,OAAO,CAAI,IAAI,QAAQ,GAAG,IAAI,MAAM;IAClD;IAEA,MAAM,KAAQ,QAAgB,EAAE,IAAU,EAAc;QACtD,OAAO,IAAI,CAAC,OAAO,CAAI,UAAU;YAC/B,QAAQ;YACR,MAAM,OAAO,KAAK,SAAS,CAAC,QAAQ;QACtC;IACF;IAEA,MAAM,IAAO,QAAgB,EAAE,IAAU,EAAc;QACrD,OAAO,IAAI,CAAC,OAAO,CAAI,UAAU;YAC/B,QAAQ;YACR,MAAM,OAAO,KAAK,SAAS,CAAC,QAAQ;QACtC;IACF;IAEA,MAAM,OAAU,QAAgB,EAAc;QAC5C,OAAO,IAAI,CAAC,OAAO,CAAI,UAAU;YAC/B,QAAQ;QACV;IACF;AACF;AAGO,MAAM,YAAY,IAAI,UAAU;AAGhC,MAAM,iBAAiB;IAC5B,IAAI;QACF,MAAM,UAAU,GAAG,CAAC;QACpB,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 493, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Development/BuzzEdge/buzzedge-frontend/src/services/blogService.ts"], "sourcesContent": ["import { apiClient } from '@/lib/api';\nimport {\n  BlogListResponse,\n  BlogPostResponse,\n  SearchResponse,\n  CategoryResponse,\n  BlogPostSummary,\n} from '@/types/blog';\n\nexport class BlogService {\n  /**\n   * Get all published blog posts with pagination\n   */\n  static async getAllPosts(\n    page: number = 1,\n    limit: number = 10\n  ): Promise<BlogListResponse> {\n    return apiClient.get<BlogListResponse>('/api/blogs', {\n      page: page.toString(),\n      limit: limit.toString(),\n    });\n  }\n\n  /**\n   * Get a single blog post by slug\n   */\n  static async getPostBySlug(slug: string): Promise<BlogPostResponse> {\n    return apiClient.get<BlogPostResponse>(`/api/blogs/${slug}`);\n  }\n\n  /**\n   * Search blog posts\n   */\n  static async searchPosts(\n    query: string,\n    page: number = 1,\n    limit: number = 10\n  ): Promise<SearchResponse> {\n    return apiClient.get<SearchResponse>('/api/blogs/search', {\n      q: query,\n      page: page.toString(),\n      limit: limit.toString(),\n    });\n  }\n\n  /**\n   * Get posts by category\n   */\n  static async getPostsByCategory(\n    category: string,\n    page: number = 1,\n    limit: number = 10\n  ): Promise<CategoryResponse> {\n    return apiClient.get<CategoryResponse>(`/api/blogs/category/${category}`, {\n      page: page.toString(),\n      limit: limit.toString(),\n    });\n  }\n\n  /**\n   * Increment post view count\n   */\n  static async incrementPostViews(slug: string): Promise<{ views: number }> {\n    return apiClient.post(`/api/blogs/${slug}/view`);\n  }\n\n  /**\n   * Get featured posts (most viewed)\n   */\n  static async getFeaturedPosts(limit: number = 5): Promise<BlogPostSummary[]> {\n    const response = await this.getAllPosts(1, limit);\n    return response.data.posts;\n  }\n\n  /**\n   * Get recent posts\n   */\n  static async getRecentPosts(limit: number = 5): Promise<BlogPostSummary[]> {\n    const response = await this.getAllPosts(1, limit);\n    return response.data.posts;\n  }\n}\n\n// Utility functions for client-side data processing\nexport const blogUtils = {\n  /**\n   * Format date for display\n   */\n  formatDate: (dateString: string): string => {\n    const date = new Date(dateString);\n    return date.toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric',\n    });\n  },\n\n  /**\n   * Calculate reading time based on content length\n   */\n  calculateReadingTime: (content: string): number => {\n    const wordsPerMinute = 200;\n    const wordCount = content.split(/\\s+/).length;\n    return Math.ceil(wordCount / wordsPerMinute);\n  },\n\n  /**\n   * Get category color class\n   */\n  getCategoryColor: (category: string): string => {\n    switch (category.toLowerCase()) {\n      case 'ai tools':\n        return 'bg-blue-100 text-blue-800';\n      case 'productivity':\n        return 'bg-green-100 text-green-800';\n      case 'developer tools':\n        return 'bg-purple-100 text-purple-800';\n      case 'design tools':\n        return 'bg-pink-100 text-pink-800';\n      default:\n        return 'bg-gray-100 text-gray-800';\n    }\n  },\n\n  /**\n   * Generate SEO-friendly URL slug\n   */\n  generateSlug: (title: string): string => {\n    return title\n      .toLowerCase()\n      .replace(/[^a-z0-9]+/g, '-')\n      .replace(/(^-|-$)/g, '');\n  },\n\n  /**\n   * Truncate text to specified length\n   */\n  truncateText: (text: string, maxLength: number): string => {\n    if (text.length <= maxLength) return text;\n    return text.substring(0, maxLength).replace(/\\s+\\S*$/, '') + '...';\n  },\n};\n"], "names": [], "mappings": ";;;;AAAA;;AASO,MAAM;IACX;;GAEC,GACD,aAAa,YACX,OAAe,CAAC,EAChB,QAAgB,EAAE,EACS;QAC3B,OAAO,iHAAA,CAAA,YAAS,CAAC,GAAG,CAAmB,cAAc;YACnD,MAAM,KAAK,QAAQ;YACnB,OAAO,MAAM,QAAQ;QACvB;IACF;IAEA;;GAEC,GACD,aAAa,cAAc,IAAY,EAA6B;QAClE,OAAO,iHAAA,CAAA,YAAS,CAAC,GAAG,CAAmB,CAAC,WAAW,EAAE,MAAM;IAC7D;IAEA;;GAEC,GACD,aAAa,YACX,KAAa,EACb,OAAe,CAAC,EAChB,QAAgB,EAAE,EACO;QACzB,OAAO,iHAAA,CAAA,YAAS,CAAC,GAAG,CAAiB,qBAAqB;YACxD,GAAG;YACH,MAAM,KAAK,QAAQ;YACnB,OAAO,MAAM,QAAQ;QACvB;IACF;IAEA;;GAEC,GACD,aAAa,mBACX,QAAgB,EAChB,OAAe,CAAC,EAChB,QAAgB,EAAE,EACS;QAC3B,OAAO,iHAAA,CAAA,YAAS,CAAC,GAAG,CAAmB,CAAC,oBAAoB,EAAE,UAAU,EAAE;YACxE,MAAM,KAAK,QAAQ;YACnB,OAAO,MAAM,QAAQ;QACvB;IACF;IAEA;;GAEC,GACD,aAAa,mBAAmB,IAAY,EAA8B;QACxE,OAAO,iHAAA,CAAA,YAAS,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE,KAAK,KAAK,CAAC;IACjD;IAEA;;GAEC,GACD,aAAa,iBAAiB,QAAgB,CAAC,EAA8B;QAC3E,MAAM,WAAW,MAAM,IAAI,CAAC,WAAW,CAAC,GAAG;QAC3C,OAAO,SAAS,IAAI,CAAC,KAAK;IAC5B;IAEA;;GAEC,GACD,aAAa,eAAe,QAAgB,CAAC,EAA8B;QACzE,MAAM,WAAW,MAAM,IAAI,CAAC,WAAW,CAAC,GAAG;QAC3C,OAAO,SAAS,IAAI,CAAC,KAAK;IAC5B;AACF;AAGO,MAAM,YAAY;IACvB;;GAEC,GACD,YAAY,CAAC;QACX,MAAM,OAAO,IAAI,KAAK;QACtB,OAAO,KAAK,kBAAkB,CAAC,SAAS;YACtC,MAAM;YACN,OAAO;YACP,KAAK;QACP;IACF;IAEA;;GAEC,GACD,sBAAsB,CAAC;QACrB,MAAM,iBAAiB;QACvB,MAAM,YAAY,QAAQ,KAAK,CAAC,OAAO,MAAM;QAC7C,OAAO,KAAK,IAAI,CAAC,YAAY;IAC/B;IAEA;;GAEC,GACD,kBAAkB,CAAC;QACjB,OAAQ,SAAS,WAAW;YAC1B,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA;;GAEC,GACD,cAAc,CAAC;QACb,OAAO,MACJ,WAAW,GACX,OAAO,CAAC,eAAe,KACvB,OAAO,CAAC,YAAY;IACzB;IAEA;;GAEC,GACD,cAAc,CAAC,MAAc;QAC3B,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;QACrC,OAAO,KAAK,SAAS,CAAC,GAAG,WAAW,OAAO,CAAC,WAAW,MAAM;IAC/D;AACF", "debugId": null}}, {"offset": {"line": 600, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Development/BuzzEdge/buzzedge-frontend/src/components/BlogCard.tsx"], "sourcesContent": ["import Image from \"next/image\";\nimport Link from \"next/link\";\nimport { BlogPostSummary } from \"@/types/blog\";\nimport { blogUtils } from \"@/services/blogService\";\n\ninterface BlogCardProps {\n  post: BlogPostSummary;\n}\n\nexport function BlogCard({ post }: BlogCardProps) {\n  return (\n    <article className=\"bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300\">\n      <Link href={`/blog/${post.slug}`}>\n        <div className=\"relative h-48 w-full\">\n          <Image\n            src={post.featuredImage.url}\n            alt={post.featuredImage.alt}\n            fill\n            className=\"object-cover\"\n            sizes=\"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw\"\n          />\n        </div>\n      </Link>\n\n      <div className=\"p-6\">\n        <div className=\"flex items-center justify-between mb-3\">\n          <span\n            className={`px-3 py-1 rounded-full text-xs font-medium ${blogUtils.getCategoryColor(\n              post.category\n            )}`}\n          >\n            {post.category}\n          </span>\n          <span className=\"text-sm text-gray-500 flex items-center\">\n            <svg\n              className=\"w-4 h-4 mr-1\"\n              fill=\"none\"\n              stroke=\"currentColor\"\n              viewBox=\"0 0 24 24\"\n            >\n              <path\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                strokeWidth={2}\n                d=\"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"\n              />\n              <path\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                strokeWidth={2}\n                d=\"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z\"\n              />\n            </svg>\n            {post.analytics.views.toLocaleString()}\n          </span>\n        </div>\n\n        <Link href={`/blog/${post.slug}`}>\n          <h3 className=\"text-xl font-bold text-gray-900 mb-3 hover:text-blue-600 transition-colors line-clamp-2\">\n            {post.title}\n          </h3>\n        </Link>\n\n        <p className=\"text-gray-600 mb-4 line-clamp-3\">{post.excerpt}</p>\n\n        <div className=\"flex items-center justify-between\">\n          <time className=\"text-sm text-gray-500\">\n            {blogUtils.formatDate(post.publishedAt)}\n          </time>\n\n          <Link\n            href={`/blog/${post.slug}`}\n            className=\"text-blue-600 hover:text-blue-800 font-medium text-sm transition-colors\"\n          >\n            Read more →\n          </Link>\n        </div>\n\n        <div className=\"flex flex-wrap gap-2 mt-4\">\n          {post.tags.slice(0, 3).map((tag) => (\n            <span\n              key={tag}\n              className=\"px-2 py-1 bg-gray-100 text-gray-600 rounded text-xs\"\n            >\n              #{tag}\n            </span>\n          ))}\n        </div>\n      </div>\n    </article>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAEA;;;;;AAMO,SAAS,SAAS,EAAE,IAAI,EAAiB;IAC9C,qBACE,8OAAC;QAAQ,WAAU;;0BACjB,8OAAC,4JAAA,CAAA,UAAI;gBAAC,MAAM,CAAC,MAAM,EAAE,KAAK,IAAI,EAAE;0BAC9B,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;wBACJ,KAAK,KAAK,aAAa,CAAC,GAAG;wBAC3B,KAAK,KAAK,aAAa,CAAC,GAAG;wBAC3B,IAAI;wBACJ,WAAU;wBACV,OAAM;;;;;;;;;;;;;;;;0BAKZ,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,WAAW,CAAC,2CAA2C,EAAE,8HAAA,CAAA,YAAS,CAAC,gBAAgB,CACjF,KAAK,QAAQ,GACZ;0CAEF,KAAK,QAAQ;;;;;;0CAEhB,8OAAC;gCAAK,WAAU;;kDACd,8OAAC;wCACC,WAAU;wCACV,MAAK;wCACL,QAAO;wCACP,SAAQ;;0DAER,8OAAC;gDACC,eAAc;gDACd,gBAAe;gDACf,aAAa;gDACb,GAAE;;;;;;0DAEJ,8OAAC;gDACC,eAAc;gDACd,gBAAe;gDACf,aAAa;gDACb,GAAE;;;;;;;;;;;;oCAGL,KAAK,SAAS,CAAC,KAAK,CAAC,cAAc;;;;;;;;;;;;;kCAIxC,8OAAC,4JAAA,CAAA,UAAI;wBAAC,MAAM,CAAC,MAAM,EAAE,KAAK,IAAI,EAAE;kCAC9B,cAAA,8OAAC;4BAAG,WAAU;sCACX,KAAK,KAAK;;;;;;;;;;;kCAIf,8OAAC;wBAAE,WAAU;kCAAmC,KAAK,OAAO;;;;;;kCAE5D,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAK,WAAU;0CACb,8HAAA,CAAA,YAAS,CAAC,UAAU,CAAC,KAAK,WAAW;;;;;;0CAGxC,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAM,CAAC,MAAM,EAAE,KAAK,IAAI,EAAE;gCAC1B,WAAU;0CACX;;;;;;;;;;;;kCAKH,8OAAC;wBAAI,WAAU;kCACZ,KAAK,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,oBAC1B,8OAAC;gCAEC,WAAU;;oCACX;oCACG;;+BAHG;;;;;;;;;;;;;;;;;;;;;;AAUnB", "debugId": null}}, {"offset": {"line": 788, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Development/BuzzEdge/buzzedge-frontend/src/components/SEOHead.tsx"], "sourcesContent": ["import { Metadata } from 'next';\n\ninterface SEOProps {\n  title: string;\n  description: string;\n  keywords?: string[];\n  canonicalUrl?: string;\n  ogImage?: string;\n  ogType?: 'website' | 'article';\n  publishedTime?: string;\n  modifiedTime?: string;\n  author?: string;\n  category?: string;\n  tags?: string[];\n  noIndex?: boolean;\n}\n\nexport function generateMetadata({\n  title,\n  description,\n  keywords = [],\n  canonicalUrl,\n  ogImage = '/og-default.jpg',\n  ogType = 'website',\n  publishedTime,\n  modifiedTime,\n  author,\n  category,\n  tags = [],\n  noIndex = false\n}: SEOProps): Metadata {\n  const siteName = 'BuzzEdge';\n  const siteUrl = process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3002';\n  const fullTitle = title.includes(siteName) ? title : `${title} | ${siteName}`;\n  const fullCanonicalUrl = canonicalUrl ? `${siteUrl}${canonicalUrl}` : siteUrl;\n  const fullOgImage = ogImage.startsWith('http') ? ogImage : `${siteUrl}${ogImage}`;\n\n  // Combine default keywords with page-specific ones\n  const defaultKeywords = [\n    'tech reviews',\n    'tool comparisons',\n    'software reviews',\n    'productivity tools',\n    'AI tools',\n    'developer tools',\n    'design tools',\n    'BuzzEdge'\n  ];\n  const allKeywords = [...new Set([...keywords, ...defaultKeywords])];\n\n  const metadata: Metadata = {\n    title: fullTitle,\n    description,\n    keywords: allKeywords.join(', '),\n    authors: author ? [{ name: author }] : [{ name: 'BuzzEdge Team' }],\n    creator: 'BuzzEdge',\n    publisher: 'BuzzEdge',\n    robots: noIndex ? 'noindex, nofollow' : 'index, follow',\n    \n    // Open Graph\n    openGraph: {\n      title: fullTitle,\n      description,\n      url: fullCanonicalUrl,\n      siteName,\n      images: [\n        {\n          url: fullOgImage,\n          width: 1200,\n          height: 630,\n          alt: title,\n        }\n      ],\n      locale: 'en_US',\n      type: ogType,\n      ...(publishedTime && { publishedTime }),\n      ...(modifiedTime && { modifiedTime }),\n    },\n\n    // Twitter Card\n    twitter: {\n      card: 'summary_large_image',\n      title: fullTitle,\n      description,\n      images: [fullOgImage],\n      creator: '@buzzedge',\n      site: '@buzzedge',\n    },\n\n    // Additional meta tags\n    other: {\n      'article:author': author || 'BuzzEdge Team',\n      ...(category && { 'article:section': category }),\n      ...(tags.length > 0 && { 'article:tag': tags.join(', ') }),\n    },\n\n    // Canonical URL\n    alternates: {\n      canonical: fullCanonicalUrl,\n    },\n\n    // Verification tags (you can add these when you have the actual values)\n    verification: {\n      // google: 'your-google-verification-code',\n      // yandex: 'your-yandex-verification-code',\n      // yahoo: 'your-yahoo-verification-code',\n    },\n  };\n\n  return metadata;\n}\n\n// Predefined SEO configurations for common pages\nexport const seoConfigs = {\n  home: {\n    title: 'BuzzEdge - Tech Tool Reviews & Comparisons',\n    description: 'Your ultimate source for tech tool reviews, AI comparisons, and productivity insights. Stay ahead with our comprehensive analysis of the latest tools and platforms.',\n    keywords: ['tech reviews', 'tool comparisons', 'AI tools', 'productivity', 'software reviews'],\n    canonicalUrl: '/',\n  },\n\n  search: (query: string, resultCount: number) => ({\n    title: `Search Results for \"${query}\" - BuzzEdge`,\n    description: `Found ${resultCount} results for \"${query}\". Discover tech tool reviews and comparisons on BuzzEdge.`,\n    keywords: ['search', 'tech tools', query],\n    canonicalUrl: `/search?q=${encodeURIComponent(query)}`,\n    noIndex: true, // Don't index search result pages\n  }),\n\n  category: (categoryName: string, postCount: number) => ({\n    title: `${categoryName} Reviews & Comparisons - BuzzEdge`,\n    description: `Explore ${postCount} comprehensive reviews and comparisons of ${categoryName.toLowerCase()}. Find the best tools for your needs.`,\n    keywords: [categoryName.toLowerCase(), 'reviews', 'comparisons', 'tools'],\n    canonicalUrl: `/category/${categoryName.toLowerCase().replace(/\\s+/g, '-')}`,\n  }),\n\n  blogPost: (post: {\n    title: string;\n    excerpt: string;\n    slug: string;\n    category: string;\n    tags: string[];\n    publishedAt: string;\n    featuredImage?: { url: string };\n    seo?: {\n      metaTitle?: string;\n      metaDescription?: string;\n      keywords?: string[];\n    };\n  }) => ({\n    title: post.seo?.metaTitle || post.title,\n    description: post.seo?.metaDescription || post.excerpt,\n    keywords: post.seo?.keywords || post.tags,\n    canonicalUrl: `/blog/${post.slug}`,\n    ogImage: post.featuredImage?.url,\n    ogType: 'article' as const,\n    publishedTime: post.publishedAt,\n    author: 'BuzzEdge Team',\n    category: post.category,\n    tags: post.tags,\n  }),\n};\n\n// JSON-LD structured data generators\nexport const generateStructuredData = {\n  website: () => ({\n    '@context': 'https://schema.org',\n    '@type': 'WebSite',\n    name: 'BuzzEdge',\n    description: 'Tech tool reviews, AI comparisons, and productivity insights',\n    url: process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3002',\n    potentialAction: {\n      '@type': 'SearchAction',\n      target: {\n        '@type': 'EntryPoint',\n        urlTemplate: `${process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3002'}/search?q={search_term_string}`,\n      },\n      'query-input': 'required name=search_term_string',\n    },\n  }),\n\n  organization: () => ({\n    '@context': 'https://schema.org',\n    '@type': 'Organization',\n    name: 'BuzzEdge',\n    description: 'Tech tool reviews and comparisons platform',\n    url: process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3002',\n    logo: `${process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3002'}/logo.png`,\n    sameAs: [\n      'https://twitter.com/buzzedge',\n      'https://linkedin.com/company/buzzedge',\n    ],\n  }),\n\n  article: (post: {\n    title: string;\n    excerpt: string;\n    slug: string;\n    category: string;\n    tags: string[];\n    publishedAt: string;\n    featuredImage?: { url: string };\n  }) => ({\n    '@context': 'https://schema.org',\n    '@type': 'Article',\n    headline: post.title,\n    description: post.excerpt,\n    image: post.featuredImage?.url,\n    datePublished: post.publishedAt,\n    dateModified: post.publishedAt,\n    author: {\n      '@type': 'Organization',\n      name: 'BuzzEdge',\n    },\n    publisher: {\n      '@type': 'Organization',\n      name: 'BuzzEdge',\n      logo: {\n        '@type': 'ImageObject',\n        url: `${process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3002'}/logo.png`,\n      },\n    },\n    mainEntityOfPage: {\n      '@type': 'WebPage',\n      '@id': `${process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3002'}/blog/${post.slug}`,\n    },\n    articleSection: post.category,\n    keywords: post.tags.join(', '),\n  }),\n\n  breadcrumb: (items: { name: string; url: string }[]) => ({\n    '@context': 'https://schema.org',\n    '@type': 'BreadcrumbList',\n    itemListElement: items.map((item, index) => ({\n      '@type': 'ListItem',\n      position: index + 1,\n      name: item.name,\n      item: `${process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3002'}${item.url}`,\n    })),\n  }),\n};\n"], "names": [], "mappings": ";;;;;AAiBO,SAAS,iBAAiB,EAC/B,KAAK,EACL,WAAW,EACX,WAAW,EAAE,EACb,YAAY,EACZ,UAAU,iBAAiB,EAC3B,SAAS,SAAS,EAClB,aAAa,EACb,YAAY,EACZ,MAAM,EACN,QAAQ,EACR,OAAO,EAAE,EACT,UAAU,KAAK,EACN;IACT,MAAM,WAAW;IACjB,MAAM,UAAU,6DAAoC;IACpD,MAAM,YAAY,MAAM,QAAQ,CAAC,YAAY,QAAQ,GAAG,MAAM,GAAG,EAAE,UAAU;IAC7E,MAAM,mBAAmB,eAAe,GAAG,UAAU,cAAc,GAAG;IACtE,MAAM,cAAc,QAAQ,UAAU,CAAC,UAAU,UAAU,GAAG,UAAU,SAAS;IAEjF,mDAAmD;IACnD,MAAM,kBAAkB;QACtB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IACD,MAAM,cAAc;WAAI,IAAI,IAAI;eAAI;eAAa;SAAgB;KAAE;IAEnE,MAAM,WAAqB;QACzB,OAAO;QACP;QACA,UAAU,YAAY,IAAI,CAAC;QAC3B,SAAS,SAAS;YAAC;gBAAE,MAAM;YAAO;SAAE,GAAG;YAAC;gBAAE,MAAM;YAAgB;SAAE;QAClE,SAAS;QACT,WAAW;QACX,QAAQ,UAAU,sBAAsB;QAExC,aAAa;QACb,WAAW;YACT,OAAO;YACP;YACA,KAAK;YACL;YACA,QAAQ;gBACN;oBACE,KAAK;oBACL,OAAO;oBACP,QAAQ;oBACR,KAAK;gBACP;aACD;YACD,QAAQ;YACR,MAAM;YACN,GAAI,iBAAiB;gBAAE;YAAc,CAAC;YACtC,GAAI,gBAAgB;gBAAE;YAAa,CAAC;QACtC;QAEA,eAAe;QACf,SAAS;YACP,MAAM;YACN,OAAO;YACP;YACA,QAAQ;gBAAC;aAAY;YACrB,SAAS;YACT,MAAM;QACR;QAEA,uBAAuB;QACvB,OAAO;YACL,kBAAkB,UAAU;YAC5B,GAAI,YAAY;gBAAE,mBAAmB;YAAS,CAAC;YAC/C,GAAI,KAAK,MAAM,GAAG,KAAK;gBAAE,eAAe,KAAK,IAAI,CAAC;YAAM,CAAC;QAC3D;QAEA,gBAAgB;QAChB,YAAY;YACV,WAAW;QACb;QAEA,wEAAwE;QACxE,cAAc;QAId;IACF;IAEA,OAAO;AACT;AAGO,MAAM,aAAa;IACxB,MAAM;QACJ,OAAO;QACP,aAAa;QACb,UAAU;YAAC;YAAgB;YAAoB;YAAY;YAAgB;SAAmB;QAC9F,cAAc;IAChB;IAEA,QAAQ,CAAC,OAAe,cAAwB,CAAC;YAC/C,OAAO,CAAC,oBAAoB,EAAE,MAAM,YAAY,CAAC;YACjD,aAAa,CAAC,MAAM,EAAE,YAAY,cAAc,EAAE,MAAM,0DAA0D,CAAC;YACnH,UAAU;gBAAC;gBAAU;gBAAc;aAAM;YACzC,cAAc,CAAC,UAAU,EAAE,mBAAmB,QAAQ;YACtD,SAAS;QACX,CAAC;IAED,UAAU,CAAC,cAAsB,YAAsB,CAAC;YACtD,OAAO,GAAG,aAAa,iCAAiC,CAAC;YACzD,aAAa,CAAC,QAAQ,EAAE,UAAU,0CAA0C,EAAE,aAAa,WAAW,GAAG,qCAAqC,CAAC;YAC/I,UAAU;gBAAC,aAAa,WAAW;gBAAI;gBAAW;gBAAe;aAAQ;YACzE,cAAc,CAAC,UAAU,EAAE,aAAa,WAAW,GAAG,OAAO,CAAC,QAAQ,MAAM;QAC9E,CAAC;IAED,UAAU,CAAC,OAaL,CAAC;YACL,OAAO,KAAK,GAAG,EAAE,aAAa,KAAK,KAAK;YACxC,aAAa,KAAK,GAAG,EAAE,mBAAmB,KAAK,OAAO;YACtD,UAAU,KAAK,GAAG,EAAE,YAAY,KAAK,IAAI;YACzC,cAAc,CAAC,MAAM,EAAE,KAAK,IAAI,EAAE;YAClC,SAAS,KAAK,aAAa,EAAE;YAC7B,QAAQ;YACR,eAAe,KAAK,WAAW;YAC/B,QAAQ;YACR,UAAU,KAAK,QAAQ;YACvB,MAAM,KAAK,IAAI;QACjB,CAAC;AACH;AAGO,MAAM,yBAAyB;IACpC,SAAS,IAAM,CAAC;YACd,YAAY;YACZ,SAAS;YACT,MAAM;YACN,aAAa;YACb,KAAK,6DAAoC;YACzC,iBAAiB;gBACf,SAAS;gBACT,QAAQ;oBACN,SAAS;oBACT,aAAa,GAAG,6DAAoC,wBAAwB,8BAA8B,CAAC;gBAC7G;gBACA,eAAe;YACjB;QACF,CAAC;IAED,cAAc,IAAM,CAAC;YACnB,YAAY;YACZ,SAAS;YACT,MAAM;YACN,aAAa;YACb,KAAK,6DAAoC;YACzC,MAAM,GAAG,6DAAoC,wBAAwB,SAAS,CAAC;YAC/E,QAAQ;gBACN;gBACA;aACD;QACH,CAAC;IAED,SAAS,CAAC,OAQJ,CAAC;YACL,YAAY;YACZ,SAAS;YACT,UAAU,KAAK,KAAK;YACpB,aAAa,KAAK,OAAO;YACzB,OAAO,KAAK,aAAa,EAAE;YAC3B,eAAe,KAAK,WAAW;YAC/B,cAAc,KAAK,WAAW;YAC9B,QAAQ;gBACN,SAAS;gBACT,MAAM;YACR;YACA,WAAW;gBACT,SAAS;gBACT,MAAM;gBACN,MAAM;oBACJ,SAAS;oBACT,KAAK,GAAG,6DAAoC,wBAAwB,SAAS,CAAC;gBAChF;YACF;YACA,kBAAkB;gBAChB,SAAS;gBACT,OAAO,GAAG,6DAAoC,wBAAwB,MAAM,EAAE,KAAK,IAAI,EAAE;YAC3F;YACA,gBAAgB,KAAK,QAAQ;YAC7B,UAAU,KAAK,IAAI,CAAC,IAAI,CAAC;QAC3B,CAAC;IAED,YAAY,CAAC,QAA2C,CAAC;YACvD,YAAY;YACZ,SAAS;YACT,iBAAiB,MAAM,GAAG,CAAC,CAAC,MAAM,QAAU,CAAC;oBAC3C,SAAS;oBACT,UAAU,QAAQ;oBAClB,MAAM,KAAK,IAAI;oBACf,MAAM,GAAG,6DAAoC,0BAA0B,KAAK,GAAG,EAAE;gBACnF,CAAC;QACH,CAAC;AACH", "debugId": null}}, {"offset": {"line": 1006, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Development/BuzzEdge/buzzedge-frontend/src/app/category/%5Bslug%5D/page.tsx"], "sourcesContent": ["import { notFound } from \"next/navigation\";\nimport Link from \"next/link\";\nimport { Header } from \"@/components/Header\";\nimport { Footer } from \"@/components/Footer\";\nimport { BlogCard } from \"@/components/BlogCard\";\nimport { BlogService } from \"@/services/blogService\";\nimport { BlogPostSummary } from \"@/types/blog\";\nimport {\n  generateMetadata as generateSEOMetadata,\n  seoConfigs,\n  generateStructuredData,\n} from \"@/components/SEOHead\";\n\ninterface CategoryPageProps {\n  params: {\n    slug: string;\n  };\n  searchParams: {\n    page?: string;\n  };\n}\n\n// Category metadata mapping\nconst categoryMetadata = {\n  \"ai-tools\": {\n    name: \"AI Tools\",\n    description:\n      \"Comprehensive reviews and comparisons of the latest AI tools and platforms. Discover the best artificial intelligence software for your needs.\",\n    icon: \"🤖\",\n    color: \"from-blue-500 to-purple-600\",\n    bgColor: \"bg-blue-50\",\n    textColor: \"text-blue-700\",\n  },\n  productivity: {\n    name: \"Productivity\",\n    description:\n      \"Boost your efficiency with our reviews of the best productivity tools, apps, and workflows. Streamline your work and get more done.\",\n    icon: \"⚡\",\n    color: \"from-green-500 to-teal-600\",\n    bgColor: \"bg-green-50\",\n    textColor: \"text-green-700\",\n  },\n  \"developer-tools\": {\n    name: \"Developer Tools\",\n    description:\n      \"In-depth reviews of development tools, IDEs, frameworks, and platforms for software engineers and programmers.\",\n    icon: \"💻\",\n    color: \"from-purple-500 to-pink-600\",\n    bgColor: \"bg-purple-50\",\n    textColor: \"text-purple-700\",\n  },\n  \"design-tools\": {\n    name: \"Design Tools\",\n    description:\n      \"Explore the best design tools, software, and platforms for UI/UX designers, graphic designers, and creative professionals.\",\n    icon: \"🎨\",\n    color: \"from-pink-500 to-rose-600\",\n    bgColor: \"bg-pink-50\",\n    textColor: \"text-pink-700\",\n  },\n};\n\nasync function getCategoryPosts(\n  categorySlug: string,\n  page: number = 1\n): Promise<{\n  posts: BlogPostSummary[];\n  category: string;\n  pagination: {\n    currentPage: number;\n    totalPages: number;\n    totalPosts: number;\n    hasNext: boolean;\n    hasPrev: boolean;\n  };\n} | null> {\n  try {\n    const response = await BlogService.getPostsByCategory(\n      categorySlug,\n      page,\n      12\n    );\n    return response.data;\n  } catch (error) {\n    console.error(\"Failed to fetch category posts:\", error);\n    return null;\n  }\n}\n\n// Generate metadata for SEO\nexport async function generateMetadata({\n  params,\n}: {\n  params: { slug: string };\n}) {\n  const { slug } = await params;\n  const categoryInfo = categoryMetadata[slug as keyof typeof categoryMetadata];\n\n  if (!categoryInfo) {\n    return generateSEOMetadata({\n      title: \"Category Not Found\",\n      description: \"The requested category could not be found.\",\n      noIndex: true,\n    });\n  }\n\n  // Get post count for this category\n  const data = await getCategoryPosts(slug, 1);\n  const postCount = data?.pagination.totalPosts || 0;\n\n  return generateSEOMetadata(seoConfigs.category(categoryInfo.name, postCount));\n}\n\nexport default async function CategoryPage({\n  params,\n  searchParams,\n}: CategoryPageProps) {\n  const { slug } = await params;\n  const { page } = await searchParams;\n  const currentPage = parseInt(page || \"1\", 10);\n\n  // Get category metadata\n  const categoryInfo = categoryMetadata[slug as keyof typeof categoryMetadata];\n\n  if (!categoryInfo) {\n    notFound();\n  }\n\n  const data = await getCategoryPosts(slug, currentPage);\n\n  if (!data) {\n    notFound();\n  }\n\n  const { posts, pagination } = data;\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <Header />\n\n      <main className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {/* Breadcrumb */}\n        <nav className=\"mb-8\">\n          <ol className=\"flex items-center space-x-2 text-sm text-gray-500\">\n            <li>\n              <Link href=\"/\" className=\"hover:text-gray-700\">\n                Home\n              </Link>\n            </li>\n            <li>/</li>\n            <li className=\"text-gray-900 font-medium\">{categoryInfo.name}</li>\n          </ol>\n        </nav>\n\n        {/* Category Header */}\n        <div className={`${categoryInfo.bgColor} rounded-lg p-8 mb-12`}>\n          <div className=\"max-w-3xl\">\n            <div className=\"flex items-center mb-4\">\n              <span className=\"text-4xl mr-4\">{categoryInfo.icon}</span>\n              <h1 className=\"text-4xl md:text-5xl font-bold text-gray-900\">\n                {categoryInfo.name}\n              </h1>\n            </div>\n            <p className=\"text-xl text-gray-600 leading-relaxed mb-6\">\n              {categoryInfo.description}\n            </p>\n            <div className=\"flex items-center space-x-4\">\n              <span\n                className={`px-4 py-2 ${categoryInfo.textColor} bg-white rounded-full text-sm font-medium shadow-sm`}\n              >\n                {pagination.totalPosts}{\" \"}\n                {pagination.totalPosts === 1 ? \"Review\" : \"Reviews\"}\n              </span>\n              <span className=\"text-gray-500 text-sm\">\n                Updated regularly with the latest tools\n              </span>\n            </div>\n          </div>\n        </div>\n\n        {/* Posts Grid */}\n        {posts.length > 0 ? (\n          <>\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12\">\n              {posts.map((post) => (\n                <BlogCard key={post._id} post={post} />\n              ))}\n            </div>\n\n            {/* Pagination */}\n            {pagination.totalPages > 1 && (\n              <div className=\"flex items-center justify-between border-t border-gray-200 bg-white px-4 py-3 sm:px-6 rounded-lg\">\n                <div className=\"flex flex-1 justify-between sm:hidden\">\n                  {pagination.hasPrev && (\n                    <Link\n                      href={`/category/${slug}?page=${\n                        pagination.currentPage - 1\n                      }`}\n                      className=\"relative inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50\"\n                    >\n                      Previous\n                    </Link>\n                  )}\n                  {pagination.hasNext && (\n                    <Link\n                      href={`/category/${slug}?page=${\n                        pagination.currentPage + 1\n                      }`}\n                      className=\"relative ml-3 inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50\"\n                    >\n                      Next\n                    </Link>\n                  )}\n                </div>\n                <div className=\"hidden sm:flex sm:flex-1 sm:items-center sm:justify-between\">\n                  <div>\n                    <p className=\"text-sm text-gray-700\">\n                      Showing{\" \"}\n                      <span className=\"font-medium\">\n                        {(pagination.currentPage - 1) * 12 + 1}\n                      </span>{\" \"}\n                      to{\" \"}\n                      <span className=\"font-medium\">\n                        {Math.min(\n                          pagination.currentPage * 12,\n                          pagination.totalPosts\n                        )}\n                      </span>{\" \"}\n                      of{\" \"}\n                      <span className=\"font-medium\">\n                        {pagination.totalPosts}\n                      </span>{\" \"}\n                      results\n                    </p>\n                  </div>\n                  <div>\n                    <nav\n                      className=\"isolate inline-flex -space-x-px rounded-md shadow-sm\"\n                      aria-label=\"Pagination\"\n                    >\n                      {pagination.hasPrev && (\n                        <Link\n                          href={`/category/${slug}?page=${\n                            pagination.currentPage - 1\n                          }`}\n                          className=\"relative inline-flex items-center rounded-l-md px-2 py-2 text-gray-400 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0\"\n                        >\n                          <span className=\"sr-only\">Previous</span>\n                          <svg\n                            className=\"h-5 w-5\"\n                            viewBox=\"0 0 20 20\"\n                            fill=\"currentColor\"\n                            aria-hidden=\"true\"\n                          >\n                            <path\n                              fillRule=\"evenodd\"\n                              d=\"M12.79 5.23a.75.75 0 01-.02 1.06L8.832 10l3.938 3.71a.75.75 0 11-1.04 1.08l-4.5-4.25a.75.75 0 010-1.08l4.5-4.25a.75.75 0 011.06.02z\"\n                              clipRule=\"evenodd\"\n                            />\n                          </svg>\n                        </Link>\n                      )}\n\n                      {/* Page numbers */}\n                      {Array.from(\n                        { length: Math.min(5, pagination.totalPages) },\n                        (_, i) => {\n                          const pageNum =\n                            Math.max(1, pagination.currentPage - 2) + i;\n                          if (pageNum > pagination.totalPages) return null;\n\n                          return (\n                            <Link\n                              key={pageNum}\n                              href={`/category/${slug}?page=${pageNum}`}\n                              className={`relative inline-flex items-center px-4 py-2 text-sm font-semibold ${\n                                pageNum === pagination.currentPage\n                                  ? \"z-10 bg-blue-600 text-white focus:z-20 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600\"\n                                  : \"text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0\"\n                              }`}\n                            >\n                              {pageNum}\n                            </Link>\n                          );\n                        }\n                      )}\n\n                      {pagination.hasNext && (\n                        <Link\n                          href={`/category/${slug}?page=${\n                            pagination.currentPage + 1\n                          }`}\n                          className=\"relative inline-flex items-center rounded-r-md px-2 py-2 text-gray-400 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0\"\n                        >\n                          <span className=\"sr-only\">Next</span>\n                          <svg\n                            className=\"h-5 w-5\"\n                            viewBox=\"0 0 20 20\"\n                            fill=\"currentColor\"\n                            aria-hidden=\"true\"\n                          >\n                            <path\n                              fillRule=\"evenodd\"\n                              d=\"M7.21 14.77a.75.75 0 01.02-1.06L11.168 10 7.23 6.29a.75.75 0 111.04-1.08l4.5 4.25a.75.75 0 010 1.08l-4.5 4.25a.75.75 0 01-1.06-.02z\"\n                              clipRule=\"evenodd\"\n                            />\n                          </svg>\n                        </Link>\n                      )}\n                    </nav>\n                  </div>\n                </div>\n              </div>\n            )}\n          </>\n        ) : (\n          <div className=\"text-center py-12\">\n            <div className=\"max-w-md mx-auto\">\n              <span className=\"text-6xl\">{categoryInfo.icon}</span>\n              <h3 className=\"mt-4 text-lg font-medium text-gray-900\">\n                No posts yet\n              </h3>\n              <p className=\"mt-2 text-gray-500\">\n                We're working on adding more {categoryInfo.name.toLowerCase()}{\" \"}\n                reviews. Check back soon!\n              </p>\n              <div className=\"mt-6\">\n                <Link\n                  href=\"/\"\n                  className=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700\"\n                >\n                  Browse all posts\n                </Link>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Related Categories */}\n        <div className=\"mt-16 border-t border-gray-200 pt-12\">\n          <h2 className=\"text-2xl font-bold text-gray-900 mb-6\">\n            Explore Other Categories\n          </h2>\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n            {Object.entries(categoryMetadata)\n              .filter(([key]) => key !== slug)\n              .map(([key, info]) => (\n                <Link\n                  key={key}\n                  href={`/category/${key}`}\n                  className={`${info.bgColor} rounded-lg p-6 hover:shadow-md transition-shadow`}\n                >\n                  <div className=\"flex items-center mb-3\">\n                    <span className=\"text-2xl mr-3\">{info.icon}</span>\n                    <h3 className=\"text-lg font-semibold text-gray-900\">\n                      {info.name}\n                    </h3>\n                  </div>\n                  <p className=\"text-gray-600 text-sm\">{info.description}</p>\n                </Link>\n              ))}\n          </div>\n        </div>\n      </main>\n\n      <Footer />\n    </div>\n  );\n}\n\n// Generate static params for better performance\nexport async function generateStaticParams() {\n  return Object.keys(categoryMetadata).map((slug) => ({\n    slug,\n  }));\n}\n"], "names": [], "mappings": ";;;;;;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AAEA;;;;;;;;;AAeA,4BAA4B;AAC5B,MAAM,mBAAmB;IACvB,YAAY;QACV,MAAM;QACN,aACE;QACF,MAAM;QACN,OAAO;QACP,SAAS;QACT,WAAW;IACb;IACA,cAAc;QACZ,MAAM;QACN,aACE;QACF,MAAM;QACN,OAAO;QACP,SAAS;QACT,WAAW;IACb;IACA,mBAAmB;QACjB,MAAM;QACN,aACE;QACF,MAAM;QACN,OAAO;QACP,SAAS;QACT,WAAW;IACb;IACA,gBAAgB;QACd,MAAM;QACN,aACE;QACF,MAAM;QACN,OAAO;QACP,SAAS;QACT,WAAW;IACb;AACF;AAEA,eAAe,iBACb,YAAoB,EACpB,OAAe,CAAC;IAYhB,IAAI;QACF,MAAM,WAAW,MAAM,8HAAA,CAAA,cAAW,CAAC,kBAAkB,CACnD,cACA,MACA;QAEF,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,OAAO;IACT;AACF;AAGO,eAAe,iBAAiB,EACrC,MAAM,EAGP;IACC,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM;IACvB,MAAM,eAAe,gBAAgB,CAAC,KAAsC;IAE5E,IAAI,CAAC,cAAc;QACjB,OAAO,CAAA,GAAA,6HAAA,CAAA,mBAAmB,AAAD,EAAE;YACzB,OAAO;YACP,aAAa;YACb,SAAS;QACX;IACF;IAEA,mCAAmC;IACnC,MAAM,OAAO,MAAM,iBAAiB,MAAM;IAC1C,MAAM,YAAY,MAAM,WAAW,cAAc;IAEjD,OAAO,CAAA,GAAA,6HAAA,CAAA,mBAAmB,AAAD,EAAE,6HAAA,CAAA,aAAU,CAAC,QAAQ,CAAC,aAAa,IAAI,EAAE;AACpE;AAEe,eAAe,aAAa,EACzC,MAAM,EACN,YAAY,EACM;IAClB,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM;IACvB,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM;IACvB,MAAM,cAAc,SAAS,QAAQ,KAAK;IAE1C,wBAAwB;IACxB,MAAM,eAAe,gBAAgB,CAAC,KAAsC;IAE5E,IAAI,CAAC,cAAc;QACjB,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD;IACT;IAEA,MAAM,OAAO,MAAM,iBAAiB,MAAM;IAE1C,IAAI,CAAC,MAAM;QACT,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD;IACT;IAEA,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG;IAE9B,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,4HAAA,CAAA,SAAM;;;;;0BAEP,8OAAC;gBAAK,WAAU;;kCAEd,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAG,WAAU;;8CACZ,8OAAC;8CACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAI,WAAU;kDAAsB;;;;;;;;;;;8CAIjD,8OAAC;8CAAG;;;;;;8CACJ,8OAAC;oCAAG,WAAU;8CAA6B,aAAa,IAAI;;;;;;;;;;;;;;;;;kCAKhE,8OAAC;wBAAI,WAAW,GAAG,aAAa,OAAO,CAAC,qBAAqB,CAAC;kCAC5D,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;sDAAiB,aAAa,IAAI;;;;;;sDAClD,8OAAC;4CAAG,WAAU;sDACX,aAAa,IAAI;;;;;;;;;;;;8CAGtB,8OAAC;oCAAE,WAAU;8CACV,aAAa,WAAW;;;;;;8CAE3B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,WAAW,CAAC,UAAU,EAAE,aAAa,SAAS,CAAC,oDAAoD,CAAC;;gDAEnG,WAAW,UAAU;gDAAE;gDACvB,WAAW,UAAU,KAAK,IAAI,WAAW;;;;;;;sDAE5C,8OAAC;4CAAK,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;;;;;;oBAQ7C,MAAM,MAAM,GAAG,kBACd;;0CACE,8OAAC;gCAAI,WAAU;0CACZ,MAAM,GAAG,CAAC,CAAC,qBACV,8OAAC,8HAAA,CAAA,WAAQ;wCAAgB,MAAM;uCAAhB,KAAK,GAAG;;;;;;;;;;4BAK1B,WAAW,UAAU,GAAG,mBACvB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;4CACZ,WAAW,OAAO,kBACjB,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAM,CAAC,UAAU,EAAE,KAAK,MAAM,EAC5B,WAAW,WAAW,GAAG,GACzB;gDACF,WAAU;0DACX;;;;;;4CAIF,WAAW,OAAO,kBACjB,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAM,CAAC,UAAU,EAAE,KAAK,MAAM,EAC5B,WAAW,WAAW,GAAG,GACzB;gDACF,WAAU;0DACX;;;;;;;;;;;;kDAKL,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;0DACC,cAAA,8OAAC;oDAAE,WAAU;;wDAAwB;wDAC3B;sEACR,8OAAC;4DAAK,WAAU;sEACb,CAAC,WAAW,WAAW,GAAG,CAAC,IAAI,KAAK;;;;;;wDAC/B;wDAAI;wDACT;sEACH,8OAAC;4DAAK,WAAU;sEACb,KAAK,GAAG,CACP,WAAW,WAAW,GAAG,IACzB,WAAW,UAAU;;;;;;wDAEjB;wDAAI;wDACT;sEACH,8OAAC;4DAAK,WAAU;sEACb,WAAW,UAAU;;;;;;wDAChB;wDAAI;;;;;;;;;;;;0DAIhB,8OAAC;0DACC,cAAA,8OAAC;oDACC,WAAU;oDACV,cAAW;;wDAEV,WAAW,OAAO,kBACjB,8OAAC,4JAAA,CAAA,UAAI;4DACH,MAAM,CAAC,UAAU,EAAE,KAAK,MAAM,EAC5B,WAAW,WAAW,GAAG,GACzB;4DACF,WAAU;;8EAEV,8OAAC;oEAAK,WAAU;8EAAU;;;;;;8EAC1B,8OAAC;oEACC,WAAU;oEACV,SAAQ;oEACR,MAAK;oEACL,eAAY;8EAEZ,cAAA,8OAAC;wEACC,UAAS;wEACT,GAAE;wEACF,UAAS;;;;;;;;;;;;;;;;;wDAOhB,MAAM,IAAI,CACT;4DAAE,QAAQ,KAAK,GAAG,CAAC,GAAG,WAAW,UAAU;wDAAE,GAC7C,CAAC,GAAG;4DACF,MAAM,UACJ,KAAK,GAAG,CAAC,GAAG,WAAW,WAAW,GAAG,KAAK;4DAC5C,IAAI,UAAU,WAAW,UAAU,EAAE,OAAO;4DAE5C,qBACE,8OAAC,4JAAA,CAAA,UAAI;gEAEH,MAAM,CAAC,UAAU,EAAE,KAAK,MAAM,EAAE,SAAS;gEACzC,WAAW,CAAC,kEAAkE,EAC5E,YAAY,WAAW,WAAW,GAC9B,uJACA,oGACJ;0EAED;+DARI;;;;;wDAWX;wDAGD,WAAW,OAAO,kBACjB,8OAAC,4JAAA,CAAA,UAAI;4DACH,MAAM,CAAC,UAAU,EAAE,KAAK,MAAM,EAC5B,WAAW,WAAW,GAAG,GACzB;4DACF,WAAU;;8EAEV,8OAAC;oEAAK,WAAU;8EAAU;;;;;;8EAC1B,8OAAC;oEACC,WAAU;oEACV,SAAQ;oEACR,MAAK;oEACL,eAAY;8EAEZ,cAAA,8OAAC;wEACC,UAAS;wEACT,GAAE;wEACF,UAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;qDAY7B,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAK,WAAU;8CAAY,aAAa,IAAI;;;;;;8CAC7C,8OAAC;oCAAG,WAAU;8CAAyC;;;;;;8CAGvD,8OAAC;oCAAE,WAAU;;wCAAqB;wCACF,aAAa,IAAI,CAAC,WAAW;wCAAI;wCAAI;;;;;;;8CAGrE,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;kCAST,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAwC;;;;;;0CAGtD,8OAAC;gCAAI,WAAU;0CACZ,OAAO,OAAO,CAAC,kBACb,MAAM,CAAC,CAAC,CAAC,IAAI,GAAK,QAAQ,MAC1B,GAAG,CAAC,CAAC,CAAC,KAAK,KAAK,iBACf,8OAAC,4JAAA,CAAA,UAAI;wCAEH,MAAM,CAAC,UAAU,EAAE,KAAK;wCACxB,WAAW,GAAG,KAAK,OAAO,CAAC,iDAAiD,CAAC;;0DAE7E,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;kEAAiB,KAAK,IAAI;;;;;;kEAC1C,8OAAC;wDAAG,WAAU;kEACX,KAAK,IAAI;;;;;;;;;;;;0DAGd,8OAAC;gDAAE,WAAU;0DAAyB,KAAK,WAAW;;;;;;;uCAVjD;;;;;;;;;;;;;;;;;;;;;;0BAiBjB,8OAAC,4HAAA,CAAA,SAAM;;;;;;;;;;;AAGb;AAGO,eAAe;IACpB,OAAO,OAAO,IAAI,CAAC,kBAAkB,GAAG,CAAC,CAAC,OAAS,CAAC;YAClD;QACF,CAAC;AACH", "debugId": null}}]}