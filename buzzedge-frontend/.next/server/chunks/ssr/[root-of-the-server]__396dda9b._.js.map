{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Development/BuzzEdge/buzzedge-frontend/src/services/blogService.ts"], "sourcesContent": ["import { apiClient } from \"@/lib/api\";\nimport {\n  BlogListResponse,\n  BlogPostResponse,\n  SearchResponse,\n  CategoryResponse,\n  BlogPostSummary,\n} from \"@/types/blog\";\n\nexport class BlogService {\n  /**\n   * Get all published blog posts with pagination\n   */\n  static async getAllPosts(\n    page: number = 1,\n    limit: number = 10\n  ): Promise<BlogListResponse> {\n    return apiClient.get<BlogListResponse>(\n      \"/api/blogs\",\n      {\n        page: page.toString(),\n        limit: limit.toString(),\n      },\n      { cache: true, ttl: 300 } // 5 minutes cache\n    );\n  }\n\n  /**\n   * Get a single blog post by slug\n   */\n  static async getPostBySlug(slug: string): Promise<BlogPostResponse> {\n    return apiClient.get<BlogPostResponse>(\n      `/api/blogs/${slug}`,\n      undefined,\n      { cache: true, ttl: 600 } // 10 minutes cache\n    );\n  }\n\n  /**\n   * Search blog posts\n   */\n  static async searchPosts(\n    query: string,\n    page: number = 1,\n    limit: number = 10\n  ): Promise<SearchResponse> {\n    return apiClient.get<SearchResponse>(\n      \"/api/blogs/search\",\n      {\n        q: query,\n        page: page.toString(),\n        limit: limit.toString(),\n      },\n      { cache: true, ttl: 60 } // 1 minute cache for search\n    );\n  }\n\n  /**\n   * Get posts by category\n   */\n  static async getPostsByCategory(\n    category: string,\n    page: number = 1,\n    limit: number = 10\n  ): Promise<CategoryResponse> {\n    return apiClient.get<CategoryResponse>(\n      `/api/blogs/category/${category}`,\n      {\n        page: page.toString(),\n        limit: limit.toString(),\n      },\n      { cache: true, ttl: 300 } // 5 minutes cache\n    );\n  }\n\n  /**\n   * Increment post view count\n   */\n  static async incrementPostViews(slug: string): Promise<{ views: number }> {\n    return apiClient.post(`/api/blogs/${slug}/view`);\n  }\n\n  /**\n   * Get featured posts (most viewed)\n   */\n  static async getFeaturedPosts(limit: number = 5): Promise<BlogPostSummary[]> {\n    const response = await this.getAllPosts(1, limit);\n    return response.data.posts;\n  }\n\n  /**\n   * Get recent posts\n   */\n  static async getRecentPosts(limit: number = 5): Promise<BlogPostSummary[]> {\n    const response = await this.getAllPosts(1, limit);\n    return response.data.posts;\n  }\n}\n\n// Utility functions for client-side data processing\nexport const blogUtils = {\n  /**\n   * Format date for display\n   */\n  formatDate: (dateString: string): string => {\n    const date = new Date(dateString);\n    return date.toLocaleDateString(\"en-US\", {\n      year: \"numeric\",\n      month: \"long\",\n      day: \"numeric\",\n    });\n  },\n\n  /**\n   * Calculate reading time based on content length\n   */\n  calculateReadingTime: (content: string): number => {\n    const wordsPerMinute = 200;\n    const wordCount = content.split(/\\s+/).length;\n    return Math.ceil(wordCount / wordsPerMinute);\n  },\n\n  /**\n   * Get category color class\n   */\n  getCategoryColor: (category: string): string => {\n    switch (category.toLowerCase()) {\n      case \"ai tools\":\n        return \"bg-blue-100 text-blue-800\";\n      case \"productivity\":\n        return \"bg-green-100 text-green-800\";\n      case \"developer tools\":\n        return \"bg-purple-100 text-purple-800\";\n      case \"design tools\":\n        return \"bg-pink-100 text-pink-800\";\n      default:\n        return \"bg-gray-100 text-gray-800\";\n    }\n  },\n\n  /**\n   * Generate SEO-friendly URL slug\n   */\n  generateSlug: (title: string): string => {\n    return title\n      .toLowerCase()\n      .replace(/[^a-z0-9]+/g, \"-\")\n      .replace(/(^-|-$)/g, \"\");\n  },\n\n  /**\n   * Truncate text to specified length\n   */\n  truncateText: (text: string, maxLength: number): string => {\n    if (text.length <= maxLength) return text;\n    return text.substring(0, maxLength).replace(/\\s+\\S*$/, \"\") + \"...\";\n  },\n};\n"], "names": [], "mappings": ";;;;AAAA;;AASO,MAAM;IACX;;GAEC,GACD,aAAa,YACX,OAAe,CAAC,EAChB,QAAgB,EAAE,EACS;QAC3B,OAAO,iHAAA,CAAA,YAAS,CAAC,GAAG,CAClB,cACA;YACE,MAAM,KAAK,QAAQ;YACnB,OAAO,MAAM,QAAQ;QACvB,GACA;YAAE,OAAO;YAAM,KAAK;QAAI,EAAE,kBAAkB;;IAEhD;IAEA;;GAEC,GACD,aAAa,cAAc,IAAY,EAA6B;QAClE,OAAO,iHAAA,CAAA,YAAS,CAAC,GAAG,CAClB,CAAC,WAAW,EAAE,MAAM,EACpB,WACA;YAAE,OAAO;YAAM,KAAK;QAAI,EAAE,mBAAmB;;IAEjD;IAEA;;GAEC,GACD,aAAa,YACX,KAAa,EACb,OAAe,CAAC,EAChB,QAAgB,EAAE,EACO;QACzB,OAAO,iHAAA,CAAA,YAAS,CAAC,GAAG,CAClB,qBACA;YACE,GAAG;YACH,MAAM,KAAK,QAAQ;YACnB,OAAO,MAAM,QAAQ;QACvB,GACA;YAAE,OAAO;YAAM,KAAK;QAAG,EAAE,4BAA4B;;IAEzD;IAEA;;GAEC,GACD,aAAa,mBACX,QAAgB,EAChB,OAAe,CAAC,EAChB,QAAgB,EAAE,EACS;QAC3B,OAAO,iHAAA,CAAA,YAAS,CAAC,GAAG,CAClB,CAAC,oBAAoB,EAAE,UAAU,EACjC;YACE,MAAM,KAAK,QAAQ;YACnB,OAAO,MAAM,QAAQ;QACvB,GACA;YAAE,OAAO;YAAM,KAAK;QAAI,EAAE,kBAAkB;;IAEhD;IAEA;;GAEC,GACD,aAAa,mBAAmB,IAAY,EAA8B;QACxE,OAAO,iHAAA,CAAA,YAAS,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE,KAAK,KAAK,CAAC;IACjD;IAEA;;GAEC,GACD,aAAa,iBAAiB,QAAgB,CAAC,EAA8B;QAC3E,MAAM,WAAW,MAAM,IAAI,CAAC,WAAW,CAAC,GAAG;QAC3C,OAAO,SAAS,IAAI,CAAC,KAAK;IAC5B;IAEA;;GAEC,GACD,aAAa,eAAe,QAAgB,CAAC,EAA8B;QACzE,MAAM,WAAW,MAAM,IAAI,CAAC,WAAW,CAAC,GAAG;QAC3C,OAAO,SAAS,IAAI,CAAC,KAAK;IAC5B;AACF;AAGO,MAAM,YAAY;IACvB;;GAEC,GACD,YAAY,CAAC;QACX,MAAM,OAAO,IAAI,KAAK;QACtB,OAAO,KAAK,kBAAkB,CAAC,SAAS;YACtC,MAAM;YACN,OAAO;YACP,KAAK;QACP;IACF;IAEA;;GAEC,GACD,sBAAsB,CAAC;QACrB,MAAM,iBAAiB;QACvB,MAAM,YAAY,QAAQ,KAAK,CAAC,OAAO,MAAM;QAC7C,OAAO,KAAK,IAAI,CAAC,YAAY;IAC/B;IAEA;;GAEC,GACD,kBAAkB,CAAC;QACjB,OAAQ,SAAS,WAAW;YAC1B,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA;;GAEC,GACD,cAAc,CAAC;QACb,OAAO,MACJ,WAAW,GACX,OAAO,CAAC,eAAe,KACvB,OAAO,CAAC,YAAY;IACzB;IAEA;;GAEC,GACD,cAAc,CAAC,MAAc;QAC3B,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;QACrC,OAAO,KAAK,SAAS,CAAC,GAAG,WAAW,OAAO,CAAC,WAAW,MAAM;IAC/D;AACF", "debugId": null}}, {"offset": {"line": 154, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Development/BuzzEdge/buzzedge-frontend/src/app/admin/posts/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport Link from 'next/link';\nimport { BlogService } from '@/services/blogService';\nimport { BlogPostSummary } from '@/types/blog';\n\nexport default function AdminPostsPage() {\n  const [posts, setPosts] = useState<BlogPostSummary[]>([]);\n  const [isLoading, setIsLoading] = useState(true);\n  const [currentPage, setCurrentPage] = useState(1);\n  const [totalPages, setTotalPages] = useState(1);\n  const router = useRouter();\n\n  useEffect(() => {\n    // Check if user is authenticated\n    const isAdmin = localStorage.getItem('buzzedge_admin');\n    if (!isAdmin) {\n      router.push('/admin/login');\n      return;\n    }\n    \n    loadPosts();\n  }, [router, currentPage]);\n\n  const loadPosts = async () => {\n    try {\n      setIsLoading(true);\n      const response = await BlogService.getAllPosts(currentPage, 10);\n      setPosts(response.data.posts);\n      setTotalPages(Math.ceil(response.data.pagination.totalPosts / 10));\n    } catch (error) {\n      console.error('Failed to load posts:', error);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleLogout = () => {\n    localStorage.removeItem('buzzedge_admin');\n    router.push('/admin/login');\n  };\n\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen bg-gray-50\">\n        <AdminHeader onLogout={handleLogout} />\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n          <div className=\"animate-pulse space-y-4\">\n            {[...Array(5)].map((_, i) => (\n              <div key={i} className=\"bg-white p-6 rounded-lg shadow\">\n                <div className=\"h-6 bg-gray-300 rounded w-3/4 mb-2\"></div>\n                <div className=\"h-4 bg-gray-300 rounded w-1/2\"></div>\n              </div>\n            ))}\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <AdminHeader onLogout={handleLogout} />\n      \n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {/* Page header */}\n        <div className=\"mb-8 flex justify-between items-center\">\n          <div>\n            <h1 className=\"text-2xl font-bold text-gray-900\">Manage Posts</h1>\n            <p className=\"mt-1 text-sm text-gray-500\">\n              View and manage all blog posts\n            </p>\n          </div>\n          <button className=\"bg-blue-600 text-white px-4 py-2 rounded-md text-sm hover:bg-blue-700\">\n            Create New Post\n          </button>\n        </div>\n\n        {/* Posts list */}\n        <div className=\"bg-white shadow rounded-lg overflow-hidden\">\n          <div className=\"px-4 py-5 sm:p-6\">\n            <div className=\"space-y-4\">\n              {posts.map((post) => (\n                <div key={post._id} className=\"border-b border-gray-200 pb-4 last:border-b-0\">\n                  <div className=\"flex items-center justify-between\">\n                    <div className=\"flex items-center space-x-4\">\n                      <img\n                        className=\"h-16 w-16 rounded object-cover\"\n                        src={post.featuredImage.url}\n                        alt={post.featuredImage.alt}\n                      />\n                      <div>\n                        <h3 className=\"text-lg font-medium text-gray-900\">\n                          {post.title}\n                        </h3>\n                        <p className=\"text-sm text-gray-500\">\n                          {post.category} • {new Date(post.publishedAt).toLocaleDateString()} • {post.analytics?.views || 0} views\n                        </p>\n                        <p className=\"text-sm text-gray-600 mt-1 line-clamp-2\">\n                          {post.excerpt}\n                        </p>\n                      </div>\n                    </div>\n                    <div className=\"flex items-center space-x-2\">\n                      <Link\n                        href={`/blog/${post.slug}`}\n                        target=\"_blank\"\n                        className=\"text-blue-600 hover:text-blue-500 text-sm\"\n                      >\n                        View\n                      </Link>\n                      <button className=\"text-gray-600 hover:text-gray-500 text-sm\">\n                        Edit\n                      </button>\n                      <button className=\"text-red-600 hover:text-red-500 text-sm\">\n                        Delete\n                      </button>\n                    </div>\n                  </div>\n                </div>\n              ))}\n            </div>\n\n            {/* Pagination */}\n            {totalPages > 1 && (\n              <div className=\"mt-6 flex justify-center\">\n                <nav className=\"flex space-x-2\">\n                  <button\n                    onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}\n                    disabled={currentPage === 1}\n                    className=\"px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50\"\n                  >\n                    Previous\n                  </button>\n                  \n                  {[...Array(totalPages)].map((_, i) => (\n                    <button\n                      key={i + 1}\n                      onClick={() => setCurrentPage(i + 1)}\n                      className={`px-3 py-2 text-sm font-medium rounded-md ${\n                        currentPage === i + 1\n                          ? 'bg-blue-600 text-white'\n                          : 'text-gray-500 bg-white border border-gray-300 hover:bg-gray-50'\n                      }`}\n                    >\n                      {i + 1}\n                    </button>\n                  ))}\n                  \n                  <button\n                    onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}\n                    disabled={currentPage === totalPages}\n                    className=\"px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50\"\n                  >\n                    Next\n                  </button>\n                </nav>\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n\n// Reusable Admin Header Component\nfunction AdminHeader({ onLogout }: { onLogout: () => void }) {\n  return (\n    <div className=\"bg-white shadow-sm border-b border-gray-200\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between items-center h-16\">\n          <div className=\"flex items-center\">\n            <Link href=\"/admin/dashboard\" className=\"flex items-center\">\n              <div className=\"w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center text-white font-bold text-lg mr-2\">\n                B\n              </div>\n              <span className=\"text-xl font-bold text-gray-900\">BuzzEdge Admin</span>\n            </Link>\n            <nav className=\"ml-8 flex space-x-4\">\n              <Link\n                href=\"/admin/dashboard\"\n                className=\"text-gray-500 hover:text-gray-700 px-3 py-2 rounded-md text-sm font-medium\"\n              >\n                Dashboard\n              </Link>\n              <Link\n                href=\"/admin/posts\"\n                className=\"text-blue-600 px-3 py-2 rounded-md text-sm font-medium\"\n              >\n                Posts\n              </Link>\n            </nav>\n          </div>\n          <div className=\"flex items-center space-x-4\">\n            <Link\n              href=\"/\"\n              target=\"_blank\"\n              className=\"text-gray-500 hover:text-gray-700\"\n            >\n              View Site\n            </Link>\n            <button\n              onClick={onLogout}\n              className=\"bg-red-600 text-white px-4 py-2 rounded-md text-sm hover:bg-red-700\"\n            >\n              Logout\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAQe,SAAS;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAqB,EAAE;IACxD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,iCAAiC;QACjC,MAAM,UAAU,aAAa,OAAO,CAAC;QACrC,IAAI,CAAC,SAAS;YACZ,OAAO,IAAI,CAAC;YACZ;QACF;QAEA;IACF,GAAG;QAAC;QAAQ;KAAY;IAExB,MAAM,YAAY;QAChB,IAAI;YACF,aAAa;YACb,MAAM,WAAW,MAAM,8HAAA,CAAA,cAAW,CAAC,WAAW,CAAC,aAAa;YAC5D,SAAS,SAAS,IAAI,CAAC,KAAK;YAC5B,cAAc,KAAK,IAAI,CAAC,SAAS,IAAI,CAAC,UAAU,CAAC,UAAU,GAAG;QAChE,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;QACzC,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,eAAe;QACnB,aAAa,UAAU,CAAC;QACxB,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,WAAW;QACb,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAY,UAAU;;;;;;8BACvB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACZ;+BAAI,MAAM;yBAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC;gCAAY,WAAU;;kDACrB,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAI,WAAU;;;;;;;+BAFP;;;;;;;;;;;;;;;;;;;;;IAStB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAY,UAAU;;;;;;0BAEvB,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAmC;;;;;;kDACjD,8OAAC;wCAAE,WAAU;kDAA6B;;;;;;;;;;;;0CAI5C,8OAAC;gCAAO,WAAU;0CAAwE;;;;;;;;;;;;kCAM5F,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACZ,MAAM,GAAG,CAAC,CAAC,qBACV,8OAAC;4CAAmB,WAAU;sDAC5B,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEACC,WAAU;gEACV,KAAK,KAAK,aAAa,CAAC,GAAG;gEAC3B,KAAK,KAAK,aAAa,CAAC,GAAG;;;;;;0EAE7B,8OAAC;;kFACC,8OAAC;wEAAG,WAAU;kFACX,KAAK,KAAK;;;;;;kFAEb,8OAAC;wEAAE,WAAU;;4EACV,KAAK,QAAQ;4EAAC;4EAAI,IAAI,KAAK,KAAK,WAAW,EAAE,kBAAkB;4EAAG;4EAAI,KAAK,SAAS,EAAE,SAAS;4EAAE;;;;;;;kFAEpG,8OAAC;wEAAE,WAAU;kFACV,KAAK,OAAO;;;;;;;;;;;;;;;;;;kEAInB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,4JAAA,CAAA,UAAI;gEACH,MAAM,CAAC,MAAM,EAAE,KAAK,IAAI,EAAE;gEAC1B,QAAO;gEACP,WAAU;0EACX;;;;;;0EAGD,8OAAC;gEAAO,WAAU;0EAA4C;;;;;;0EAG9D,8OAAC;gEAAO,WAAU;0EAA0C;;;;;;;;;;;;;;;;;;2CA/BxD,KAAK,GAAG;;;;;;;;;;gCAyCrB,aAAa,mBACZ,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,SAAS,IAAM,eAAe,KAAK,GAAG,CAAC,GAAG,cAAc;gDACxD,UAAU,gBAAgB;gDAC1B,WAAU;0DACX;;;;;;4CAIA;mDAAI,MAAM;6CAAY,CAAC,GAAG,CAAC,CAAC,GAAG,kBAC9B,8OAAC;oDAEC,SAAS,IAAM,eAAe,IAAI;oDAClC,WAAW,CAAC,yCAAyC,EACnD,gBAAgB,IAAI,IAChB,2BACA,kEACJ;8DAED,IAAI;mDARA,IAAI;;;;;0DAYb,8OAAC;gDACC,SAAS,IAAM,eAAe,KAAK,GAAG,CAAC,YAAY,cAAc;gDACjE,UAAU,gBAAgB;gDAC1B,WAAU;0DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWnB;AAEA,kCAAkC;AAClC,SAAS,YAAY,EAAE,QAAQ,EAA4B;IACzD,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAmB,WAAU;;kDACtC,8OAAC;wCAAI,WAAU;kDAAqI;;;;;;kDAGpJ,8OAAC;wCAAK,WAAU;kDAAkC;;;;;;;;;;;;0CAEpD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;kDAGD,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;;kCAKL,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,QAAO;gCACP,WAAU;0CACX;;;;;;0CAGD,8OAAC;gCACC,SAAS;gCACT,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb", "debugId": null}}]}