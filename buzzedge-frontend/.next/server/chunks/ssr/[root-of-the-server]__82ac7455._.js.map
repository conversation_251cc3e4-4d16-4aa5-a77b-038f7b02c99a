{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Development/BuzzEdge/buzzedge-frontend/src/lib/api.ts"], "sourcesContent": ["// API Configuration\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || \"http://localhost:5001\";\n\nexport class ApiError extends Error {\n  constructor(message: string, public status: number, public response?: any) {\n    super(message);\n    this.name = \"ApiError\";\n  }\n}\n\nexport interface ApiResponse<T> {\n  success: boolean;\n  data?: T;\n  error?: string;\n  message?: string;\n}\n\nclass ApiClient {\n  private baseURL: string;\n  private cache: Map<string, { data: any; timestamp: number; ttl: number }>;\n\n  constructor(baseURL: string) {\n    this.baseURL = baseURL;\n    this.cache = new Map();\n  }\n\n  private getCacheKey(\n    endpoint: string,\n    params?: Record<string, string>\n  ): string {\n    const url = new URL(endpoint, this.baseURL);\n    if (params) {\n      Object.entries(params).forEach(([key, value]) => {\n        url.searchParams.append(key, value);\n      });\n    }\n    return url.pathname + url.search;\n  }\n\n  private isValidCache(cacheEntry: {\n    timestamp: number;\n    ttl: number;\n  }): boolean {\n    return Date.now() - cacheEntry.timestamp < cacheEntry.ttl * 1000;\n  }\n\n  private setCache(key: string, data: any, ttl: number = 300): void {\n    this.cache.set(key, {\n      data,\n      timestamp: Date.now(),\n      ttl,\n    });\n  }\n\n  private getCache(key: string): any | null {\n    const entry = this.cache.get(key);\n    if (entry && this.isValidCache(entry)) {\n      return entry.data;\n    }\n    if (entry) {\n      this.cache.delete(key); // Remove expired cache\n    }\n    return null;\n  }\n\n  private async request<T>(\n    endpoint: string,\n    options: RequestInit = {}\n  ): Promise<T> {\n    const url = `${this.baseURL}${endpoint}`;\n\n    // Get auth token if available\n    const getAuthToken = () => {\n      if (typeof window !== \"undefined\") {\n        return localStorage.getItem(\"buzzedge_auth_token\");\n      }\n      return null;\n    };\n\n    const token = getAuthToken();\n    const authHeaders: Record<string, string> = token\n      ? { Authorization: `Bearer ${token}` }\n      : {};\n\n    const config: RequestInit = {\n      headers: {\n        \"Content-Type\": \"application/json\",\n        ...authHeaders,\n        ...options.headers,\n      },\n      ...options,\n    };\n\n    try {\n      const response = await fetch(url, config);\n\n      if (!response.ok) {\n        const errorData = await response.json().catch(() => ({}));\n        throw new ApiError(\n          errorData.error || `HTTP ${response.status}: ${response.statusText}`,\n          response.status,\n          errorData\n        );\n      }\n\n      const data = await response.json();\n      return data;\n    } catch (error) {\n      if (error instanceof ApiError) {\n        throw error;\n      }\n\n      // Network or other errors\n      throw new ApiError(\n        error instanceof Error ? error.message : \"Network error occurred\",\n        0\n      );\n    }\n  }\n\n  async get<T>(\n    endpoint: string,\n    params?: Record<string, string>,\n    options: { cache?: boolean; ttl?: number } = {}\n  ): Promise<T> {\n    const { cache = true, ttl = 300 } = options;\n    const cacheKey = this.getCacheKey(endpoint, params);\n\n    // Check cache first\n    if (cache) {\n      const cachedData = this.getCache(cacheKey);\n      if (cachedData) {\n        return cachedData;\n      }\n    }\n\n    const url = new URL(endpoint, this.baseURL);\n    if (params) {\n      Object.entries(params).forEach(([key, value]) => {\n        url.searchParams.append(key, value);\n      });\n    }\n\n    const data = await this.request<T>(url.pathname + url.search);\n\n    // Cache the response\n    if (cache) {\n      this.setCache(cacheKey, data, ttl);\n    }\n\n    return data;\n  }\n\n  async post<T>(endpoint: string, data?: any): Promise<T> {\n    return this.request<T>(endpoint, {\n      method: \"POST\",\n      body: data ? JSON.stringify(data) : undefined,\n    });\n  }\n\n  async put<T>(endpoint: string, data?: any): Promise<T> {\n    return this.request<T>(endpoint, {\n      method: \"PUT\",\n      body: data ? JSON.stringify(data) : undefined,\n    });\n  }\n\n  async delete<T>(endpoint: string): Promise<T> {\n    return this.request<T>(endpoint, {\n      method: \"DELETE\",\n    });\n  }\n\n  // Cache management methods\n  clearCache(): void {\n    this.cache.clear();\n  }\n\n  clearCacheByPattern(pattern: string): void {\n    const regex = new RegExp(pattern);\n    for (const [key] of this.cache) {\n      if (regex.test(key)) {\n        this.cache.delete(key);\n      }\n    }\n  }\n\n  getCacheStats(): { size: number; keys: string[] } {\n    return {\n      size: this.cache.size,\n      keys: Array.from(this.cache.keys()),\n    };\n  }\n}\n\n// Create and export the API client instance\nexport const apiClient = new ApiClient(API_BASE_URL);\n\n// Health check function\nexport const checkApiHealth = async (): Promise<boolean> => {\n  try {\n    await apiClient.get(\"/health\");\n    return true;\n  } catch (error) {\n    console.error(\"API health check failed:\", error);\n    return false;\n  }\n};\n"], "names": [], "mappings": "AAAA,oBAAoB;;;;;;AACpB,MAAM,eAAe,6DAAmC;AAEjD,MAAM,iBAAiB;;;IAC5B,YAAY,OAAe,EAAE,AAAO,MAAc,EAAE,AAAO,QAAc,CAAE;QACzE,KAAK,CAAC,eAD4B,SAAA,aAAuB,WAAA;QAEzD,IAAI,CAAC,IAAI,GAAG;IACd;AACF;AASA,MAAM;IACI,QAAgB;IAChB,MAAkE;IAE1E,YAAY,OAAe,CAAE;QAC3B,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,KAAK,GAAG,IAAI;IACnB;IAEQ,YACN,QAAgB,EAChB,MAA+B,EACvB;QACR,MAAM,MAAM,IAAI,IAAI,UAAU,IAAI,CAAC,OAAO;QAC1C,IAAI,QAAQ;YACV,OAAO,OAAO,CAAC,QAAQ,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;gBAC1C,IAAI,YAAY,CAAC,MAAM,CAAC,KAAK;YAC/B;QACF;QACA,OAAO,IAAI,QAAQ,GAAG,IAAI,MAAM;IAClC;IAEQ,aAAa,UAGpB,EAAW;QACV,OAAO,KAAK,GAAG,KAAK,WAAW,SAAS,GAAG,WAAW,GAAG,GAAG;IAC9D;IAEQ,SAAS,GAAW,EAAE,IAAS,EAAE,MAAc,GAAG,EAAQ;QAChE,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK;YAClB;YACA,WAAW,KAAK,GAAG;YACnB;QACF;IACF;IAEQ,SAAS,GAAW,EAAc;QACxC,MAAM,QAAQ,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;QAC7B,IAAI,SAAS,IAAI,CAAC,YAAY,CAAC,QAAQ;YACrC,OAAO,MAAM,IAAI;QACnB;QACA,IAAI,OAAO;YACT,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,uBAAuB;QACjD;QACA,OAAO;IACT;IAEA,MAAc,QACZ,QAAgB,EAChB,UAAuB,CAAC,CAAC,EACb;QACZ,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,GAAG,UAAU;QAExC,8BAA8B;QAC9B,MAAM,eAAe;YACnB,uCAAmC;;YAEnC;YACA,OAAO;QACT;QAEA,MAAM,QAAQ;QACd,MAAM,cAAsC,QACxC;YAAE,eAAe,CAAC,OAAO,EAAE,OAAO;QAAC,IACnC,CAAC;QAEL,MAAM,SAAsB;YAC1B,SAAS;gBACP,gBAAgB;gBAChB,GAAG,WAAW;gBACd,GAAG,QAAQ,OAAO;YACpB;YACA,GAAG,OAAO;QACZ;QAEA,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,KAAK;YAElC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC,CAAC,CAAC;gBACvD,MAAM,IAAI,SACR,UAAU,KAAK,IAAI,CAAC,KAAK,EAAE,SAAS,MAAM,CAAC,EAAE,EAAE,SAAS,UAAU,EAAE,EACpE,SAAS,MAAM,EACf;YAEJ;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,OAAO;QACT,EAAE,OAAO,OAAO;YACd,IAAI,iBAAiB,UAAU;gBAC7B,MAAM;YACR;YAEA,0BAA0B;YAC1B,MAAM,IAAI,SACR,iBAAiB,QAAQ,MAAM,OAAO,GAAG,0BACzC;QAEJ;IACF;IAEA,MAAM,IACJ,QAAgB,EAChB,MAA+B,EAC/B,UAA6C,CAAC,CAAC,EACnC;QACZ,MAAM,EAAE,QAAQ,IAAI,EAAE,MAAM,GAAG,EAAE,GAAG;QACpC,MAAM,WAAW,IAAI,CAAC,WAAW,CAAC,UAAU;QAE5C,oBAAoB;QACpB,IAAI,OAAO;YACT,MAAM,aAAa,IAAI,CAAC,QAAQ,CAAC;YACjC,IAAI,YAAY;gBACd,OAAO;YACT;QACF;QAEA,MAAM,MAAM,IAAI,IAAI,UAAU,IAAI,CAAC,OAAO;QAC1C,IAAI,QAAQ;YACV,OAAO,OAAO,CAAC,QAAQ,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;gBAC1C,IAAI,YAAY,CAAC,MAAM,CAAC,KAAK;YAC/B;QACF;QAEA,MAAM,OAAO,MAAM,IAAI,CAAC,OAAO,CAAI,IAAI,QAAQ,GAAG,IAAI,MAAM;QAE5D,qBAAqB;QACrB,IAAI,OAAO;YACT,IAAI,CAAC,QAAQ,CAAC,UAAU,MAAM;QAChC;QAEA,OAAO;IACT;IAEA,MAAM,KAAQ,QAAgB,EAAE,IAAU,EAAc;QACtD,OAAO,IAAI,CAAC,OAAO,CAAI,UAAU;YAC/B,QAAQ;YACR,MAAM,OAAO,KAAK,SAAS,CAAC,QAAQ;QACtC;IACF;IAEA,MAAM,IAAO,QAAgB,EAAE,IAAU,EAAc;QACrD,OAAO,IAAI,CAAC,OAAO,CAAI,UAAU;YAC/B,QAAQ;YACR,MAAM,OAAO,KAAK,SAAS,CAAC,QAAQ;QACtC;IACF;IAEA,MAAM,OAAU,QAAgB,EAAc;QAC5C,OAAO,IAAI,CAAC,OAAO,CAAI,UAAU;YAC/B,QAAQ;QACV;IACF;IAEA,2BAA2B;IAC3B,aAAmB;QACjB,IAAI,CAAC,KAAK,CAAC,KAAK;IAClB;IAEA,oBAAoB,OAAe,EAAQ;QACzC,MAAM,QAAQ,IAAI,OAAO;QACzB,KAAK,MAAM,CAAC,IAAI,IAAI,IAAI,CAAC,KAAK,CAAE;YAC9B,IAAI,MAAM,IAAI,CAAC,MAAM;gBACnB,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;YACpB;QACF;IACF;IAEA,gBAAkD;QAChD,OAAO;YACL,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI;YACrB,MAAM,MAAM,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI;QAClC;IACF;AACF;AAGO,MAAM,YAAY,IAAI,UAAU;AAGhC,MAAM,iBAAiB;IAC5B,IAAI;QACF,MAAM,UAAU,GAAG,CAAC;QACpB,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 178, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Development/BuzzEdge/buzzedge-frontend/src/components/PerformanceMonitor.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useEffect, useState } from \"react\";\nimport { apiClient } from \"@/lib/api\";\n\ninterface PerformanceMetrics {\n  pageLoadTime: number;\n  apiResponseTime: number;\n  cacheHitRate: number;\n  totalRequests: number;\n  cachedRequests: number;\n}\n\ninterface PerformanceMonitorProps {\n  enabled?: boolean;\n  showStats?: boolean;\n}\n\nexport function PerformanceMonitor({\n  enabled = process.env.NODE_ENV === \"development\",\n  showStats = false,\n}: PerformanceMonitorProps) {\n  const [metrics, setMetrics] = useState<PerformanceMetrics>({\n    pageLoadTime: 0,\n    apiResponseTime: 0,\n    cacheHitRate: 0,\n    totalRequests: 0,\n    cachedRequests: 0,\n  });\n  const [isVisible, setIsVisible] = useState(false);\n\n  useEffect(() => {\n    if (!enabled) return;\n\n    // Measure page load time\n    const measurePageLoad = () => {\n      if (typeof window !== \"undefined\" && window.performance) {\n        const navigation = performance.getEntriesByType(\n          \"navigation\"\n        )[0] as PerformanceNavigationTiming;\n        if (navigation) {\n          const loadTime = navigation.loadEventEnd - navigation.fetchStart;\n          setMetrics((prev) => ({ ...prev, pageLoadTime: loadTime }));\n        }\n      }\n    };\n\n    // Measure API performance\n    const measureApiPerformance = () => {\n      const cacheStats = apiClient.getCacheStats();\n      setMetrics((prev) => ({\n        ...prev,\n        totalRequests: cacheStats.size,\n        cachedRequests: cacheStats.size,\n        cacheHitRate:\n          cacheStats.size > 0\n            ? (cacheStats.size / (cacheStats.size + 1)) * 100\n            : 0,\n      }));\n    };\n\n    // Initial measurements\n    setTimeout(measurePageLoad, 1000);\n    measureApiPerformance();\n\n    // Update metrics periodically\n    const interval = setInterval(measureApiPerformance, 5000);\n\n    return () => clearInterval(interval);\n  }, [enabled]);\n\n  if (!enabled || !showStats) return null;\n\n  return (\n    <>\n      {/* Performance Stats Toggle */}\n      <button\n        onClick={() => setIsVisible(!isVisible)}\n        className=\"fixed bottom-4 right-4 z-50 bg-gray-800 text-white p-2 rounded-full shadow-lg hover:bg-gray-700 transition-colors\"\n        title=\"Performance Stats\"\n      >\n        📊\n      </button>\n\n      {/* Performance Stats Panel */}\n      {isVisible && (\n        <div className=\"fixed bottom-16 right-4 z-50 bg-white border border-gray-200 rounded-lg shadow-lg p-4 w-80\">\n          <div className=\"flex items-center justify-between mb-3\">\n            <h3 className=\"font-semibold text-gray-900\">Performance Stats</h3>\n            <button\n              onClick={() => setIsVisible(false)}\n              className=\"text-gray-400 hover:text-gray-600\"\n            >\n              ✕\n            </button>\n          </div>\n\n          <div className=\"space-y-3 text-sm\">\n            <div className=\"flex justify-between\">\n              <span className=\"text-gray-600\">Page Load Time:</span>\n              <span className=\"font-mono\">\n                {metrics.pageLoadTime > 0\n                  ? `${Math.round(metrics.pageLoadTime)}ms`\n                  : \"Measuring...\"}\n              </span>\n            </div>\n\n            <div className=\"flex justify-between\">\n              <span className=\"text-gray-600\">Cache Hit Rate:</span>\n              <span className=\"font-mono\">\n                {metrics.cacheHitRate.toFixed(1)}%\n              </span>\n            </div>\n\n            <div className=\"flex justify-between\">\n              <span className=\"text-gray-600\">Cached Requests:</span>\n              <span className=\"font-mono\">{metrics.cachedRequests}</span>\n            </div>\n\n            <div className=\"border-t pt-2\">\n              <button\n                onClick={() => {\n                  apiClient.clearCache();\n                  setMetrics((prev) => ({\n                    ...prev,\n                    cachedRequests: 0,\n                    cacheHitRate: 0,\n                  }));\n                }}\n                className=\"w-full text-xs bg-red-50 text-red-600 py-1 px-2 rounded hover:bg-red-100 transition-colors\"\n              >\n                Clear Cache\n              </button>\n            </div>\n          </div>\n        </div>\n      )}\n    </>\n  );\n}\n\n// Web Vitals monitoring\nexport function WebVitalsMonitor() {\n  useEffect(() => {\n    if (typeof window === \"undefined\") return;\n\n    // Monitor Core Web Vitals\n    const observer = new PerformanceObserver((list) => {\n      for (const entry of list.getEntries()) {\n        const metric = entry.name;\n        const value =\n          (entry as any).value ||\n          (entry as any).processingStart ||\n          entry.duration;\n\n        // Log metrics in development\n        if (process.env.NODE_ENV === \"development\") {\n          console.log(`${metric}:`, value);\n        }\n\n        // Send to analytics in production\n        if (process.env.NODE_ENV === \"production\") {\n          // Example: Send to Google Analytics or other analytics service\n          // gtag('event', metric, { value: Math.round(value) });\n        }\n      }\n    });\n\n    // Observe different metric types\n    try {\n      observer.observe({ entryTypes: [\"measure\", \"navigation\", \"paint\"] });\n    } catch (error) {\n      console.warn(\"Performance Observer not supported:\", error);\n    }\n\n    return () => observer.disconnect();\n  }, []);\n\n  return null;\n}\n\n// Image loading optimization\nexport function ImageLoadMonitor() {\n  useEffect(() => {\n    if (typeof window === \"undefined\") return;\n\n    const images = document.querySelectorAll(\"img[data-src]\");\n\n    const imageObserver = new IntersectionObserver((entries) => {\n      entries.forEach((entry) => {\n        if (entry.isIntersecting) {\n          const img = entry.target as HTMLImageElement;\n          const src = img.getAttribute(\"data-src\");\n          if (src) {\n            img.src = src;\n            img.removeAttribute(\"data-src\");\n            imageObserver.unobserve(img);\n          }\n        }\n      });\n    });\n\n    images.forEach((img) => imageObserver.observe(img));\n\n    return () => imageObserver.disconnect();\n  }, []);\n\n  return null;\n}\n\n// Performance utilities\nexport const performanceUtils = {\n  // Measure function execution time\n  measureTime: async function <T>(\n    fn: () => Promise<T>,\n    label: string\n  ): Promise<T> {\n    const start = performance.now();\n    const result = await fn();\n    const end = performance.now();\n\n    if (process.env.NODE_ENV === \"development\") {\n      console.log(`${label}: ${Math.round(end - start)}ms`);\n    }\n\n    return result;\n  },\n\n  // Debounce function for performance\n  debounce: function <T extends (...args: any[]) => any>(\n    func: T,\n    wait: number\n  ): (...args: Parameters<T>) => void {\n    let timeout: NodeJS.Timeout;\n    return (...args: Parameters<T>) => {\n      clearTimeout(timeout);\n      timeout = setTimeout(() => func(...args), wait);\n    };\n  },\n\n  // Throttle function for performance\n  throttle: function <T extends (...args: any[]) => any>(\n    func: T,\n    limit: number\n  ): (...args: Parameters<T>) => void {\n    let inThrottle: boolean;\n    return (...args: Parameters<T>) => {\n      if (!inThrottle) {\n        func(...args);\n        inThrottle = true;\n        setTimeout(() => (inThrottle = false), limit);\n      }\n    };\n  },\n};\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AAHA;;;;AAkBO,SAAS,mBAAmB,EACjC,UAAU,oDAAyB,aAAa,EAChD,YAAY,KAAK,EACO;IACxB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsB;QACzD,cAAc;QACd,iBAAiB;QACjB,cAAc;QACd,eAAe;QACf,gBAAgB;IAClB;IACA,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,SAAS;QAEd,yBAAyB;QACzB,MAAM,kBAAkB;YACtB,uCAAyD;;YAQzD;QACF;QAEA,0BAA0B;QAC1B,MAAM,wBAAwB;YAC5B,MAAM,aAAa,iHAAA,CAAA,YAAS,CAAC,aAAa;YAC1C,WAAW,CAAC,OAAS,CAAC;oBACpB,GAAG,IAAI;oBACP,eAAe,WAAW,IAAI;oBAC9B,gBAAgB,WAAW,IAAI;oBAC/B,cACE,WAAW,IAAI,GAAG,IACd,AAAC,WAAW,IAAI,GAAG,CAAC,WAAW,IAAI,GAAG,CAAC,IAAK,MAC5C;gBACR,CAAC;QACH;QAEA,uBAAuB;QACvB,WAAW,iBAAiB;QAC5B;QAEA,8BAA8B;QAC9B,MAAM,WAAW,YAAY,uBAAuB;QAEpD,OAAO,IAAM,cAAc;IAC7B,GAAG;QAAC;KAAQ;IAEZ,IAAI,CAAC,WAAW,CAAC,WAAW,OAAO;IAEnC,qBACE;;0BAEE,8OAAC;gBACC,SAAS,IAAM,aAAa,CAAC;gBAC7B,WAAU;gBACV,OAAM;0BACP;;;;;;YAKA,2BACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA8B;;;;;;0CAC5C,8OAAC;gCACC,SAAS,IAAM,aAAa;gCAC5B,WAAU;0CACX;;;;;;;;;;;;kCAKH,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAAgB;;;;;;kDAChC,8OAAC;wCAAK,WAAU;kDACb,QAAQ,YAAY,GAAG,IACpB,GAAG,KAAK,KAAK,CAAC,QAAQ,YAAY,EAAE,EAAE,CAAC,GACvC;;;;;;;;;;;;0CAIR,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAAgB;;;;;;kDAChC,8OAAC;wCAAK,WAAU;;4CACb,QAAQ,YAAY,CAAC,OAAO,CAAC;4CAAG;;;;;;;;;;;;;0CAIrC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAAgB;;;;;;kDAChC,8OAAC;wCAAK,WAAU;kDAAa,QAAQ,cAAc;;;;;;;;;;;;0CAGrD,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCACC,SAAS;wCACP,iHAAA,CAAA,YAAS,CAAC,UAAU;wCACpB,WAAW,CAAC,OAAS,CAAC;gDACpB,GAAG,IAAI;gDACP,gBAAgB;gDAChB,cAAc;4CAChB,CAAC;oCACH;oCACA,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;AASf;AAGO,SAAS;IACd,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,wCAAmC;;QAEnC,0BAA0B;QAC1B,MAAM;IA6BR,GAAG,EAAE;IAEL,OAAO;AACT;AAGO,SAAS;IACd,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,wCAAmC;;QAEnC,MAAM;QAEN,MAAM;IAiBR,GAAG,EAAE;IAEL,OAAO;AACT;AAGO,MAAM,mBAAmB;IAC9B,kCAAkC;IAClC,aAAa,eACX,EAAoB,EACpB,KAAa;QAEb,MAAM,QAAQ,YAAY,GAAG;QAC7B,MAAM,SAAS,MAAM;QACrB,MAAM,MAAM,YAAY,GAAG;QAE3B,wCAA4C;YAC1C,QAAQ,GAAG,CAAC,GAAG,MAAM,EAAE,EAAE,KAAK,KAAK,CAAC,MAAM,OAAO,EAAE,CAAC;QACtD;QAEA,OAAO;IACT;IAEA,oCAAoC;IACpC,UAAU,SACR,IAAO,EACP,IAAY;QAEZ,IAAI;QACJ,OAAO,CAAC,GAAG;YACT,aAAa;YACb,UAAU,WAAW,IAAM,QAAQ,OAAO;QAC5C;IACF;IAEA,oCAAoC;IACpC,UAAU,SACR,IAAO,EACP,KAAa;QAEb,IAAI;QACJ,OAAO,CAAC,GAAG;YACT,IAAI,CAAC,YAAY;gBACf,QAAQ;gBACR,aAAa;gBACb,WAAW,IAAO,aAAa,OAAQ;YACzC;QACF;IACF;AACF", "debugId": null}}, {"offset": {"line": 442, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Development/BuzzEdge/buzzedge-frontend/node_modules/next/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": ";AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 465, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Development/BuzzEdge/buzzedge-frontend/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-ssr'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 472, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Development/BuzzEdge/buzzedge-frontend/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored['react-ssr'].React\n"], "names": ["module", "exports", "require", "vendored", "React"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CAAC,YAAY,CAACC,KAAK", "ignoreList": [0], "debugId": null}}]}