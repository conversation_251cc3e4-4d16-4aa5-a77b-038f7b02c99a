{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Development/BuzzEdge/buzzedge-frontend/src/app/icon.tsx"], "sourcesContent": ["import { ImageResponse } from 'next/og';\n\n// Route segment config\nexport const runtime = 'edge';\n\n// Image metadata\nexport const size = {\n  width: 32,\n  height: 32,\n};\nexport const contentType = 'image/png';\n\n// Image generation\nexport default function Icon() {\n  return new ImageResponse(\n    (\n      <div\n        style={{\n          fontSize: 24,\n          background: 'linear-gradient(90deg, #3B82F6 0%, #8B5CF6 100%)',\n          width: '100%',\n          height: '100%',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          color: 'white',\n          fontWeight: 'bold',\n          borderRadius: '6px',\n        }}\n      >\n        B\n      </div>\n    ),\n    {\n      ...size,\n    }\n  );\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;;;AAGO,MAAM,UAAU;AAGhB,MAAM,OAAO;IAClB,OAAO;IACP,QAAQ;AACV;AACO,MAAM,cAAc;AAGZ,SAAS;IACtB,OAAO,IAAI,0HAAA,CAAA,gBAAa,eAEpB,8OAAC;QACC,OAAO;YACL,UAAU;YACV,YAAY;YACZ,OAAO;YACP,QAAQ;YACR,SAAS;YACT,YAAY;YACZ,gBAAgB;YAChB,OAAO;YACP,YAAY;YACZ,cAAc;QAChB;kBACD;;;;;cAIH;QACE,GAAG,IAAI;IACT;AAEJ", "debugId": null}}, {"offset": {"line": 52, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Development/BuzzEdge/buzzedge-frontend/src/app/icon--metadata.js"], "sourcesContent": ["import { contentType, runtime, size } from \"./icon.tsx\"\nimport { fillMetadataSegment } from 'next/dist/lib/metadata/get-metadata-route'\n\nconst imageModule = { contentType, runtime, size }\n\nexport default async function (props) {\n    const { __metadata_id__: _, ...params } = await props.params\n    const imageUrl = fillMetadataSegment(\"/\", params, \"icon\")\n\n    const { generateImageMetadata } = imageModule\n\n    function getImageMetadata(imageMetadata, idParam) {\n        const data = {\n            alt: imageMetadata.alt,\n            type: imageMetadata.contentType || 'image/png',\n            url: imageUrl + (idParam ? ('/' + idParam) : '') + \"?43e340fdd58f09e\",\n        }\n        const { size } = imageMetadata\n        if (size) {\n            data.sizes = size.width + \"x\" + size.height;\n        }\n        return data\n    }\n\n    if (generateImageMetadata) {\n        const imageMetadataArray = await generateImageMetadata({ params })\n        return imageMetadataArray.map((imageMetadata, index) => {\n            const idParam = (imageMetadata.id || index) + ''\n            return getImageMetadata(imageMetadata, idParam)\n        })\n    } else {\n        return [getImageMetadata(imageModule, '')]\n    }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,MAAM,cAAc;IAAE,aAAA,mHAAA,CAAA,cAAW;IAAE,SAAA,mHAAA,CAAA,UAAO;IAAE,MAAA,mHAAA,CAAA,OAAI;AAAC;AAElC,8CAAgB,KAAK;IAChC,MAAM,EAAE,iBAAiB,CAAC,EAAE,GAAG,QAAQ,GAAG,MAAM,MAAM,MAAM;IAC5D,MAAM,WAAW,CAAA,GAAA,2KAAA,CAAA,sBAAmB,AAAD,EAAE,KAAK,QAAQ;IAElD,MAAM,EAAE,qBAAqB,EAAE,GAAG;IAElC,SAAS,iBAAiB,aAAa,EAAE,OAAO;QAC5C,MAAM,OAAO;YACT,KAAK,cAAc,GAAG;YACtB,MAAM,cAAc,WAAW,IAAI;YACnC,KAAK,WAAW,CAAC,UAAW,MAAM,UAAW,EAAE,IAAI;QACvD;QACA,MAAM,EAAE,IAAI,EAAE,GAAG;QACjB,IAAI,MAAM;YACN,KAAK,KAAK,GAAG,KAAK,KAAK,GAAG,MAAM,KAAK,MAAM;QAC/C;QACA,OAAO;IACX;IAEA,IAAI,uBAAuB;QACvB,MAAM,qBAAqB,MAAM,sBAAsB;YAAE;QAAO;QAChE,OAAO,mBAAmB,GAAG,CAAC,CAAC,eAAe;YAC1C,MAAM,UAAU,CAAC,cAAc,EAAE,IAAI,KAAK,IAAI;YAC9C,OAAO,iBAAiB,eAAe;QAC3C;IACJ,OAAO;QACH,OAAO;YAAC,iBAAiB,aAAa;SAAI;IAC9C;AACJ", "debugId": null}}, {"offset": {"line": 99, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Development/BuzzEdge/buzzedge-frontend/node_modules/next/src/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-rsc'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 106, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Development/BuzzEdge/buzzedge-frontend/node_modules/next/src/server/og/image-response.ts"], "sourcesContent": ["type OgModule = typeof import('next/dist/compiled/@vercel/og')\n\nfunction importModule(): Promise<\n  typeof import('next/dist/compiled/@vercel/og')\n> {\n  return import(\n    process.env.NEXT_RUNTIME === 'edge'\n      ? 'next/dist/compiled/@vercel/og/index.edge.js'\n      : 'next/dist/compiled/@vercel/og/index.node.js'\n  )\n}\n\n/**\n * The ImageResponse class allows you to generate dynamic images using JSX and CSS.\n * This is useful for generating social media images such as Open Graph images, Twitter cards, and more.\n *\n * Read more: [Next.js Docs: `ImageResponse`](https://nextjs.org/docs/app/api-reference/functions/image-response)\n */\nexport class ImageResponse extends Response {\n  public static displayName = 'ImageResponse'\n  constructor(...args: ConstructorParameters<OgModule['ImageResponse']>) {\n    const readable = new ReadableStream({\n      async start(controller) {\n        const OGImageResponse: typeof import('next/dist/compiled/@vercel/og').ImageResponse =\n          // So far we have to manually determine which build to use,\n          // as the auto resolving is not working\n          (await importModule()).ImageResponse\n        const imageResponse = new OGImageResponse(...args) as Response\n\n        if (!imageResponse.body) {\n          return controller.close()\n        }\n\n        const reader = imageResponse.body!.getReader()\n        while (true) {\n          const { done, value } = await reader.read()\n          if (done) {\n            return controller.close()\n          }\n          controller.enqueue(value)\n        }\n      },\n    })\n\n    const options = args[1] || {}\n\n    const headers = new Headers({\n      'content-type': 'image/png',\n      'cache-control':\n        process.env.NODE_ENV === 'development'\n          ? 'no-cache, no-store'\n          : 'public, immutable, no-transform, max-age=31536000',\n    })\n    if (options.headers) {\n      const newHeaders = new Headers(options.headers)\n      newHeaders.forEach((value, key) => headers.set(key, value))\n    }\n    super(readable, {\n      headers,\n      status: options.status,\n      statusText: options.statusText,\n    })\n  }\n}\n"], "names": ["ImageResponse", "importModule", "process", "env", "NEXT_RUNTIME", "Response", "displayName", "constructor", "args", "readable", "ReadableStream", "start", "controller", "OGImageResponse", "imageResponse", "body", "close", "reader", "<PERSON><PERSON><PERSON><PERSON>", "done", "value", "read", "enqueue", "options", "headers", "Headers", "NODE_ENV", "newHeaders", "for<PERSON>ach", "key", "set", "status", "statusText"], "mappings": ";;;;+BAkBaA,iBAAAA;;;eAAAA;;;AAhBb,SAASC;IAGP,OAAO,MAAM,CACXC,QAAQC,GAAG,CAACC,YAAY,KAAK,SACzB,gDACA;AAER;AAQO,MAAMJ,sBAAsBK;qBACnBC,WAAAA,GAAc,gBAAA;IAC5BC,YAAY,GAAGC,IAAsD,CAAE;QACrE,MAAMC,WAAW,IAAIC,eAAe;YAClC,MAAMC,OAAMC,UAAU;gBACpB,MAAMC,kBAGJ,AAFA,AACA,uCAAuC,oBADoB;gBAE1D,CAAA,MAAMZ,cAAa,EAAGD,aAAa;gBACtC,MAAMc,gBAAgB,IAAID,mBAAmBL;gBAE7C,IAAI,CAACM,cAAcC,IAAI,EAAE;oBACvB,OAAOH,WAAWI,KAAK;gBACzB;gBAEA,MAAMC,SAASH,cAAcC,IAAI,CAAEG,SAAS;gBAC5C,MAAO,KAAM;oBACX,MAAM,EAAEC,IAAI,EAAEC,KAAK,EAAE,GAAG,MAAMH,OAAOI,IAAI;oBACzC,IAAIF,MAAM;wBACR,OAAOP,WAAWI,KAAK;oBACzB;oBACAJ,WAAWU,OAAO,CAACF;gBACrB;YACF;QACF;QAEA,MAAMG,UAAUf,IAAI,CAAC,EAAE,IAAI,CAAC;QAE5B,MAAMgB,UAAU,IAAIC,QAAQ;YAC1B,gBAAgB;YAChB,iBACEvB,QAAQC,GAAG,CAACuB,QAAQ,KAAK,cACrB,uBACA;QACR;QACA,IAAIH,QAAQC,OAAO,EAAE;YACnB,MAAMG,aAAa,IAAIF,QAAQF,QAAQC,OAAO;YAC9CG,WAAWC,OAAO,CAAC,CAACR,OAAOS,MAAQL,QAAQM,GAAG,CAACD,KAAKT;QACtD;QACA,KAAK,CAACX,UAAU;YACde;YACAO,QAAQR,QAAQQ,MAAM;YACtBC,YAAYT,QAAQS,UAAU;QAChC;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 161, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Development/BuzzEdge/buzzedge-frontend/node_modules/next/og.js"], "sourcesContent": ["module.exports = require('./dist/server/og/image-response')\n"], "names": [], "mappings": "AAAA,OAAO,OAAO", "ignoreList": [0], "debugId": null}}]}