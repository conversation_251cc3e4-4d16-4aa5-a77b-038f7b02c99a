{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Development/BuzzEdge/buzzedge-frontend/src/components/AnalyticsDashboard.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { analyticsUtils } from '@/lib/analytics';\n\ninterface AnalyticsData {\n  total: {\n    events: number;\n    pageViews: number;\n    userActions: number;\n    contentInteractions: number;\n  };\n  today: {\n    events: number;\n    pageViews: number;\n    userActions: number;\n  };\n  week: {\n    events: number;\n    pageViews: number;\n    userActions: number;\n  };\n  month: {\n    events: number;\n    pageViews: number;\n    userActions: number;\n  };\n}\n\ninterface PopularPage {\n  path: string;\n  views: number;\n}\n\nexport function AnalyticsDashboard() {\n  const [data, setData] = useState<AnalyticsData | null>(null);\n  const [popularPages, setPopularPages] = useState<PopularPage[]>([]);\n  const [isLoading, setIsLoading] = useState(true);\n\n  useEffect(() => {\n    loadAnalyticsData();\n  }, []);\n\n  const loadAnalyticsData = () => {\n    try {\n      setIsLoading(true);\n      const summary = analyticsUtils.getSummary();\n      const popular = analyticsUtils.getPopularPages(10);\n      \n      setData(summary);\n      setPopularPages(popular);\n    } catch (error) {\n      console.error('Failed to load analytics data:', error);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const clearAnalytics = () => {\n    if (confirm('Are you sure you want to clear all analytics data? This action cannot be undone.')) {\n      analyticsUtils.clearData();\n      loadAnalyticsData();\n    }\n  };\n\n  if (isLoading) {\n    return (\n      <div className=\"space-y-6\">\n        <div className=\"animate-pulse\">\n          <div className=\"grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 mb-8\">\n            {[...Array(4)].map((_, i) => (\n              <div key={i} className=\"bg-white overflow-hidden shadow rounded-lg\">\n                <div className=\"p-5\">\n                  <div className=\"h-6 bg-gray-300 rounded w-20 mb-2\"></div>\n                  <div className=\"h-8 bg-gray-300 rounded w-16\"></div>\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  if (!data) {\n    return (\n      <div className=\"text-center py-12\">\n        <div className=\"text-gray-500\">\n          <div className=\"text-4xl mb-4\">📊</div>\n          <h3 className=\"text-lg font-medium mb-2\">No Analytics Data</h3>\n          <p>Start browsing your site to collect analytics data.</p>\n        </div>\n      </div>\n    );\n  }\n\n  const statCards = [\n    {\n      name: 'Total Page Views',\n      value: data.total.pageViews,\n      change: data.today.pageViews,\n      changeLabel: 'today',\n      icon: '👁️',\n      color: 'bg-blue-500',\n    },\n    {\n      name: 'User Actions',\n      value: data.total.userActions,\n      change: data.today.userActions,\n      changeLabel: 'today',\n      icon: '🎯',\n      color: 'bg-green-500',\n    },\n    {\n      name: 'Content Interactions',\n      value: data.total.contentInteractions,\n      change: data.week.pageViews - data.today.pageViews,\n      changeLabel: 'this week',\n      icon: '💬',\n      color: 'bg-purple-500',\n    },\n    {\n      name: 'Total Events',\n      value: data.total.events,\n      change: data.month.events - data.week.events,\n      changeLabel: 'this month',\n      icon: '📈',\n      color: 'bg-orange-500',\n    },\n  ];\n\n  return (\n    <div className=\"space-y-8\">\n      {/* Header */}\n      <div className=\"flex justify-between items-center\">\n        <div>\n          <h2 className=\"text-2xl font-bold text-gray-900\">Analytics Dashboard</h2>\n          <p className=\"mt-1 text-sm text-gray-500\">\n            Track your website performance and user engagement\n          </p>\n        </div>\n        <div className=\"flex space-x-3\">\n          <button\n            onClick={loadAnalyticsData}\n            className=\"px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50\"\n          >\n            Refresh\n          </button>\n          <button\n            onClick={clearAnalytics}\n            className=\"px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700\"\n          >\n            Clear Data\n          </button>\n        </div>\n      </div>\n\n      {/* Stats Cards */}\n      <div className=\"grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4\">\n        {statCards.map((stat) => (\n          <div key={stat.name} className=\"bg-white overflow-hidden shadow rounded-lg\">\n            <div className=\"p-5\">\n              <div className=\"flex items-center\">\n                <div className=\"flex-shrink-0\">\n                  <div className={`${stat.color} rounded-md p-3`}>\n                    <span className=\"text-white text-xl\">{stat.icon}</span>\n                  </div>\n                </div>\n                <div className=\"ml-5 w-0 flex-1\">\n                  <dl>\n                    <dt className=\"text-sm font-medium text-gray-500 truncate\">\n                      {stat.name}\n                    </dt>\n                    <dd className=\"flex items-baseline\">\n                      <div className=\"text-2xl font-semibold text-gray-900\">\n                        {stat.value.toLocaleString()}\n                      </div>\n                      <div className=\"ml-2 flex items-baseline text-sm\">\n                        <span className=\"text-gray-500\">\n                          +{stat.change} {stat.changeLabel}\n                        </span>\n                      </div>\n                    </dd>\n                  </dl>\n                </div>\n              </div>\n            </div>\n          </div>\n        ))}\n      </div>\n\n      {/* Time Period Stats */}\n      <div className=\"bg-white shadow rounded-lg\">\n        <div className=\"px-4 py-5 sm:p-6\">\n          <h3 className=\"text-lg leading-6 font-medium text-gray-900 mb-4\">\n            Performance Over Time\n          </h3>\n          <div className=\"grid grid-cols-1 gap-5 sm:grid-cols-3\">\n            <div className=\"text-center\">\n              <div className=\"text-2xl font-semibold text-gray-900\">\n                {data.today.pageViews}\n              </div>\n              <div className=\"text-sm text-gray-500\">Page Views Today</div>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"text-2xl font-semibold text-gray-900\">\n                {data.week.pageViews}\n              </div>\n              <div className=\"text-sm text-gray-500\">Page Views This Week</div>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"text-2xl font-semibold text-gray-900\">\n                {data.month.pageViews}\n              </div>\n              <div className=\"text-sm text-gray-500\">Page Views This Month</div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Popular Pages */}\n      <div className=\"bg-white shadow rounded-lg\">\n        <div className=\"px-4 py-5 sm:p-6\">\n          <h3 className=\"text-lg leading-6 font-medium text-gray-900 mb-4\">\n            Most Popular Pages\n          </h3>\n          {popularPages.length > 0 ? (\n            <div className=\"space-y-3\">\n              {popularPages.map((page, index) => (\n                <div key={page.path} className=\"flex items-center justify-between\">\n                  <div className=\"flex items-center\">\n                    <div className=\"flex-shrink-0 w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center text-sm font-medium text-gray-600\">\n                      {index + 1}\n                    </div>\n                    <div className=\"ml-3\">\n                      <div className=\"text-sm font-medium text-gray-900\">\n                        {page.path === '/' ? 'Homepage' : page.path}\n                      </div>\n                      <div className=\"text-sm text-gray-500\">\n                        {page.path}\n                      </div>\n                    </div>\n                  </div>\n                  <div className=\"text-sm font-medium text-gray-900\">\n                    {page.views} views\n                  </div>\n                </div>\n              ))}\n            </div>\n          ) : (\n            <div className=\"text-center py-6 text-gray-500\">\n              <div className=\"text-2xl mb-2\">📄</div>\n              <p>No page view data available yet.</p>\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* Real-time Stats */}\n      <div className=\"bg-white shadow rounded-lg\">\n        <div className=\"px-4 py-5 sm:p-6\">\n          <h3 className=\"text-lg leading-6 font-medium text-gray-900 mb-4\">\n            Real-time Analytics\n          </h3>\n          <div className=\"grid grid-cols-1 gap-5 sm:grid-cols-2\">\n            <div>\n              <div className=\"text-sm font-medium text-gray-500 mb-2\">\n                Events Today\n              </div>\n              <div className=\"space-y-2\">\n                <div className=\"flex justify-between\">\n                  <span className=\"text-sm text-gray-600\">Page Views</span>\n                  <span className=\"text-sm font-medium\">{data.today.pageViews}</span>\n                </div>\n                <div className=\"flex justify-between\">\n                  <span className=\"text-sm text-gray-600\">User Actions</span>\n                  <span className=\"text-sm font-medium\">{data.today.userActions}</span>\n                </div>\n                <div className=\"flex justify-between\">\n                  <span className=\"text-sm text-gray-600\">Total Events</span>\n                  <span className=\"text-sm font-medium\">{data.today.events}</span>\n                </div>\n              </div>\n            </div>\n            <div>\n              <div className=\"text-sm font-medium text-gray-500 mb-2\">\n                Growth Metrics\n              </div>\n              <div className=\"space-y-2\">\n                <div className=\"flex justify-between\">\n                  <span className=\"text-sm text-gray-600\">Weekly Growth</span>\n                  <span className=\"text-sm font-medium text-green-600\">\n                    +{((data.week.pageViews / Math.max(data.month.pageViews - data.week.pageViews, 1)) * 100).toFixed(1)}%\n                  </span>\n                </div>\n                <div className=\"flex justify-between\">\n                  <span className=\"text-sm text-gray-600\">Monthly Active</span>\n                  <span className=\"text-sm font-medium\">{data.month.pageViews}</span>\n                </div>\n                <div className=\"flex justify-between\">\n                  <span className=\"text-sm text-gray-600\">Engagement Rate</span>\n                  <span className=\"text-sm font-medium\">\n                    {data.total.pageViews > 0 ? ((data.total.userActions / data.total.pageViews) * 100).toFixed(1) : 0}%\n                  </span>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAkCO,SAAS;IACd,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAwB;IACvD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IAClE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,MAAM,oBAAoB;QACxB,IAAI;YACF,aAAa;YACb,MAAM,UAAU,wHAAA,CAAA,iBAAc,CAAC,UAAU;YACzC,MAAM,UAAU,wHAAA,CAAA,iBAAc,CAAC,eAAe,CAAC;YAE/C,QAAQ;YACR,gBAAgB;QAClB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;QAClD,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,iBAAiB;QACrB,IAAI,QAAQ,qFAAqF;YAC/F,wHAAA,CAAA,iBAAc,CAAC,SAAS;YACxB;QACF;IACF;IAEA,IAAI,WAAW;QACb,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACZ;2BAAI,MAAM;qBAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC;4BAAY,WAAU;sCACrB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAI,WAAU;;;;;;;;;;;;2BAHT;;;;;;;;;;;;;;;;;;;;IAWtB;IAEA,IAAI,CAAC,MAAM;QACT,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCAAgB;;;;;;kCAC/B,8OAAC;wBAAG,WAAU;kCAA2B;;;;;;kCACzC,8OAAC;kCAAE;;;;;;;;;;;;;;;;;IAIX;IAEA,MAAM,YAAY;QAChB;YACE,MAAM;YACN,OAAO,KAAK,KAAK,CAAC,SAAS;YAC3B,QAAQ,KAAK,KAAK,CAAC,SAAS;YAC5B,aAAa;YACb,MAAM;YACN,OAAO;QACT;QACA;YACE,MAAM;YACN,OAAO,KAAK,KAAK,CAAC,WAAW;YAC7B,QAAQ,KAAK,KAAK,CAAC,WAAW;YAC9B,aAAa;YACb,MAAM;YACN,OAAO;QACT;QACA;YACE,MAAM;YACN,OAAO,KAAK,KAAK,CAAC,mBAAmB;YACrC,QAAQ,KAAK,IAAI,CAAC,SAAS,GAAG,KAAK,KAAK,CAAC,SAAS;YAClD,aAAa;YACb,MAAM;YACN,OAAO;QACT;QACA;YACE,MAAM;YACN,OAAO,KAAK,KAAK,CAAC,MAAM;YACxB,QAAQ,KAAK,KAAK,CAAC,MAAM,GAAG,KAAK,IAAI,CAAC,MAAM;YAC5C,aAAa;YACb,MAAM;YACN,OAAO;QACT;KACD;IAED,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,8OAAC;gCAAE,WAAU;0CAA6B;;;;;;;;;;;;kCAI5C,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,SAAS;gCACT,WAAU;0CACX;;;;;;0CAGD,8OAAC;gCACC,SAAS;gCACT,WAAU;0CACX;;;;;;;;;;;;;;;;;;0BAOL,8OAAC;gBAAI,WAAU;0BACZ,UAAU,GAAG,CAAC,CAAC,qBACd,8OAAC;wBAAoB,WAAU;kCAC7B,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAW,GAAG,KAAK,KAAK,CAAC,eAAe,CAAC;sDAC5C,cAAA,8OAAC;gDAAK,WAAU;0DAAsB,KAAK,IAAI;;;;;;;;;;;;;;;;kDAGnD,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DACX,KAAK,IAAI;;;;;;8DAEZ,8OAAC;oDAAG,WAAU;;sEACZ,8OAAC;4DAAI,WAAU;sEACZ,KAAK,KAAK,CAAC,cAAc;;;;;;sEAE5B,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAK,WAAU;;oEAAgB;oEAC5B,KAAK,MAAM;oEAAC;oEAAE,KAAK,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;uBAnBtC,KAAK,IAAI;;;;;;;;;;0BAgCvB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAmD;;;;;;sCAGjE,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACZ,KAAK,KAAK,CAAC,SAAS;;;;;;sDAEvB,8OAAC;4CAAI,WAAU;sDAAwB;;;;;;;;;;;;8CAEzC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACZ,KAAK,IAAI,CAAC,SAAS;;;;;;sDAEtB,8OAAC;4CAAI,WAAU;sDAAwB;;;;;;;;;;;;8CAEzC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACZ,KAAK,KAAK,CAAC,SAAS;;;;;;sDAEvB,8OAAC;4CAAI,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO/C,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAmD;;;;;;wBAGhE,aAAa,MAAM,GAAG,kBACrB,8OAAC;4BAAI,WAAU;sCACZ,aAAa,GAAG,CAAC,CAAC,MAAM,sBACvB,8OAAC;oCAAoB,WAAU;;sDAC7B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACZ,QAAQ;;;;;;8DAEX,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACZ,KAAK,IAAI,KAAK,MAAM,aAAa,KAAK,IAAI;;;;;;sEAE7C,8OAAC;4DAAI,WAAU;sEACZ,KAAK,IAAI;;;;;;;;;;;;;;;;;;sDAIhB,8OAAC;4CAAI,WAAU;;gDACZ,KAAK,KAAK;gDAAC;;;;;;;;mCAfN,KAAK,IAAI;;;;;;;;;iDAqBvB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CAAgB;;;;;;8CAC/B,8OAAC;8CAAE;;;;;;;;;;;;;;;;;;;;;;;0BAOX,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAmD;;;;;;sCAGjE,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAI,WAAU;sDAAyC;;;;;;sDAGxD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAwB;;;;;;sEACxC,8OAAC;4DAAK,WAAU;sEAAuB,KAAK,KAAK,CAAC,SAAS;;;;;;;;;;;;8DAE7D,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAwB;;;;;;sEACxC,8OAAC;4DAAK,WAAU;sEAAuB,KAAK,KAAK,CAAC,WAAW;;;;;;;;;;;;8DAE/D,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAwB;;;;;;sEACxC,8OAAC;4DAAK,WAAU;sEAAuB,KAAK,KAAK,CAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;8CAI9D,8OAAC;;sDACC,8OAAC;4CAAI,WAAU;sDAAyC;;;;;;sDAGxD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAwB;;;;;;sEACxC,8OAAC;4DAAK,WAAU;;gEAAqC;gEACjD,CAAC,AAAC,KAAK,IAAI,CAAC,SAAS,GAAG,KAAK,GAAG,CAAC,KAAK,KAAK,CAAC,SAAS,GAAG,KAAK,IAAI,CAAC,SAAS,EAAE,KAAM,GAAG,EAAE,OAAO,CAAC;gEAAG;;;;;;;;;;;;;8DAGzG,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAwB;;;;;;sEACxC,8OAAC;4DAAK,WAAU;sEAAuB,KAAK,KAAK,CAAC,SAAS;;;;;;;;;;;;8DAE7D,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAwB;;;;;;sEACxC,8OAAC;4DAAK,WAAU;;gEACb,KAAK,KAAK,CAAC,SAAS,GAAG,IAAI,CAAC,AAAC,KAAK,KAAK,CAAC,WAAW,GAAG,KAAK,KAAK,CAAC,SAAS,GAAI,GAAG,EAAE,OAAO,CAAC,KAAK;gEAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUvH", "debugId": null}}, {"offset": {"line": 849, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Development/BuzzEdge/buzzedge-frontend/src/app/admin/analytics/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport Link from 'next/link';\nimport { AnalyticsDashboard } from '@/components/AnalyticsDashboard';\n\nexport default function AdminAnalyticsPage() {\n  const router = useRouter();\n\n  useEffect(() => {\n    // Check if user is authenticated\n    const isAdmin = localStorage.getItem('buzzedge_admin');\n    if (!isAdmin) {\n      router.push('/admin/login');\n      return;\n    }\n  }, [router]);\n\n  const handleLogout = () => {\n    localStorage.removeItem('buzzedge_admin');\n    router.push('/admin/login');\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Admin Header */}\n      <div className=\"bg-white shadow-sm border-b border-gray-200\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center h-16\">\n            <div className=\"flex items-center\">\n              <Link href=\"/admin/dashboard\" className=\"flex items-center\">\n                <div className=\"w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center text-white font-bold text-lg mr-2\">\n                  B\n                </div>\n                <span className=\"text-xl font-bold text-gray-900\">BuzzEdge Admin</span>\n              </Link>\n              <nav className=\"ml-8 flex space-x-4\">\n                <Link\n                  href=\"/admin/dashboard\"\n                  className=\"text-gray-500 hover:text-gray-700 px-3 py-2 rounded-md text-sm font-medium\"\n                >\n                  Dashboard\n                </Link>\n                <Link\n                  href=\"/admin/posts\"\n                  className=\"text-gray-500 hover:text-gray-700 px-3 py-2 rounded-md text-sm font-medium\"\n                >\n                  Posts\n                </Link>\n                <span className=\"text-blue-600 px-3 py-2 rounded-md text-sm font-medium\">\n                  Analytics\n                </span>\n              </nav>\n            </div>\n            <div className=\"flex items-center space-x-4\">\n              <Link\n                href=\"/\"\n                target=\"_blank\"\n                className=\"text-gray-500 hover:text-gray-700\"\n              >\n                View Site\n              </Link>\n              <button\n                onClick={handleLogout}\n                className=\"bg-red-600 text-white px-4 py-2 rounded-md text-sm hover:bg-red-700\"\n              >\n                Logout\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Breadcrumb */}\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4\">\n        <nav className=\"flex\" aria-label=\"Breadcrumb\">\n          <ol className=\"flex items-center space-x-4\">\n            <li>\n              <Link href=\"/admin/dashboard\" className=\"text-gray-400 hover:text-gray-500\">\n                Dashboard\n              </Link>\n            </li>\n            <li>\n              <div className=\"flex items-center\">\n                <svg className=\"flex-shrink-0 h-5 w-5 text-gray-300\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                  <path fillRule=\"evenodd\" d=\"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z\" clipRule=\"evenodd\" />\n                </svg>\n                <span className=\"ml-4 text-gray-500\">Analytics</span>\n              </div>\n            </li>\n          </ol>\n        </nav>\n      </div>\n\n      {/* Main Content */}\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-12\">\n        <AnalyticsDashboard />\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAOe,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,iCAAiC;QACjC,MAAM,UAAU,aAAa,OAAO,CAAC;QACrC,IAAI,CAAC,SAAS;YACZ,OAAO,IAAI,CAAC;YACZ;QACF;IACF,GAAG;QAAC;KAAO;IAEX,MAAM,eAAe;QACnB,aAAa,UAAU,CAAC;QACxB,OAAO,IAAI,CAAC;IACd;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAmB,WAAU;;0DACtC,8OAAC;gDAAI,WAAU;0DAAqI;;;;;;0DAGpJ,8OAAC;gDAAK,WAAU;0DAAkC;;;;;;;;;;;;kDAEpD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;0DACX;;;;;;0DAGD,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;0DACX;;;;;;0DAGD,8OAAC;gDAAK,WAAU;0DAAyD;;;;;;;;;;;;;;;;;;0CAK7E,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,QAAO;wCACP,WAAU;kDACX;;;;;;kDAGD,8OAAC;wCACC,SAAS;wCACT,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAST,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;oBAAO,cAAW;8BAC/B,cAAA,8OAAC;wBAAG,WAAU;;0CACZ,8OAAC;0CACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAmB,WAAU;8CAAoC;;;;;;;;;;;0CAI9E,8OAAC;0CACC,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;4CAAsC,MAAK;4CAAe,SAAQ;sDAC/E,cAAA,8OAAC;gDAAK,UAAS;gDAAU,GAAE;gDAAqH,UAAS;;;;;;;;;;;sDAE3J,8OAAC;4CAAK,WAAU;sDAAqB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ/C,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,wIAAA,CAAA,qBAAkB;;;;;;;;;;;;;;;;AAI3B", "debugId": null}}]}