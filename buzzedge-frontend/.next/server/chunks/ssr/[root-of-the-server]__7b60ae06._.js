module.exports = {

"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[project]/src/lib/api.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// API Configuration
__turbopack_context__.s({
    "ApiError": (()=>ApiError),
    "apiClient": (()=>apiClient),
    "checkApiHealth": (()=>checkApiHealth)
});
const API_BASE_URL = ("TURBOPACK compile-time value", "http://localhost:5001") || "http://localhost:5001";
class ApiError extends Error {
    status;
    response;
    constructor(message, status, response){
        super(message), this.status = status, this.response = response;
        this.name = "ApiError";
    }
}
class ApiClient {
    baseURL;
    cache;
    constructor(baseURL){
        this.baseURL = baseURL;
        this.cache = new Map();
    }
    getCacheKey(endpoint, params) {
        const url = new URL(endpoint, this.baseURL);
        if (params) {
            Object.entries(params).forEach(([key, value])=>{
                url.searchParams.append(key, value);
            });
        }
        return url.pathname + url.search;
    }
    isValidCache(cacheEntry) {
        return Date.now() - cacheEntry.timestamp < cacheEntry.ttl * 1000;
    }
    setCache(key, data, ttl = 300) {
        this.cache.set(key, {
            data,
            timestamp: Date.now(),
            ttl
        });
    }
    getCache(key) {
        const entry = this.cache.get(key);
        if (entry && this.isValidCache(entry)) {
            return entry.data;
        }
        if (entry) {
            this.cache.delete(key); // Remove expired cache
        }
        return null;
    }
    async request(endpoint, options = {}) {
        const url = `${this.baseURL}${endpoint}`;
        // Get auth token if available
        const getAuthToken = ()=>{
            if ("TURBOPACK compile-time falsy", 0) {
                "TURBOPACK unreachable";
            }
            return null;
        };
        const token = getAuthToken();
        const authHeaders = token ? {
            Authorization: `Bearer ${token}`
        } : {};
        const config = {
            headers: {
                "Content-Type": "application/json",
                ...authHeaders,
                ...options.headers
            },
            ...options
        };
        try {
            const response = await fetch(url, config);
            if (!response.ok) {
                const errorData = await response.json().catch(()=>({}));
                throw new ApiError(errorData.error || `HTTP ${response.status}: ${response.statusText}`, response.status, errorData);
            }
            const data = await response.json();
            return data;
        } catch (error) {
            if (error instanceof ApiError) {
                throw error;
            }
            // Network or other errors
            throw new ApiError(error instanceof Error ? error.message : "Network error occurred", 0);
        }
    }
    async get(endpoint, params, options = {}) {
        const { cache = true, ttl = 300 } = options;
        const cacheKey = this.getCacheKey(endpoint, params);
        // Check cache first
        if (cache) {
            const cachedData = this.getCache(cacheKey);
            if (cachedData) {
                return cachedData;
            }
        }
        const url = new URL(endpoint, this.baseURL);
        if (params) {
            Object.entries(params).forEach(([key, value])=>{
                url.searchParams.append(key, value);
            });
        }
        const data = await this.request(url.pathname + url.search);
        // Cache the response
        if (cache) {
            this.setCache(cacheKey, data, ttl);
        }
        return data;
    }
    async post(endpoint, data) {
        return this.request(endpoint, {
            method: "POST",
            body: data ? JSON.stringify(data) : undefined
        });
    }
    async put(endpoint, data) {
        return this.request(endpoint, {
            method: "PUT",
            body: data ? JSON.stringify(data) : undefined
        });
    }
    async delete(endpoint) {
        return this.request(endpoint, {
            method: "DELETE"
        });
    }
    // Cache management methods
    clearCache() {
        this.cache.clear();
    }
    clearCacheByPattern(pattern) {
        const regex = new RegExp(pattern);
        for (const [key] of this.cache){
            if (regex.test(key)) {
                this.cache.delete(key);
            }
        }
    }
    getCacheStats() {
        return {
            size: this.cache.size,
            keys: Array.from(this.cache.keys())
        };
    }
}
const apiClient = new ApiClient(API_BASE_URL);
const checkApiHealth = async ()=>{
    try {
        await apiClient.get("/health");
        return true;
    } catch (error) {
        console.error("API health check failed:", error);
        return false;
    }
};
}}),
"[project]/src/components/PerformanceMonitor.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ImageLoadMonitor": (()=>ImageLoadMonitor),
    "PerformanceMonitor": (()=>PerformanceMonitor),
    "WebVitalsMonitor": (()=>WebVitalsMonitor),
    "performanceUtils": (()=>performanceUtils)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api.ts [app-ssr] (ecmascript)");
"use client";
;
;
;
function PerformanceMonitor({ enabled = ("TURBOPACK compile-time value", "development") === "development", showStats = false }) {
    const [metrics, setMetrics] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])({
        pageLoadTime: 0,
        apiResponseTime: 0,
        cacheHitRate: 0,
        totalRequests: 0,
        cachedRequests: 0
    });
    const [isVisible, setIsVisible] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (!enabled) return;
        // Measure page load time
        const measurePageLoad = ()=>{
            if ("TURBOPACK compile-time falsy", 0) {
                "TURBOPACK unreachable";
            }
        };
        // Measure API performance
        const measureApiPerformance = ()=>{
            const cacheStats = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].getCacheStats();
            setMetrics((prev)=>({
                    ...prev,
                    totalRequests: cacheStats.size,
                    cachedRequests: cacheStats.size,
                    cacheHitRate: cacheStats.size > 0 ? cacheStats.size / (cacheStats.size + 1) * 100 : 0
                }));
        };
        // Initial measurements
        setTimeout(measurePageLoad, 1000);
        measureApiPerformance();
        // Update metrics periodically
        const interval = setInterval(measureApiPerformance, 5000);
        return ()=>clearInterval(interval);
    }, [
        enabled
    ]);
    if (!enabled || !showStats) return null;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                onClick: ()=>setIsVisible(!isVisible),
                className: "fixed bottom-4 right-4 z-50 bg-gray-800 text-white p-2 rounded-full shadow-lg hover:bg-gray-700 transition-colors",
                title: "Performance Stats",
                children: "📊"
            }, void 0, false, {
                fileName: "[project]/src/components/PerformanceMonitor.tsx",
                lineNumber: 77,
                columnNumber: 7
            }, this),
            isVisible && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "fixed bottom-16 right-4 z-50 bg-white border border-gray-200 rounded-lg shadow-lg p-4 w-80",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-center justify-between mb-3",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                className: "font-semibold text-gray-900",
                                children: "Performance Stats"
                            }, void 0, false, {
                                fileName: "[project]/src/components/PerformanceMonitor.tsx",
                                lineNumber: 89,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                onClick: ()=>setIsVisible(false),
                                className: "text-gray-400 hover:text-gray-600",
                                children: "✕"
                            }, void 0, false, {
                                fileName: "[project]/src/components/PerformanceMonitor.tsx",
                                lineNumber: 90,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/PerformanceMonitor.tsx",
                        lineNumber: 88,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "space-y-3 text-sm",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex justify-between",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "text-gray-600",
                                        children: "Page Load Time:"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/PerformanceMonitor.tsx",
                                        lineNumber: 100,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "font-mono",
                                        children: metrics.pageLoadTime > 0 ? `${Math.round(metrics.pageLoadTime)}ms` : "Measuring..."
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/PerformanceMonitor.tsx",
                                        lineNumber: 101,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/PerformanceMonitor.tsx",
                                lineNumber: 99,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex justify-between",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "text-gray-600",
                                        children: "Cache Hit Rate:"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/PerformanceMonitor.tsx",
                                        lineNumber: 109,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "font-mono",
                                        children: [
                                            metrics.cacheHitRate.toFixed(1),
                                            "%"
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/PerformanceMonitor.tsx",
                                        lineNumber: 110,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/PerformanceMonitor.tsx",
                                lineNumber: 108,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex justify-between",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "text-gray-600",
                                        children: "Cached Requests:"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/PerformanceMonitor.tsx",
                                        lineNumber: 116,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "font-mono",
                                        children: metrics.cachedRequests
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/PerformanceMonitor.tsx",
                                        lineNumber: 117,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/PerformanceMonitor.tsx",
                                lineNumber: 115,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "border-t pt-2",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                    onClick: ()=>{
                                        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].clearCache();
                                        setMetrics((prev)=>({
                                                ...prev,
                                                cachedRequests: 0,
                                                cacheHitRate: 0
                                            }));
                                    },
                                    className: "w-full text-xs bg-red-50 text-red-600 py-1 px-2 rounded hover:bg-red-100 transition-colors",
                                    children: "Clear Cache"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/PerformanceMonitor.tsx",
                                    lineNumber: 121,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/PerformanceMonitor.tsx",
                                lineNumber: 120,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/PerformanceMonitor.tsx",
                        lineNumber: 98,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/PerformanceMonitor.tsx",
                lineNumber: 87,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true);
}
function WebVitalsMonitor() {
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if ("TURBOPACK compile-time truthy", 1) return;
        "TURBOPACK unreachable";
        // Monitor Core Web Vitals
        const observer = undefined;
    }, []);
    return null;
}
function ImageLoadMonitor() {
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if ("TURBOPACK compile-time truthy", 1) return;
        "TURBOPACK unreachable";
        const images = undefined;
        const imageObserver = undefined;
    }, []);
    return null;
}
const performanceUtils = {
    // Measure function execution time
    measureTime: async function(fn, label) {
        const start = performance.now();
        const result = await fn();
        const end = performance.now();
        if ("TURBOPACK compile-time truthy", 1) {
            console.log(`${label}: ${Math.round(end - start)}ms`);
        }
        return result;
    },
    // Debounce function for performance
    debounce: function(func, wait) {
        let timeout;
        return (...args)=>{
            clearTimeout(timeout);
            timeout = setTimeout(()=>func(...args), wait);
        };
    },
    // Throttle function for performance
    throttle: function(func, limit) {
        let inThrottle;
        return (...args)=>{
            if (!inThrottle) {
                func(...args);
                inThrottle = true;
                setTimeout(()=>inThrottle = false, limit);
            }
        };
    }
};
}}),
"[project]/src/lib/analytics.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "AnalyticsProvider": (()=>AnalyticsProvider),
    "analyticsUtils": (()=>analyticsUtils),
    "useAnalytics": (()=>useAnalytics),
    "useContentTracking": (()=>useContentTracking),
    "usePageTracking": (()=>usePageTracking)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$vercel$2f$analytics$2f$dist$2f$react$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@vercel/analytics/dist/react/index.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$ga4$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-ga4/dist/index.js [app-ssr] (ecmascript)");
'use client';
;
;
;
;
const AnalyticsContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createContext"])(undefined);
function AnalyticsProvider({ children, gaId = process.env.NEXT_PUBLIC_GA_ID, enabled = ("TURBOPACK compile-time value", "development") === 'production' }) {
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (enabled && gaId) {
            // Initialize Google Analytics
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$ga4$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].initialize(gaId);
            // Track initial page view
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$ga4$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].send({
                hitType: 'pageview',
                page: window.location.pathname
            });
        }
    }, [
        enabled,
        gaId
    ]);
    const trackEvent = (eventName, parameters)=>{
        if (!enabled) {
            console.log('Analytics Event:', eventName, parameters);
            return;
        }
        // Google Analytics 4
        if (gaId) {
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$ga4$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].event(eventName, parameters);
        }
        // Custom analytics (could be sent to your own API)
        if ("TURBOPACK compile-time falsy", 0) {
            "TURBOPACK unreachable";
        }
    };
    const trackPageView = (path, title)=>{
        if (!enabled) {
            console.log('Page View:', path, title);
            return;
        }
        if (gaId) {
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$ga4$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].send({
                hitType: 'pageview',
                page: path,
                title: title || document.title
            });
        }
        trackEvent('page_view', {
            page_path: path,
            page_title: title || document.title
        });
    };
    const trackUserAction = (action, category, label)=>{
        trackEvent('user_action', {
            action,
            category,
            label
        });
    };
    const trackContentInteraction = (contentType, contentId, action)=>{
        trackEvent('content_interaction', {
            content_type: contentType,
            content_id: contentId,
            action
        });
    };
    const value = {
        trackEvent,
        trackPageView,
        trackUserAction,
        trackContentInteraction
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(AnalyticsContext.Provider, {
        value: value,
        children: [
            children,
            enabled && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$vercel$2f$analytics$2f$dist$2f$react$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Analytics"], {}, void 0, false, {
                fileName: "[project]/src/lib/analytics.tsx",
                lineNumber: 115,
                columnNumber: 19
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/lib/analytics.tsx",
        lineNumber: 113,
        columnNumber: 5
    }, this);
}
function useAnalytics() {
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useContext"])(AnalyticsContext);
    if (context === undefined) {
        throw new Error('useAnalytics must be used within an AnalyticsProvider');
    }
    return context;
}
function usePageTracking() {
    const { trackPageView } = useAnalytics();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        trackPageView(window.location.pathname);
    }, [
        trackPageView
    ]);
}
function useContentTracking(contentType, contentId) {
    const { trackContentInteraction } = useAnalytics();
    const trackView = ()=>trackContentInteraction(contentType, contentId, 'view');
    const trackShare = ()=>trackContentInteraction(contentType, contentId, 'share');
    const trackLike = ()=>trackContentInteraction(contentType, contentId, 'like');
    const trackComment = ()=>trackContentInteraction(contentType, contentId, 'comment');
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        trackView();
    }, [
        contentType,
        contentId
    ]);
    return {
        trackView,
        trackShare,
        trackLike,
        trackComment
    };
}
const analyticsUtils = {
    // Get analytics data from localStorage
    getLocalAnalytics: ()=>{
        if ("TURBOPACK compile-time truthy", 1) return [];
        "TURBOPACK unreachable";
    },
    // Get page views for a specific path
    getPageViews: (path)=>{
        const data = analyticsUtils.getLocalAnalytics();
        return data.filter((event)=>event.event === 'page_view' && event.parameters?.page_path === path).length;
    },
    // Get most popular pages
    getPopularPages: (limit = 10)=>{
        const data = analyticsUtils.getLocalAnalytics();
        const pageViews = data.filter((event)=>event.event === 'page_view');
        const pathCounts = {};
        pageViews.forEach((event)=>{
            const path = event.parameters?.page_path || event.url;
            pathCounts[path] = (pathCounts[path] || 0) + 1;
        });
        return Object.entries(pathCounts).map(([path, views])=>({
                path,
                views
            })).sort((a, b)=>b.views - a.views).slice(0, limit);
    },
    // Get analytics summary
    getSummary: ()=>{
        const data = analyticsUtils.getLocalAnalytics();
        const now = new Date();
        const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        const thisWeek = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
        const thisMonth = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);
        const todayEvents = data.filter((event)=>new Date(event.timestamp) >= today);
        const weekEvents = data.filter((event)=>new Date(event.timestamp) >= thisWeek);
        const monthEvents = data.filter((event)=>new Date(event.timestamp) >= thisMonth);
        return {
            total: {
                events: data.length,
                pageViews: data.filter((e)=>e.event === 'page_view').length,
                userActions: data.filter((e)=>e.event === 'user_action').length,
                contentInteractions: data.filter((e)=>e.event === 'content_interaction').length
            },
            today: {
                events: todayEvents.length,
                pageViews: todayEvents.filter((e)=>e.event === 'page_view').length,
                userActions: todayEvents.filter((e)=>e.event === 'user_action').length
            },
            week: {
                events: weekEvents.length,
                pageViews: weekEvents.filter((e)=>e.event === 'page_view').length,
                userActions: weekEvents.filter((e)=>e.event === 'user_action').length
            },
            month: {
                events: monthEvents.length,
                pageViews: monthEvents.filter((e)=>e.event === 'page_view').length,
                userActions: monthEvents.filter((e)=>e.event === 'user_action').length
            }
        };
    },
    // Clear analytics data
    clearData: ()=>{
        if ("TURBOPACK compile-time falsy", 0) {
            "TURBOPACK unreachable";
        }
    }
};
}}),
"[project]/node_modules/next/dist/server/route-modules/app-page/module.compiled.js [app-ssr] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
if ("TURBOPACK compile-time falsy", 0) {
    "TURBOPACK unreachable";
} else {
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    } else {
        if ("TURBOPACK compile-time truthy", 1) {
            if ("TURBOPACK compile-time truthy", 1) {
                module.exports = __turbopack_context__.r("[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)");
            } else {
                "TURBOPACK unreachable";
            }
        } else {
            "TURBOPACK unreachable";
        }
    }
} //# sourceMappingURL=module.compiled.js.map
}}),
"[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
module.exports = __turbopack_context__.r("[project]/node_modules/next/dist/server/route-modules/app-page/module.compiled.js [app-ssr] (ecmascript)").vendored['react-ssr'].ReactJsxDevRuntime; //# sourceMappingURL=react-jsx-dev-runtime.js.map
}}),
"[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
module.exports = __turbopack_context__.r("[project]/node_modules/next/dist/server/route-modules/app-page/module.compiled.js [app-ssr] (ecmascript)").vendored['react-ssr'].React; //# sourceMappingURL=react.js.map
}}),
"[project]/node_modules/@vercel/analytics/dist/react/index.mjs [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Analytics": (()=>Analytics),
    "track": (()=>track)
});
// src/react/index.tsx
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
"use client";
;
// package.json
var name = "@vercel/analytics";
var version = "1.5.0";
// src/queue.ts
var initQueue = ()=>{
    if (window.va) return;
    window.va = function a(...params) {
        (window.vaq = window.vaq || []).push(params);
    };
};
// src/utils.ts
function isBrowser() {
    return typeof window !== "undefined";
}
function detectEnvironment() {
    try {
        const env = ("TURBOPACK compile-time value", "development");
        if ("TURBOPACK compile-time truthy", 1) {
            return "development";
        }
    } catch (e) {}
    return "production";
}
function setMode(mode = "auto") {
    if (mode === "auto") {
        window.vam = detectEnvironment();
        return;
    }
    window.vam = mode;
}
function getMode() {
    const mode = isBrowser() ? window.vam : detectEnvironment();
    return mode || "production";
}
function isProduction() {
    return getMode() === "production";
}
function isDevelopment() {
    return getMode() === "development";
}
function removeKey(key, { [key]: _, ...rest }) {
    return rest;
}
function parseProperties(properties, options) {
    if (!properties) return void 0;
    let props = properties;
    const errorProperties = [];
    for (const [key, value] of Object.entries(properties)){
        if (typeof value === "object" && value !== null) {
            if (options.strip) {
                props = removeKey(key, props);
            } else {
                errorProperties.push(key);
            }
        }
    }
    if (errorProperties.length > 0 && !options.strip) {
        throw Error(`The following properties are not valid: ${errorProperties.join(", ")}. Only strings, numbers, booleans, and null are allowed.`);
    }
    return props;
}
function getScriptSrc(props) {
    if (props.scriptSrc) {
        return props.scriptSrc;
    }
    if (isDevelopment()) {
        return "https://va.vercel-scripts.com/v1/script.debug.js";
    }
    if (props.basePath) {
        return `${props.basePath}/insights/script.js`;
    }
    return "/_vercel/insights/script.js";
}
// src/generic.ts
function inject(props = {
    debug: true
}) {
    var _a;
    if (!isBrowser()) return;
    setMode(props.mode);
    initQueue();
    if (props.beforeSend) {
        (_a = window.va) == null ? void 0 : _a.call(window, "beforeSend", props.beforeSend);
    }
    const src = getScriptSrc(props);
    if (document.head.querySelector(`script[src*="${src}"]`)) return;
    const script = document.createElement("script");
    script.src = src;
    script.defer = true;
    script.dataset.sdkn = name + (props.framework ? `/${props.framework}` : "");
    script.dataset.sdkv = version;
    if (props.disableAutoTrack) {
        script.dataset.disableAutoTrack = "1";
    }
    if (props.endpoint) {
        script.dataset.endpoint = props.endpoint;
    } else if (props.basePath) {
        script.dataset.endpoint = `${props.basePath}/insights`;
    }
    if (props.dsn) {
        script.dataset.dsn = props.dsn;
    }
    script.onerror = ()=>{
        const errorMessage = isDevelopment() ? "Please check if any ad blockers are enabled and try again." : "Be sure to enable Web Analytics for your project and deploy again. See https://vercel.com/docs/analytics/quickstart for more information.";
        console.log(`[Vercel Web Analytics] Failed to load script from ${src}. ${errorMessage}`);
    };
    if (isDevelopment() && props.debug === false) {
        script.dataset.debug = "false";
    }
    document.head.appendChild(script);
}
function track(name2, properties, options) {
    var _a, _b;
    if (!isBrowser()) {
        const msg = "[Vercel Web Analytics] Please import `track` from `@vercel/analytics/server` when using this function in a server environment";
        if (isProduction()) {
            console.warn(msg);
        } else {
            throw new Error(msg);
        }
        return;
    }
    if (!properties) {
        (_a = window.va) == null ? void 0 : _a.call(window, "event", {
            name: name2,
            options
        });
        return;
    }
    try {
        const props = parseProperties(properties, {
            strip: isProduction()
        });
        (_b = window.va) == null ? void 0 : _b.call(window, "event", {
            name: name2,
            data: props,
            options
        });
    } catch (err) {
        if (err instanceof Error && isDevelopment()) {
            console.error(err);
        }
    }
}
function pageview({ route, path }) {
    var _a;
    (_a = window.va) == null ? void 0 : _a.call(window, "pageview", {
        route,
        path
    });
}
// src/react/utils.ts
function getBasePath() {
    if (typeof process === "undefined" || typeof process.env === "undefined") {
        return void 0;
    }
    return process.env.REACT_APP_VERCEL_OBSERVABILITY_BASEPATH;
}
// src/react/index.tsx
function Analytics(props) {
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        var _a;
        if (props.beforeSend) {
            (_a = window.va) == null ? void 0 : _a.call(window, "beforeSend", props.beforeSend);
        }
    }, [
        props.beforeSend
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        inject({
            framework: props.framework || "react",
            basePath: props.basePath ?? getBasePath(),
            ...props.route !== void 0 && {
                disableAutoTrack: true
            },
            ...props
        });
    }, []);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (props.route && props.path) {
            pageview({
                route: props.route,
                path: props.path
            });
        }
    }, [
        props.route,
        props.path
    ]);
    return null;
}
;
 //# sourceMappingURL=index.mjs.map
}}),
"[project]/node_modules/react-ga4/dist/gtag.js [app-ssr] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports["default"] = void 0;
var gtag = function gtag() {
    for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){
        args[_key] = arguments[_key];
    }
    if (typeof window !== "undefined") {
        var _window;
        if (typeof window.gtag === "undefined") {
            window.dataLayer = window.dataLayer || [];
            window.gtag = function gtag() {
                window.dataLayer.push(arguments);
            };
        }
        (_window = window).gtag.apply(_window, args);
    }
};
var _default = gtag;
exports["default"] = _default;
}}),
"[project]/node_modules/react-ga4/dist/format.js [app-ssr] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports["default"] = format;
var smallWords = /^(a|an|and|as|at|but|by|en|for|if|in|nor|of|on|or|per|the|to|vs?\.?|via)$/i;
function toTitleCase(string) {
    return string.toString().trim().replace(/[A-Za-z0-9\u00C0-\u00FF]+[^\s-]*/g, function(match, index, title) {
        if (index > 0 && index + match.length !== title.length && match.search(smallWords) > -1 && title.charAt(index - 2) !== ":" && (title.charAt(index + match.length) !== "-" || title.charAt(index - 1) === "-") && title.charAt(index - 1).search(/[^\s-]/) < 0) {
            return match.toLowerCase();
        }
        if (match.substr(1).search(/[A-Z]|\../) > -1) {
            return match;
        }
        return match.charAt(0).toUpperCase() + match.substr(1);
    });
}
// See if s could be an email address. We don't want to send personal data like email.
// https://support.google.com/analytics/answer/2795983?hl=en
function mightBeEmail(s) {
    // There's no point trying to validate rfc822 fully, just look for ...@...
    return typeof s === "string" && s.indexOf("@") !== -1;
}
var redacted = "REDACTED (Potential Email Address)";
function redactEmail(string) {
    if (mightBeEmail(string)) {
        console.warn("This arg looks like an email address, redacting.");
        return redacted;
    }
    return string;
}
function format() {
    var s = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : "";
    var titleCase = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : true;
    var redactingEmail = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : true;
    var _str = s || "";
    if (titleCase) {
        _str = toTitleCase(s);
    }
    if (redactingEmail) {
        _str = redactEmail(_str);
    }
    return _str;
}
}}),
"[project]/node_modules/react-ga4/dist/ga4.js [app-ssr] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports["default"] = exports.GA4 = void 0;
var _gtag = _interopRequireDefault(__turbopack_context__.r("[project]/node_modules/react-ga4/dist/gtag.js [app-ssr] (ecmascript)"));
var _format = _interopRequireDefault(__turbopack_context__.r("[project]/node_modules/react-ga4/dist/format.js [app-ssr] (ecmascript)"));
var _excluded = [
    "eventCategory",
    "eventAction",
    "eventLabel",
    "eventValue",
    "hitType"
], _excluded2 = [
    "title",
    "location"
], _excluded3 = [
    "page",
    "hitType"
];
function _interopRequireDefault(obj) {
    return obj && obj.__esModule ? obj : {
        "default": obj
    };
}
function _objectWithoutProperties(source, excluded) {
    if (source == null) return {};
    var target = _objectWithoutPropertiesLoose(source, excluded);
    var key, i;
    if (Object.getOwnPropertySymbols) {
        var sourceSymbolKeys = Object.getOwnPropertySymbols(source);
        for(i = 0; i < sourceSymbolKeys.length; i++){
            key = sourceSymbolKeys[i];
            if (excluded.indexOf(key) >= 0) continue;
            if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;
            target[key] = source[key];
        }
    }
    return target;
}
function _objectWithoutPropertiesLoose(source, excluded) {
    if (source == null) return {};
    var target = {};
    var sourceKeys = Object.keys(source);
    var key, i;
    for(i = 0; i < sourceKeys.length; i++){
        key = sourceKeys[i];
        if (excluded.indexOf(key) >= 0) continue;
        target[key] = source[key];
    }
    return target;
}
function _typeof(obj) {
    "@babel/helpers - typeof";
    return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(obj) {
        return typeof obj;
    } : function(obj) {
        return obj && "function" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? "symbol" : typeof obj;
    }, _typeof(obj);
}
function _toConsumableArray(arr) {
    return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread();
}
function _nonIterableSpread() {
    throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
}
function _iterableToArray(iter) {
    if (typeof Symbol !== "undefined" && iter[Symbol.iterator] != null || iter["@@iterator"] != null) return Array.from(iter);
}
function _arrayWithoutHoles(arr) {
    if (Array.isArray(arr)) return _arrayLikeToArray(arr);
}
function ownKeys(object, enumerableOnly) {
    var keys = Object.keys(object);
    if (Object.getOwnPropertySymbols) {
        var symbols = Object.getOwnPropertySymbols(object);
        enumerableOnly && (symbols = symbols.filter(function(sym) {
            return Object.getOwnPropertyDescriptor(object, sym).enumerable;
        })), keys.push.apply(keys, symbols);
    }
    return keys;
}
function _objectSpread(target) {
    for(var i = 1; i < arguments.length; i++){
        var source = null != arguments[i] ? arguments[i] : {};
        i % 2 ? ownKeys(Object(source), !0).forEach(function(key) {
            _defineProperty(target, key, source[key]);
        }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function(key) {
            Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));
        });
    }
    return target;
}
function _slicedToArray(arr, i) {
    return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest();
}
function _nonIterableRest() {
    throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
}
function _unsupportedIterableToArray(o, minLen) {
    if (!o) return;
    if (typeof o === "string") return _arrayLikeToArray(o, minLen);
    var n = Object.prototype.toString.call(o).slice(8, -1);
    if (n === "Object" && o.constructor) n = o.constructor.name;
    if (n === "Map" || n === "Set") return Array.from(o);
    if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);
}
function _arrayLikeToArray(arr, len) {
    if (len == null || len > arr.length) len = arr.length;
    for(var i = 0, arr2 = new Array(len); i < len; i++)arr2[i] = arr[i];
    return arr2;
}
function _iterableToArrayLimit(arr, i) {
    var _i = null == arr ? null : "undefined" != typeof Symbol && arr[Symbol.iterator] || arr["@@iterator"];
    if (null != _i) {
        var _s, _e, _x, _r, _arr = [], _n = !0, _d = !1;
        try {
            if (_x = (_i = _i.call(arr)).next, 0 === i) {
                if (Object(_i) !== _i) return;
                _n = !1;
            } else for(; !(_n = (_s = _x.call(_i)).done) && (_arr.push(_s.value), _arr.length !== i); _n = !0);
        } catch (err) {
            _d = !0, _e = err;
        } finally{
            try {
                if (!_n && null != _i["return"] && (_r = _i["return"](), Object(_r) !== _r)) return;
            } finally{
                if (_d) throw _e;
            }
        }
        return _arr;
    }
}
function _arrayWithHoles(arr) {
    if (Array.isArray(arr)) return arr;
}
function _classCallCheck(instance, Constructor) {
    if (!(instance instanceof Constructor)) {
        throw new TypeError("Cannot call a class as a function");
    }
}
function _defineProperties(target, props) {
    for(var i = 0; i < props.length; i++){
        var descriptor = props[i];
        descriptor.enumerable = descriptor.enumerable || false;
        descriptor.configurable = true;
        if ("value" in descriptor) descriptor.writable = true;
        Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor);
    }
}
function _createClass(Constructor, protoProps, staticProps) {
    if (protoProps) _defineProperties(Constructor.prototype, protoProps);
    if (staticProps) _defineProperties(Constructor, staticProps);
    Object.defineProperty(Constructor, "prototype", {
        writable: false
    });
    return Constructor;
}
function _defineProperty(obj, key, value) {
    key = _toPropertyKey(key);
    if (key in obj) {
        Object.defineProperty(obj, key, {
            value: value,
            enumerable: true,
            configurable: true,
            writable: true
        });
    } else {
        obj[key] = value;
    }
    return obj;
}
function _toPropertyKey(arg) {
    var key = _toPrimitive(arg, "string");
    return _typeof(key) === "symbol" ? key : String(key);
}
function _toPrimitive(input, hint) {
    if (_typeof(input) !== "object" || input === null) return input;
    var prim = input[Symbol.toPrimitive];
    if (prim !== undefined) {
        var res = prim.call(input, hint || "default");
        if (_typeof(res) !== "object") return res;
        throw new TypeError("@@toPrimitive must return a primitive value.");
    }
    return (hint === "string" ? String : Number)(input);
}
/*
Links
https://developers.google.com/gtagjs/reference/api
https://developers.google.com/tag-platform/gtagjs/reference
*/ /**
 * @typedef GaOptions
 * @type {Object}
 * @property {boolean} [cookieUpdate=true]
 * @property {number} [cookieExpires=63072000] Default two years
 * @property {string} [cookieDomain="auto"]
 * @property {string} [cookieFlags]
 * @property {string} [userId]
 * @property {string} [clientId]
 * @property {boolean} [anonymizeIp]
 * @property {string} [contentGroup1]
 * @property {string} [contentGroup2]
 * @property {string} [contentGroup3]
 * @property {string} [contentGroup4]
 * @property {string} [contentGroup5]
 * @property {boolean} [allowAdFeatures=true]
 * @property {boolean} [allowAdPersonalizationSignals]
 * @property {boolean} [nonInteraction]
 * @property {string} [page]
 */ /**
 * @typedef UaEventOptions
 * @type {Object}
 * @property {string} action
 * @property {string} category
 * @property {string} [label]
 * @property {number} [value]
 * @property {boolean} [nonInteraction]
 * @property {('beacon'|'xhr'|'image')} [transport]
 */ /**
 * @typedef InitOptions
 * @type {Object}
 * @property {string} trackingId
 * @property {GaOptions|any} [gaOptions]
 * @property {Object} [gtagOptions] New parameter
 */ var GA4 = /*#__PURE__*/ function() {
    function GA4() {
        var _this = this;
        _classCallCheck(this, GA4);
        _defineProperty(this, "reset", function() {
            _this.isInitialized = false;
            _this._testMode = false;
            _this._currentMeasurementId;
            _this._hasLoadedGA = false;
            _this._isQueuing = false;
            _this._queueGtag = [];
        });
        _defineProperty(this, "_gtag", function() {
            for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){
                args[_key] = arguments[_key];
            }
            if (!_this._testMode) {
                if (_this._isQueuing) {
                    _this._queueGtag.push(args);
                } else {
                    _gtag["default"].apply(void 0, args);
                }
            } else {
                _this._queueGtag.push(args);
            }
        });
        _defineProperty(this, "_loadGA", function(GA_MEASUREMENT_ID, nonce) {
            var gtagUrl = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : "https://www.googletagmanager.com/gtag/js";
            if (typeof window === "undefined" || typeof document === "undefined") {
                return;
            }
            if (!_this._hasLoadedGA) {
                // Global Site Tag (gtag.js) - Google Analytics
                var script = document.createElement("script");
                script.async = true;
                script.src = "".concat(gtagUrl, "?id=").concat(GA_MEASUREMENT_ID);
                if (nonce) {
                    script.setAttribute("nonce", nonce);
                }
                document.body.appendChild(script);
                window.dataLayer = window.dataLayer || [];
                window.gtag = function gtag() {
                    window.dataLayer.push(arguments);
                };
                _this._hasLoadedGA = true;
            }
        });
        _defineProperty(this, "_toGtagOptions", function(gaOptions) {
            if (!gaOptions) {
                return;
            }
            var mapFields = {
                // Old https://developers.google.com/analytics/devguides/collection/analyticsjs/field-reference#cookieUpdate
                // New https://developers.google.com/analytics/devguides/collection/gtagjs/cookies-user-id#cookie_update
                cookieUpdate: "cookie_update",
                cookieExpires: "cookie_expires",
                cookieDomain: "cookie_domain",
                cookieFlags: "cookie_flags",
                // must be in set method?
                userId: "user_id",
                clientId: "client_id",
                anonymizeIp: "anonymize_ip",
                // https://support.google.com/analytics/answer/2853546?hl=en#zippy=%2Cin-this-article
                contentGroup1: "content_group1",
                contentGroup2: "content_group2",
                contentGroup3: "content_group3",
                contentGroup4: "content_group4",
                contentGroup5: "content_group5",
                // https://support.google.com/analytics/answer/9050852?hl=en
                allowAdFeatures: "allow_google_signals",
                allowAdPersonalizationSignals: "allow_ad_personalization_signals",
                nonInteraction: "non_interaction",
                page: "page_path",
                hitCallback: "event_callback"
            };
            var gtagOptions = Object.entries(gaOptions).reduce(function(prev, _ref) {
                var _ref2 = _slicedToArray(_ref, 2), key = _ref2[0], value = _ref2[1];
                if (mapFields[key]) {
                    prev[mapFields[key]] = value;
                } else {
                    prev[key] = value;
                }
                return prev;
            }, {});
            return gtagOptions;
        });
        _defineProperty(this, "initialize", function(GA_MEASUREMENT_ID) {
            var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
            if (!GA_MEASUREMENT_ID) {
                throw new Error("Require GA_MEASUREMENT_ID");
            }
            var initConfigs = typeof GA_MEASUREMENT_ID === "string" ? [
                {
                    trackingId: GA_MEASUREMENT_ID
                }
            ] : GA_MEASUREMENT_ID;
            _this._currentMeasurementId = initConfigs[0].trackingId;
            var gaOptions = options.gaOptions, gtagOptions = options.gtagOptions, nonce = options.nonce, _options$testMode = options.testMode, testMode = _options$testMode === void 0 ? false : _options$testMode, gtagUrl = options.gtagUrl;
            _this._testMode = testMode;
            if (!testMode) {
                _this._loadGA(_this._currentMeasurementId, nonce, gtagUrl);
            }
            if (!_this.isInitialized) {
                _this._gtag("js", new Date());
                initConfigs.forEach(function(config) {
                    var mergedGtagOptions = _objectSpread(_objectSpread(_objectSpread({}, _this._toGtagOptions(_objectSpread(_objectSpread({}, gaOptions), config.gaOptions))), gtagOptions), config.gtagOptions);
                    if (Object.keys(mergedGtagOptions).length) {
                        _this._gtag("config", config.trackingId, mergedGtagOptions);
                    } else {
                        _this._gtag("config", config.trackingId);
                    }
                });
            }
            _this.isInitialized = true;
            if (!testMode) {
                var queues = _toConsumableArray(_this._queueGtag);
                _this._queueGtag = [];
                _this._isQueuing = false;
                while(queues.length){
                    var queue = queues.shift();
                    _this._gtag.apply(_this, _toConsumableArray(queue));
                    if (queue[0] === "get") {
                        _this._isQueuing = true;
                    }
                }
            }
        });
        _defineProperty(this, "set", function(fieldsObject) {
            if (!fieldsObject) {
                console.warn("`fieldsObject` is required in .set()");
                return;
            }
            if (_typeof(fieldsObject) !== "object") {
                console.warn("Expected `fieldsObject` arg to be an Object");
                return;
            }
            if (Object.keys(fieldsObject).length === 0) {
                console.warn("empty `fieldsObject` given to .set()");
            }
            _this._gaCommand("set", fieldsObject);
        });
        _defineProperty(this, "_gaCommandSendEvent", function(eventCategory, eventAction, eventLabel, eventValue, fieldsObject) {
            _this._gtag("event", eventAction, _objectSpread(_objectSpread({
                event_category: eventCategory,
                event_label: eventLabel,
                value: eventValue
            }, fieldsObject && {
                non_interaction: fieldsObject.nonInteraction
            }), _this._toGtagOptions(fieldsObject)));
        });
        _defineProperty(this, "_gaCommandSendEventParameters", function() {
            for(var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++){
                args[_key2] = arguments[_key2];
            }
            if (typeof args[0] === "string") {
                _this._gaCommandSendEvent.apply(_this, _toConsumableArray(args.slice(1)));
            } else {
                var _args$ = args[0], eventCategory = _args$.eventCategory, eventAction = _args$.eventAction, eventLabel = _args$.eventLabel, eventValue = _args$.eventValue, hitType = _args$.hitType, rest = _objectWithoutProperties(_args$, _excluded);
                _this._gaCommandSendEvent(eventCategory, eventAction, eventLabel, eventValue, rest);
            }
        });
        _defineProperty(this, "_gaCommandSendTiming", function(timingCategory, timingVar, timingValue, timingLabel) {
            _this._gtag("event", "timing_complete", {
                name: timingVar,
                value: timingValue,
                event_category: timingCategory,
                event_label: timingLabel
            });
        });
        _defineProperty(this, "_gaCommandSendPageview", function(page, fieldsObject) {
            if (fieldsObject && Object.keys(fieldsObject).length) {
                var _this$_toGtagOptions = _this._toGtagOptions(fieldsObject), title = _this$_toGtagOptions.title, location = _this$_toGtagOptions.location, rest = _objectWithoutProperties(_this$_toGtagOptions, _excluded2);
                _this._gtag("event", "page_view", _objectSpread(_objectSpread(_objectSpread(_objectSpread({}, page && {
                    page_path: page
                }), title && {
                    page_title: title
                }), location && {
                    page_location: location
                }), rest));
            } else if (page) {
                _this._gtag("event", "page_view", {
                    page_path: page
                });
            } else {
                _this._gtag("event", "page_view");
            }
        });
        _defineProperty(this, "_gaCommandSendPageviewParameters", function() {
            for(var _len3 = arguments.length, args = new Array(_len3), _key3 = 0; _key3 < _len3; _key3++){
                args[_key3] = arguments[_key3];
            }
            if (typeof args[0] === "string") {
                _this._gaCommandSendPageview.apply(_this, _toConsumableArray(args.slice(1)));
            } else {
                var _args$2 = args[0], page = _args$2.page, hitType = _args$2.hitType, rest = _objectWithoutProperties(_args$2, _excluded3);
                _this._gaCommandSendPageview(page, rest);
            }
        });
        _defineProperty(this, "_gaCommandSend", function() {
            for(var _len4 = arguments.length, args = new Array(_len4), _key4 = 0; _key4 < _len4; _key4++){
                args[_key4] = arguments[_key4];
            }
            var hitType = typeof args[0] === "string" ? args[0] : args[0].hitType;
            switch(hitType){
                case "event":
                    _this._gaCommandSendEventParameters.apply(_this, args);
                    break;
                case "pageview":
                    _this._gaCommandSendPageviewParameters.apply(_this, args);
                    break;
                case "timing":
                    _this._gaCommandSendTiming.apply(_this, _toConsumableArray(args.slice(1)));
                    break;
                case "screenview":
                case "transaction":
                case "item":
                case "social":
                case "exception":
                    console.warn("Unsupported send command: ".concat(hitType));
                    break;
                default:
                    console.warn("Send command doesn't exist: ".concat(hitType));
            }
        });
        _defineProperty(this, "_gaCommandSet", function() {
            for(var _len5 = arguments.length, args = new Array(_len5), _key5 = 0; _key5 < _len5; _key5++){
                args[_key5] = arguments[_key5];
            }
            if (typeof args[0] === "string") {
                args[0] = _defineProperty({}, args[0], args[1]);
            }
            _this._gtag("set", _this._toGtagOptions(args[0]));
        });
        _defineProperty(this, "_gaCommand", function(command) {
            for(var _len6 = arguments.length, args = new Array(_len6 > 1 ? _len6 - 1 : 0), _key6 = 1; _key6 < _len6; _key6++){
                args[_key6 - 1] = arguments[_key6];
            }
            switch(command){
                case "send":
                    _this._gaCommandSend.apply(_this, args);
                    break;
                case "set":
                    _this._gaCommandSet.apply(_this, args);
                    break;
                default:
                    console.warn("Command doesn't exist: ".concat(command));
            }
        });
        _defineProperty(this, "ga", function() {
            for(var _len7 = arguments.length, args = new Array(_len7), _key7 = 0; _key7 < _len7; _key7++){
                args[_key7] = arguments[_key7];
            }
            if (typeof args[0] === "string") {
                _this._gaCommand.apply(_this, args);
            } else {
                var readyCallback = args[0];
                _this._gtag("get", _this._currentMeasurementId, "client_id", function(clientId) {
                    _this._isQueuing = false;
                    var queues = _this._queueGtag;
                    readyCallback({
                        get: function get(property) {
                            return property === "clientId" ? clientId : property === "trackingId" ? _this._currentMeasurementId : property === "apiVersion" ? "1" : undefined;
                        }
                    });
                    while(queues.length){
                        var queue = queues.shift();
                        _this._gtag.apply(_this, _toConsumableArray(queue));
                    }
                });
                _this._isQueuing = true;
            }
            return _this.ga;
        });
        _defineProperty(this, "event", function(optionsOrName, params) {
            if (typeof optionsOrName === "string") {
                _this._gtag("event", optionsOrName, _this._toGtagOptions(params));
            } else {
                var action = optionsOrName.action, category = optionsOrName.category, label = optionsOrName.label, value = optionsOrName.value, nonInteraction = optionsOrName.nonInteraction, transport = optionsOrName.transport;
                if (!category || !action) {
                    console.warn("args.category AND args.action are required in event()");
                    return;
                }
                // Required Fields
                var fieldObject = {
                    hitType: "event",
                    eventCategory: (0, _format["default"])(category),
                    eventAction: (0, _format["default"])(action)
                };
                // Optional Fields
                if (label) {
                    fieldObject.eventLabel = (0, _format["default"])(label);
                }
                if (typeof value !== "undefined") {
                    if (typeof value !== "number") {
                        console.warn("Expected `args.value` arg to be a Number.");
                    } else {
                        fieldObject.eventValue = value;
                    }
                }
                if (typeof nonInteraction !== "undefined") {
                    if (typeof nonInteraction !== "boolean") {
                        console.warn("`args.nonInteraction` must be a boolean.");
                    } else {
                        fieldObject.nonInteraction = nonInteraction;
                    }
                }
                if (typeof transport !== "undefined") {
                    if (typeof transport !== "string") {
                        console.warn("`args.transport` must be a string.");
                    } else {
                        if ([
                            "beacon",
                            "xhr",
                            "image"
                        ].indexOf(transport) === -1) {
                            console.warn("`args.transport` must be either one of these values: `beacon`, `xhr` or `image`");
                        }
                        fieldObject.transport = transport;
                    }
                }
                _this._gaCommand("send", fieldObject);
            }
        });
        _defineProperty(this, "send", function(fieldObject) {
            _this._gaCommand("send", fieldObject);
        });
        this.reset();
    }
    _createClass(GA4, [
        {
            key: "gtag",
            value: function gtag() {
                this._gtag.apply(this, arguments);
            }
        }
    ]);
    return GA4;
}();
exports.GA4 = GA4;
var _default = new GA4();
exports["default"] = _default;
}}),
"[project]/node_modules/react-ga4/dist/index.js [app-ssr] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
function _typeof(obj) {
    "@babel/helpers - typeof";
    return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(obj) {
        return typeof obj;
    } : function(obj) {
        return obj && "function" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? "symbol" : typeof obj;
    }, _typeof(obj);
}
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports["default"] = exports.ReactGAImplementation = void 0;
var _ga = _interopRequireWildcard(__turbopack_context__.r("[project]/node_modules/react-ga4/dist/ga4.js [app-ssr] (ecmascript)"));
function _getRequireWildcardCache(nodeInterop) {
    if (typeof WeakMap !== "function") return null;
    var cacheBabelInterop = new WeakMap();
    var cacheNodeInterop = new WeakMap();
    return (_getRequireWildcardCache = function _getRequireWildcardCache(nodeInterop) {
        return nodeInterop ? cacheNodeInterop : cacheBabelInterop;
    })(nodeInterop);
}
function _interopRequireWildcard(obj, nodeInterop) {
    if (!nodeInterop && obj && obj.__esModule) {
        return obj;
    }
    if (obj === null || _typeof(obj) !== "object" && typeof obj !== "function") {
        return {
            "default": obj
        };
    }
    var cache = _getRequireWildcardCache(nodeInterop);
    if (cache && cache.has(obj)) {
        return cache.get(obj);
    }
    var newObj = {};
    var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor;
    for(var key in obj){
        if (key !== "default" && Object.prototype.hasOwnProperty.call(obj, key)) {
            var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null;
            if (desc && (desc.get || desc.set)) {
                Object.defineProperty(newObj, key, desc);
            } else {
                newObj[key] = obj[key];
            }
        }
    }
    newObj["default"] = obj;
    if (cache) {
        cache.set(obj, newObj);
    }
    return newObj;
}
var ReactGAImplementation = _ga.GA4;
exports.ReactGAImplementation = ReactGAImplementation;
var _default = _ga["default"];
exports["default"] = _default;
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__7b60ae06._.js.map