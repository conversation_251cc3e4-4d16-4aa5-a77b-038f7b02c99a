{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Development/BuzzEdge/buzzedge-frontend/src/services/blogService.ts"], "sourcesContent": ["import { apiClient } from \"@/lib/api\";\nimport {\n  BlogListResponse,\n  BlogPostResponse,\n  SearchResponse,\n  CategoryResponse,\n  BlogPostSummary,\n} from \"@/types/blog\";\n\nexport class BlogService {\n  /**\n   * Get all published blog posts with pagination\n   */\n  static async getAllPosts(\n    page: number = 1,\n    limit: number = 10\n  ): Promise<BlogListResponse> {\n    return apiClient.get<BlogListResponse>(\n      \"/api/blogs\",\n      {\n        page: page.toString(),\n        limit: limit.toString(),\n      },\n      { cache: true, ttl: 300 } // 5 minutes cache\n    );\n  }\n\n  /**\n   * Get a single blog post by slug\n   */\n  static async getPostBySlug(slug: string): Promise<BlogPostResponse> {\n    return apiClient.get<BlogPostResponse>(\n      `/api/blogs/${slug}`,\n      undefined,\n      { cache: true, ttl: 600 } // 10 minutes cache\n    );\n  }\n\n  /**\n   * Search blog posts\n   */\n  static async searchPosts(\n    query: string,\n    page: number = 1,\n    limit: number = 10\n  ): Promise<SearchResponse> {\n    return apiClient.get<SearchResponse>(\n      \"/api/blogs/search\",\n      {\n        q: query,\n        page: page.toString(),\n        limit: limit.toString(),\n      },\n      { cache: true, ttl: 60 } // 1 minute cache for search\n    );\n  }\n\n  /**\n   * Get posts by category\n   */\n  static async getPostsByCategory(\n    category: string,\n    page: number = 1,\n    limit: number = 10\n  ): Promise<CategoryResponse> {\n    return apiClient.get<CategoryResponse>(\n      `/api/blogs/category/${category}`,\n      {\n        page: page.toString(),\n        limit: limit.toString(),\n      },\n      { cache: true, ttl: 300 } // 5 minutes cache\n    );\n  }\n\n  /**\n   * Increment post view count\n   */\n  static async incrementPostViews(slug: string): Promise<{ views: number }> {\n    return apiClient.post(`/api/blogs/${slug}/view`);\n  }\n\n  /**\n   * Get featured posts (most viewed)\n   */\n  static async getFeaturedPosts(limit: number = 5): Promise<BlogPostSummary[]> {\n    const response = await this.getAllPosts(1, limit);\n    return response.data.posts;\n  }\n\n  /**\n   * Get recent posts\n   */\n  static async getRecentPosts(limit: number = 5): Promise<BlogPostSummary[]> {\n    const response = await this.getAllPosts(1, limit);\n    return response.data.posts;\n  }\n}\n\n// Utility functions for client-side data processing\nexport const blogUtils = {\n  /**\n   * Format date for display\n   */\n  formatDate: (dateString: string): string => {\n    const date = new Date(dateString);\n    return date.toLocaleDateString(\"en-US\", {\n      year: \"numeric\",\n      month: \"long\",\n      day: \"numeric\",\n    });\n  },\n\n  /**\n   * Calculate reading time based on content length\n   */\n  calculateReadingTime: (content: string): number => {\n    const wordsPerMinute = 200;\n    const wordCount = content.split(/\\s+/).length;\n    return Math.ceil(wordCount / wordsPerMinute);\n  },\n\n  /**\n   * Get category color class\n   */\n  getCategoryColor: (category: string): string => {\n    switch (category.toLowerCase()) {\n      case \"ai tools\":\n        return \"bg-blue-100 text-blue-800\";\n      case \"productivity\":\n        return \"bg-green-100 text-green-800\";\n      case \"developer tools\":\n        return \"bg-purple-100 text-purple-800\";\n      case \"design tools\":\n        return \"bg-pink-100 text-pink-800\";\n      default:\n        return \"bg-gray-100 text-gray-800\";\n    }\n  },\n\n  /**\n   * Generate SEO-friendly URL slug\n   */\n  generateSlug: (title: string): string => {\n    return title\n      .toLowerCase()\n      .replace(/[^a-z0-9]+/g, \"-\")\n      .replace(/(^-|-$)/g, \"\");\n  },\n\n  /**\n   * Truncate text to specified length\n   */\n  truncateText: (text: string, maxLength: number): string => {\n    if (text.length <= maxLength) return text;\n    return text.substring(0, maxLength).replace(/\\s+\\S*$/, \"\") + \"...\";\n  },\n};\n"], "names": [], "mappings": ";;;;AAAA;;AASO,MAAM;IACX;;GAEC,GACD,aAAa,YACX,OAAe,CAAC,EAChB,QAAgB,EAAE,EACS;QAC3B,OAAO,iHAAA,CAAA,YAAS,CAAC,GAAG,CAClB,cACA;YACE,MAAM,KAAK,QAAQ;YACnB,OAAO,MAAM,QAAQ;QACvB,GACA;YAAE,OAAO;YAAM,KAAK;QAAI,EAAE,kBAAkB;;IAEhD;IAEA;;GAEC,GACD,aAAa,cAAc,IAAY,EAA6B;QAClE,OAAO,iHAAA,CAAA,YAAS,CAAC,GAAG,CAClB,CAAC,WAAW,EAAE,MAAM,EACpB,WACA;YAAE,OAAO;YAAM,KAAK;QAAI,EAAE,mBAAmB;;IAEjD;IAEA;;GAEC,GACD,aAAa,YACX,KAAa,EACb,OAAe,CAAC,EAChB,QAAgB,EAAE,EACO;QACzB,OAAO,iHAAA,CAAA,YAAS,CAAC,GAAG,CAClB,qBACA;YACE,GAAG;YACH,MAAM,KAAK,QAAQ;YACnB,OAAO,MAAM,QAAQ;QACvB,GACA;YAAE,OAAO;YAAM,KAAK;QAAG,EAAE,4BAA4B;;IAEzD;IAEA;;GAEC,GACD,aAAa,mBACX,QAAgB,EAChB,OAAe,CAAC,EAChB,QAAgB,EAAE,EACS;QAC3B,OAAO,iHAAA,CAAA,YAAS,CAAC,GAAG,CAClB,CAAC,oBAAoB,EAAE,UAAU,EACjC;YACE,MAAM,KAAK,QAAQ;YACnB,OAAO,MAAM,QAAQ;QACvB,GACA;YAAE,OAAO;YAAM,KAAK;QAAI,EAAE,kBAAkB;;IAEhD;IAEA;;GAEC,GACD,aAAa,mBAAmB,IAAY,EAA8B;QACxE,OAAO,iHAAA,CAAA,YAAS,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE,KAAK,KAAK,CAAC;IACjD;IAEA;;GAEC,GACD,aAAa,iBAAiB,QAAgB,CAAC,EAA8B;QAC3E,MAAM,WAAW,MAAM,IAAI,CAAC,WAAW,CAAC,GAAG;QAC3C,OAAO,SAAS,IAAI,CAAC,KAAK;IAC5B;IAEA;;GAEC,GACD,aAAa,eAAe,QAAgB,CAAC,EAA8B;QACzE,MAAM,WAAW,MAAM,IAAI,CAAC,WAAW,CAAC,GAAG;QAC3C,OAAO,SAAS,IAAI,CAAC,KAAK;IAC5B;AACF;AAGO,MAAM,YAAY;IACvB;;GAEC,GACD,YAAY,CAAC;QACX,MAAM,OAAO,IAAI,KAAK;QACtB,OAAO,KAAK,kBAAkB,CAAC,SAAS;YACtC,MAAM;YACN,OAAO;YACP,KAAK;QACP;IACF;IAEA;;GAEC,GACD,sBAAsB,CAAC;QACrB,MAAM,iBAAiB;QACvB,MAAM,YAAY,QAAQ,KAAK,CAAC,OAAO,MAAM;QAC7C,OAAO,KAAK,IAAI,CAAC,YAAY;IAC/B;IAEA;;GAEC,GACD,kBAAkB,CAAC;QACjB,OAAQ,SAAS,WAAW;YAC1B,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA;;GAEC,GACD,cAAc,CAAC;QACb,OAAO,MACJ,WAAW,GACX,OAAO,CAAC,eAAe,KACvB,OAAO,CAAC,YAAY;IACzB;IAEA;;GAEC,GACD,cAAc,CAAC,MAAc;QAC3B,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;QACrC,OAAO,KAAK,SAAS,CAAC,GAAG,WAAW,OAAO,CAAC,WAAW,MAAM;IAC/D;AACF", "debugId": null}}, {"offset": {"line": 154, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Development/BuzzEdge/buzzedge-frontend/src/components/SearchSuggestions.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect, useRef } from 'react';\nimport Link from 'next/link';\nimport { BlogService } from '@/services/blogService';\nimport { BlogPostSummary } from '@/types/blog';\n\ninterface SearchSuggestionsProps {\n  query: string;\n  onSelect: () => void;\n}\n\nexport function SearchSuggestions({ query, onSelect }: SearchSuggestionsProps) {\n  const [suggestions, setSuggestions] = useState<BlogPostSummary[]>([]);\n  const [isLoading, setIsLoading] = useState(false);\n  const [isVisible, setIsVisible] = useState(false);\n  const timeoutRef = useRef<NodeJS.Timeout>();\n\n  useEffect(() => {\n    // Clear previous timeout\n    if (timeoutRef.current) {\n      clearTimeout(timeoutRef.current);\n    }\n\n    // Don't search for very short queries\n    if (query.trim().length < 2) {\n      setSuggestions([]);\n      setIsVisible(false);\n      return;\n    }\n\n    // Debounce the search\n    timeoutRef.current = setTimeout(async () => {\n      setIsLoading(true);\n      try {\n        const response = await BlogService.searchPosts(query.trim(), 1, 5);\n        setSuggestions(response.data.posts);\n        setIsVisible(response.data.posts.length > 0);\n      } catch (error) {\n        console.error('Failed to fetch search suggestions:', error);\n        setSuggestions([]);\n        setIsVisible(false);\n      } finally {\n        setIsLoading(false);\n      }\n    }, 300); // 300ms debounce\n\n    return () => {\n      if (timeoutRef.current) {\n        clearTimeout(timeoutRef.current);\n      }\n    };\n  }, [query]);\n\n  if (!isVisible && !isLoading) {\n    return null;\n  }\n\n  return (\n    <div className=\"absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-md shadow-lg z-50 max-h-96 overflow-y-auto\">\n      {isLoading ? (\n        <div className=\"p-4 text-center\">\n          <div className=\"inline-flex items-center\">\n            <svg className=\"animate-spin -ml-1 mr-3 h-4 w-4 text-gray-400\" fill=\"none\" viewBox=\"0 0 24 24\">\n              <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\" />\n              <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\" />\n            </svg>\n            <span className=\"text-sm text-gray-500\">Searching...</span>\n          </div>\n        </div>\n      ) : suggestions.length > 0 ? (\n        <>\n          <div className=\"p-2 border-b border-gray-100\">\n            <p className=\"text-xs text-gray-500 font-medium uppercase tracking-wide\">\n              Suggestions\n            </p>\n          </div>\n          <div className=\"py-1\">\n            {suggestions.map((post) => (\n              <Link\n                key={post._id}\n                href={`/blog/${post.slug}`}\n                onClick={onSelect}\n                className=\"block px-4 py-3 hover:bg-gray-50 transition-colors\"\n              >\n                <div className=\"flex items-start space-x-3\">\n                  <div className=\"flex-shrink-0\">\n                    <img\n                      src={post.featuredImage.url}\n                      alt={post.featuredImage.alt}\n                      className=\"w-12 h-12 rounded object-cover\"\n                    />\n                  </div>\n                  <div className=\"flex-1 min-w-0\">\n                    <p className=\"text-sm font-medium text-gray-900 line-clamp-1\">\n                      {post.title}\n                    </p>\n                    <p className=\"text-xs text-gray-500 line-clamp-2 mt-1\">\n                      {post.excerpt}\n                    </p>\n                    <div className=\"flex items-center mt-2 space-x-2\">\n                      <span className=\"inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800\">\n                        {post.category}\n                      </span>\n                      <span className=\"text-xs text-gray-400\">\n                        {post.analytics.views} views\n                      </span>\n                    </div>\n                  </div>\n                </div>\n              </Link>\n            ))}\n          </div>\n          <div className=\"p-2 border-t border-gray-100\">\n            <Link\n              href={`/search?q=${encodeURIComponent(query)}`}\n              onClick={onSelect}\n              className=\"block w-full text-center py-2 text-sm text-blue-600 hover:text-blue-800 font-medium\"\n            >\n              View all results for \"{query}\"\n            </Link>\n          </div>\n        </>\n      ) : null}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAYO,SAAS,kBAAkB,EAAE,KAAK,EAAE,QAAQ,EAA0B;IAC3E,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAqB,EAAE;IACpE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD;IAExB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,yBAAyB;QACzB,IAAI,WAAW,OAAO,EAAE;YACtB,aAAa,WAAW,OAAO;QACjC;QAEA,sCAAsC;QACtC,IAAI,MAAM,IAAI,GAAG,MAAM,GAAG,GAAG;YAC3B,eAAe,EAAE;YACjB,aAAa;YACb;QACF;QAEA,sBAAsB;QACtB,WAAW,OAAO,GAAG,WAAW;YAC9B,aAAa;YACb,IAAI;gBACF,MAAM,WAAW,MAAM,8HAAA,CAAA,cAAW,CAAC,WAAW,CAAC,MAAM,IAAI,IAAI,GAAG;gBAChE,eAAe,SAAS,IAAI,CAAC,KAAK;gBAClC,aAAa,SAAS,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG;YAC5C,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,uCAAuC;gBACrD,eAAe,EAAE;gBACjB,aAAa;YACf,SAAU;gBACR,aAAa;YACf;QACF,GAAG,MAAM,iBAAiB;QAE1B,OAAO;YACL,IAAI,WAAW,OAAO,EAAE;gBACtB,aAAa,WAAW,OAAO;YACjC;QACF;IACF,GAAG;QAAC;KAAM;IAEV,IAAI,CAAC,aAAa,CAAC,WAAW;QAC5B,OAAO;IACT;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACZ,0BACC,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;wBAAgD,MAAK;wBAAO,SAAQ;;0CACjF,8OAAC;gCAAO,WAAU;gCAAa,IAAG;gCAAK,IAAG;gCAAK,GAAE;gCAAK,QAAO;gCAAe,aAAY;;;;;;0CACxF,8OAAC;gCAAK,WAAU;gCAAa,MAAK;gCAAe,GAAE;;;;;;;;;;;;kCAErD,8OAAC;wBAAK,WAAU;kCAAwB;;;;;;;;;;;;;;;;mBAG1C,YAAY,MAAM,GAAG,kBACvB;;8BACE,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAE,WAAU;kCAA4D;;;;;;;;;;;8BAI3E,8OAAC;oBAAI,WAAU;8BACZ,YAAY,GAAG,CAAC,CAAC,qBAChB,8OAAC,4JAAA,CAAA,UAAI;4BAEH,MAAM,CAAC,MAAM,EAAE,KAAK,IAAI,EAAE;4BAC1B,SAAS;4BACT,WAAU;sCAEV,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CACC,KAAK,KAAK,aAAa,CAAC,GAAG;4CAC3B,KAAK,KAAK,aAAa,CAAC,GAAG;4CAC3B,WAAU;;;;;;;;;;;kDAGd,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DACV,KAAK,KAAK;;;;;;0DAEb,8OAAC;gDAAE,WAAU;0DACV,KAAK,OAAO;;;;;;0DAEf,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;kEACb,KAAK,QAAQ;;;;;;kEAEhB,8OAAC;wDAAK,WAAU;;4DACb,KAAK,SAAS,CAAC,KAAK;4DAAC;;;;;;;;;;;;;;;;;;;;;;;;;2BAzBzB,KAAK,GAAG;;;;;;;;;;8BAiCnB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;wBACH,MAAM,CAAC,UAAU,EAAE,mBAAmB,QAAQ;wBAC9C,SAAS;wBACT,WAAU;;4BACX;4BACwB;4BAAM;;;;;;;;;;;;;2BAIjC;;;;;;AAGV", "debugId": null}}, {"offset": {"line": 411, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Development/BuzzEdge/buzzedge-frontend/src/components/Header.tsx"], "sourcesContent": ["\"use client\";\n\nimport Link from \"next/link\";\nimport { useState, useRef, useEffect } from \"react\";\nimport { useRouter } from \"next/navigation\";\nimport { SearchSuggestions } from \"./SearchSuggestions\";\n\nexport function Header() {\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const [searchQuery, setSearchQuery] = useState(\"\");\n  const [showSuggestions, setShowSuggestions] = useState(false);\n  const router = useRouter();\n  const searchRef = useRef<HTMLDivElement>(null);\n\n  const navigation = [\n    { name: \"Home\", href: \"/\" },\n    { name: \"AI Tools\", href: \"/category/ai-tools\" },\n    { name: \"Productivity\", href: \"/category/productivity\" },\n    { name: \"Developer Tools\", href: \"/category/developer-tools\" },\n    { name: \"Design Tools\", href: \"/category/design-tools\" },\n  ];\n\n  const handleSearch = (e: React.FormEvent) => {\n    e.preventDefault();\n    if (searchQuery.trim()) {\n      router.push(`/search?q=${encodeURIComponent(searchQuery.trim())}`);\n      setSearchQuery(\"\");\n      setShowSuggestions(false);\n    }\n  };\n\n  const handleSearchInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const value = e.target.value;\n    setSearchQuery(value);\n    setShowSuggestions(value.trim().length > 0);\n  };\n\n  const handleSuggestionSelect = () => {\n    setSearchQuery(\"\");\n    setShowSuggestions(false);\n  };\n\n  // Close suggestions when clicking outside\n  useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      if (\n        searchRef.current &&\n        !searchRef.current.contains(event.target as Node)\n      ) {\n        setShowSuggestions(false);\n      }\n    };\n\n    document.addEventListener(\"mousedown\", handleClickOutside);\n    return () => {\n      document.removeEventListener(\"mousedown\", handleClickOutside);\n    };\n  }, []);\n\n  return (\n    <header className=\"bg-white shadow-sm border-b border-gray-200\">\n      <nav className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between items-center h-16\">\n          {/* Logo */}\n          <div className=\"flex-shrink-0\">\n            <Link href=\"/\" className=\"flex items-center\">\n              <div className=\"w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center\">\n                <span className=\"text-white font-bold text-lg\">B</span>\n              </div>\n              <span className=\"ml-2 text-xl font-bold text-gray-900\">\n                BuzzEdge\n              </span>\n            </Link>\n          </div>\n\n          {/* Desktop Navigation */}\n          <div className=\"hidden md:block\">\n            <div className=\"ml-10 flex items-baseline space-x-4\">\n              {navigation.map((item) => (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  className=\"text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium transition-colors\"\n                >\n                  {item.name}\n                </Link>\n              ))}\n            </div>\n          </div>\n\n          {/* Search Bar */}\n          <div className=\"hidden md:block flex-1 max-w-lg mx-8\" ref={searchRef}>\n            <form onSubmit={handleSearch}>\n              <div className=\"relative\">\n                <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                  <svg\n                    className=\"h-5 w-5 text-gray-400\"\n                    fill=\"none\"\n                    stroke=\"currentColor\"\n                    viewBox=\"0 0 24 24\"\n                  >\n                    <path\n                      strokeLinecap=\"round\"\n                      strokeLinejoin=\"round\"\n                      strokeWidth={2}\n                      d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"\n                    />\n                  </svg>\n                </div>\n                <input\n                  type=\"search\"\n                  placeholder=\"Search reviews...\"\n                  value={searchQuery}\n                  onChange={handleSearchInputChange}\n                  onFocus={() =>\n                    searchQuery.trim().length > 0 && setShowSuggestions(true)\n                  }\n                  className=\"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n                />\n                {showSuggestions && (\n                  <SearchSuggestions\n                    query={searchQuery}\n                    onSelect={handleSuggestionSelect}\n                  />\n                )}\n              </div>\n            </form>\n          </div>\n\n          {/* Mobile menu button */}\n          <div className=\"md:hidden\">\n            <button\n              onClick={() => setIsMenuOpen(!isMenuOpen)}\n              className=\"inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500\"\n            >\n              <span className=\"sr-only\">Open main menu</span>\n              {!isMenuOpen ? (\n                <svg\n                  className=\"block h-6 w-6\"\n                  fill=\"none\"\n                  stroke=\"currentColor\"\n                  viewBox=\"0 0 24 24\"\n                >\n                  <path\n                    strokeLinecap=\"round\"\n                    strokeLinejoin=\"round\"\n                    strokeWidth={2}\n                    d=\"M4 6h16M4 12h16M4 18h16\"\n                  />\n                </svg>\n              ) : (\n                <svg\n                  className=\"block h-6 w-6\"\n                  fill=\"none\"\n                  stroke=\"currentColor\"\n                  viewBox=\"0 0 24 24\"\n                >\n                  <path\n                    strokeLinecap=\"round\"\n                    strokeLinejoin=\"round\"\n                    strokeWidth={2}\n                    d=\"M6 18L18 6M6 6l12 12\"\n                  />\n                </svg>\n              )}\n            </button>\n          </div>\n        </div>\n\n        {/* Mobile Navigation */}\n        {isMenuOpen && (\n          <div className=\"md:hidden\">\n            <div className=\"px-2 pt-2 pb-3 space-y-1 sm:px-3 border-t border-gray-200\">\n              {navigation.map((item) => (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  className=\"text-gray-600 hover:text-gray-900 block px-3 py-2 rounded-md text-base font-medium transition-colors\"\n                  onClick={() => setIsMenuOpen(false)}\n                >\n                  {item.name}\n                </Link>\n              ))}\n\n              {/* Mobile Search */}\n              <div className=\"px-3 py-2\">\n                <div className=\"relative\">\n                  <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                    <svg\n                      className=\"h-5 w-5 text-gray-400\"\n                      fill=\"none\"\n                      stroke=\"currentColor\"\n                      viewBox=\"0 0 24 24\"\n                    >\n                      <path\n                        strokeLinecap=\"round\"\n                        strokeLinejoin=\"round\"\n                        strokeWidth={2}\n                        d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"\n                      />\n                    </svg>\n                  </div>\n                  <input\n                    type=\"search\"\n                    placeholder=\"Search reviews...\"\n                    className=\"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n                  />\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n      </nav>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAOO,SAAS;IACd,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAEzC,MAAM,aAAa;QACjB;YAAE,MAAM;YAAQ,MAAM;QAAI;QAC1B;YAAE,MAAM;YAAY,MAAM;QAAqB;QAC/C;YAAE,MAAM;YAAgB,MAAM;QAAyB;QACvD;YAAE,MAAM;YAAmB,MAAM;QAA4B;QAC7D;YAAE,MAAM;YAAgB,MAAM;QAAyB;KACxD;IAED,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,IAAI,YAAY,IAAI,IAAI;YACtB,OAAO,IAAI,CAAC,CAAC,UAAU,EAAE,mBAAmB,YAAY,IAAI,KAAK;YACjE,eAAe;YACf,mBAAmB;QACrB;IACF;IAEA,MAAM,0BAA0B,CAAC;QAC/B,MAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;QAC5B,eAAe;QACf,mBAAmB,MAAM,IAAI,GAAG,MAAM,GAAG;IAC3C;IAEA,MAAM,yBAAyB;QAC7B,eAAe;QACf,mBAAmB;IACrB;IAEA,0CAA0C;IAC1C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,qBAAqB,CAAC;YAC1B,IACE,UAAU,OAAO,IACjB,CAAC,UAAU,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GACxC;gBACA,mBAAmB;YACrB;QACF;QAEA,SAAS,gBAAgB,CAAC,aAAa;QACvC,OAAO;YACL,SAAS,mBAAmB,CAAC,aAAa;QAC5C;IACF,GAAG,EAAE;IAEL,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;;kDACvB,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAK,WAAU;sDAA+B;;;;;;;;;;;kDAEjD,8OAAC;wCAAK,WAAU;kDAAuC;;;;;;;;;;;;;;;;;sCAO3D,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACZ,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC,4JAAA,CAAA,UAAI;wCAEH,MAAM,KAAK,IAAI;wCACf,WAAU;kDAET,KAAK,IAAI;uCAJL,KAAK,IAAI;;;;;;;;;;;;;;;sCAWtB,8OAAC;4BAAI,WAAU;4BAAuC,KAAK;sCACzD,cAAA,8OAAC;gCAAK,UAAU;0CACd,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDACC,WAAU;gDACV,MAAK;gDACL,QAAO;gDACP,SAAQ;0DAER,cAAA,8OAAC;oDACC,eAAc;oDACd,gBAAe;oDACf,aAAa;oDACb,GAAE;;;;;;;;;;;;;;;;sDAIR,8OAAC;4CACC,MAAK;4CACL,aAAY;4CACZ,OAAO;4CACP,UAAU;4CACV,SAAS,IACP,YAAY,IAAI,GAAG,MAAM,GAAG,KAAK,mBAAmB;4CAEtD,WAAU;;;;;;wCAEX,iCACC,8OAAC,uIAAA,CAAA,oBAAiB;4CAChB,OAAO;4CACP,UAAU;;;;;;;;;;;;;;;;;;;;;;sCAQpB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,SAAS,IAAM,cAAc,CAAC;gCAC9B,WAAU;;kDAEV,8OAAC;wCAAK,WAAU;kDAAU;;;;;;oCACzB,CAAC,2BACA,8OAAC;wCACC,WAAU;wCACV,MAAK;wCACL,QAAO;wCACP,SAAQ;kDAER,cAAA,8OAAC;4CACC,eAAc;4CACd,gBAAe;4CACf,aAAa;4CACb,GAAE;;;;;;;;;;6DAIN,8OAAC;wCACC,WAAU;wCACV,MAAK;wCACL,QAAO;wCACP,SAAQ;kDAER,cAAA,8OAAC;4CACC,eAAc;4CACd,gBAAe;4CACf,aAAa;4CACb,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBASb,4BACC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;4BACZ,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC,4JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAU;oCACV,SAAS,IAAM,cAAc;8CAE5B,KAAK,IAAI;mCALL,KAAK,IAAI;;;;;0CAUlB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDACC,WAAU;gDACV,MAAK;gDACL,QAAO;gDACP,SAAQ;0DAER,cAAA,8OAAC;oDACC,eAAc;oDACd,gBAAe;oDACf,aAAa;oDACb,GAAE;;;;;;;;;;;;;;;;sDAIR,8OAAC;4CACC,MAAK;4CACL,aAAY;4CACZ,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU9B", "debugId": null}}, {"offset": {"line": 790, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Development/BuzzEdge/buzzedge-frontend/src/lib/publicAuth.tsx"], "sourcesContent": ["'use client';\n\nimport { createContext, useContext, useEffect, useState, ReactNode } from 'react';\n\ninterface PublicUser {\n  id: string;\n  name: string;\n  email: string;\n  avatar: string;\n  isVerified: boolean;\n  bio?: string;\n  website?: string;\n  socialLinks?: {\n    twitter?: string;\n    linkedin?: string;\n    github?: string;\n  };\n  preferences?: {\n    emailNotifications: boolean;\n    marketingEmails: boolean;\n    commentReplies: boolean;\n  };\n  stats?: {\n    commentsCount: number;\n    likesReceived: number;\n    likesGiven: number;\n  };\n}\n\ninterface PublicAuthContextType {\n  user: PublicUser | null;\n  token: string | null;\n  isLoading: boolean;\n  login: (email: string, password: string) => Promise<void>;\n  register: (name: string, email: string, password: string) => Promise<void>;\n  logout: () => void;\n  updateProfile: (data: Partial<PublicUser>) => Promise<void>;\n}\n\nconst PublicAuthContext = createContext<PublicAuthContextType | undefined>(undefined);\n\ninterface PublicAuthProviderProps {\n  children: ReactNode;\n}\n\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5001/api';\n\nexport function PublicAuthProvider({ children }: PublicAuthProviderProps) {\n  const [user, setUser] = useState<PublicUser | null>(null);\n  const [token, setToken] = useState<string | null>(null);\n  const [isLoading, setIsLoading] = useState(true);\n\n  useEffect(() => {\n    // Check for stored token on mount\n    const storedToken = localStorage.getItem('buzzedge_user_token');\n    if (storedToken) {\n      setToken(storedToken);\n      fetchCurrentUser(storedToken);\n    } else {\n      setIsLoading(false);\n    }\n  }, []);\n\n  const fetchCurrentUser = async (authToken: string) => {\n    try {\n      const response = await fetch(`${API_BASE_URL}/auth/me`, {\n        headers: {\n          'Authorization': `Bearer ${authToken}`,\n          'Content-Type': 'application/json',\n        },\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n        setUser(data.data.user);\n      } else {\n        // Token is invalid, remove it\n        localStorage.removeItem('buzzedge_user_token');\n        setToken(null);\n      }\n    } catch (error) {\n      console.error('Failed to fetch current user:', error);\n      localStorage.removeItem('buzzedge_user_token');\n      setToken(null);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const login = async (email: string, password: string) => {\n    try {\n      const response = await fetch(`${API_BASE_URL}/auth/login`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ email, password }),\n      });\n\n      const data = await response.json();\n\n      if (!response.ok) {\n        throw new Error(data.message || 'Login failed');\n      }\n\n      const { token: authToken, user: userData } = data.data;\n      \n      setToken(authToken);\n      setUser(userData);\n      localStorage.setItem('buzzedge_user_token', authToken);\n    } catch (error) {\n      throw error;\n    }\n  };\n\n  const register = async (name: string, email: string, password: string) => {\n    try {\n      const response = await fetch(`${API_BASE_URL}/auth/register`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ name, email, password }),\n      });\n\n      const data = await response.json();\n\n      if (!response.ok) {\n        throw new Error(data.message || 'Registration failed');\n      }\n\n      const { token: authToken, user: userData } = data.data;\n      \n      setToken(authToken);\n      setUser(userData);\n      localStorage.setItem('buzzedge_user_token', authToken);\n    } catch (error) {\n      throw error;\n    }\n  };\n\n  const logout = () => {\n    setUser(null);\n    setToken(null);\n    localStorage.removeItem('buzzedge_user_token');\n  };\n\n  const updateProfile = async (profileData: Partial<PublicUser>) => {\n    if (!token) {\n      throw new Error('Not authenticated');\n    }\n\n    try {\n      const response = await fetch(`${API_BASE_URL}/auth/profile`, {\n        method: 'PUT',\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(profileData),\n      });\n\n      const data = await response.json();\n\n      if (!response.ok) {\n        throw new Error(data.message || 'Profile update failed');\n      }\n\n      setUser(data.data.user);\n    } catch (error) {\n      throw error;\n    }\n  };\n\n  const value = {\n    user,\n    token,\n    isLoading,\n    login,\n    register,\n    logout,\n    updateProfile,\n  };\n\n  return (\n    <PublicAuthContext.Provider value={value}>\n      {children}\n    </PublicAuthContext.Provider>\n  );\n}\n\nexport function usePublicAuth() {\n  const context = useContext(PublicAuthContext);\n  if (context === undefined) {\n    throw new Error('usePublicAuth must be used within a PublicAuthProvider');\n  }\n  return context;\n}\n\n// API helper functions for public users\nexport const publicAuthAPI = {\n  // Make authenticated requests\n  request: async (endpoint: string, options: RequestInit = {}) => {\n    const token = localStorage.getItem('buzzedge_user_token');\n    \n    const config: RequestInit = {\n      ...options,\n      headers: {\n        'Content-Type': 'application/json',\n        ...(token && { 'Authorization': `Bearer ${token}` }),\n        ...options.headers,\n      },\n    };\n\n    const response = await fetch(`${API_BASE_URL}${endpoint}`, config);\n    \n    if (!response.ok) {\n      const error = await response.json();\n      throw new Error(error.message || 'Request failed');\n    }\n\n    return response.json();\n  },\n\n  // Forgot password\n  forgotPassword: async (email: string) => {\n    const response = await fetch(`${API_BASE_URL}/auth/forgot-password`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify({ email }),\n    });\n\n    const data = await response.json();\n    if (!response.ok) {\n      throw new Error(data.message || 'Failed to send reset email');\n    }\n\n    return data;\n  },\n\n  // Reset password\n  resetPassword: async (token: string, password: string) => {\n    const response = await fetch(`${API_BASE_URL}/auth/reset-password`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify({ token, password }),\n    });\n\n    const data = await response.json();\n    if (!response.ok) {\n      throw new Error(data.message || 'Failed to reset password');\n    }\n\n    return data;\n  },\n\n  // Verify email\n  verifyEmail: async (token: string) => {\n    const response = await fetch(`${API_BASE_URL}/auth/verify-email`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify({ token }),\n    });\n\n    const data = await response.json();\n    if (!response.ok) {\n      throw new Error(data.message || 'Failed to verify email');\n    }\n\n    return data;\n  },\n};\n"], "names": [], "mappings": ";;;;;;AAEA;AAFA;;;AAuCA,MAAM,kCAAoB,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAqC;AAM3E,MAAM,eAAe,6DAAmC;AAEjD,SAAS,mBAAmB,EAAE,QAAQ,EAA2B;IACtE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAqB;IACpD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,kCAAkC;QAClC,MAAM,cAAc,aAAa,OAAO,CAAC;QACzC,IAAI,aAAa;YACf,SAAS;YACT,iBAAiB;QACnB,OAAO;YACL,aAAa;QACf;IACF,GAAG,EAAE;IAEL,MAAM,mBAAmB,OAAO;QAC9B,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,QAAQ,CAAC,EAAE;gBACtD,SAAS;oBACP,iBAAiB,CAAC,OAAO,EAAE,WAAW;oBACtC,gBAAgB;gBAClB;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,QAAQ,KAAK,IAAI,CAAC,IAAI;YACxB,OAAO;gBACL,8BAA8B;gBAC9B,aAAa,UAAU,CAAC;gBACxB,SAAS;YACX;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,aAAa,UAAU,CAAC;YACxB,SAAS;QACX,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,QAAQ,OAAO,OAAe;QAClC,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,WAAW,CAAC,EAAE;gBACzD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE;oBAAO;gBAAS;YACzC;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,KAAK,OAAO,IAAI;YAClC;YAEA,MAAM,EAAE,OAAO,SAAS,EAAE,MAAM,QAAQ,EAAE,GAAG,KAAK,IAAI;YAEtD,SAAS;YACT,QAAQ;YACR,aAAa,OAAO,CAAC,uBAAuB;QAC9C,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,MAAM,WAAW,OAAO,MAAc,OAAe;QACnD,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,cAAc,CAAC,EAAE;gBAC5D,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE;oBAAM;oBAAO;gBAAS;YAC/C;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,KAAK,OAAO,IAAI;YAClC;YAEA,MAAM,EAAE,OAAO,SAAS,EAAE,MAAM,QAAQ,EAAE,GAAG,KAAK,IAAI;YAEtD,SAAS;YACT,QAAQ;YACR,aAAa,OAAO,CAAC,uBAAuB;QAC9C,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,MAAM,SAAS;QACb,QAAQ;QACR,SAAS;QACT,aAAa,UAAU,CAAC;IAC1B;IAEA,MAAM,gBAAgB,OAAO;QAC3B,IAAI,CAAC,OAAO;YACV,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,aAAa,CAAC,EAAE;gBAC3D,QAAQ;gBACR,SAAS;oBACP,iBAAiB,CAAC,OAAO,EAAE,OAAO;oBAClC,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,KAAK,OAAO,IAAI;YAClC;YAEA,QAAQ,KAAK,IAAI,CAAC,IAAI;QACxB,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,MAAM,QAAQ;QACZ;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,8OAAC,kBAAkB,QAAQ;QAAC,OAAO;kBAChC;;;;;;AAGP;AAEO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAGO,MAAM,gBAAgB;IAC3B,8BAA8B;IAC9B,SAAS,OAAO,UAAkB,UAAuB,CAAC,CAAC;QACzD,MAAM,QAAQ,aAAa,OAAO,CAAC;QAEnC,MAAM,SAAsB;YAC1B,GAAG,OAAO;YACV,SAAS;gBACP,gBAAgB;gBAChB,GAAI,SAAS;oBAAE,iBAAiB,CAAC,OAAO,EAAE,OAAO;gBAAC,CAAC;gBACnD,GAAG,QAAQ,OAAO;YACpB;QACF;QAEA,MAAM,WAAW,MAAM,MAAM,GAAG,eAAe,UAAU,EAAE;QAE3D,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,QAAQ,MAAM,SAAS,IAAI;YACjC,MAAM,IAAI,MAAM,MAAM,OAAO,IAAI;QACnC;QAEA,OAAO,SAAS,IAAI;IACtB;IAEA,kBAAkB;IAClB,gBAAgB,OAAO;QACrB,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,qBAAqB,CAAC,EAAE;YACnE,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;gBAAE;YAAM;QAC/B;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,KAAK,OAAO,IAAI;QAClC;QAEA,OAAO;IACT;IAEA,iBAAiB;IACjB,eAAe,OAAO,OAAe;QACnC,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,oBAAoB,CAAC,EAAE;YAClE,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;gBAAE;gBAAO;YAAS;QACzC;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,KAAK,OAAO,IAAI;QAClC;QAEA,OAAO;IACT;IAEA,eAAe;IACf,aAAa,OAAO;QAClB,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,kBAAkB,CAAC,EAAE;YAChE,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;gBAAE;YAAM;QAC/B;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,KAAK,OAAO,IAAI;QAClC;QAEA,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 1021, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Development/BuzzEdge/buzzedge-frontend/src/lib/commentAPI.ts"], "sourcesContent": ["const API_BASE_URL =\n  process.env.NEXT_PUBLIC_API_URL || \"http://localhost:5001/api\";\n\nexport interface Comment {\n  _id: string;\n  content: string;\n  author: {\n    name: string;\n    email: string;\n    avatar: string;\n    userId?: string;\n  };\n  post: string;\n  parentComment?: string;\n  status: \"pending\" | \"approved\" | \"rejected\" | \"spam\";\n  likes: number;\n  likedBy: string[];\n  isEdited: boolean;\n  editedAt?: string;\n  replies?: Comment[];\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface CommentFormData {\n  content: string;\n  postId: string;\n  parentCommentId?: string;\n  author: {\n    name: string;\n    email: string;\n    avatar?: string;\n    userId?: string;\n  };\n}\n\nexport interface CommentsResponse {\n  success: boolean;\n  data: {\n    comments: Comment[];\n    pagination: {\n      currentPage: number;\n      totalPages: number;\n      totalComments: number;\n      hasNext: boolean;\n      hasPrev: boolean;\n    };\n  };\n}\n\nexport interface CommentResponse {\n  success: boolean;\n  data: {\n    comment: Comment;\n  };\n  message?: string;\n}\n\nexport const commentAPI = {\n  // Get comments for a post\n  getComments: async (\n    postId: string,\n    page: number = 1,\n    limit: number = 10\n  ): Promise<CommentsResponse> => {\n    const response = await fetch(\n      `${API_BASE_URL}/comments/${postId}?page=${page}&limit=${limit}`,\n      {\n        method: \"GET\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n        },\n      }\n    );\n\n    if (!response.ok) {\n      const error = await response.json();\n      throw new Error(error.message || \"Failed to fetch comments\");\n    }\n\n    return response.json();\n  },\n\n  // Create a new comment\n  createComment: async (\n    commentData: CommentFormData\n  ): Promise<CommentResponse> => {\n    const response = await fetch(`${API_BASE_URL}/comments`, {\n      method: \"POST\",\n      headers: {\n        \"Content-Type\": \"application/json\",\n      },\n      body: JSON.stringify(commentData),\n    });\n\n    if (!response.ok) {\n      const error = await response.json();\n      throw new Error(error.message || \"Failed to create comment\");\n    }\n\n    return response.json();\n  },\n\n  // Update a comment (authenticated users only)\n  updateComment: async (\n    commentId: string,\n    content: string\n  ): Promise<CommentResponse> => {\n    const token = localStorage.getItem(\"buzzedge_user_token\");\n\n    if (!token) {\n      throw new Error(\"Authentication required\");\n    }\n\n    const response = await fetch(`${API_BASE_URL}/comments/${commentId}`, {\n      method: \"PUT\",\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${token}`,\n      },\n      body: JSON.stringify({ content }),\n    });\n\n    if (!response.ok) {\n      const error = await response.json();\n      throw new Error(error.message || \"Failed to update comment\");\n    }\n\n    return response.json();\n  },\n\n  // Delete a comment (authenticated users only)\n  deleteComment: async (\n    commentId: string\n  ): Promise<{ success: boolean; message: string }> => {\n    const token = localStorage.getItem(\"buzzedge_user_token\");\n\n    if (!token) {\n      throw new Error(\"Authentication required\");\n    }\n\n    const response = await fetch(`${API_BASE_URL}/comments/${commentId}`, {\n      method: \"DELETE\",\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${token}`,\n      },\n    });\n\n    if (!response.ok) {\n      const error = await response.json();\n      throw new Error(error.message || \"Failed to delete comment\");\n    }\n\n    return response.json();\n  },\n\n  // Like/unlike a comment (authenticated users only)\n  toggleLike: async (\n    commentId: string\n  ): Promise<{\n    success: boolean;\n    data: { likes: number; hasLiked: boolean };\n  }> => {\n    const token = localStorage.getItem(\"buzzedge_user_token\");\n\n    if (!token) {\n      throw new Error(\"Authentication required\");\n    }\n\n    const response = await fetch(`${API_BASE_URL}/comments/${commentId}/like`, {\n      method: \"POST\",\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${token}`,\n      },\n    });\n\n    if (!response.ok) {\n      const error = await response.json();\n      throw new Error(error.message || \"Failed to toggle like\");\n    }\n\n    return response.json();\n  },\n\n  // Get comment statistics (admin only)\n  getStats: async (): Promise<{ success: boolean; data: { stats: any } }> => {\n    const token = localStorage.getItem(\"buzzedge_admin_token\"); // Admin token\n\n    if (!token) {\n      throw new Error(\"Admin authentication required\");\n    }\n\n    const response = await fetch(`${API_BASE_URL}/comments/admin/stats`, {\n      method: \"GET\",\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${token}`,\n      },\n    });\n\n    if (!response.ok) {\n      const error = await response.json();\n      throw new Error(error.message || \"Failed to fetch comment stats\");\n    }\n\n    return response.json();\n  },\n};\n\n// Helper functions\nexport const commentHelpers = {\n  // Format comment date\n  formatDate: (dateString: string): string => {\n    const date = new Date(dateString);\n    const now = new Date();\n    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);\n\n    if (diffInSeconds < 60) {\n      return \"just now\";\n    } else if (diffInSeconds < 3600) {\n      const minutes = Math.floor(diffInSeconds / 60);\n      return `${minutes} minute${minutes > 1 ? \"s\" : \"\"} ago`;\n    } else if (diffInSeconds < 86400) {\n      const hours = Math.floor(diffInSeconds / 3600);\n      return `${hours} hour${hours > 1 ? \"s\" : \"\"} ago`;\n    } else if (diffInSeconds < 604800) {\n      const days = Math.floor(diffInSeconds / 86400);\n      return `${days} day${days > 1 ? \"s\" : \"\"} ago`;\n    } else {\n      return date.toLocaleDateString();\n    }\n  },\n\n  // Validate comment content\n  validateComment: (content: string): { isValid: boolean; error?: string } => {\n    if (!content.trim()) {\n      return { isValid: false, error: \"Comment cannot be empty\" };\n    }\n\n    if (content.length > 1000) {\n      return {\n        isValid: false,\n        error: \"Comment must be less than 1000 characters\",\n      };\n    }\n\n    return { isValid: true };\n  },\n\n  // Validate email\n  validateEmail: (email: string): boolean => {\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    return emailRegex.test(email);\n  },\n\n  // Validate name\n  validateName: (name: string): { isValid: boolean; error?: string } => {\n    if (!name.trim()) {\n      return { isValid: false, error: \"Name is required\" };\n    }\n\n    if (name.length > 50) {\n      return { isValid: false, error: \"Name must be less than 50 characters\" };\n    }\n\n    return { isValid: true };\n  },\n\n  // Sort comments by date\n  sortComments: (\n    comments: Comment[],\n    order: \"newest\" | \"oldest\" = \"newest\"\n  ): Comment[] => {\n    return [...comments].sort((a, b) => {\n      const dateA = new Date(a.createdAt).getTime();\n      const dateB = new Date(b.createdAt).getTime();\n\n      return order === \"newest\" ? dateB - dateA : dateA - dateB;\n    });\n  },\n\n  // Filter comments by status\n  filterComments: (\n    comments: Comment[],\n    status: Comment[\"status\"]\n  ): Comment[] => {\n    return comments.filter((comment) => comment.status === status);\n  },\n\n  // Get approved comments only\n  getApprovedComments: (comments: Comment[]): Comment[] => {\n    return commentHelpers.filterComments(comments, \"approved\");\n  },\n\n  // Count total replies for a comment\n  countReplies: (comment: Comment): number => {\n    if (!comment.replies) return 0;\n\n    let count = comment.replies.length;\n    comment.replies.forEach((reply) => {\n      count += commentHelpers.countReplies(reply);\n    });\n\n    return count;\n  },\n};\n"], "names": [], "mappings": ";;;;AAAA,MAAM,eACJ,6DAAmC;AAyD9B,MAAM,aAAa;IACxB,0BAA0B;IAC1B,aAAa,OACX,QACA,OAAe,CAAC,EAChB,QAAgB,EAAE;QAElB,MAAM,WAAW,MAAM,MACrB,GAAG,aAAa,UAAU,EAAE,OAAO,MAAM,EAAE,KAAK,OAAO,EAAE,OAAO,EAChE;YACE,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;QACF;QAGF,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,QAAQ,MAAM,SAAS,IAAI;YACjC,MAAM,IAAI,MAAM,MAAM,OAAO,IAAI;QACnC;QAEA,OAAO,SAAS,IAAI;IACtB;IAEA,uBAAuB;IACvB,eAAe,OACb;QAEA,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,SAAS,CAAC,EAAE;YACvD,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,QAAQ,MAAM,SAAS,IAAI;YACjC,MAAM,IAAI,MAAM,MAAM,OAAO,IAAI;QACnC;QAEA,OAAO,SAAS,IAAI;IACtB;IAEA,8CAA8C;IAC9C,eAAe,OACb,WACA;QAEA,MAAM,QAAQ,aAAa,OAAO,CAAC;QAEnC,IAAI,CAAC,OAAO;YACV,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,EAAE,WAAW,EAAE;YACpE,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,eAAe,CAAC,OAAO,EAAE,OAAO;YAClC;YACA,MAAM,KAAK,SAAS,CAAC;gBAAE;YAAQ;QACjC;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,QAAQ,MAAM,SAAS,IAAI;YACjC,MAAM,IAAI,MAAM,MAAM,OAAO,IAAI;QACnC;QAEA,OAAO,SAAS,IAAI;IACtB;IAEA,8CAA8C;IAC9C,eAAe,OACb;QAEA,MAAM,QAAQ,aAAa,OAAO,CAAC;QAEnC,IAAI,CAAC,OAAO;YACV,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,EAAE,WAAW,EAAE;YACpE,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,eAAe,CAAC,OAAO,EAAE,OAAO;YAClC;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,QAAQ,MAAM,SAAS,IAAI;YACjC,MAAM,IAAI,MAAM,MAAM,OAAO,IAAI;QACnC;QAEA,OAAO,SAAS,IAAI;IACtB;IAEA,mDAAmD;IACnD,YAAY,OACV;QAKA,MAAM,QAAQ,aAAa,OAAO,CAAC;QAEnC,IAAI,CAAC,OAAO;YACV,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,EAAE,UAAU,KAAK,CAAC,EAAE;YACzE,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,eAAe,CAAC,OAAO,EAAE,OAAO;YAClC;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,QAAQ,MAAM,SAAS,IAAI;YACjC,MAAM,IAAI,MAAM,MAAM,OAAO,IAAI;QACnC;QAEA,OAAO,SAAS,IAAI;IACtB;IAEA,sCAAsC;IACtC,UAAU;QACR,MAAM,QAAQ,aAAa,OAAO,CAAC,yBAAyB,cAAc;QAE1E,IAAI,CAAC,OAAO;YACV,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,qBAAqB,CAAC,EAAE;YACnE,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,eAAe,CAAC,OAAO,EAAE,OAAO;YAClC;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,QAAQ,MAAM,SAAS,IAAI;YACjC,MAAM,IAAI,MAAM,MAAM,OAAO,IAAI;QACnC;QAEA,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,iBAAiB;IAC5B,sBAAsB;IACtB,YAAY,CAAC;QACX,MAAM,OAAO,IAAI,KAAK;QACtB,MAAM,MAAM,IAAI;QAChB,MAAM,gBAAgB,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,KAAK,OAAO,EAAE,IAAI;QAEpE,IAAI,gBAAgB,IAAI;YACtB,OAAO;QACT,OAAO,IAAI,gBAAgB,MAAM;YAC/B,MAAM,UAAU,KAAK,KAAK,CAAC,gBAAgB;YAC3C,OAAO,GAAG,QAAQ,OAAO,EAAE,UAAU,IAAI,MAAM,GAAG,IAAI,CAAC;QACzD,OAAO,IAAI,gBAAgB,OAAO;YAChC,MAAM,QAAQ,KAAK,KAAK,CAAC,gBAAgB;YACzC,OAAO,GAAG,MAAM,KAAK,EAAE,QAAQ,IAAI,MAAM,GAAG,IAAI,CAAC;QACnD,OAAO,IAAI,gBAAgB,QAAQ;YACjC,MAAM,OAAO,KAAK,KAAK,CAAC,gBAAgB;YACxC,OAAO,GAAG,KAAK,IAAI,EAAE,OAAO,IAAI,MAAM,GAAG,IAAI,CAAC;QAChD,OAAO;YACL,OAAO,KAAK,kBAAkB;QAChC;IACF;IAEA,2BAA2B;IAC3B,iBAAiB,CAAC;QAChB,IAAI,CAAC,QAAQ,IAAI,IAAI;YACnB,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAA0B;QAC5D;QAEA,IAAI,QAAQ,MAAM,GAAG,MAAM;YACzB,OAAO;gBACL,SAAS;gBACT,OAAO;YACT;QACF;QAEA,OAAO;YAAE,SAAS;QAAK;IACzB;IAEA,iBAAiB;IACjB,eAAe,CAAC;QACd,MAAM,aAAa;QACnB,OAAO,WAAW,IAAI,CAAC;IACzB;IAEA,gBAAgB;IAChB,cAAc,CAAC;QACb,IAAI,CAAC,KAAK,IAAI,IAAI;YAChB,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAmB;QACrD;QAEA,IAAI,KAAK,MAAM,GAAG,IAAI;YACpB,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAuC;QACzE;QAEA,OAAO;YAAE,SAAS;QAAK;IACzB;IAEA,wBAAwB;IACxB,cAAc,CACZ,UACA,QAA6B,QAAQ;QAErC,OAAO;eAAI;SAAS,CAAC,IAAI,CAAC,CAAC,GAAG;YAC5B,MAAM,QAAQ,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO;YAC3C,MAAM,QAAQ,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO;YAE3C,OAAO,UAAU,WAAW,QAAQ,QAAQ,QAAQ;QACtD;IACF;IAEA,4BAA4B;IAC5B,gBAAgB,CACd,UACA;QAEA,OAAO,SAAS,MAAM,CAAC,CAAC,UAAY,QAAQ,MAAM,KAAK;IACzD;IAEA,6BAA6B;IAC7B,qBAAqB,CAAC;QACpB,OAAO,eAAe,cAAc,CAAC,UAAU;IACjD;IAEA,oCAAoC;IACpC,cAAc,CAAC;QACb,IAAI,CAAC,QAAQ,OAAO,EAAE,OAAO;QAE7B,IAAI,QAAQ,QAAQ,OAAO,CAAC,MAAM;QAClC,QAAQ,OAAO,CAAC,OAAO,CAAC,CAAC;YACvB,SAAS,eAAe,YAAY,CAAC;QACvC;QAEA,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 1232, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Development/BuzzEdge/buzzedge-frontend/src/components/UserAuth.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { usePublicAuth } from '@/lib/publicAuth';\n\ninterface UserAuthProps {\n  onClose: () => void;\n  defaultMode?: 'login' | 'register';\n}\n\nexport function UserAuth({ onClose, defaultMode = 'login' }: UserAuthProps) {\n  const [mode, setMode] = useState<'login' | 'register' | 'forgot'>(defaultMode);\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n  \n  const { login, register } = usePublicAuth();\n\n  const [formData, setFormData] = useState({\n    name: '',\n    email: '',\n    password: '',\n    confirmPassword: '',\n  });\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setError('');\n    setSuccess('');\n    setIsLoading(true);\n\n    try {\n      if (mode === 'register') {\n        if (formData.password !== formData.confirmPassword) {\n          throw new Error('Passwords do not match');\n        }\n        \n        if (formData.password.length < 6) {\n          throw new Error('Password must be at least 6 characters');\n        }\n\n        await register(formData.name, formData.email, formData.password);\n        setSuccess('Account created successfully! You can now comment on posts.');\n        setTimeout(() => onClose(), 2000);\n      } else if (mode === 'login') {\n        await login(formData.email, formData.password);\n        setSuccess('Logged in successfully!');\n        setTimeout(() => onClose(), 1000);\n      }\n    } catch (err: any) {\n      setError(err.message || 'An error occurred');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleForgotPassword = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setError('');\n    setSuccess('');\n    setIsLoading(true);\n\n    try {\n      // TODO: Implement forgot password API call\n      setSuccess('Password reset link sent to your email!');\n      setTimeout(() => setMode('login'), 3000);\n    } catch (err: any) {\n      setError(err.message || 'Failed to send reset email');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\n      <div className=\"bg-white rounded-lg max-w-md w-full p-6 relative\">\n        {/* Close button */}\n        <button\n          onClick={onClose}\n          className=\"absolute top-4 right-4 text-gray-400 hover:text-gray-600\"\n        >\n          <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n          </svg>\n        </button>\n\n        {/* Header */}\n        <div className=\"mb-6\">\n          <h2 className=\"text-2xl font-bold text-gray-900\">\n            {mode === 'login' && 'Sign In'}\n            {mode === 'register' && 'Create Account'}\n            {mode === 'forgot' && 'Reset Password'}\n          </h2>\n          <p className=\"text-gray-600 mt-1\">\n            {mode === 'login' && 'Sign in to comment and interact with posts'}\n            {mode === 'register' && 'Join our community to comment and engage'}\n            {mode === 'forgot' && 'Enter your email to receive a reset link'}\n          </p>\n        </div>\n\n        {/* Error/Success Messages */}\n        {error && (\n          <div className=\"mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded\">\n            {error}\n          </div>\n        )}\n        \n        {success && (\n          <div className=\"mb-4 p-3 bg-green-100 border border-green-400 text-green-700 rounded\">\n            {success}\n          </div>\n        )}\n\n        {/* Forms */}\n        {mode === 'forgot' ? (\n          <form onSubmit={handleForgotPassword} className=\"space-y-4\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                Email Address\n              </label>\n              <input\n                type=\"email\"\n                value={formData.email}\n                onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                placeholder=\"Enter your email\"\n                required\n              />\n            </div>\n\n            <button\n              type=\"submit\"\n              disabled={isLoading}\n              className=\"w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed\"\n            >\n              {isLoading ? 'Sending...' : 'Send Reset Link'}\n            </button>\n\n            <div className=\"text-center\">\n              <button\n                type=\"button\"\n                onClick={() => setMode('login')}\n                className=\"text-blue-600 hover:text-blue-800 text-sm\"\n              >\n                Back to Sign In\n              </button>\n            </div>\n          </form>\n        ) : (\n          <form onSubmit={handleSubmit} className=\"space-y-4\">\n            {mode === 'register' && (\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  Full Name\n                </label>\n                <input\n                  type=\"text\"\n                  value={formData.name}\n                  onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                  placeholder=\"Enter your full name\"\n                  required\n                />\n              </div>\n            )}\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                Email Address\n              </label>\n              <input\n                type=\"email\"\n                value={formData.email}\n                onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                placeholder=\"Enter your email\"\n                required\n              />\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                Password\n              </label>\n              <input\n                type=\"password\"\n                value={formData.password}\n                onChange={(e) => setFormData(prev => ({ ...prev, password: e.target.value }))}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                placeholder=\"Enter your password\"\n                required\n                minLength={6}\n              />\n            </div>\n\n            {mode === 'register' && (\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  Confirm Password\n                </label>\n                <input\n                  type=\"password\"\n                  value={formData.confirmPassword}\n                  onChange={(e) => setFormData(prev => ({ ...prev, confirmPassword: e.target.value }))}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                  placeholder=\"Confirm your password\"\n                  required\n                  minLength={6}\n                />\n              </div>\n            )}\n\n            <button\n              type=\"submit\"\n              disabled={isLoading}\n              className=\"w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed\"\n            >\n              {isLoading ? 'Please wait...' : (mode === 'login' ? 'Sign In' : 'Create Account')}\n            </button>\n\n            {mode === 'login' && (\n              <div className=\"text-center\">\n                <button\n                  type=\"button\"\n                  onClick={() => setMode('forgot')}\n                  className=\"text-blue-600 hover:text-blue-800 text-sm\"\n                >\n                  Forgot your password?\n                </button>\n              </div>\n            )}\n\n            <div className=\"text-center text-sm text-gray-600\">\n              {mode === 'login' ? (\n                <>\n                  Don't have an account?{' '}\n                  <button\n                    type=\"button\"\n                    onClick={() => setMode('register')}\n                    className=\"text-blue-600 hover:text-blue-800 font-medium\"\n                  >\n                    Sign up\n                  </button>\n                </>\n              ) : (\n                <>\n                  Already have an account?{' '}\n                  <button\n                    type=\"button\"\n                    onClick={() => setMode('login')}\n                    className=\"text-blue-600 hover:text-blue-800 font-medium\"\n                  >\n                    Sign in\n                  </button>\n                </>\n              )}\n            </div>\n          </form>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAUO,SAAS,SAAS,EAAE,OAAO,EAAE,cAAc,OAAO,EAAiB;IACxE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmC;IAClE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,yHAAA,CAAA,gBAAa,AAAD;IAExC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM;QACN,OAAO;QACP,UAAU;QACV,iBAAiB;IACnB;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,SAAS;QACT,WAAW;QACX,aAAa;QAEb,IAAI;YACF,IAAI,SAAS,YAAY;gBACvB,IAAI,SAAS,QAAQ,KAAK,SAAS,eAAe,EAAE;oBAClD,MAAM,IAAI,MAAM;gBAClB;gBAEA,IAAI,SAAS,QAAQ,CAAC,MAAM,GAAG,GAAG;oBAChC,MAAM,IAAI,MAAM;gBAClB;gBAEA,MAAM,SAAS,SAAS,IAAI,EAAE,SAAS,KAAK,EAAE,SAAS,QAAQ;gBAC/D,WAAW;gBACX,WAAW,IAAM,WAAW;YAC9B,OAAO,IAAI,SAAS,SAAS;gBAC3B,MAAM,MAAM,SAAS,KAAK,EAAE,SAAS,QAAQ;gBAC7C,WAAW;gBACX,WAAW,IAAM,WAAW;YAC9B;QACF,EAAE,OAAO,KAAU;YACjB,SAAS,IAAI,OAAO,IAAI;QAC1B,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,uBAAuB,OAAO;QAClC,EAAE,cAAc;QAChB,SAAS;QACT,WAAW;QACX,aAAa;QAEb,IAAI;YACF,2CAA2C;YAC3C,WAAW;YACX,WAAW,IAAM,QAAQ,UAAU;QACrC,EAAE,OAAO,KAAU;YACjB,SAAS,IAAI,OAAO,IAAI;QAC1B,SAAU;YACR,aAAa;QACf;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBACC,SAAS;oBACT,WAAU;8BAEV,cAAA,8OAAC;wBAAI,WAAU;wBAAU,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCACjE,cAAA,8OAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;;;;;;8BAKzE,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;;gCACX,SAAS,WAAW;gCACpB,SAAS,cAAc;gCACvB,SAAS,YAAY;;;;;;;sCAExB,8OAAC;4BAAE,WAAU;;gCACV,SAAS,WAAW;gCACpB,SAAS,cAAc;gCACvB,SAAS,YAAY;;;;;;;;;;;;;gBAKzB,uBACC,8OAAC;oBAAI,WAAU;8BACZ;;;;;;gBAIJ,yBACC,8OAAC;oBAAI,WAAU;8BACZ;;;;;;gBAKJ,SAAS,yBACR,8OAAC;oBAAK,UAAU;oBAAsB,WAAU;;sCAC9C,8OAAC;;8CACC,8OAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,8OAAC;oCACC,MAAK;oCACL,OAAO,SAAS,KAAK;oCACrB,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;gDAAE,GAAG,IAAI;gDAAE,OAAO,EAAE,MAAM,CAAC,KAAK;4CAAC,CAAC;oCACxE,WAAU;oCACV,aAAY;oCACZ,QAAQ;;;;;;;;;;;;sCAIZ,8OAAC;4BACC,MAAK;4BACL,UAAU;4BACV,WAAU;sCAET,YAAY,eAAe;;;;;;sCAG9B,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,MAAK;gCACL,SAAS,IAAM,QAAQ;gCACvB,WAAU;0CACX;;;;;;;;;;;;;;;;yCAML,8OAAC;oBAAK,UAAU;oBAAc,WAAU;;wBACrC,SAAS,4BACR,8OAAC;;8CACC,8OAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,8OAAC;oCACC,MAAK;oCACL,OAAO,SAAS,IAAI;oCACpB,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;gDAAE,GAAG,IAAI;gDAAE,MAAM,EAAE,MAAM,CAAC,KAAK;4CAAC,CAAC;oCACvE,WAAU;oCACV,aAAY;oCACZ,QAAQ;;;;;;;;;;;;sCAKd,8OAAC;;8CACC,8OAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,8OAAC;oCACC,MAAK;oCACL,OAAO,SAAS,KAAK;oCACrB,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;gDAAE,GAAG,IAAI;gDAAE,OAAO,EAAE,MAAM,CAAC,KAAK;4CAAC,CAAC;oCACxE,WAAU;oCACV,aAAY;oCACZ,QAAQ;;;;;;;;;;;;sCAIZ,8OAAC;;8CACC,8OAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,8OAAC;oCACC,MAAK;oCACL,OAAO,SAAS,QAAQ;oCACxB,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;gDAAE,GAAG,IAAI;gDAAE,UAAU,EAAE,MAAM,CAAC,KAAK;4CAAC,CAAC;oCAC3E,WAAU;oCACV,aAAY;oCACZ,QAAQ;oCACR,WAAW;;;;;;;;;;;;wBAId,SAAS,4BACR,8OAAC;;8CACC,8OAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,8OAAC;oCACC,MAAK;oCACL,OAAO,SAAS,eAAe;oCAC/B,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;gDAAE,GAAG,IAAI;gDAAE,iBAAiB,EAAE,MAAM,CAAC,KAAK;4CAAC,CAAC;oCAClF,WAAU;oCACV,aAAY;oCACZ,QAAQ;oCACR,WAAW;;;;;;;;;;;;sCAKjB,8OAAC;4BACC,MAAK;4BACL,UAAU;4BACV,WAAU;sCAET,YAAY,mBAAoB,SAAS,UAAU,YAAY;;;;;;wBAGjE,SAAS,yBACR,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,MAAK;gCACL,SAAS,IAAM,QAAQ;gCACvB,WAAU;0CACX;;;;;;;;;;;sCAML,8OAAC;4BAAI,WAAU;sCACZ,SAAS,wBACR;;oCAAE;oCACuB;kDACvB,8OAAC;wCACC,MAAK;wCACL,SAAS,IAAM,QAAQ;wCACvB,WAAU;kDACX;;;;;;;6DAKH;;oCAAE;oCACyB;kDACzB,8OAAC;wCACC,MAAK;wCACL,SAAS,IAAM,QAAQ;wCACvB,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWnB", "debugId": null}}, {"offset": {"line": 1664, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Development/BuzzEdge/buzzedge-frontend/src/components/CommentForm.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState } from \"react\";\nimport { usePublicAuth } from \"@/lib/publicAuth\";\nimport { commentHelpers } from \"@/lib/commentAPI\";\n\ninterface CommentFormData {\n  content: string;\n  parentCommentId?: string;\n  author: {\n    name: string;\n    email: string;\n    avatar?: string;\n    userId?: string;\n  };\n}\n\ninterface CommentFormProps {\n  onSubmit: (commentData: CommentFormData) => Promise<void>;\n  onAuthRequired: (mode: \"login\" | \"register\") => void;\n  parentCommentId?: string;\n  placeholder?: string;\n  buttonText?: string;\n}\n\nexport function CommentForm({\n  onSubmit,\n  onAuthRequired,\n  parentCommentId,\n  placeholder = \"Share your thoughts...\",\n  buttonText = \"Post Comment\",\n}: CommentFormProps) {\n  const [content, setContent] = useState(\"\");\n  const [guestName, setGuestName] = useState(\"\");\n  const [guestEmail, setGuestEmail] = useState(\"\");\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [showGuestForm, setShowGuestForm] = useState(false);\n  const [errors, setErrors] = useState<Record<string, string>>({});\n\n  const { user } = usePublicAuth();\n\n  const validateForm = () => {\n    const newErrors: Record<string, string> = {};\n\n    // Validate content\n    const contentValidation = commentHelpers.validateComment(content);\n    if (!contentValidation.isValid) {\n      newErrors.content = contentValidation.error!;\n    }\n\n    // If not logged in, validate guest fields\n    if (!user && showGuestForm) {\n      const nameValidation = commentHelpers.validateName(guestName);\n      if (!nameValidation.isValid) {\n        newErrors.name = nameValidation.error!;\n      }\n\n      if (!guestEmail.trim()) {\n        newErrors.email = \"Email is required\";\n      } else if (!commentHelpers.validateEmail(guestEmail)) {\n        newErrors.email = \"Please enter a valid email\";\n      }\n    }\n\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n\n    if (!validateForm()) {\n      return;\n    }\n\n    setIsSubmitting(true);\n\n    try {\n      const commentData = {\n        content: content.trim(),\n        parentCommentId,\n        author: user\n          ? {\n              name: user.name,\n              email: user.email,\n              avatar: user.avatar,\n              userId: user.id,\n            }\n          : {\n              name: guestName.trim(),\n              email: guestEmail.trim(),\n            },\n      };\n\n      await onSubmit(commentData);\n\n      // Reset form\n      setContent(\"\");\n      if (!user) {\n        setGuestName(\"\");\n        setGuestEmail(\"\");\n        setShowGuestForm(false);\n      }\n      setErrors({});\n    } catch {\n      // Error handling is done in parent component\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  const handleGuestComment = () => {\n    setShowGuestForm(true);\n  };\n\n  const handleLoginRequired = () => {\n    onAuthRequired(\"login\");\n  };\n\n  const handleRegisterRequired = () => {\n    onAuthRequired(\"register\");\n  };\n\n  return (\n    <div className=\"bg-gray-50 rounded-lg p-6\">\n      <form onSubmit={handleSubmit} className=\"space-y-4\">\n        {/* Comment Content */}\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n            {user ? `Comment as ${user.name}` : \"Add a comment\"}\n          </label>\n          <textarea\n            value={content}\n            onChange={(e) => setContent(e.target.value)}\n            placeholder={placeholder}\n            rows={4}\n            className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none ${\n              errors.content ? \"border-red-500\" : \"border-gray-300\"\n            }`}\n            maxLength={1000}\n          />\n          {errors.content && (\n            <p className=\"mt-1 text-sm text-red-600\">{errors.content}</p>\n          )}\n          <div className=\"mt-1 text-sm text-gray-500 text-right\">\n            {content.length}/1000 characters\n          </div>\n        </div>\n\n        {/* Guest User Fields */}\n        {!user && showGuestForm && (\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4 p-4 bg-blue-50 rounded-md\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                Your Name *\n              </label>\n              <input\n                type=\"text\"\n                value={guestName}\n                onChange={(e) => setGuestName(e.target.value)}\n                className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${\n                  errors.name ? \"border-red-500\" : \"border-gray-300\"\n                }`}\n                placeholder=\"Enter your name\"\n                maxLength={50}\n              />\n              {errors.name && (\n                <p className=\"mt-1 text-sm text-red-600\">{errors.name}</p>\n              )}\n            </div>\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                Your Email *\n              </label>\n              <input\n                type=\"email\"\n                value={guestEmail}\n                onChange={(e) => setGuestEmail(e.target.value)}\n                className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${\n                  errors.email ? \"border-red-500\" : \"border-gray-300\"\n                }`}\n                placeholder=\"Enter your email\"\n              />\n              {errors.email && (\n                <p className=\"mt-1 text-sm text-red-600\">{errors.email}</p>\n              )}\n            </div>\n            <div className=\"md:col-span-2\">\n              <p className=\"text-sm text-gray-600\">\n                Your email will not be published. It's only used for\n                notifications and Gravatar.\n              </p>\n            </div>\n          </div>\n        )}\n\n        {/* Action Buttons */}\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center space-x-3\">\n            {user ? (\n              <div className=\"flex items-center space-x-2\">\n                <img\n                  src={user.avatar}\n                  alt={user.name}\n                  className=\"w-8 h-8 rounded-full\"\n                />\n                <span className=\"text-sm text-gray-600\">\n                  Commenting as <span className=\"font-medium\">{user.name}</span>\n                </span>\n              </div>\n            ) : (\n              <div className=\"flex items-center space-x-2 text-sm\">\n                <button\n                  type=\"button\"\n                  onClick={handleLoginRequired}\n                  className=\"text-blue-600 hover:text-blue-800 font-medium\"\n                >\n                  Sign in\n                </button>\n                <span className=\"text-gray-400\">or</span>\n                <button\n                  type=\"button\"\n                  onClick={handleRegisterRequired}\n                  className=\"text-blue-600 hover:text-blue-800 font-medium\"\n                >\n                  create account\n                </button>\n                <span className=\"text-gray-400\">or</span>\n                <button\n                  type=\"button\"\n                  onClick={handleGuestComment}\n                  className=\"text-gray-600 hover:text-gray-800\"\n                >\n                  comment as guest\n                </button>\n              </div>\n            )}\n          </div>\n\n          <div className=\"flex items-center space-x-3\">\n            {!user && showGuestForm && (\n              <button\n                type=\"button\"\n                onClick={() => {\n                  setShowGuestForm(false);\n                  setGuestName(\"\");\n                  setGuestEmail(\"\");\n                  setErrors({});\n                }}\n                className=\"px-4 py-2 text-gray-600 hover:text-gray-800\"\n              >\n                Cancel\n              </button>\n            )}\n\n            <button\n              type=\"submit\"\n              disabled={\n                isSubmitting || !content.trim() || (!user && !showGuestForm)\n              }\n              className=\"px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed\"\n            >\n              {isSubmitting ? \"Posting...\" : buttonText}\n            </button>\n          </div>\n        </div>\n\n        {/* Help Text */}\n        {!user && !showGuestForm && (\n          <div className=\"text-sm text-gray-500 bg-blue-50 p-3 rounded-md\">\n            💡 <strong>Tip:</strong> Sign in or create an account to get\n            notifications when someone replies to your comments, and to build\n            your commenting reputation in our community.\n          </div>\n        )}\n      </form>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAyBO,SAAS,YAAY,EAC1B,QAAQ,EACR,cAAc,EACd,eAAe,EACf,cAAc,wBAAwB,EACtC,aAAa,cAAc,EACV;IACjB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAE9D,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,yHAAA,CAAA,gBAAa,AAAD;IAE7B,MAAM,eAAe;QACnB,MAAM,YAAoC,CAAC;QAE3C,mBAAmB;QACnB,MAAM,oBAAoB,wHAAA,CAAA,iBAAc,CAAC,eAAe,CAAC;QACzD,IAAI,CAAC,kBAAkB,OAAO,EAAE;YAC9B,UAAU,OAAO,GAAG,kBAAkB,KAAK;QAC7C;QAEA,0CAA0C;QAC1C,IAAI,CAAC,QAAQ,eAAe;YAC1B,MAAM,iBAAiB,wHAAA,CAAA,iBAAc,CAAC,YAAY,CAAC;YACnD,IAAI,CAAC,eAAe,OAAO,EAAE;gBAC3B,UAAU,IAAI,GAAG,eAAe,KAAK;YACvC;YAEA,IAAI,CAAC,WAAW,IAAI,IAAI;gBACtB,UAAU,KAAK,GAAG;YACpB,OAAO,IAAI,CAAC,wHAAA,CAAA,iBAAc,CAAC,aAAa,CAAC,aAAa;gBACpD,UAAU,KAAK,GAAG;YACpB;QACF;QAEA,UAAU;QACV,OAAO,OAAO,IAAI,CAAC,WAAW,MAAM,KAAK;IAC3C;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,gBAAgB;YACnB;QACF;QAEA,gBAAgB;QAEhB,IAAI;YACF,MAAM,cAAc;gBAClB,SAAS,QAAQ,IAAI;gBACrB;gBACA,QAAQ,OACJ;oBACE,MAAM,KAAK,IAAI;oBACf,OAAO,KAAK,KAAK;oBACjB,QAAQ,KAAK,MAAM;oBACnB,QAAQ,KAAK,EAAE;gBACjB,IACA;oBACE,MAAM,UAAU,IAAI;oBACpB,OAAO,WAAW,IAAI;gBACxB;YACN;YAEA,MAAM,SAAS;YAEf,aAAa;YACb,WAAW;YACX,IAAI,CAAC,MAAM;gBACT,aAAa;gBACb,cAAc;gBACd,iBAAiB;YACnB;YACA,UAAU,CAAC;QACb,EAAE,OAAM;QACN,6CAA6C;QAC/C,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,qBAAqB;QACzB,iBAAiB;IACnB;IAEA,MAAM,sBAAsB;QAC1B,eAAe;IACjB;IAEA,MAAM,yBAAyB;QAC7B,eAAe;IACjB;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAK,UAAU;YAAc,WAAU;;8BAEtC,8OAAC;;sCACC,8OAAC;4BAAM,WAAU;sCACd,OAAO,CAAC,WAAW,EAAE,KAAK,IAAI,EAAE,GAAG;;;;;;sCAEtC,8OAAC;4BACC,OAAO;4BACP,UAAU,CAAC,IAAM,WAAW,EAAE,MAAM,CAAC,KAAK;4BAC1C,aAAa;4BACb,MAAM;4BACN,WAAW,CAAC,mGAAmG,EAC7G,OAAO,OAAO,GAAG,mBAAmB,mBACpC;4BACF,WAAW;;;;;;wBAEZ,OAAO,OAAO,kBACb,8OAAC;4BAAE,WAAU;sCAA6B,OAAO,OAAO;;;;;;sCAE1D,8OAAC;4BAAI,WAAU;;gCACZ,QAAQ,MAAM;gCAAC;;;;;;;;;;;;;gBAKnB,CAAC,QAAQ,+BACR,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,8OAAC;oCACC,MAAK;oCACL,OAAO;oCACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;oCAC5C,WAAW,CAAC,uFAAuF,EACjG,OAAO,IAAI,GAAG,mBAAmB,mBACjC;oCACF,aAAY;oCACZ,WAAW;;;;;;gCAEZ,OAAO,IAAI,kBACV,8OAAC;oCAAE,WAAU;8CAA6B,OAAO,IAAI;;;;;;;;;;;;sCAGzD,8OAAC;;8CACC,8OAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,8OAAC;oCACC,MAAK;oCACL,OAAO;oCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;oCAC7C,WAAW,CAAC,uFAAuF,EACjG,OAAO,KAAK,GAAG,mBAAmB,mBAClC;oCACF,aAAY;;;;;;gCAEb,OAAO,KAAK,kBACX,8OAAC;oCAAE,WAAU;8CAA6B,OAAO,KAAK;;;;;;;;;;;;sCAG1D,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;;;;;;8BAS3C,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACZ,qBACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,KAAK,KAAK,MAAM;wCAChB,KAAK,KAAK,IAAI;wCACd,WAAU;;;;;;kDAEZ,8OAAC;wCAAK,WAAU;;4CAAwB;0DACxB,8OAAC;gDAAK,WAAU;0DAAe,KAAK,IAAI;;;;;;;;;;;;;;;;;qDAI1D,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,MAAK;wCACL,SAAS;wCACT,WAAU;kDACX;;;;;;kDAGD,8OAAC;wCAAK,WAAU;kDAAgB;;;;;;kDAChC,8OAAC;wCACC,MAAK;wCACL,SAAS;wCACT,WAAU;kDACX;;;;;;kDAGD,8OAAC;wCAAK,WAAU;kDAAgB;;;;;;kDAChC,8OAAC;wCACC,MAAK;wCACL,SAAS;wCACT,WAAU;kDACX;;;;;;;;;;;;;;;;;sCAOP,8OAAC;4BAAI,WAAU;;gCACZ,CAAC,QAAQ,+BACR,8OAAC;oCACC,MAAK;oCACL,SAAS;wCACP,iBAAiB;wCACjB,aAAa;wCACb,cAAc;wCACd,UAAU,CAAC;oCACb;oCACA,WAAU;8CACX;;;;;;8CAKH,8OAAC;oCACC,MAAK;oCACL,UACE,gBAAgB,CAAC,QAAQ,IAAI,MAAO,CAAC,QAAQ,CAAC;oCAEhD,WAAU;8CAET,eAAe,eAAe;;;;;;;;;;;;;;;;;;gBAMpC,CAAC,QAAQ,CAAC,+BACT,8OAAC;oBAAI,WAAU;;wBAAkD;sCAC5D,8OAAC;sCAAO;;;;;;wBAAa;;;;;;;;;;;;;;;;;;AAQpC", "debugId": null}}, {"offset": {"line": 2074, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Development/BuzzEdge/buzzedge-frontend/src/components/CommentItem.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { usePublicAuth } from '@/lib/publicAuth';\nimport { Comment, commentHelpers, commentAPI } from '@/lib/commentAPI';\n\ninterface CommentItemProps {\n  comment: Comment;\n  onUpdate: (commentId: string, updatedComment: Comment) => void;\n  onDelete: (commentId: string) => void;\n  onLike: (commentId: string) => void;\n  onReply: (parentId: string) => void;\n  onAuthRequired: (mode: 'login' | 'register') => void;\n  depth?: number;\n}\n\nexport function CommentItem({ \n  comment, \n  onUpdate, \n  onDelete, \n  onLike, \n  onReply, \n  onAuthRequired,\n  depth = 0 \n}: CommentItemProps) {\n  const [isEditing, setIsEditing] = useState(false);\n  const [editContent, setEditContent] = useState(comment.content);\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [showReplies, setShowReplies] = useState(true);\n\n  const { user } = usePublicAuth();\n\n  const isOwner = user && comment.author.userId === user.id;\n  const hasLiked = user && comment.likedBy.includes(user.id);\n  const maxDepth = 3; // Maximum nesting level\n\n  const handleEdit = async () => {\n    if (!editContent.trim()) return;\n\n    setIsSubmitting(true);\n    try {\n      const response = await commentAPI.updateComment(comment._id, editContent.trim());\n      onUpdate(comment._id, response.data.comment);\n      setIsEditing(false);\n    } catch (err: any) {\n      alert(err.message || 'Failed to update comment');\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  const handleDelete = async () => {\n    if (!confirm('Are you sure you want to delete this comment?')) return;\n\n    try {\n      await commentAPI.deleteComment(comment._id);\n      onDelete(comment._id);\n    } catch (err: any) {\n      alert(err.message || 'Failed to delete comment');\n    }\n  };\n\n  const handleLike = () => {\n    if (!user) {\n      onAuthRequired('login');\n      return;\n    }\n    onLike(comment._id);\n  };\n\n  const handleReply = () => {\n    if (!user) {\n      onAuthRequired('login');\n      return;\n    }\n    onReply(comment._id);\n  };\n\n  return (\n    <div className={`${depth > 0 ? 'ml-8 border-l-2 border-gray-100 pl-4' : ''}`}>\n      <div className=\"bg-white rounded-lg border border-gray-200 p-4\">\n        {/* Comment Header */}\n        <div className=\"flex items-start justify-between mb-3\">\n          <div className=\"flex items-center space-x-3\">\n            <img\n              src={comment.author.avatar}\n              alt={comment.author.name}\n              className=\"w-10 h-10 rounded-full\"\n            />\n            <div>\n              <div className=\"flex items-center space-x-2\">\n                <h4 className=\"font-medium text-gray-900\">{comment.author.name}</h4>\n                {comment.author.userId && (\n                  <span className=\"inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800\">\n                    Member\n                  </span>\n                )}\n                {comment.isEdited && (\n                  <span className=\"text-xs text-gray-500\">(edited)</span>\n                )}\n              </div>\n              <p className=\"text-sm text-gray-500\">\n                {commentHelpers.formatDate(comment.createdAt)}\n              </p>\n            </div>\n          </div>\n\n          {/* Comment Actions Menu */}\n          {isOwner && (\n            <div className=\"flex items-center space-x-2\">\n              <button\n                onClick={() => setIsEditing(!isEditing)}\n                className=\"text-gray-400 hover:text-gray-600 p-1\"\n                title=\"Edit comment\"\n              >\n                <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\" />\n                </svg>\n              </button>\n              <button\n                onClick={handleDelete}\n                className=\"text-gray-400 hover:text-red-600 p-1\"\n                title=\"Delete comment\"\n              >\n                <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\" />\n                </svg>\n              </button>\n            </div>\n          )}\n        </div>\n\n        {/* Comment Content */}\n        {isEditing ? (\n          <div className=\"mb-4\">\n            <textarea\n              value={editContent}\n              onChange={(e) => setEditContent(e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none\"\n              rows={3}\n              maxLength={1000}\n            />\n            <div className=\"flex items-center justify-between mt-2\">\n              <span className=\"text-sm text-gray-500\">\n                {editContent.length}/1000 characters\n              </span>\n              <div className=\"flex items-center space-x-2\">\n                <button\n                  onClick={() => {\n                    setIsEditing(false);\n                    setEditContent(comment.content);\n                  }}\n                  className=\"px-3 py-1 text-sm text-gray-600 hover:text-gray-800\"\n                >\n                  Cancel\n                </button>\n                <button\n                  onClick={handleEdit}\n                  disabled={isSubmitting || !editContent.trim()}\n                  className=\"px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50\"\n                >\n                  {isSubmitting ? 'Saving...' : 'Save'}\n                </button>\n              </div>\n            </div>\n          </div>\n        ) : (\n          <div className=\"mb-4\">\n            <p className=\"text-gray-800 whitespace-pre-wrap\">{comment.content}</p>\n          </div>\n        )}\n\n        {/* Comment Actions */}\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center space-x-4\">\n            {/* Like Button */}\n            <button\n              onClick={handleLike}\n              className={`flex items-center space-x-1 text-sm ${\n                hasLiked \n                  ? 'text-red-600 hover:text-red-700' \n                  : 'text-gray-500 hover:text-red-600'\n              }`}\n            >\n              <svg \n                className={`w-4 h-4 ${hasLiked ? 'fill-current' : ''}`} \n                fill={hasLiked ? 'currentColor' : 'none'} \n                stroke=\"currentColor\" \n                viewBox=\"0 0 24 24\"\n              >\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z\" />\n              </svg>\n              <span>{comment.likes}</span>\n            </button>\n\n            {/* Reply Button */}\n            {depth < maxDepth && (\n              <button\n                onClick={handleReply}\n                className=\"flex items-center space-x-1 text-sm text-gray-500 hover:text-blue-600\"\n              >\n                <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6\" />\n                </svg>\n                <span>Reply</span>\n              </button>\n            )}\n          </div>\n\n          {/* Status Badge */}\n          {comment.status !== 'approved' && (\n            <span className={`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${\n              comment.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :\n              comment.status === 'rejected' ? 'bg-red-100 text-red-800' :\n              'bg-gray-100 text-gray-800'\n            }`}>\n              {comment.status}\n            </span>\n          )}\n        </div>\n      </div>\n\n      {/* Replies */}\n      {comment.replies && comment.replies.length > 0 && (\n        <div className=\"mt-4\">\n          <button\n            onClick={() => setShowReplies(!showReplies)}\n            className=\"text-sm text-blue-600 hover:text-blue-800 mb-3\"\n          >\n            {showReplies ? 'Hide' : 'Show'} {comment.replies.length} {comment.replies.length === 1 ? 'reply' : 'replies'}\n          </button>\n          \n          {showReplies && (\n            <div className=\"space-y-4\">\n              {comment.replies.map((reply) => (\n                <CommentItem\n                  key={reply._id}\n                  comment={reply}\n                  onUpdate={onUpdate}\n                  onDelete={onDelete}\n                  onLike={onLike}\n                  onReply={onReply}\n                  onAuthRequired={onAuthRequired}\n                  depth={depth + 1}\n                />\n              ))}\n            </div>\n          )}\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAgBO,SAAS,YAAY,EAC1B,OAAO,EACP,QAAQ,EACR,QAAQ,EACR,MAAM,EACN,OAAO,EACP,cAAc,EACd,QAAQ,CAAC,EACQ;IACjB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,QAAQ,OAAO;IAC9D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,yHAAA,CAAA,gBAAa,AAAD;IAE7B,MAAM,UAAU,QAAQ,QAAQ,MAAM,CAAC,MAAM,KAAK,KAAK,EAAE;IACzD,MAAM,WAAW,QAAQ,QAAQ,OAAO,CAAC,QAAQ,CAAC,KAAK,EAAE;IACzD,MAAM,WAAW,GAAG,wBAAwB;IAE5C,MAAM,aAAa;QACjB,IAAI,CAAC,YAAY,IAAI,IAAI;QAEzB,gBAAgB;QAChB,IAAI;YACF,MAAM,WAAW,MAAM,wHAAA,CAAA,aAAU,CAAC,aAAa,CAAC,QAAQ,GAAG,EAAE,YAAY,IAAI;YAC7E,SAAS,QAAQ,GAAG,EAAE,SAAS,IAAI,CAAC,OAAO;YAC3C,aAAa;QACf,EAAE,OAAO,KAAU;YACjB,MAAM,IAAI,OAAO,IAAI;QACvB,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,eAAe;QACnB,IAAI,CAAC,QAAQ,kDAAkD;QAE/D,IAAI;YACF,MAAM,wHAAA,CAAA,aAAU,CAAC,aAAa,CAAC,QAAQ,GAAG;YAC1C,SAAS,QAAQ,GAAG;QACtB,EAAE,OAAO,KAAU;YACjB,MAAM,IAAI,OAAO,IAAI;QACvB;IACF;IAEA,MAAM,aAAa;QACjB,IAAI,CAAC,MAAM;YACT,eAAe;YACf;QACF;QACA,OAAO,QAAQ,GAAG;IACpB;IAEA,MAAM,cAAc;QAClB,IAAI,CAAC,MAAM;YACT,eAAe;YACf;QACF;QACA,QAAQ,QAAQ,GAAG;IACrB;IAEA,qBACE,8OAAC;QAAI,WAAW,GAAG,QAAQ,IAAI,yCAAyC,IAAI;;0BAC1E,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,KAAK,QAAQ,MAAM,CAAC,MAAM;wCAC1B,KAAK,QAAQ,MAAM,CAAC,IAAI;wCACxB,WAAU;;;;;;kDAEZ,8OAAC;;0DACC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAA6B,QAAQ,MAAM,CAAC,IAAI;;;;;;oDAC7D,QAAQ,MAAM,CAAC,MAAM,kBACpB,8OAAC;wDAAK,WAAU;kEAA6F;;;;;;oDAI9G,QAAQ,QAAQ,kBACf,8OAAC;wDAAK,WAAU;kEAAwB;;;;;;;;;;;;0DAG5C,8OAAC;gDAAE,WAAU;0DACV,wHAAA,CAAA,iBAAc,CAAC,UAAU,CAAC,QAAQ,SAAS;;;;;;;;;;;;;;;;;;4BAMjD,yBACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,SAAS,IAAM,aAAa,CAAC;wCAC7B,WAAU;wCACV,OAAM;kDAEN,cAAA,8OAAC;4CAAI,WAAU;4CAAU,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDACjE,cAAA,8OAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;;;;;;kDAGzE,8OAAC;wCACC,SAAS;wCACT,WAAU;wCACV,OAAM;kDAEN,cAAA,8OAAC;4CAAI,WAAU;4CAAU,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDACjE,cAAA,8OAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAQ9E,0BACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,OAAO;gCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;gCAC9C,WAAU;gCACV,MAAM;gCACN,WAAW;;;;;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;;4CACb,YAAY,MAAM;4CAAC;;;;;;;kDAEtB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,SAAS;oDACP,aAAa;oDACb,eAAe,QAAQ,OAAO;gDAChC;gDACA,WAAU;0DACX;;;;;;0DAGD,8OAAC;gDACC,SAAS;gDACT,UAAU,gBAAgB,CAAC,YAAY,IAAI;gDAC3C,WAAU;0DAET,eAAe,cAAc;;;;;;;;;;;;;;;;;;;;;;;6CAMtC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAE,WAAU;sCAAqC,QAAQ,OAAO;;;;;;;;;;;kCAKrE,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCACC,SAAS;wCACT,WAAW,CAAC,oCAAoC,EAC9C,WACI,oCACA,oCACJ;;0DAEF,8OAAC;gDACC,WAAW,CAAC,QAAQ,EAAE,WAAW,iBAAiB,IAAI;gDACtD,MAAM,WAAW,iBAAiB;gDAClC,QAAO;gDACP,SAAQ;0DAER,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;0DAEvE,8OAAC;0DAAM,QAAQ,KAAK;;;;;;;;;;;;oCAIrB,QAAQ,0BACP,8OAAC;wCACC,SAAS;wCACT,WAAU;;0DAEV,8OAAC;gDAAI,WAAU;gDAAU,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DACjE,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;0DAEvE,8OAAC;0DAAK;;;;;;;;;;;;;;;;;;4BAMX,QAAQ,MAAM,KAAK,4BAClB,8OAAC;gCAAK,WAAW,CAAC,iEAAiE,EACjF,QAAQ,MAAM,KAAK,YAAY,kCAC/B,QAAQ,MAAM,KAAK,aAAa,4BAChC,6BACA;0CACC,QAAQ,MAAM;;;;;;;;;;;;;;;;;;YAOtB,QAAQ,OAAO,IAAI,QAAQ,OAAO,CAAC,MAAM,GAAG,mBAC3C,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,SAAS,IAAM,eAAe,CAAC;wBAC/B,WAAU;;4BAET,cAAc,SAAS;4BAAO;4BAAE,QAAQ,OAAO,CAAC,MAAM;4BAAC;4BAAE,QAAQ,OAAO,CAAC,MAAM,KAAK,IAAI,UAAU;;;;;;;oBAGpG,6BACC,8OAAC;wBAAI,WAAU;kCACZ,QAAQ,OAAO,CAAC,GAAG,CAAC,CAAC,sBACpB,8OAAC;gCAEC,SAAS;gCACT,UAAU;gCACV,UAAU;gCACV,QAAQ;gCACR,SAAS;gCACT,gBAAgB;gCAChB,OAAO,QAAQ;+BAPV,MAAM,GAAG;;;;;;;;;;;;;;;;;;;;;;AAgBhC", "debugId": null}}, {"offset": {"line": 2528, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Development/BuzzEdge/buzzedge-frontend/src/components/CommentSection.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport { usePublicAuth } from \"@/lib/publicAuth\";\nimport { commentAPI, Comment, commentHelpers } from \"@/lib/commentAPI\";\nimport { UserAuth } from \"./UserAuth\";\nimport { CommentForm } from \"./CommentForm\";\nimport { CommentItem } from \"./CommentItem\";\n\ninterface CommentSectionProps {\n  postId: string;\n  postTitle: string;\n}\n\nexport function CommentSection({ postId, postTitle }: CommentSectionProps) {\n  const [comments, setComments] = useState<Comment[]>([]);\n  const [isLoading, setIsLoading] = useState(true);\n  const [error, setError] = useState(\"\");\n  const [showAuth, setShowAuth] = useState(false);\n  const [authMode, setAuthMode] = useState<\"login\" | \"register\">(\"login\");\n  const [sortOrder, setSortOrder] = useState<\"newest\" | \"oldest\">(\"newest\");\n  const [page, setPage] = useState(1);\n  const [hasMore, setHasMore] = useState(false);\n  const [totalComments, setTotalComments] = useState(0);\n\n  const { user } = usePublicAuth();\n\n  useEffect(() => {\n    loadComments();\n  }, [postId, page, sortOrder]);\n\n  const loadComments = async () => {\n    try {\n      setIsLoading(true);\n      const response = await commentAPI.getComments(postId, page, 10);\n\n      if (page === 1) {\n        setComments(response.data.comments);\n      } else {\n        setComments((prev) => [...prev, ...response.data.comments]);\n      }\n\n      setHasMore(response.data.pagination.hasNext);\n      setTotalComments(response.data.pagination.totalComments);\n      setError(\"\");\n    } catch (err: any) {\n      setError(err.message || \"Failed to load comments\");\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleCommentSubmit = async (commentData: {\n    content: string;\n    parentCommentId?: string;\n    author: {\n      name: string;\n      email: string;\n      avatar?: string;\n      userId?: string;\n    };\n  }) => {\n    try {\n      await commentAPI.createComment({\n        ...commentData,\n        postId,\n      });\n\n      // Show success message\n      alert(\"Comment submitted for moderation. It will appear once approved.\");\n\n      // Optionally reload comments to show pending comment if you want\n      // loadComments();\n    } catch (err: unknown) {\n      alert((err as Error).message || \"Failed to submit comment\");\n    }\n  };\n\n  const handleCommentUpdate = (commentId: string, updatedComment: Comment) => {\n    setComments((prev) =>\n      prev.map((comment) =>\n        comment._id === commentId ? updatedComment : comment\n      )\n    );\n  };\n\n  const handleCommentDelete = (commentId: string) => {\n    setComments((prev) => prev.filter((comment) => comment._id !== commentId));\n    setTotalComments((prev) => prev - 1);\n  };\n\n  const handleLikeToggle = async (commentId: string) => {\n    if (!user) {\n      setAuthMode(\"login\");\n      setShowAuth(true);\n      return;\n    }\n\n    try {\n      const response = await commentAPI.toggleLike(commentId);\n\n      setComments((prev) =>\n        prev.map((comment) =>\n          comment._id === commentId\n            ? {\n                ...comment,\n                likes: response.data.likes,\n                likedBy: response.data.hasLiked\n                  ? [...comment.likedBy, user.id]\n                  : comment.likedBy.filter((id) => id !== user.id),\n              }\n            : comment\n        )\n      );\n    } catch (err: unknown) {\n      alert((err as Error).message || \"Failed to toggle like\");\n    }\n  };\n\n  const handleLoadMore = () => {\n    setPage((prev) => prev + 1);\n  };\n\n  const handleSortChange = (newOrder: \"newest\" | \"oldest\") => {\n    setSortOrder(newOrder);\n    setPage(1);\n  };\n\n  const sortedComments = commentHelpers.sortComments(comments, sortOrder);\n\n  return (\n    <div className=\"mt-12 border-t border-gray-200 pt-8\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between mb-6\">\n        <h3 className=\"text-2xl font-bold text-gray-900\">\n          Comments ({totalComments})\n        </h3>\n\n        {/* Sort Options */}\n        <div className=\"flex items-center space-x-2\">\n          <span className=\"text-sm text-gray-500\">Sort by:</span>\n          <select\n            value={sortOrder}\n            onChange={(e) =>\n              handleSortChange(e.target.value as \"newest\" | \"oldest\")\n            }\n            className=\"text-sm border border-gray-300 rounded px-2 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500\"\n          >\n            <option value=\"newest\">Newest first</option>\n            <option value=\"oldest\">Oldest first</option>\n          </select>\n        </div>\n      </div>\n\n      {/* Comment Form */}\n      <div className=\"mb-8\">\n        <CommentForm\n          onSubmit={handleCommentSubmit}\n          onAuthRequired={(mode) => {\n            setAuthMode(mode);\n            setShowAuth(true);\n          }}\n        />\n      </div>\n\n      {/* Comments List */}\n      <div className=\"space-y-6\">\n        {isLoading && page === 1 ? (\n          <div className=\"flex justify-center py-8\">\n            <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"></div>\n          </div>\n        ) : error ? (\n          <div className=\"text-center py-8\">\n            <div className=\"text-red-600 mb-2\">{error}</div>\n            <button\n              onClick={() => {\n                setPage(1);\n                loadComments();\n              }}\n              className=\"text-blue-600 hover:text-blue-800\"\n            >\n              Try again\n            </button>\n          </div>\n        ) : sortedComments.length === 0 ? (\n          <div className=\"text-center py-8 text-gray-500\">\n            <div className=\"text-4xl mb-4\">💬</div>\n            <h4 className=\"text-lg font-medium mb-2\">No comments yet</h4>\n            <p>Be the first to share your thoughts on \"{postTitle}\"</p>\n          </div>\n        ) : (\n          <>\n            {sortedComments.map((comment) => (\n              <CommentItem\n                key={comment._id}\n                comment={comment}\n                onUpdate={handleCommentUpdate}\n                onDelete={handleCommentDelete}\n                onLike={handleLikeToggle}\n                onReply={(parentId) => {\n                  // Handle reply functionality\n                  console.log(\"Reply to:\", parentId);\n                }}\n                onAuthRequired={(mode) => {\n                  setAuthMode(mode);\n                  setShowAuth(true);\n                }}\n              />\n            ))}\n\n            {/* Load More Button */}\n            {hasMore && (\n              <div className=\"text-center pt-6\">\n                <button\n                  onClick={handleLoadMore}\n                  disabled={isLoading}\n                  className=\"px-6 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50\"\n                >\n                  {isLoading ? \"Loading...\" : \"Load More Comments\"}\n                </button>\n              </div>\n            )}\n          </>\n        )}\n      </div>\n\n      {/* Authentication Modal */}\n      {showAuth && (\n        <UserAuth onClose={() => setShowAuth(false)} defaultMode={authMode} />\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAPA;;;;;;;;AAcO,SAAS,eAAe,EAAE,MAAM,EAAE,SAAS,EAAuB;IACvE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAwB;IAC/D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuB;IAChE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,yHAAA,CAAA,gBAAa,AAAD;IAE7B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;QAAQ;QAAM;KAAU;IAE5B,MAAM,eAAe;QACnB,IAAI;YACF,aAAa;YACb,MAAM,WAAW,MAAM,wHAAA,CAAA,aAAU,CAAC,WAAW,CAAC,QAAQ,MAAM;YAE5D,IAAI,SAAS,GAAG;gBACd,YAAY,SAAS,IAAI,CAAC,QAAQ;YACpC,OAAO;gBACL,YAAY,CAAC,OAAS;2BAAI;2BAAS,SAAS,IAAI,CAAC,QAAQ;qBAAC;YAC5D;YAEA,WAAW,SAAS,IAAI,CAAC,UAAU,CAAC,OAAO;YAC3C,iBAAiB,SAAS,IAAI,CAAC,UAAU,CAAC,aAAa;YACvD,SAAS;QACX,EAAE,OAAO,KAAU;YACjB,SAAS,IAAI,OAAO,IAAI;QAC1B,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,sBAAsB,OAAO;QAUjC,IAAI;YACF,MAAM,wHAAA,CAAA,aAAU,CAAC,aAAa,CAAC;gBAC7B,GAAG,WAAW;gBACd;YACF;YAEA,uBAAuB;YACvB,MAAM;QAEN,iEAAiE;QACjE,kBAAkB;QACpB,EAAE,OAAO,KAAc;YACrB,MAAM,AAAC,IAAc,OAAO,IAAI;QAClC;IACF;IAEA,MAAM,sBAAsB,CAAC,WAAmB;QAC9C,YAAY,CAAC,OACX,KAAK,GAAG,CAAC,CAAC,UACR,QAAQ,GAAG,KAAK,YAAY,iBAAiB;IAGnD;IAEA,MAAM,sBAAsB,CAAC;QAC3B,YAAY,CAAC,OAAS,KAAK,MAAM,CAAC,CAAC,UAAY,QAAQ,GAAG,KAAK;QAC/D,iBAAiB,CAAC,OAAS,OAAO;IACpC;IAEA,MAAM,mBAAmB,OAAO;QAC9B,IAAI,CAAC,MAAM;YACT,YAAY;YACZ,YAAY;YACZ;QACF;QAEA,IAAI;YACF,MAAM,WAAW,MAAM,wHAAA,CAAA,aAAU,CAAC,UAAU,CAAC;YAE7C,YAAY,CAAC,OACX,KAAK,GAAG,CAAC,CAAC,UACR,QAAQ,GAAG,KAAK,YACZ;wBACE,GAAG,OAAO;wBACV,OAAO,SAAS,IAAI,CAAC,KAAK;wBAC1B,SAAS,SAAS,IAAI,CAAC,QAAQ,GAC3B;+BAAI,QAAQ,OAAO;4BAAE,KAAK,EAAE;yBAAC,GAC7B,QAAQ,OAAO,CAAC,MAAM,CAAC,CAAC,KAAO,OAAO,KAAK,EAAE;oBACnD,IACA;QAGV,EAAE,OAAO,KAAc;YACrB,MAAM,AAAC,IAAc,OAAO,IAAI;QAClC;IACF;IAEA,MAAM,iBAAiB;QACrB,QAAQ,CAAC,OAAS,OAAO;IAC3B;IAEA,MAAM,mBAAmB,CAAC;QACxB,aAAa;QACb,QAAQ;IACV;IAEA,MAAM,iBAAiB,wHAAA,CAAA,iBAAc,CAAC,YAAY,CAAC,UAAU;IAE7D,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;;4BAAmC;4BACpC;4BAAc;;;;;;;kCAI3B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAK,WAAU;0CAAwB;;;;;;0CACxC,8OAAC;gCACC,OAAO;gCACP,UAAU,CAAC,IACT,iBAAiB,EAAE,MAAM,CAAC,KAAK;gCAEjC,WAAU;;kDAEV,8OAAC;wCAAO,OAAM;kDAAS;;;;;;kDACvB,8OAAC;wCAAO,OAAM;kDAAS;;;;;;;;;;;;;;;;;;;;;;;;0BAM7B,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,iIAAA,CAAA,cAAW;oBACV,UAAU;oBACV,gBAAgB,CAAC;wBACf,YAAY;wBACZ,YAAY;oBACd;;;;;;;;;;;0BAKJ,8OAAC;gBAAI,WAAU;0BACZ,aAAa,SAAS,kBACrB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;;;;;;;;;2BAEf,sBACF,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCAAqB;;;;;;sCACpC,8OAAC;4BACC,SAAS;gCACP,QAAQ;gCACR;4BACF;4BACA,WAAU;sCACX;;;;;;;;;;;2BAID,eAAe,MAAM,KAAK,kBAC5B,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCAAgB;;;;;;sCAC/B,8OAAC;4BAAG,WAAU;sCAA2B;;;;;;sCACzC,8OAAC;;gCAAE;gCAAyC;gCAAU;;;;;;;;;;;;yCAGxD;;wBACG,eAAe,GAAG,CAAC,CAAC,wBACnB,8OAAC,iIAAA,CAAA,cAAW;gCAEV,SAAS;gCACT,UAAU;gCACV,UAAU;gCACV,QAAQ;gCACR,SAAS,CAAC;oCACR,6BAA6B;oCAC7B,QAAQ,GAAG,CAAC,aAAa;gCAC3B;gCACA,gBAAgB,CAAC;oCACf,YAAY;oCACZ,YAAY;gCACd;+BAZK,QAAQ,GAAG;;;;;wBAiBnB,yBACC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,SAAS;gCACT,UAAU;gCACV,WAAU;0CAET,YAAY,eAAe;;;;;;;;;;;;;;;;;;YASvC,0BACC,8OAAC,8HAAA,CAAA,WAAQ;gBAAC,SAAS,IAAM,YAAY;gBAAQ,aAAa;;;;;;;;;;;;AAIlE", "debugId": null}}, {"offset": {"line": 2863, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Development/BuzzEdge/buzzedge-frontend/src/components/UserProfile.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { usePublicAuth } from '@/lib/publicAuth';\n\ninterface UserProfileProps {\n  onClose: () => void;\n}\n\nexport function UserProfile({ onClose }: UserProfileProps) {\n  const { user, updateProfile, logout } = usePublicAuth();\n  const [isEditing, setIsEditing] = useState(false);\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n\n  const [formData, setFormData] = useState({\n    name: user?.name || '',\n    bio: user?.bio || '',\n    website: user?.website || '',\n    socialLinks: {\n      twitter: user?.socialLinks?.twitter || '',\n      linkedin: user?.socialLinks?.linkedin || '',\n      github: user?.socialLinks?.github || '',\n    },\n    preferences: {\n      emailNotifications: user?.preferences?.emailNotifications ?? true,\n      marketingEmails: user?.preferences?.marketingEmails ?? false,\n      commentReplies: user?.preferences?.commentReplies ?? true,\n    },\n  });\n\n  if (!user) {\n    return null;\n  }\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setError('');\n    setSuccess('');\n    setIsLoading(true);\n\n    try {\n      await updateProfile(formData);\n      setSuccess('Profile updated successfully!');\n      setIsEditing(false);\n    } catch (err: any) {\n      setError(err.message || 'Failed to update profile');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleLogout = () => {\n    logout();\n    onClose();\n  };\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\n      <div className=\"bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto\">\n        {/* Header */}\n        <div className=\"flex items-center justify-between p-6 border-b border-gray-200\">\n          <h2 className=\"text-2xl font-bold text-gray-900\">User Profile</h2>\n          <button\n            onClick={onClose}\n            className=\"text-gray-400 hover:text-gray-600\"\n          >\n            <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n            </svg>\n          </button>\n        </div>\n\n        <div className=\"p-6\">\n          {/* Error/Success Messages */}\n          {error && (\n            <div className=\"mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded\">\n              {error}\n            </div>\n          )}\n          \n          {success && (\n            <div className=\"mb-4 p-3 bg-green-100 border border-green-400 text-green-700 rounded\">\n              {success}\n            </div>\n          )}\n\n          {/* Profile Header */}\n          <div className=\"flex items-center space-x-4 mb-6\">\n            <img\n              src={user.avatar}\n              alt={user.name}\n              className=\"w-16 h-16 rounded-full\"\n            />\n            <div>\n              <h3 className=\"text-xl font-semibold text-gray-900\">{user.name}</h3>\n              <p className=\"text-gray-600\">{user.email}</p>\n              <div className=\"flex items-center space-x-4 mt-1\">\n                <span className={`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${\n                  user.isVerified ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'\n                }`}>\n                  {user.isVerified ? 'Verified' : 'Unverified'}\n                </span>\n                {user.stats && (\n                  <span className=\"text-sm text-gray-500\">\n                    {user.stats.commentsCount} comments\n                  </span>\n                )}\n              </div>\n            </div>\n          </div>\n\n          {/* Profile Form */}\n          <form onSubmit={handleSubmit} className=\"space-y-6\">\n            {/* Basic Information */}\n            <div>\n              <h4 className=\"text-lg font-medium text-gray-900 mb-4\">Basic Information</h4>\n              <div className=\"space-y-4\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                    Display Name\n                  </label>\n                  <input\n                    type=\"text\"\n                    value={formData.name}\n                    onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}\n                    disabled={!isEditing}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-50\"\n                    maxLength={50}\n                  />\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                    Bio\n                  </label>\n                  <textarea\n                    value={formData.bio}\n                    onChange={(e) => setFormData(prev => ({ ...prev, bio: e.target.value }))}\n                    disabled={!isEditing}\n                    rows={3}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-50 resize-none\"\n                    placeholder=\"Tell us about yourself...\"\n                    maxLength={200}\n                  />\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                    Website\n                  </label>\n                  <input\n                    type=\"url\"\n                    value={formData.website}\n                    onChange={(e) => setFormData(prev => ({ ...prev, website: e.target.value }))}\n                    disabled={!isEditing}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-50\"\n                    placeholder=\"https://yourwebsite.com\"\n                  />\n                </div>\n              </div>\n            </div>\n\n            {/* Social Links */}\n            <div>\n              <h4 className=\"text-lg font-medium text-gray-900 mb-4\">Social Links</h4>\n              <div className=\"space-y-4\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                    Twitter\n                  </label>\n                  <input\n                    type=\"text\"\n                    value={formData.socialLinks.twitter}\n                    onChange={(e) => setFormData(prev => ({ \n                      ...prev, \n                      socialLinks: { ...prev.socialLinks, twitter: e.target.value }\n                    }))}\n                    disabled={!isEditing}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-50\"\n                    placeholder=\"@username\"\n                  />\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                    LinkedIn\n                  </label>\n                  <input\n                    type=\"text\"\n                    value={formData.socialLinks.linkedin}\n                    onChange={(e) => setFormData(prev => ({ \n                      ...prev, \n                      socialLinks: { ...prev.socialLinks, linkedin: e.target.value }\n                    }))}\n                    disabled={!isEditing}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-50\"\n                    placeholder=\"linkedin.com/in/username\"\n                  />\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                    GitHub\n                  </label>\n                  <input\n                    type=\"text\"\n                    value={formData.socialLinks.github}\n                    onChange={(e) => setFormData(prev => ({ \n                      ...prev, \n                      socialLinks: { ...prev.socialLinks, github: e.target.value }\n                    }))}\n                    disabled={!isEditing}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-50\"\n                    placeholder=\"github.com/username\"\n                  />\n                </div>\n              </div>\n            </div>\n\n            {/* Preferences */}\n            <div>\n              <h4 className=\"text-lg font-medium text-gray-900 mb-4\">Preferences</h4>\n              <div className=\"space-y-3\">\n                <label className=\"flex items-center\">\n                  <input\n                    type=\"checkbox\"\n                    checked={formData.preferences.emailNotifications}\n                    onChange={(e) => setFormData(prev => ({ \n                      ...prev, \n                      preferences: { ...prev.preferences, emailNotifications: e.target.checked }\n                    }))}\n                    disabled={!isEditing}\n                    className=\"rounded border-gray-300 text-blue-600 focus:ring-blue-500 disabled:opacity-50\"\n                  />\n                  <span className=\"ml-2 text-sm text-gray-700\">Email notifications</span>\n                </label>\n\n                <label className=\"flex items-center\">\n                  <input\n                    type=\"checkbox\"\n                    checked={formData.preferences.commentReplies}\n                    onChange={(e) => setFormData(prev => ({ \n                      ...prev, \n                      preferences: { ...prev.preferences, commentReplies: e.target.checked }\n                    }))}\n                    disabled={!isEditing}\n                    className=\"rounded border-gray-300 text-blue-600 focus:ring-blue-500 disabled:opacity-50\"\n                  />\n                  <span className=\"ml-2 text-sm text-gray-700\">Notify me when someone replies to my comments</span>\n                </label>\n\n                <label className=\"flex items-center\">\n                  <input\n                    type=\"checkbox\"\n                    checked={formData.preferences.marketingEmails}\n                    onChange={(e) => setFormData(prev => ({ \n                      ...prev, \n                      preferences: { ...prev.preferences, marketingEmails: e.target.checked }\n                    }))}\n                    disabled={!isEditing}\n                    className=\"rounded border-gray-300 text-blue-600 focus:ring-blue-500 disabled:opacity-50\"\n                  />\n                  <span className=\"ml-2 text-sm text-gray-700\">Marketing emails and newsletters</span>\n                </label>\n              </div>\n            </div>\n\n            {/* Action Buttons */}\n            <div className=\"flex items-center justify-between pt-6 border-t border-gray-200\">\n              <button\n                type=\"button\"\n                onClick={handleLogout}\n                className=\"px-4 py-2 text-red-600 hover:text-red-800 font-medium\"\n              >\n                Sign Out\n              </button>\n\n              <div className=\"flex items-center space-x-3\">\n                {isEditing ? (\n                  <>\n                    <button\n                      type=\"button\"\n                      onClick={() => {\n                        setIsEditing(false);\n                        setFormData({\n                          name: user.name || '',\n                          bio: user.bio || '',\n                          website: user.website || '',\n                          socialLinks: {\n                            twitter: user.socialLinks?.twitter || '',\n                            linkedin: user.socialLinks?.linkedin || '',\n                            github: user.socialLinks?.github || '',\n                          },\n                          preferences: {\n                            emailNotifications: user.preferences?.emailNotifications ?? true,\n                            marketingEmails: user.preferences?.marketingEmails ?? false,\n                            commentReplies: user.preferences?.commentReplies ?? true,\n                          },\n                        });\n                        setError('');\n                        setSuccess('');\n                      }}\n                      className=\"px-4 py-2 text-gray-600 hover:text-gray-800\"\n                    >\n                      Cancel\n                    </button>\n                    <button\n                      type=\"submit\"\n                      disabled={isLoading}\n                      className=\"px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50\"\n                    >\n                      {isLoading ? 'Saving...' : 'Save Changes'}\n                    </button>\n                  </>\n                ) : (\n                  <button\n                    type=\"button\"\n                    onClick={() => setIsEditing(true)}\n                    className=\"px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2\"\n                  >\n                    Edit Profile\n                  </button>\n                )}\n              </div>\n            </div>\n          </form>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AASO,SAAS,YAAY,EAAE,OAAO,EAAoB;IACvD,MAAM,EAAE,IAAI,EAAE,aAAa,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,yHAAA,CAAA,gBAAa,AAAD;IACpD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM,MAAM,QAAQ;QACpB,KAAK,MAAM,OAAO;QAClB,SAAS,MAAM,WAAW;QAC1B,aAAa;YACX,SAAS,MAAM,aAAa,WAAW;YACvC,UAAU,MAAM,aAAa,YAAY;YACzC,QAAQ,MAAM,aAAa,UAAU;QACvC;QACA,aAAa;YACX,oBAAoB,MAAM,aAAa,sBAAsB;YAC7D,iBAAiB,MAAM,aAAa,mBAAmB;YACvD,gBAAgB,MAAM,aAAa,kBAAkB;QACvD;IACF;IAEA,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,SAAS;QACT,WAAW;QACX,aAAa;QAEb,IAAI;YACF,MAAM,cAAc;YACpB,WAAW;YACX,aAAa;QACf,EAAE,OAAO,KAAU;YACjB,SAAS,IAAI,OAAO,IAAI;QAC1B,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,eAAe;QACnB;QACA;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAmC;;;;;;sCACjD,8OAAC;4BACC,SAAS;4BACT,WAAU;sCAEV,cAAA,8OAAC;gCAAI,WAAU;gCAAU,MAAK;gCAAO,QAAO;gCAAe,SAAQ;0CACjE,cAAA,8OAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;8BAK3E,8OAAC;oBAAI,WAAU;;wBAEZ,uBACC,8OAAC;4BAAI,WAAU;sCACZ;;;;;;wBAIJ,yBACC,8OAAC;4BAAI,WAAU;sCACZ;;;;;;sCAKL,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,KAAK,KAAK,MAAM;oCAChB,KAAK,KAAK,IAAI;oCACd,WAAU;;;;;;8CAEZ,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAuC,KAAK,IAAI;;;;;;sDAC9D,8OAAC;4CAAE,WAAU;sDAAiB,KAAK,KAAK;;;;;;sDACxC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAW,CAAC,iEAAiE,EACjF,KAAK,UAAU,GAAG,gCAAgC,iCAClD;8DACC,KAAK,UAAU,GAAG,aAAa;;;;;;gDAEjC,KAAK,KAAK,kBACT,8OAAC;oDAAK,WAAU;;wDACb,KAAK,KAAK,CAAC,aAAa;wDAAC;;;;;;;;;;;;;;;;;;;;;;;;;sCAQpC,8OAAC;4BAAK,UAAU;4BAAc,WAAU;;8CAEtC,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAyC;;;;;;sDACvD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEACC,8OAAC;4DAAM,WAAU;sEAA+C;;;;;;sEAGhE,8OAAC;4DACC,MAAK;4DACL,OAAO,SAAS,IAAI;4DACpB,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;wEAAE,GAAG,IAAI;wEAAE,MAAM,EAAE,MAAM,CAAC,KAAK;oEAAC,CAAC;4DACvE,UAAU,CAAC;4DACX,WAAU;4DACV,WAAW;;;;;;;;;;;;8DAIf,8OAAC;;sEACC,8OAAC;4DAAM,WAAU;sEAA+C;;;;;;sEAGhE,8OAAC;4DACC,OAAO,SAAS,GAAG;4DACnB,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;wEAAE,GAAG,IAAI;wEAAE,KAAK,EAAE,MAAM,CAAC,KAAK;oEAAC,CAAC;4DACtE,UAAU,CAAC;4DACX,MAAM;4DACN,WAAU;4DACV,aAAY;4DACZ,WAAW;;;;;;;;;;;;8DAIf,8OAAC;;sEACC,8OAAC;4DAAM,WAAU;sEAA+C;;;;;;sEAGhE,8OAAC;4DACC,MAAK;4DACL,OAAO,SAAS,OAAO;4DACvB,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;wEAAE,GAAG,IAAI;wEAAE,SAAS,EAAE,MAAM,CAAC,KAAK;oEAAC,CAAC;4DAC1E,UAAU,CAAC;4DACX,WAAU;4DACV,aAAY;;;;;;;;;;;;;;;;;;;;;;;;8CAOpB,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAyC;;;;;;sDACvD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEACC,8OAAC;4DAAM,WAAU;sEAA+C;;;;;;sEAGhE,8OAAC;4DACC,MAAK;4DACL,OAAO,SAAS,WAAW,CAAC,OAAO;4DACnC,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;wEACpC,GAAG,IAAI;wEACP,aAAa;4EAAE,GAAG,KAAK,WAAW;4EAAE,SAAS,EAAE,MAAM,CAAC,KAAK;wEAAC;oEAC9D,CAAC;4DACD,UAAU,CAAC;4DACX,WAAU;4DACV,aAAY;;;;;;;;;;;;8DAIhB,8OAAC;;sEACC,8OAAC;4DAAM,WAAU;sEAA+C;;;;;;sEAGhE,8OAAC;4DACC,MAAK;4DACL,OAAO,SAAS,WAAW,CAAC,QAAQ;4DACpC,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;wEACpC,GAAG,IAAI;wEACP,aAAa;4EAAE,GAAG,KAAK,WAAW;4EAAE,UAAU,EAAE,MAAM,CAAC,KAAK;wEAAC;oEAC/D,CAAC;4DACD,UAAU,CAAC;4DACX,WAAU;4DACV,aAAY;;;;;;;;;;;;8DAIhB,8OAAC;;sEACC,8OAAC;4DAAM,WAAU;sEAA+C;;;;;;sEAGhE,8OAAC;4DACC,MAAK;4DACL,OAAO,SAAS,WAAW,CAAC,MAAM;4DAClC,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;wEACpC,GAAG,IAAI;wEACP,aAAa;4EAAE,GAAG,KAAK,WAAW;4EAAE,QAAQ,EAAE,MAAM,CAAC,KAAK;wEAAC;oEAC7D,CAAC;4DACD,UAAU,CAAC;4DACX,WAAU;4DACV,aAAY;;;;;;;;;;;;;;;;;;;;;;;;8CAOpB,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAyC;;;;;;sDACvD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAM,WAAU;;sEACf,8OAAC;4DACC,MAAK;4DACL,SAAS,SAAS,WAAW,CAAC,kBAAkB;4DAChD,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;wEACpC,GAAG,IAAI;wEACP,aAAa;4EAAE,GAAG,KAAK,WAAW;4EAAE,oBAAoB,EAAE,MAAM,CAAC,OAAO;wEAAC;oEAC3E,CAAC;4DACD,UAAU,CAAC;4DACX,WAAU;;;;;;sEAEZ,8OAAC;4DAAK,WAAU;sEAA6B;;;;;;;;;;;;8DAG/C,8OAAC;oDAAM,WAAU;;sEACf,8OAAC;4DACC,MAAK;4DACL,SAAS,SAAS,WAAW,CAAC,cAAc;4DAC5C,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;wEACpC,GAAG,IAAI;wEACP,aAAa;4EAAE,GAAG,KAAK,WAAW;4EAAE,gBAAgB,EAAE,MAAM,CAAC,OAAO;wEAAC;oEACvE,CAAC;4DACD,UAAU,CAAC;4DACX,WAAU;;;;;;sEAEZ,8OAAC;4DAAK,WAAU;sEAA6B;;;;;;;;;;;;8DAG/C,8OAAC;oDAAM,WAAU;;sEACf,8OAAC;4DACC,MAAK;4DACL,SAAS,SAAS,WAAW,CAAC,eAAe;4DAC7C,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;wEACpC,GAAG,IAAI;wEACP,aAAa;4EAAE,GAAG,KAAK,WAAW;4EAAE,iBAAiB,EAAE,MAAM,CAAC,OAAO;wEAAC;oEACxE,CAAC;4DACD,UAAU,CAAC;4DACX,WAAU;;;;;;sEAEZ,8OAAC;4DAAK,WAAU;sEAA6B;;;;;;;;;;;;;;;;;;;;;;;;8CAMnD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,MAAK;4CACL,SAAS;4CACT,WAAU;sDACX;;;;;;sDAID,8OAAC;4CAAI,WAAU;sDACZ,0BACC;;kEACE,8OAAC;wDACC,MAAK;wDACL,SAAS;4DACP,aAAa;4DACb,YAAY;gEACV,MAAM,KAAK,IAAI,IAAI;gEACnB,KAAK,KAAK,GAAG,IAAI;gEACjB,SAAS,KAAK,OAAO,IAAI;gEACzB,aAAa;oEACX,SAAS,KAAK,WAAW,EAAE,WAAW;oEACtC,UAAU,KAAK,WAAW,EAAE,YAAY;oEACxC,QAAQ,KAAK,WAAW,EAAE,UAAU;gEACtC;gEACA,aAAa;oEACX,oBAAoB,KAAK,WAAW,EAAE,sBAAsB;oEAC5D,iBAAiB,KAAK,WAAW,EAAE,mBAAmB;oEACtD,gBAAgB,KAAK,WAAW,EAAE,kBAAkB;gEACtD;4DACF;4DACA,SAAS;4DACT,WAAW;wDACb;wDACA,WAAU;kEACX;;;;;;kEAGD,8OAAC;wDACC,MAAK;wDACL,UAAU;wDACV,WAAU;kEAET,YAAY,cAAc;;;;;;;6EAI/B,8OAAC;gDACC,MAAK;gDACL,SAAS,IAAM,aAAa;gDAC5B,WAAU;0DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWnB", "debugId": null}}, {"offset": {"line": 3540, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Development/BuzzEdge/buzzedge-frontend/src/components/BlogPostClient.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { BlogPost } from '@/types/blog';\nimport { CommentSection } from './CommentSection';\nimport { PublicAuthProvider, usePublicAuth } from '@/lib/publicAuth';\nimport { UserProfile } from './UserProfile';\n\ninterface BlogPostClientProps {\n  post: BlogPost;\n}\n\nfunction BlogPostClientContent({ post }: BlogPostClientProps) {\n  const [showProfile, setShowProfile] = useState(false);\n  const { user } = usePublicAuth();\n\n  return (\n    <>\n      {/* User Profile Button */}\n      {user && (\n        <div className=\"flex justify-end mb-6\">\n          <button\n            onClick={() => setShowProfile(true)}\n            className=\"flex items-center space-x-2 px-4 py-2 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500\"\n          >\n            <img\n              src={user.avatar}\n              alt={user.name}\n              className=\"w-6 h-6 rounded-full\"\n            />\n            <span className=\"text-sm font-medium text-gray-700\">{user.name}</span>\n            <svg className=\"w-4 h-4 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 9l-7 7-7-7\" />\n            </svg>\n          </button>\n        </div>\n      )}\n\n      {/* Social Share Buttons */}\n      <div className=\"flex items-center justify-between mb-8 p-4 bg-white rounded-lg border border-gray-200\">\n        <div className=\"flex items-center space-x-2\">\n          <span className=\"text-sm font-medium text-gray-700\">Share this article:</span>\n        </div>\n        <div className=\"flex items-center space-x-3\">\n          {/* Twitter Share */}\n          <a\n            href={`https://twitter.com/intent/tweet?text=${encodeURIComponent(post.title)}&url=${encodeURIComponent(typeof window !== 'undefined' ? window.location.href : '')}`}\n            target=\"_blank\"\n            rel=\"noopener noreferrer\"\n            className=\"flex items-center space-x-1 px-3 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 text-sm\"\n          >\n            <svg className=\"w-4 h-4\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path d=\"M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z\"/>\n            </svg>\n            <span>Tweet</span>\n          </a>\n\n          {/* LinkedIn Share */}\n          <a\n            href={`https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(typeof window !== 'undefined' ? window.location.href : '')}`}\n            target=\"_blank\"\n            rel=\"noopener noreferrer\"\n            className=\"flex items-center space-x-1 px-3 py-2 bg-blue-700 text-white rounded-md hover:bg-blue-800 text-sm\"\n          >\n            <svg className=\"w-4 h-4\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path d=\"M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z\"/>\n            </svg>\n            <span>Share</span>\n          </a>\n\n          {/* Copy Link */}\n          <button\n            onClick={() => {\n              if (typeof window !== 'undefined') {\n                navigator.clipboard.writeText(window.location.href);\n                alert('Link copied to clipboard!');\n              }\n            }}\n            className=\"flex items-center space-x-1 px-3 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 text-sm\"\n          >\n            <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z\" />\n            </svg>\n            <span>Copy</span>\n          </button>\n        </div>\n      </div>\n\n      {/* Newsletter Signup */}\n      <div className=\"bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg p-6 mb-8 text-white\">\n        <div className=\"text-center\">\n          <h3 className=\"text-xl font-bold mb-2\">Stay Updated with BuzzEdge</h3>\n          <p className=\"text-blue-100 mb-4\">\n            Get the latest tech tool reviews and productivity tips delivered to your inbox.\n          </p>\n          <div className=\"flex flex-col sm:flex-row gap-3 max-w-md mx-auto\">\n            <input\n              type=\"email\"\n              placeholder=\"Enter your email\"\n              className=\"flex-1 px-4 py-2 rounded-md text-gray-900 focus:outline-none focus:ring-2 focus:ring-white\"\n            />\n            <button className=\"px-6 py-2 bg-white text-blue-600 rounded-md hover:bg-gray-100 font-medium\">\n              Subscribe\n            </button>\n          </div>\n          <p className=\"text-xs text-blue-200 mt-2\">\n            No spam, unsubscribe at any time.\n          </p>\n        </div>\n      </div>\n\n      {/* Comments Section */}\n      <CommentSection \n        postId={post._id} \n        postTitle={post.title}\n      />\n\n      {/* User Profile Modal */}\n      {showProfile && (\n        <UserProfile onClose={() => setShowProfile(false)} />\n      )}\n    </>\n  );\n}\n\nexport function BlogPostClient({ post }: BlogPostClientProps) {\n  return (\n    <PublicAuthProvider>\n      <BlogPostClientContent post={post} />\n    </PublicAuthProvider>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AACA;AANA;;;;;;AAYA,SAAS,sBAAsB,EAAE,IAAI,EAAuB;IAC1D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,yHAAA,CAAA,gBAAa,AAAD;IAE7B,qBACE;;YAEG,sBACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBACC,SAAS,IAAM,eAAe;oBAC9B,WAAU;;sCAEV,8OAAC;4BACC,KAAK,KAAK,MAAM;4BAChB,KAAK,KAAK,IAAI;4BACd,WAAU;;;;;;sCAEZ,8OAAC;4BAAK,WAAU;sCAAqC,KAAK,IAAI;;;;;;sCAC9D,8OAAC;4BAAI,WAAU;4BAAwB,MAAK;4BAAO,QAAO;4BAAe,SAAQ;sCAC/E,cAAA,8OAAC;gCAAK,eAAc;gCAAQ,gBAAe;gCAAQ,aAAa;gCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;0BAO7E,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAK,WAAU;sCAAoC;;;;;;;;;;;kCAEtD,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCACC,MAAM,CAAC,sCAAsC,EAAE,mBAAmB,KAAK,KAAK,EAAE,KAAK,EAAE,mBAAmB,6EAAuD,KAAK;gCACpK,QAAO;gCACP,KAAI;gCACJ,WAAU;;kDAEV,8OAAC;wCAAI,WAAU;wCAAU,MAAK;wCAAe,SAAQ;kDACnD,cAAA,8OAAC;4CAAK,GAAE;;;;;;;;;;;kDAEV,8OAAC;kDAAK;;;;;;;;;;;;0CAIR,8OAAC;gCACC,MAAM,CAAC,oDAAoD,EAAE,mBAAmB,6EAAuD,KAAK;gCAC5I,QAAO;gCACP,KAAI;gCACJ,WAAU;;kDAEV,8OAAC;wCAAI,WAAU;wCAAU,MAAK;wCAAe,SAAQ;kDACnD,cAAA,8OAAC;4CAAK,GAAE;;;;;;;;;;;kDAEV,8OAAC;kDAAK;;;;;;;;;;;;0CAIR,8OAAC;gCACC,SAAS;oCACP,uCAAmC;;oCAGnC;gCACF;gCACA,WAAU;;kDAEV,8OAAC;wCAAI,WAAU;wCAAU,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDACjE,cAAA,8OAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;kDAEvE,8OAAC;kDAAK;;;;;;;;;;;;;;;;;;;;;;;;0BAMZ,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAyB;;;;;;sCACvC,8OAAC;4BAAE,WAAU;sCAAqB;;;;;;sCAGlC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,MAAK;oCACL,aAAY;oCACZ,WAAU;;;;;;8CAEZ,8OAAC;oCAAO,WAAU;8CAA4E;;;;;;;;;;;;sCAIhG,8OAAC;4BAAE,WAAU;sCAA6B;;;;;;;;;;;;;;;;;0BAO9C,8OAAC,oIAAA,CAAA,iBAAc;gBACb,QAAQ,KAAK,GAAG;gBAChB,WAAW,KAAK,KAAK;;;;;;YAItB,6BACC,8OAAC,iIAAA,CAAA,cAAW;gBAAC,SAAS,IAAM,eAAe;;;;;;;;AAInD;AAEO,SAAS,eAAe,EAAE,IAAI,EAAuB;IAC1D,qBACE,8OAAC,yHAAA,CAAA,qBAAkB;kBACjB,cAAA,8OAAC;YAAsB,MAAM;;;;;;;;;;;AAGnC", "debugId": null}}]}