{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Development/BuzzEdge/buzzedge-frontend/src/lib/api.ts"], "sourcesContent": ["// API Configuration\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || \"http://localhost:5001\";\n\nexport class ApiError extends Error {\n  constructor(message: string, public status: number, public response?: any) {\n    super(message);\n    this.name = \"ApiError\";\n  }\n}\n\nexport interface ApiResponse<T> {\n  success: boolean;\n  data?: T;\n  error?: string;\n  message?: string;\n}\n\nclass ApiClient {\n  private baseURL: string;\n  private cache: Map<string, { data: any; timestamp: number; ttl: number }>;\n\n  constructor(baseURL: string) {\n    this.baseURL = baseURL;\n    this.cache = new Map();\n  }\n\n  private getCacheKey(\n    endpoint: string,\n    params?: Record<string, string>\n  ): string {\n    const url = new URL(endpoint, this.baseURL);\n    if (params) {\n      Object.entries(params).forEach(([key, value]) => {\n        url.searchParams.append(key, value);\n      });\n    }\n    return url.pathname + url.search;\n  }\n\n  private isValidCache(cacheEntry: {\n    timestamp: number;\n    ttl: number;\n  }): boolean {\n    return Date.now() - cacheEntry.timestamp < cacheEntry.ttl * 1000;\n  }\n\n  private setCache(key: string, data: any, ttl: number = 300): void {\n    this.cache.set(key, {\n      data,\n      timestamp: Date.now(),\n      ttl,\n    });\n  }\n\n  private getCache(key: string): any | null {\n    const entry = this.cache.get(key);\n    if (entry && this.isValidCache(entry)) {\n      return entry.data;\n    }\n    if (entry) {\n      this.cache.delete(key); // Remove expired cache\n    }\n    return null;\n  }\n\n  private async request<T>(\n    endpoint: string,\n    options: RequestInit = {}\n  ): Promise<T> {\n    const url = `${this.baseURL}${endpoint}`;\n\n    // Get auth token if available\n    const getAuthToken = () => {\n      if (typeof window !== \"undefined\") {\n        return localStorage.getItem(\"buzzedge_auth_token\");\n      }\n      return null;\n    };\n\n    const token = getAuthToken();\n    const authHeaders: Record<string, string> = token\n      ? { Authorization: `Bearer ${token}` }\n      : {};\n\n    const config: RequestInit = {\n      headers: {\n        \"Content-Type\": \"application/json\",\n        ...authHeaders,\n        ...options.headers,\n      },\n      ...options,\n    };\n\n    try {\n      const response = await fetch(url, config);\n\n      if (!response.ok) {\n        const errorData = await response.json().catch(() => ({}));\n        throw new ApiError(\n          errorData.error || `HTTP ${response.status}: ${response.statusText}`,\n          response.status,\n          errorData\n        );\n      }\n\n      const data = await response.json();\n      return data;\n    } catch (error) {\n      if (error instanceof ApiError) {\n        throw error;\n      }\n\n      // Network or other errors\n      throw new ApiError(\n        error instanceof Error ? error.message : \"Network error occurred\",\n        0\n      );\n    }\n  }\n\n  async get<T>(\n    endpoint: string,\n    params?: Record<string, string>,\n    options: { cache?: boolean; ttl?: number } = {}\n  ): Promise<T> {\n    const { cache = true, ttl = 300 } = options;\n    const cacheKey = this.getCacheKey(endpoint, params);\n\n    // Check cache first\n    if (cache) {\n      const cachedData = this.getCache(cacheKey);\n      if (cachedData) {\n        return cachedData;\n      }\n    }\n\n    const url = new URL(endpoint, this.baseURL);\n    if (params) {\n      Object.entries(params).forEach(([key, value]) => {\n        url.searchParams.append(key, value);\n      });\n    }\n\n    const data = await this.request<T>(url.pathname + url.search);\n\n    // Cache the response\n    if (cache) {\n      this.setCache(cacheKey, data, ttl);\n    }\n\n    return data;\n  }\n\n  async post<T>(endpoint: string, data?: any): Promise<T> {\n    return this.request<T>(endpoint, {\n      method: \"POST\",\n      body: data ? JSON.stringify(data) : undefined,\n    });\n  }\n\n  async put<T>(endpoint: string, data?: any): Promise<T> {\n    return this.request<T>(endpoint, {\n      method: \"PUT\",\n      body: data ? JSON.stringify(data) : undefined,\n    });\n  }\n\n  async delete<T>(endpoint: string): Promise<T> {\n    return this.request<T>(endpoint, {\n      method: \"DELETE\",\n    });\n  }\n\n  // Cache management methods\n  clearCache(): void {\n    this.cache.clear();\n  }\n\n  clearCacheByPattern(pattern: string): void {\n    const regex = new RegExp(pattern);\n    for (const [key] of this.cache) {\n      if (regex.test(key)) {\n        this.cache.delete(key);\n      }\n    }\n  }\n\n  getCacheStats(): { size: number; keys: string[] } {\n    return {\n      size: this.cache.size,\n      keys: Array.from(this.cache.keys()),\n    };\n  }\n}\n\n// Create and export the API client instance\nexport const apiClient = new ApiClient(API_BASE_URL);\n\n// Health check function\nexport const checkApiHealth = async (): Promise<boolean> => {\n  try {\n    await apiClient.get(\"/health\");\n    return true;\n  } catch (error) {\n    console.error(\"API health check failed:\", error);\n    return false;\n  }\n};\n"], "names": [], "mappings": "AAAA,oBAAoB;;;;;;AACpB,MAAM,eAAe,6DAAmC;AAEjD,MAAM,iBAAiB;;;IAC5B,YAAY,OAAe,EAAE,AAAO,MAAc,EAAE,AAAO,QAAc,CAAE;QACzE,KAAK,CAAC,eAD4B,SAAA,aAAuB,WAAA;QAEzD,IAAI,CAAC,IAAI,GAAG;IACd;AACF;AASA,MAAM;IACI,QAAgB;IAChB,MAAkE;IAE1E,YAAY,OAAe,CAAE;QAC3B,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,KAAK,GAAG,IAAI;IACnB;IAEQ,YACN,QAAgB,EAChB,MAA+B,EACvB;QACR,MAAM,MAAM,IAAI,IAAI,UAAU,IAAI,CAAC,OAAO;QAC1C,IAAI,QAAQ;YACV,OAAO,OAAO,CAAC,QAAQ,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;gBAC1C,IAAI,YAAY,CAAC,MAAM,CAAC,KAAK;YAC/B;QACF;QACA,OAAO,IAAI,QAAQ,GAAG,IAAI,MAAM;IAClC;IAEQ,aAAa,UAGpB,EAAW;QACV,OAAO,KAAK,GAAG,KAAK,WAAW,SAAS,GAAG,WAAW,GAAG,GAAG;IAC9D;IAEQ,SAAS,GAAW,EAAE,IAAS,EAAE,MAAc,GAAG,EAAQ;QAChE,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK;YAClB;YACA,WAAW,KAAK,GAAG;YACnB;QACF;IACF;IAEQ,SAAS,GAAW,EAAc;QACxC,MAAM,QAAQ,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;QAC7B,IAAI,SAAS,IAAI,CAAC,YAAY,CAAC,QAAQ;YACrC,OAAO,MAAM,IAAI;QACnB;QACA,IAAI,OAAO;YACT,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,uBAAuB;QACjD;QACA,OAAO;IACT;IAEA,MAAc,QACZ,QAAgB,EAChB,UAAuB,CAAC,CAAC,EACb;QACZ,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,GAAG,UAAU;QAExC,8BAA8B;QAC9B,MAAM,eAAe;YACnB,uCAAmC;;YAEnC;YACA,OAAO;QACT;QAEA,MAAM,QAAQ;QACd,MAAM,cAAsC,QACxC;YAAE,eAAe,CAAC,OAAO,EAAE,OAAO;QAAC,IACnC,CAAC;QAEL,MAAM,SAAsB;YAC1B,SAAS;gBACP,gBAAgB;gBAChB,GAAG,WAAW;gBACd,GAAG,QAAQ,OAAO;YACpB;YACA,GAAG,OAAO;QACZ;QAEA,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,KAAK;YAElC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC,CAAC,CAAC;gBACvD,MAAM,IAAI,SACR,UAAU,KAAK,IAAI,CAAC,KAAK,EAAE,SAAS,MAAM,CAAC,EAAE,EAAE,SAAS,UAAU,EAAE,EACpE,SAAS,MAAM,EACf;YAEJ;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,OAAO;QACT,EAAE,OAAO,OAAO;YACd,IAAI,iBAAiB,UAAU;gBAC7B,MAAM;YACR;YAEA,0BAA0B;YAC1B,MAAM,IAAI,SACR,iBAAiB,QAAQ,MAAM,OAAO,GAAG,0BACzC;QAEJ;IACF;IAEA,MAAM,IACJ,QAAgB,EAChB,MAA+B,EAC/B,UAA6C,CAAC,CAAC,EACnC;QACZ,MAAM,EAAE,QAAQ,IAAI,EAAE,MAAM,GAAG,EAAE,GAAG;QACpC,MAAM,WAAW,IAAI,CAAC,WAAW,CAAC,UAAU;QAE5C,oBAAoB;QACpB,IAAI,OAAO;YACT,MAAM,aAAa,IAAI,CAAC,QAAQ,CAAC;YACjC,IAAI,YAAY;gBACd,OAAO;YACT;QACF;QAEA,MAAM,MAAM,IAAI,IAAI,UAAU,IAAI,CAAC,OAAO;QAC1C,IAAI,QAAQ;YACV,OAAO,OAAO,CAAC,QAAQ,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;gBAC1C,IAAI,YAAY,CAAC,MAAM,CAAC,KAAK;YAC/B;QACF;QAEA,MAAM,OAAO,MAAM,IAAI,CAAC,OAAO,CAAI,IAAI,QAAQ,GAAG,IAAI,MAAM;QAE5D,qBAAqB;QACrB,IAAI,OAAO;YACT,IAAI,CAAC,QAAQ,CAAC,UAAU,MAAM;QAChC;QAEA,OAAO;IACT;IAEA,MAAM,KAAQ,QAAgB,EAAE,IAAU,EAAc;QACtD,OAAO,IAAI,CAAC,OAAO,CAAI,UAAU;YAC/B,QAAQ;YACR,MAAM,OAAO,KAAK,SAAS,CAAC,QAAQ;QACtC;IACF;IAEA,MAAM,IAAO,QAAgB,EAAE,IAAU,EAAc;QACrD,OAAO,IAAI,CAAC,OAAO,CAAI,UAAU;YAC/B,QAAQ;YACR,MAAM,OAAO,KAAK,SAAS,CAAC,QAAQ;QACtC;IACF;IAEA,MAAM,OAAU,QAAgB,EAAc;QAC5C,OAAO,IAAI,CAAC,OAAO,CAAI,UAAU;YAC/B,QAAQ;QACV;IACF;IAEA,2BAA2B;IAC3B,aAAmB;QACjB,IAAI,CAAC,KAAK,CAAC,KAAK;IAClB;IAEA,oBAAoB,OAAe,EAAQ;QACzC,MAAM,QAAQ,IAAI,OAAO;QACzB,KAAK,MAAM,CAAC,IAAI,IAAI,IAAI,CAAC,KAAK,CAAE;YAC9B,IAAI,MAAM,IAAI,CAAC,MAAM;gBACnB,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;YACpB;QACF;IACF;IAEA,gBAAkD;QAChD,OAAO;YACL,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI;YACrB,MAAM,MAAM,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI;QAClC;IACF;AACF;AAGO,MAAM,YAAY,IAAI,UAAU;AAGhC,MAAM,iBAAiB;IAC5B,IAAI;QACF,MAAM,UAAU,GAAG,CAAC;QACpB,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 178, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Development/BuzzEdge/buzzedge-frontend/src/components/PerformanceMonitor.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useEffect, useState } from \"react\";\nimport { apiClient } from \"@/lib/api\";\n\ninterface PerformanceMetrics {\n  pageLoadTime: number;\n  apiResponseTime: number;\n  cacheHitRate: number;\n  totalRequests: number;\n  cachedRequests: number;\n}\n\ninterface PerformanceMonitorProps {\n  enabled?: boolean;\n  showStats?: boolean;\n}\n\nexport function PerformanceMonitor({\n  enabled = process.env.NODE_ENV === \"development\",\n  showStats = false,\n}: PerformanceMonitorProps) {\n  const [metrics, setMetrics] = useState<PerformanceMetrics>({\n    pageLoadTime: 0,\n    apiResponseTime: 0,\n    cacheHitRate: 0,\n    totalRequests: 0,\n    cachedRequests: 0,\n  });\n  const [isVisible, setIsVisible] = useState(false);\n\n  useEffect(() => {\n    if (!enabled) return;\n\n    // Measure page load time\n    const measurePageLoad = () => {\n      if (typeof window !== \"undefined\" && window.performance) {\n        const navigation = performance.getEntriesByType(\n          \"navigation\"\n        )[0] as PerformanceNavigationTiming;\n        if (navigation) {\n          const loadTime = navigation.loadEventEnd - navigation.fetchStart;\n          setMetrics((prev) => ({ ...prev, pageLoadTime: loadTime }));\n        }\n      }\n    };\n\n    // Measure API performance\n    const measureApiPerformance = () => {\n      const cacheStats = apiClient.getCacheStats();\n      setMetrics((prev) => ({\n        ...prev,\n        totalRequests: cacheStats.size,\n        cachedRequests: cacheStats.size,\n        cacheHitRate:\n          cacheStats.size > 0\n            ? (cacheStats.size / (cacheStats.size + 1)) * 100\n            : 0,\n      }));\n    };\n\n    // Initial measurements\n    setTimeout(measurePageLoad, 1000);\n    measureApiPerformance();\n\n    // Update metrics periodically\n    const interval = setInterval(measureApiPerformance, 5000);\n\n    return () => clearInterval(interval);\n  }, [enabled]);\n\n  if (!enabled || !showStats) return null;\n\n  return (\n    <>\n      {/* Performance Stats Toggle */}\n      <button\n        onClick={() => setIsVisible(!isVisible)}\n        className=\"fixed bottom-4 right-4 z-50 bg-gray-800 text-white p-2 rounded-full shadow-lg hover:bg-gray-700 transition-colors\"\n        title=\"Performance Stats\"\n      >\n        📊\n      </button>\n\n      {/* Performance Stats Panel */}\n      {isVisible && (\n        <div className=\"fixed bottom-16 right-4 z-50 bg-white border border-gray-200 rounded-lg shadow-lg p-4 w-80\">\n          <div className=\"flex items-center justify-between mb-3\">\n            <h3 className=\"font-semibold text-gray-900\">Performance Stats</h3>\n            <button\n              onClick={() => setIsVisible(false)}\n              className=\"text-gray-400 hover:text-gray-600\"\n            >\n              ✕\n            </button>\n          </div>\n\n          <div className=\"space-y-3 text-sm\">\n            <div className=\"flex justify-between\">\n              <span className=\"text-gray-600\">Page Load Time:</span>\n              <span className=\"font-mono\">\n                {metrics.pageLoadTime > 0\n                  ? `${Math.round(metrics.pageLoadTime)}ms`\n                  : \"Measuring...\"}\n              </span>\n            </div>\n\n            <div className=\"flex justify-between\">\n              <span className=\"text-gray-600\">Cache Hit Rate:</span>\n              <span className=\"font-mono\">\n                {metrics.cacheHitRate.toFixed(1)}%\n              </span>\n            </div>\n\n            <div className=\"flex justify-between\">\n              <span className=\"text-gray-600\">Cached Requests:</span>\n              <span className=\"font-mono\">{metrics.cachedRequests}</span>\n            </div>\n\n            <div className=\"border-t pt-2\">\n              <button\n                onClick={() => {\n                  apiClient.clearCache();\n                  setMetrics((prev) => ({\n                    ...prev,\n                    cachedRequests: 0,\n                    cacheHitRate: 0,\n                  }));\n                }}\n                className=\"w-full text-xs bg-red-50 text-red-600 py-1 px-2 rounded hover:bg-red-100 transition-colors\"\n              >\n                Clear Cache\n              </button>\n            </div>\n          </div>\n        </div>\n      )}\n    </>\n  );\n}\n\n// Web Vitals monitoring\nexport function WebVitalsMonitor() {\n  useEffect(() => {\n    if (typeof window === \"undefined\") return;\n\n    // Monitor Core Web Vitals\n    const observer = new PerformanceObserver((list) => {\n      for (const entry of list.getEntries()) {\n        const metric = entry.name;\n        const value =\n          (entry as any).value ||\n          (entry as any).processingStart ||\n          entry.duration;\n\n        // Log metrics in development\n        if (process.env.NODE_ENV === \"development\") {\n          console.log(`${metric}:`, value);\n        }\n\n        // Send to analytics in production\n        if (process.env.NODE_ENV === \"production\") {\n          // Example: Send to Google Analytics or other analytics service\n          // gtag('event', metric, { value: Math.round(value) });\n        }\n      }\n    });\n\n    // Observe different metric types\n    try {\n      observer.observe({ entryTypes: [\"measure\", \"navigation\", \"paint\"] });\n    } catch (error) {\n      console.warn(\"Performance Observer not supported:\", error);\n    }\n\n    return () => observer.disconnect();\n  }, []);\n\n  return null;\n}\n\n// Image loading optimization\nexport function ImageLoadMonitor() {\n  useEffect(() => {\n    if (typeof window === \"undefined\") return;\n\n    const images = document.querySelectorAll(\"img[data-src]\");\n\n    const imageObserver = new IntersectionObserver((entries) => {\n      entries.forEach((entry) => {\n        if (entry.isIntersecting) {\n          const img = entry.target as HTMLImageElement;\n          const src = img.getAttribute(\"data-src\");\n          if (src) {\n            img.src = src;\n            img.removeAttribute(\"data-src\");\n            imageObserver.unobserve(img);\n          }\n        }\n      });\n    });\n\n    images.forEach((img) => imageObserver.observe(img));\n\n    return () => imageObserver.disconnect();\n  }, []);\n\n  return null;\n}\n\n// Performance utilities\nexport const performanceUtils = {\n  // Measure function execution time\n  measureTime: async function <T>(\n    fn: () => Promise<T>,\n    label: string\n  ): Promise<T> {\n    const start = performance.now();\n    const result = await fn();\n    const end = performance.now();\n\n    if (process.env.NODE_ENV === \"development\") {\n      console.log(`${label}: ${Math.round(end - start)}ms`);\n    }\n\n    return result;\n  },\n\n  // Debounce function for performance\n  debounce: function <T extends (...args: any[]) => any>(\n    func: T,\n    wait: number\n  ): (...args: Parameters<T>) => void {\n    let timeout: NodeJS.Timeout;\n    return (...args: Parameters<T>) => {\n      clearTimeout(timeout);\n      timeout = setTimeout(() => func(...args), wait);\n    };\n  },\n\n  // Throttle function for performance\n  throttle: function <T extends (...args: any[]) => any>(\n    func: T,\n    limit: number\n  ): (...args: Parameters<T>) => void {\n    let inThrottle: boolean;\n    return (...args: Parameters<T>) => {\n      if (!inThrottle) {\n        func(...args);\n        inThrottle = true;\n        setTimeout(() => (inThrottle = false), limit);\n      }\n    };\n  },\n};\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AAHA;;;;AAkBO,SAAS,mBAAmB,EACjC,UAAU,oDAAyB,aAAa,EAChD,YAAY,KAAK,EACO;IACxB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsB;QACzD,cAAc;QACd,iBAAiB;QACjB,cAAc;QACd,eAAe;QACf,gBAAgB;IAClB;IACA,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,SAAS;QAEd,yBAAyB;QACzB,MAAM,kBAAkB;YACtB,uCAAyD;;YAQzD;QACF;QAEA,0BAA0B;QAC1B,MAAM,wBAAwB;YAC5B,MAAM,aAAa,iHAAA,CAAA,YAAS,CAAC,aAAa;YAC1C,WAAW,CAAC,OAAS,CAAC;oBACpB,GAAG,IAAI;oBACP,eAAe,WAAW,IAAI;oBAC9B,gBAAgB,WAAW,IAAI;oBAC/B,cACE,WAAW,IAAI,GAAG,IACd,AAAC,WAAW,IAAI,GAAG,CAAC,WAAW,IAAI,GAAG,CAAC,IAAK,MAC5C;gBACR,CAAC;QACH;QAEA,uBAAuB;QACvB,WAAW,iBAAiB;QAC5B;QAEA,8BAA8B;QAC9B,MAAM,WAAW,YAAY,uBAAuB;QAEpD,OAAO,IAAM,cAAc;IAC7B,GAAG;QAAC;KAAQ;IAEZ,IAAI,CAAC,WAAW,CAAC,WAAW,OAAO;IAEnC,qBACE;;0BAEE,8OAAC;gBACC,SAAS,IAAM,aAAa,CAAC;gBAC7B,WAAU;gBACV,OAAM;0BACP;;;;;;YAKA,2BACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA8B;;;;;;0CAC5C,8OAAC;gCACC,SAAS,IAAM,aAAa;gCAC5B,WAAU;0CACX;;;;;;;;;;;;kCAKH,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAAgB;;;;;;kDAChC,8OAAC;wCAAK,WAAU;kDACb,QAAQ,YAAY,GAAG,IACpB,GAAG,KAAK,KAAK,CAAC,QAAQ,YAAY,EAAE,EAAE,CAAC,GACvC;;;;;;;;;;;;0CAIR,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAAgB;;;;;;kDAChC,8OAAC;wCAAK,WAAU;;4CACb,QAAQ,YAAY,CAAC,OAAO,CAAC;4CAAG;;;;;;;;;;;;;0CAIrC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAAgB;;;;;;kDAChC,8OAAC;wCAAK,WAAU;kDAAa,QAAQ,cAAc;;;;;;;;;;;;0CAGrD,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCACC,SAAS;wCACP,iHAAA,CAAA,YAAS,CAAC,UAAU;wCACpB,WAAW,CAAC,OAAS,CAAC;gDACpB,GAAG,IAAI;gDACP,gBAAgB;gDAChB,cAAc;4CAChB,CAAC;oCACH;oCACA,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;AASf;AAGO,SAAS;IACd,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,wCAAmC;;QAEnC,0BAA0B;QAC1B,MAAM;IA6BR,GAAG,EAAE;IAEL,OAAO;AACT;AAGO,SAAS;IACd,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,wCAAmC;;QAEnC,MAAM;QAEN,MAAM;IAiBR,GAAG,EAAE;IAEL,OAAO;AACT;AAGO,MAAM,mBAAmB;IAC9B,kCAAkC;IAClC,aAAa,eACX,EAAoB,EACpB,KAAa;QAEb,MAAM,QAAQ,YAAY,GAAG;QAC7B,MAAM,SAAS,MAAM;QACrB,MAAM,MAAM,YAAY,GAAG;QAE3B,wCAA4C;YAC1C,QAAQ,GAAG,CAAC,GAAG,MAAM,EAAE,EAAE,KAAK,KAAK,CAAC,MAAM,OAAO,EAAE,CAAC;QACtD;QAEA,OAAO;IACT;IAEA,oCAAoC;IACpC,UAAU,SACR,IAAO,EACP,IAAY;QAEZ,IAAI;QACJ,OAAO,CAAC,GAAG;YACT,aAAa;YACb,UAAU,WAAW,IAAM,QAAQ,OAAO;QAC5C;IACF;IAEA,oCAAoC;IACpC,UAAU,SACR,IAAO,EACP,KAAa;QAEb,IAAI;QACJ,OAAO,CAAC,GAAG;YACT,IAAI,CAAC,YAAY;gBACf,QAAQ;gBACR,aAAa;gBACb,WAAW,IAAO,aAAa,OAAQ;YACzC;QACF;IACF;AACF", "debugId": null}}, {"offset": {"line": 443, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Development/BuzzEdge/buzzedge-frontend/src/lib/analytics.tsx"], "sourcesContent": ["'use client';\n\nimport { createContext, useContext, useEffect, ReactNode } from 'react';\nimport { Analytics } from '@vercel/analytics/react';\nimport ReactGA from 'react-ga4';\n\ninterface AnalyticsContextType {\n  trackEvent: (eventName: string, parameters?: Record<string, any>) => void;\n  trackPageView: (path: string, title?: string) => void;\n  trackUserAction: (action: string, category: string, label?: string) => void;\n  trackContentInteraction: (contentType: string, contentId: string, action: string) => void;\n}\n\nconst AnalyticsContext = createContext<AnalyticsContextType | undefined>(undefined);\n\ninterface AnalyticsProviderProps {\n  children: ReactNode;\n  gaId?: string;\n  enabled?: boolean;\n}\n\nexport function AnalyticsProvider({ \n  children, \n  gaId = process.env.NEXT_PUBLIC_GA_ID,\n  enabled = process.env.NODE_ENV === 'production'\n}: AnalyticsProviderProps) {\n  \n  useEffect(() => {\n    if (enabled && gaId) {\n      // Initialize Google Analytics\n      ReactGA.initialize(gaId);\n      \n      // Track initial page view\n      ReactGA.send({ hitType: 'pageview', page: window.location.pathname });\n    }\n  }, [enabled, gaId]);\n\n  const trackEvent = (eventName: string, parameters?: Record<string, any>) => {\n    if (!enabled) {\n      console.log('Analytics Event:', eventName, parameters);\n      return;\n    }\n\n    // Google Analytics 4\n    if (gaId) {\n      ReactGA.event(eventName, parameters);\n    }\n\n    // Custom analytics (could be sent to your own API)\n    if (typeof window !== 'undefined') {\n      // Store analytics data locally for admin dashboard\n      const analyticsData = JSON.parse(localStorage.getItem('buzzedge_analytics') || '[]');\n      analyticsData.push({\n        event: eventName,\n        parameters,\n        timestamp: new Date().toISOString(),\n        url: window.location.pathname,\n      });\n      \n      // Keep only last 1000 events\n      if (analyticsData.length > 1000) {\n        analyticsData.splice(0, analyticsData.length - 1000);\n      }\n      \n      localStorage.setItem('buzzedge_analytics', JSON.stringify(analyticsData));\n    }\n  };\n\n  const trackPageView = (path: string, title?: string) => {\n    if (!enabled) {\n      console.log('Page View:', path, title);\n      return;\n    }\n\n    if (gaId) {\n      ReactGA.send({ \n        hitType: 'pageview', \n        page: path,\n        title: title || document.title\n      });\n    }\n\n    trackEvent('page_view', {\n      page_path: path,\n      page_title: title || document.title,\n    });\n  };\n\n  const trackUserAction = (action: string, category: string, label?: string) => {\n    trackEvent('user_action', {\n      action,\n      category,\n      label,\n    });\n  };\n\n  const trackContentInteraction = (contentType: string, contentId: string, action: string) => {\n    trackEvent('content_interaction', {\n      content_type: contentType,\n      content_id: contentId,\n      action,\n    });\n  };\n\n  const value = {\n    trackEvent,\n    trackPageView,\n    trackUserAction,\n    trackContentInteraction,\n  };\n\n  return (\n    <AnalyticsContext.Provider value={value}>\n      {children}\n      {enabled && <Analytics />}\n    </AnalyticsContext.Provider>\n  );\n}\n\nexport function useAnalytics() {\n  const context = useContext(AnalyticsContext);\n  if (context === undefined) {\n    throw new Error('useAnalytics must be used within an AnalyticsProvider');\n  }\n  return context;\n}\n\n// Analytics hooks for common use cases\nexport function usePageTracking() {\n  const { trackPageView } = useAnalytics();\n  \n  useEffect(() => {\n    trackPageView(window.location.pathname);\n  }, [trackPageView]);\n}\n\nexport function useContentTracking(contentType: string, contentId: string) {\n  const { trackContentInteraction } = useAnalytics();\n  \n  const trackView = () => trackContentInteraction(contentType, contentId, 'view');\n  const trackShare = () => trackContentInteraction(contentType, contentId, 'share');\n  const trackLike = () => trackContentInteraction(contentType, contentId, 'like');\n  const trackComment = () => trackContentInteraction(contentType, contentId, 'comment');\n  \n  useEffect(() => {\n    trackView();\n  }, [contentType, contentId]);\n  \n  return {\n    trackView,\n    trackShare,\n    trackLike,\n    trackComment,\n  };\n}\n\n// Analytics utilities\nexport const analyticsUtils = {\n  // Get analytics data from localStorage\n  getLocalAnalytics: (): any[] => {\n    if (typeof window === 'undefined') return [];\n    return JSON.parse(localStorage.getItem('buzzedge_analytics') || '[]');\n  },\n\n  // Get page views for a specific path\n  getPageViews: (path: string): number => {\n    const data = analyticsUtils.getLocalAnalytics();\n    return data.filter(event => \n      event.event === 'page_view' && event.parameters?.page_path === path\n    ).length;\n  },\n\n  // Get most popular pages\n  getPopularPages: (limit: number = 10): Array<{ path: string; views: number }> => {\n    const data = analyticsUtils.getLocalAnalytics();\n    const pageViews = data.filter(event => event.event === 'page_view');\n    \n    const pathCounts: Record<string, number> = {};\n    pageViews.forEach(event => {\n      const path = event.parameters?.page_path || event.url;\n      pathCounts[path] = (pathCounts[path] || 0) + 1;\n    });\n\n    return Object.entries(pathCounts)\n      .map(([path, views]) => ({ path, views }))\n      .sort((a, b) => b.views - a.views)\n      .slice(0, limit);\n  },\n\n  // Get analytics summary\n  getSummary: () => {\n    const data = analyticsUtils.getLocalAnalytics();\n    const now = new Date();\n    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());\n    const thisWeek = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);\n    const thisMonth = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);\n\n    const todayEvents = data.filter(event => new Date(event.timestamp) >= today);\n    const weekEvents = data.filter(event => new Date(event.timestamp) >= thisWeek);\n    const monthEvents = data.filter(event => new Date(event.timestamp) >= thisMonth);\n\n    return {\n      total: {\n        events: data.length,\n        pageViews: data.filter(e => e.event === 'page_view').length,\n        userActions: data.filter(e => e.event === 'user_action').length,\n        contentInteractions: data.filter(e => e.event === 'content_interaction').length,\n      },\n      today: {\n        events: todayEvents.length,\n        pageViews: todayEvents.filter(e => e.event === 'page_view').length,\n        userActions: todayEvents.filter(e => e.event === 'user_action').length,\n      },\n      week: {\n        events: weekEvents.length,\n        pageViews: weekEvents.filter(e => e.event === 'page_view').length,\n        userActions: weekEvents.filter(e => e.event === 'user_action').length,\n      },\n      month: {\n        events: monthEvents.length,\n        pageViews: monthEvents.filter(e => e.event === 'page_view').length,\n        userActions: monthEvents.filter(e => e.event === 'user_action').length,\n      },\n    };\n  },\n\n  // Clear analytics data\n  clearData: () => {\n    if (typeof window !== 'undefined') {\n      localStorage.removeItem('buzzedge_analytics');\n    }\n  },\n};\n"], "names": [], "mappings": ";;;;;;;;AAEA;AACA;AACA;AAJA;;;;;AAaA,MAAM,iCAAmB,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAoC;AAQlE,SAAS,kBAAkB,EAChC,QAAQ,EACR,OAAO,QAAQ,GAAG,CAAC,iBAAiB,EACpC,UAAU,oDAAyB,YAAY,EACxB;IAEvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,WAAW,MAAM;YACnB,8BAA8B;YAC9B,6IAAA,CAAA,UAAO,CAAC,UAAU,CAAC;YAEnB,0BAA0B;YAC1B,6IAAA,CAAA,UAAO,CAAC,IAAI,CAAC;gBAAE,SAAS;gBAAY,MAAM,OAAO,QAAQ,CAAC,QAAQ;YAAC;QACrE;IACF,GAAG;QAAC;QAAS;KAAK;IAElB,MAAM,aAAa,CAAC,WAAmB;QACrC,IAAI,CAAC,SAAS;YACZ,QAAQ,GAAG,CAAC,oBAAoB,WAAW;YAC3C;QACF;QAEA,qBAAqB;QACrB,IAAI,MAAM;YACR,6IAAA,CAAA,UAAO,CAAC,KAAK,CAAC,WAAW;QAC3B;QAEA,mDAAmD;QACnD,uCAAmC;;QAgBnC;IACF;IAEA,MAAM,gBAAgB,CAAC,MAAc;QACnC,IAAI,CAAC,SAAS;YACZ,QAAQ,GAAG,CAAC,cAAc,MAAM;YAChC;QACF;QAEA,IAAI,MAAM;YACR,6IAAA,CAAA,UAAO,CAAC,IAAI,CAAC;gBACX,SAAS;gBACT,MAAM;gBACN,OAAO,SAAS,SAAS,KAAK;YAChC;QACF;QAEA,WAAW,aAAa;YACtB,WAAW;YACX,YAAY,SAAS,SAAS,KAAK;QACrC;IACF;IAEA,MAAM,kBAAkB,CAAC,QAAgB,UAAkB;QACzD,WAAW,eAAe;YACxB;YACA;YACA;QACF;IACF;IAEA,MAAM,0BAA0B,CAAC,aAAqB,WAAmB;QACvE,WAAW,uBAAuB;YAChC,cAAc;YACd,YAAY;YACZ;QACF;IACF;IAEA,MAAM,QAAQ;QACZ;QACA;QACA;QACA;IACF;IAEA,qBACE,8OAAC,iBAAiB,QAAQ;QAAC,OAAO;;YAC/B;YACA,yBAAW,8OAAC,gKAAA,CAAA,YAAS;;;;;;;;;;;AAG5B;AAEO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAGO,SAAS;IACd,MAAM,EAAE,aAAa,EAAE,GAAG;IAE1B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,cAAc,OAAO,QAAQ,CAAC,QAAQ;IACxC,GAAG;QAAC;KAAc;AACpB;AAEO,SAAS,mBAAmB,WAAmB,EAAE,SAAiB;IACvE,MAAM,EAAE,uBAAuB,EAAE,GAAG;IAEpC,MAAM,YAAY,IAAM,wBAAwB,aAAa,WAAW;IACxE,MAAM,aAAa,IAAM,wBAAwB,aAAa,WAAW;IACzE,MAAM,YAAY,IAAM,wBAAwB,aAAa,WAAW;IACxE,MAAM,eAAe,IAAM,wBAAwB,aAAa,WAAW;IAE3E,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;QAAa;KAAU;IAE3B,OAAO;QACL;QACA;QACA;QACA;IACF;AACF;AAGO,MAAM,iBAAiB;IAC5B,uCAAuC;IACvC,mBAAmB;QACjB,wCAAmC,OAAO,EAAE;;IAE9C;IAEA,qCAAqC;IACrC,cAAc,CAAC;QACb,MAAM,OAAO,eAAe,iBAAiB;QAC7C,OAAO,KAAK,MAAM,CAAC,CAAA,QACjB,MAAM,KAAK,KAAK,eAAe,MAAM,UAAU,EAAE,cAAc,MAC/D,MAAM;IACV;IAEA,yBAAyB;IACzB,iBAAiB,CAAC,QAAgB,EAAE;QAClC,MAAM,OAAO,eAAe,iBAAiB;QAC7C,MAAM,YAAY,KAAK,MAAM,CAAC,CAAA,QAAS,MAAM,KAAK,KAAK;QAEvD,MAAM,aAAqC,CAAC;QAC5C,UAAU,OAAO,CAAC,CAAA;YAChB,MAAM,OAAO,MAAM,UAAU,EAAE,aAAa,MAAM,GAAG;YACrD,UAAU,CAAC,KAAK,GAAG,CAAC,UAAU,CAAC,KAAK,IAAI,CAAC,IAAI;QAC/C;QAEA,OAAO,OAAO,OAAO,CAAC,YACnB,GAAG,CAAC,CAAC,CAAC,MAAM,MAAM,GAAK,CAAC;gBAAE;gBAAM;YAAM,CAAC,GACvC,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK,EAChC,KAAK,CAAC,GAAG;IACd;IAEA,wBAAwB;IACxB,YAAY;QACV,MAAM,OAAO,eAAe,iBAAiB;QAC7C,MAAM,MAAM,IAAI;QAChB,MAAM,QAAQ,IAAI,KAAK,IAAI,WAAW,IAAI,IAAI,QAAQ,IAAI,IAAI,OAAO;QACrE,MAAM,WAAW,IAAI,KAAK,MAAM,OAAO,KAAK,IAAI,KAAK,KAAK,KAAK;QAC/D,MAAM,YAAY,IAAI,KAAK,MAAM,OAAO,KAAK,KAAK,KAAK,KAAK,KAAK;QAEjE,MAAM,cAAc,KAAK,MAAM,CAAC,CAAA,QAAS,IAAI,KAAK,MAAM,SAAS,KAAK;QACtE,MAAM,aAAa,KAAK,MAAM,CAAC,CAAA,QAAS,IAAI,KAAK,MAAM,SAAS,KAAK;QACrE,MAAM,cAAc,KAAK,MAAM,CAAC,CAAA,QAAS,IAAI,KAAK,MAAM,SAAS,KAAK;QAEtE,OAAO;YACL,OAAO;gBACL,QAAQ,KAAK,MAAM;gBACnB,WAAW,KAAK,MAAM,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK,aAAa,MAAM;gBAC3D,aAAa,KAAK,MAAM,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK,eAAe,MAAM;gBAC/D,qBAAqB,KAAK,MAAM,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK,uBAAuB,MAAM;YACjF;YACA,OAAO;gBACL,QAAQ,YAAY,MAAM;gBAC1B,WAAW,YAAY,MAAM,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK,aAAa,MAAM;gBAClE,aAAa,YAAY,MAAM,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK,eAAe,MAAM;YACxE;YACA,MAAM;gBACJ,QAAQ,WAAW,MAAM;gBACzB,WAAW,WAAW,MAAM,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK,aAAa,MAAM;gBACjE,aAAa,WAAW,MAAM,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK,eAAe,MAAM;YACvE;YACA,OAAO;gBACL,QAAQ,YAAY,MAAM;gBAC1B,WAAW,YAAY,MAAM,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK,aAAa,MAAM;gBAClE,aAAa,YAAY,MAAM,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK,eAAe,MAAM;YACxE;QACF;IACF;IAEA,uBAAuB;IACvB,WAAW;QACT,uCAAmC;;QAEnC;IACF;AACF", "debugId": null}}, {"offset": {"line": 647, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Development/BuzzEdge/buzzedge-frontend/node_modules/next/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": ";AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 670, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Development/BuzzEdge/buzzedge-frontend/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-ssr'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 677, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Development/BuzzEdge/buzzedge-frontend/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored['react-ssr'].React\n"], "names": ["module", "exports", "require", "vendored", "React"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CAAC,YAAY,CAACC,KAAK", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 685, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Development/BuzzEdge/buzzedge-frontend/node_modules/%40vercel/analytics/src/react/index.tsx", "file:///Users/<USER>/Development/BuzzEdge/buzzedge-frontend/node_modules/%40vercel/analytics/package.json", "file:///Users/<USER>/Development/BuzzEdge/buzzedge-frontend/node_modules/%40vercel/analytics/src/queue.ts", "file:///Users/<USER>/Development/BuzzEdge/buzzedge-frontend/node_modules/%40vercel/analytics/src/utils.ts", "file:///Users/<USER>/Development/BuzzEdge/buzzedge-frontend/node_modules/%40vercel/analytics/src/generic.ts", "file:///Users/<USER>/Development/BuzzEdge/buzzedge-frontend/node_modules/%40vercel/analytics/src/react/utils.ts"], "sourcesContent": ["'use client';\nimport { useEffect } from 'react';\nimport { inject, track, pageview } from '../generic';\nimport type { AnalyticsProps, BeforeSend, BeforeSendEvent } from '../types';\nimport { getBasePath } from './utils';\n\n/**\n * Injects the Vercel Web Analytics script into the page head and starts tracking page views. Read more in our [documentation](https://vercel.com/docs/concepts/analytics/package).\n * @param [props] - Analytics options.\n * @param [props.mode] - The mode to use for the analytics script. Defaults to `auto`.\n *  - `auto` - Automatically detect the environment.  Uses `production` if the environment cannot be determined.\n *  - `production` - Always use the production script. (Sends events to the server)\n *  - `development` - Always use the development script. (Logs events to the console)\n * @param [props.debug] - Whether to enable debug logging in development. Defaults to `true`.\n * @param [props.beforeSend] - A middleware function to modify events before they are sent. Should return the event object or `null` to cancel the event.\n * @example\n * ```js\n * import { Analytics } from '@vercel/analytics/react';\n *\n * export default function App() {\n *  return (\n *   <div>\n *    <Analytics />\n *    <h1>My App</h1>\n *  </div>\n * );\n * }\n * ```\n */\nfunction Analytics(\n  props: AnalyticsProps & {\n    framework?: string;\n    route?: string | null;\n    path?: string | null;\n    basePath?: string;\n  }\n): null {\n  useEffect(() => {\n    if (props.beforeSend) {\n      window.va?.('beforeSend', props.beforeSend);\n    }\n  }, [props.beforeSend]);\n\n  // biome-ignore lint/correctness/useExhaustiveDependencies: only run once\n  useEffect(() => {\n    inject({\n      framework: props.framework || 'react',\n      basePath: props.basePath ?? getBasePath(),\n      ...(props.route !== undefined && { disableAutoTrack: true }),\n      ...props,\n    });\n    // eslint-disable-next-line react-hooks/exhaustive-deps -- only run once\n  }, []);\n\n  useEffect(() => {\n    // explicitely track page view, since we disabled auto tracking\n    if (props.route && props.path) {\n      pageview({ route: props.route, path: props.path });\n    }\n  }, [props.route, props.path]);\n\n  return null;\n}\n\nexport { track, Analytics };\nexport type { AnalyticsProps, BeforeSend, BeforeSendEvent };\n", "{\n  \"name\": \"@vercel/analytics\",\n  \"version\": \"1.5.0\",\n  \"description\": \"Gain real-time traffic insights with Vercel Web Analytics\",\n  \"keywords\": [\n    \"analytics\",\n    \"vercel\"\n  ],\n  \"repository\": {\n    \"url\": \"github:vercel/analytics\",\n    \"directory\": \"packages/web\"\n  },\n  \"license\": \"MPL-2.0\",\n  \"exports\": {\n    \"./package.json\": \"./package.json\",\n    \".\": {\n      \"browser\": \"./dist/index.mjs\",\n      \"import\": \"./dist/index.mjs\",\n      \"require\": \"./dist/index.js\"\n    },\n    \"./astro\": {\n      \"import\": \"./dist/astro/component.ts\"\n    },\n    \"./next\": {\n      \"browser\": \"./dist/next/index.mjs\",\n      \"import\": \"./dist/next/index.mjs\",\n      \"require\": \"./dist/next/index.js\"\n    },\n    \"./nuxt\": {\n      \"browser\": \"./dist/nuxt/index.mjs\",\n      \"import\": \"./dist/nuxt/index.mjs\",\n      \"require\": \"./dist/nuxt/index.js\"\n    },\n    \"./react\": {\n      \"browser\": \"./dist/react/index.mjs\",\n      \"import\": \"./dist/react/index.mjs\",\n      \"require\": \"./dist/react/index.js\"\n    },\n    \"./remix\": {\n      \"browser\": \"./dist/remix/index.mjs\",\n      \"import\": \"./dist/remix/index.mjs\",\n      \"require\": \"./dist/remix/index.js\"\n    },\n    \"./server\": {\n      \"node\": \"./dist/server/index.mjs\",\n      \"edge-light\": \"./dist/server/index.mjs\",\n      \"import\": \"./dist/server/index.mjs\",\n      \"require\": \"./dist/server/index.js\",\n      \"default\": \"./dist/server/index.js\"\n    },\n    \"./sveltekit\": {\n      \"svelte\": \"./dist/sveltekit/index.mjs\",\n      \"types\": \"./dist/sveltekit/index.d.ts\"\n    },\n    \"./vue\": {\n      \"browser\": \"./dist/vue/index.mjs\",\n      \"import\": \"./dist/vue/index.mjs\",\n      \"require\": \"./dist/vue/index.js\"\n    }\n  },\n  \"main\": \"./dist/index.mjs\",\n  \"types\": \"./dist/index.d.ts\",\n  \"typesVersions\": {\n    \"*\": {\n      \"*\": [\n        \"dist/index.d.ts\"\n      ],\n      \"next\": [\n        \"dist/next/index.d.ts\"\n      ],\n      \"nuxt\": [\n        \"dist/nuxt/index.d.ts\"\n      ],\n      \"react\": [\n        \"dist/react/index.d.ts\"\n      ],\n      \"remix\": [\n        \"dist/remix/index.d.ts\"\n      ],\n      \"server\": [\n        \"dist/server/index.d.ts\"\n      ],\n      \"sveltekit\": [\n        \"dist/sveltekit/index.d.ts\"\n      ],\n      \"vue\": [\n        \"dist/vue/index.d.ts\"\n      ]\n    }\n  },\n  \"scripts\": {\n    \"build\": \"tsup && pnpm copy-astro\",\n    \"copy-astro\": \"cp -R src/astro dist/\",\n    \"dev\": \"pnpm copy-astro && tsup --watch\",\n    \"lint\": \"eslint .\",\n    \"lint-fix\": \"eslint . --fix\",\n    \"test\": \"vitest\",\n    \"type-check\": \"tsc --noEmit\"\n  },\n  \"eslintConfig\": {\n    \"extends\": [\n      \"@vercel/eslint-config\"\n    ],\n    \"rules\": {\n      \"tsdoc/syntax\": \"off\"\n    },\n    \"ignorePatterns\": [\n      \"jest.setup.ts\"\n    ]\n  },\n  \"devDependencies\": {\n    \"@swc/core\": \"^1.9.2\",\n    \"@testing-library/jest-dom\": \"^6.6.3\",\n    \"@testing-library/react\": \"^16.0.1\",\n    \"@types/node\": \"^22.9.0\",\n    \"@types/react\": \"^18.3.12\",\n    \"@vercel/eslint-config\": \"workspace:0.0.0\",\n    \"server-only\": \"^0.0.1\",\n    \"svelte\": \"^5.1.10\",\n    \"tsup\": \"8.3.5\",\n    \"vitest\": \"^2.1.5\",\n    \"vue\": \"^3.5.12\",\n    \"vue-router\": \"^4.4.5\"\n  },\n  \"peerDependencies\": {\n    \"@remix-run/react\": \"^2\",\n    \"@sveltejs/kit\": \"^1 || ^2\",\n    \"next\": \">= 13\",\n    \"react\": \"^18 || ^19 || ^19.0.0-rc\",\n    \"svelte\": \">= 4\",\n    \"vue\": \"^3\",\n    \"vue-router\": \"^4\"\n  },\n  \"peerDependenciesMeta\": {\n    \"@remix-run/react\": {\n      \"optional\": true\n    },\n    \"@sveltejs/kit\": {\n      \"optional\": true\n    },\n    \"next\": {\n      \"optional\": true\n    },\n    \"react\": {\n      \"optional\": true\n    },\n    \"svelte\": {\n      \"optional\": true\n    },\n    \"vue\": {\n      \"optional\": true\n    },\n    \"vue-router\": {\n      \"optional\": true\n    }\n  }\n}\n", "export const initQueue = (): void => {\n  // initialize va until script is loaded\n  if (window.va) return;\n\n  window.va = function a(...params): void {\n    (window.vaq = window.vaq || []).push(params);\n  };\n};\n", "import type { AllowedPropertyValues, AnalyticsProps, Mode } from './types';\n\nexport function isBrowser(): boolean {\n  return typeof window !== 'undefined';\n}\n\nfunction detectEnvironment(): 'development' | 'production' {\n  try {\n    const env = process.env.NODE_ENV;\n    if (env === 'development' || env === 'test') {\n      return 'development';\n    }\n  } catch (e) {\n    // do nothing, this is okay\n  }\n  return 'production';\n}\n\nexport function setMode(mode: Mode = 'auto'): void {\n  if (mode === 'auto') {\n    window.vam = detectEnvironment();\n    return;\n  }\n\n  window.vam = mode;\n}\n\nexport function getMode(): Mode {\n  const mode = isBrowser() ? window.vam : detectEnvironment();\n  return mode || 'production';\n}\n\nexport function isProduction(): boolean {\n  return getMode() === 'production';\n}\n\nexport function isDevelopment(): boolean {\n  return getMode() === 'development';\n}\n\nfunction removeKey(\n  key: string,\n  { [key]: _, ...rest }\n): Record<string, unknown> {\n  return rest;\n}\n\nexport function parseProperties(\n  properties: Record<string, unknown> | undefined,\n  options: {\n    strip?: boolean;\n  }\n): Error | Record<string, AllowedPropertyValues> | undefined {\n  if (!properties) return undefined;\n  let props = properties;\n  const errorProperties: string[] = [];\n  for (const [key, value] of Object.entries(properties)) {\n    if (typeof value === 'object' && value !== null) {\n      if (options.strip) {\n        props = removeKey(key, props);\n      } else {\n        errorProperties.push(key);\n      }\n    }\n  }\n\n  if (errorProperties.length > 0 && !options.strip) {\n    throw Error(\n      `The following properties are not valid: ${errorProperties.join(\n        ', '\n      )}. Only strings, numbers, booleans, and null are allowed.`\n    );\n  }\n  return props as Record<string, AllowedPropertyValues>;\n}\n\nexport function computeRoute(\n  pathname: string | null,\n  pathParams: Record<string, string | string[]> | null\n): string | null {\n  if (!pathname || !pathParams) {\n    return pathname;\n  }\n\n  let result = pathname;\n  try {\n    const entries = Object.entries(pathParams);\n    // simple keys must be handled first\n    for (const [key, value] of entries) {\n      if (!Array.isArray(value)) {\n        const matcher = turnValueToRegExp(value);\n        if (matcher.test(result)) {\n          result = result.replace(matcher, `/[${key}]`);\n        }\n      }\n    }\n    // array values next\n    for (const [key, value] of entries) {\n      if (Array.isArray(value)) {\n        const matcher = turnValueToRegExp(value.join('/'));\n        if (matcher.test(result)) {\n          result = result.replace(matcher, `/[...${key}]`);\n        }\n      }\n    }\n    return result;\n  } catch (e) {\n    return pathname;\n  }\n}\n\nfunction turnValueToRegExp(value: string): RegExp {\n  return new RegExp(`/${escapeRegExp(value)}(?=[/?#]|$)`);\n}\n\nfunction escapeRegExp(string: string): string {\n  return string.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&');\n}\n\nexport function getScriptSrc(\n  props: AnalyticsProps & { basePath?: string }\n): string {\n  if (props.scriptSrc) {\n    return props.scriptSrc;\n  }\n  if (isDevelopment()) {\n    return 'https://va.vercel-scripts.com/v1/script.debug.js';\n  }\n  if (props.basePath) {\n    return `${props.basePath}/insights/script.js`;\n  }\n  return '/_vercel/insights/script.js';\n}\n", "import { name as packageName, version } from '../package.json';\nimport { initQueue } from './queue';\nimport type {\n  AllowedPropertyValues,\n  AnalyticsProps,\n  FlagsDataInput,\n  BeforeSend,\n  BeforeSendEvent,\n} from './types';\nimport {\n  isBrowser,\n  parseProperties,\n  setMode,\n  isDevelopment,\n  isProduction,\n  computeRoute,\n  getScriptSrc,\n} from './utils';\n\n/**\n * Injects the Vercel Web Analytics script into the page head and starts tracking page views. Read more in our [documentation](https://vercel.com/docs/concepts/analytics/package).\n * @param [props] - Analytics options.\n * @param [props.mode] - The mode to use for the analytics script. Defaults to `auto`.\n *  - `auto` - Automatically detect the environment.  Uses `production` if the environment cannot be determined.\n *  - `production` - Always use the production script. (Sends events to the server)\n *  - `development` - Always use the development script. (Logs events to the console)\n * @param [props.debug] - Whether to enable debug logging in development. Defaults to `true`.\n * @param [props.beforeSend] - A middleware function to modify events before they are sent. Should return the event object or `null` to cancel the event.\n * @param [props.dsn] - The DSN of the project to send events to. Only required when self-hosting.\n * @param [props.disableAutoTrack] - Whether the injected script should track page views from pushState events. Disable if route is updated after pushState, a manually call page pageview().\n */\nfunction inject(\n  props: AnalyticsProps & {\n    framework?: string;\n    disableAutoTrack?: boolean;\n    basePath?: string;\n  } = {\n    debug: true,\n  }\n): void {\n  if (!isBrowser()) return;\n\n  setMode(props.mode);\n\n  initQueue();\n\n  if (props.beforeSend) {\n    window.va?.('beforeSend', props.beforeSend);\n  }\n\n  const src = getScriptSrc(props);\n\n  if (document.head.querySelector(`script[src*=\"${src}\"]`)) return;\n\n  const script = document.createElement('script');\n  script.src = src;\n  script.defer = true;\n  script.dataset.sdkn =\n    packageName + (props.framework ? `/${props.framework}` : '');\n  script.dataset.sdkv = version;\n\n  if (props.disableAutoTrack) {\n    script.dataset.disableAutoTrack = '1';\n  }\n  if (props.endpoint) {\n    script.dataset.endpoint = props.endpoint;\n  } else if (props.basePath) {\n    script.dataset.endpoint = `${props.basePath}/insights`;\n  }\n  if (props.dsn) {\n    script.dataset.dsn = props.dsn;\n  }\n\n  script.onerror = (): void => {\n    const errorMessage = isDevelopment()\n      ? 'Please check if any ad blockers are enabled and try again.'\n      : 'Be sure to enable Web Analytics for your project and deploy again. See https://vercel.com/docs/analytics/quickstart for more information.';\n\n    // eslint-disable-next-line no-console -- Logging to console is intentional\n    console.log(\n      `[Vercel Web Analytics] Failed to load script from ${src}. ${errorMessage}`\n    );\n  };\n\n  if (isDevelopment() && props.debug === false) {\n    script.dataset.debug = 'false';\n  }\n\n  document.head.appendChild(script);\n}\n\n/**\n * Tracks a custom event. Please refer to the [documentation](https://vercel.com/docs/concepts/analytics/custom-events) for more information on custom events.\n * @param name - The name of the event.\n * * Examples: `Purchase`, `Click Button`, or `Play Video`.\n * @param [properties] - Additional properties of the event. Nested objects are not supported. Allowed values are `string`, `number`, `boolean`, and `null`.\n */\nfunction track(\n  name: string,\n  properties?: Record<string, AllowedPropertyValues>,\n  options?: {\n    flags?: FlagsDataInput;\n  }\n): void {\n  if (!isBrowser()) {\n    const msg =\n      '[Vercel Web Analytics] Please import `track` from `@vercel/analytics/server` when using this function in a server environment';\n\n    if (isProduction()) {\n      // eslint-disable-next-line no-console -- Show warning in production\n      console.warn(msg);\n    } else {\n      throw new Error(msg);\n    }\n\n    return;\n  }\n\n  if (!properties) {\n    window.va?.('event', { name, options });\n    return;\n  }\n\n  try {\n    const props = parseProperties(properties, {\n      strip: isProduction(),\n    });\n\n    window.va?.('event', {\n      name,\n      data: props,\n      options,\n    });\n  } catch (err) {\n    if (err instanceof Error && isDevelopment()) {\n      // eslint-disable-next-line no-console -- Logging to console is intentional\n      console.error(err);\n    }\n  }\n}\n\nfunction pageview({\n  route,\n  path,\n}: {\n  route?: string | null;\n  path?: string;\n}): void {\n  window.va?.('pageview', { route, path });\n}\n\nexport { inject, track, pageview, computeRoute };\nexport type { AnalyticsProps, BeforeSend, BeforeSendEvent };\n\n// eslint-disable-next-line import/no-default-export -- Default export is intentional\nexport default {\n  inject,\n  track,\n  computeRoute,\n};\n", "export function getBasePath(): string | undefined {\n  // !! important !!\n  // do not access env variables using process.env[varname]\n  // some bundles won't replace the value at build time.\n  // eslint-disable-next-line @typescript-eslint/prefer-optional-chain -- we can't use optionnal here, it'll break if process does not exist.\n  if (typeof process === 'undefined' || typeof process.env === 'undefined') {\n    return undefined;\n  }\n  return process.env.REACT_APP_VERCEL_OBSERVABILITY_BASEPATH;\n}\n"], "names": ["name"], "mappings": ";;;;;AACA,SAAS,iBAAiB;;;;ACAxB,IAAA,OAAQ;AACR,IAAA,UAAW;;ACFN,IAAM,YAAY,MAAY;IAEnC,IAAI,OAAO,EAAA,CAAI,CAAA;IAEf,OAAO,EAAA,GAAK,SAAS,EAAA,GAAK,MAAA,EAAc;QACtC,CAAC,OAAO,GAAA,GAAM,OAAO,GAAA,IAAO,CAAC,CAAA,EAAG,IAAA,CAAK,MAAM;IAC7C;AACF;;ACLO,SAAS,YAAqB;IACnC,OAAO,OAAO,WAAW;AAC3B;AAEA,SAAS,oBAAkD;IACzD,IAAI;QACF,MAAM,MAAM,QAAQ,IAAI;QACxB,IAAI,QAAQ,iBAAiB,QAAQ,GAAQ;YAC3C,OAAO;QACT;IACF,EAAA,OAAS,GAAG,CAEZ;IACA,OAAO;AACT;AAEO,SAAS,QAAQ,OAAa,MAAA,EAAc;IACjD,IAAI,SAAS,QAAQ;QACnB,OAAO,GAAA,GAAM,kBAAkB;QAC/B;IACF;IAEA,OAAO,GAAA,GAAM;AACf;AAEO,SAAS,UAAgB;IAC9B,MAAM,OAAO,UAAU,IAAI,OAAO,GAAA,GAAM,kBAAkB;IAC1D,OAAO,QAAQ;AACjB;AAEO,SAAS,eAAwB;IACtC,OAAO,QAAQ,MAAM;AACvB;AAEO,SAAS,gBAAyB;IACvC,OAAO,QAAQ,MAAM;AACvB;AAEA,SAAS,UACP,GAAA,EACA,EAAE,CAAC,GAAG,CAAA,EAAG,CAAA,EAAG,GAAG,KAAK,CAAA,EACK;IACzB,OAAO;AACT;AAEO,SAAS,gBACd,UAAA,EACA,OAAA,EAG2D;IAC3D,IAAI,CAAC,WAAY,CAAA,OAAO,KAAA;IACxB,IAAI,QAAQ;IACZ,MAAM,kBAA4B,CAAC,CAAA;IACnC,KAAA,MAAW,CAAC,KAAK,KAAK,CAAA,IAAK,OAAO,OAAA,CAAQ,UAAU,EAAG;QACrD,IAAI,OAAO,UAAU,YAAY,UAAU,MAAM;YAC/C,IAAI,QAAQ,KAAA,EAAO;gBACjB,QAAQ,UAAU,KAAK,KAAK;YAC9B,OAAO;gBACL,gBAAgB,IAAA,CAAK,GAAG;YAC1B;QACF;IACF;IAEA,IAAI,gBAAgB,MAAA,GAAS,KAAK,CAAC,QAAQ,KAAA,EAAO;QAChD,MAAM,MACJ,CAAA,wCAAA,EAA2C,gBAAgB,IAAA,CACzD,MACD,wDAAA,CAAA;IAEL;IACA,OAAO;AACT;AA6CO,SAAS,aACd,KAAA,EACQ;IACR,IAAI,MAAM,SAAA,EAAW;QACnB,OAAO,MAAM,SAAA;IACf;IACA,IAAI,cAAc,GAAG;QACnB,OAAO;IACT;IACA,IAAI,MAAM,QAAA,EAAU;QAClB,OAAO,GAAG,MAAM,QAAQ,CAAA,mBAAA,CAAA;IAC1B;IACA,OAAO;AACT;;ACrGA,SAAS,OACP,QAII;IACF,OAAO;AACT,CAAA,EACM;IAvCR,IAAA;IAwCE,IAAI,CAAC,UAAU,EAAG,CAAA;IAElB,QAAQ,MAAM,IAAI;IAElB,UAAU;IAEV,IAAI,MAAM,UAAA,EAAY;QACpB,CAAA,KAAA,OAAO,EAAA,KAAP,OAAA,KAAA,IAAA,GAAA,IAAA,CAAA,QAAY,cAAc,MAAM,UAAA;IAClC;IAEA,MAAM,MAAM,aAAa,KAAK;IAE9B,IAAI,SAAS,IAAA,CAAK,aAAA,CAAc,CAAA,aAAA,EAAgB,GAAG,CAAA,EAAA,CAAI,EAAG,CAAA;IAE1D,MAAM,SAAS,SAAS,aAAA,CAAc,QAAQ;IAC9C,OAAO,GAAA,GAAM;IACb,OAAO,KAAA,GAAQ;IACf,OAAO,OAAA,CAAQ,IAAA,GACb,OAAA,CAAe,MAAM,SAAA,GAAY,CAAA,CAAA,EAAI,MAAM,SAAS,EAAA,GAAK,EAAA;IAC3D,OAAO,OAAA,CAAQ,IAAA,GAAO;IAEtB,IAAI,MAAM,gBAAA,EAAkB;QAC1B,OAAO,OAAA,CAAQ,gBAAA,GAAmB;IACpC;IACA,IAAI,MAAM,QAAA,EAAU;QAClB,OAAO,OAAA,CAAQ,QAAA,GAAW,MAAM,QAAA;IAClC,OAAA,IAAW,MAAM,QAAA,EAAU;QACzB,OAAO,OAAA,CAAQ,QAAA,GAAW,GAAG,MAAM,QAAQ,CAAA,SAAA,CAAA;IAC7C;IACA,IAAI,MAAM,GAAA,EAAK;QACb,OAAO,OAAA,CAAQ,GAAA,GAAM,MAAM,GAAA;IAC7B;IAEA,OAAO,OAAA,GAAU,MAAY;QAC3B,MAAM,eAAe,cAAc,IAC/B,+DACA;QAGJ,QAAQ,GAAA,CACN,CAAA,kDAAA,EAAqD,GAAG,CAAA,EAAA,EAAK,YAAY,EAAA;IAE7E;IAEA,IAAI,cAAc,KAAK,MAAM,KAAA,KAAU,OAAO;QAC5C,OAAO,OAAA,CAAQ,KAAA,GAAQ;IACzB;IAEA,SAAS,IAAA,CAAK,WAAA,CAAY,MAAM;AAClC;AAQA,SAAS,MACPA,KAAAA,EACA,UAAA,EACA,OAAA,EAGM;IAvGR,IAAA,IAAA;IAwGE,IAAI,CAAC,UAAU,GAAG;QAChB,MAAM,MACJ;QAEF,IAAI,aAAa,GAAG;YAElB,QAAQ,IAAA,CAAK,GAAG;QAClB,OAAO;YACL,MAAM,IAAI,MAAM,GAAG;QACrB;QAEA;IACF;IAEA,IAAI,CAAC,YAAY;QACf,CAAA,KAAA,OAAO,EAAA,KAAP,OAAA,KAAA,IAAA,GAAA,IAAA,CAAA,QAAY,SAAS;YAAE,MAAAA;YAAM;QAAQ;QACrC;IACF;IAEA,IAAI;QACF,MAAM,QAAQ,gBAAgB,YAAY;YACxC,OAAO,aAAa;QACtB,CAAC;QAED,CAAA,KAAA,OAAO,EAAA,KAAP,OAAA,KAAA,IAAA,GAAA,IAAA,CAAA,QAAY,SAAS;YACnB,MAAAA;YACA,MAAM;YACN;QACF;IACF,EAAA,OAAS,KAAK;QACZ,IAAI,eAAe,SAAS,cAAc,GAAG;YAE3C,QAAQ,KAAA,CAAM,GAAG;QACnB;IACF;AACF;AAEA,SAAS,SAAS,EAChB,KAAA,EACA,IAAA,EACF,EAGS;IAnJT,IAAA;IAoJE,CAAA,KAAA,OAAO,EAAA,KAAP,OAAA,KAAA,IAAA,GAAA,IAAA,CAAA,QAAY,YAAY;QAAE;QAAO;IAAK;AACxC;;ACrJO,SAAS,cAAkC;IAKhD,IAAI,OAAO,YAAY,eAAe,OAAO,QAAQ,GAAA,KAAQ,aAAa;QACxE,OAAO,KAAA;IACT;IACA,OAAO,QAAQ,GAAA,CAAI,uCAAA;AACrB;;ALoBA,SAAS,UACP,KAAA,EAMM;IACN,CAAA,GAAA,qMAAA,CAAA,YAAA,EAAU,MAAM;QArClB,IAAA;QAsCI,IAAI,MAAM,UAAA,EAAY;YACpB,CAAA,KAAA,OAAO,EAAA,KAAP,OAAA,KAAA,IAAA,GAAA,IAAA,CAAA,QAAY,cAAc,MAAM,UAAA;QAClC;IACF,GAAG;QAAC,MAAM,UAAU;KAAC;IAGrB,CAAA,GAAA,qMAAA,CAAA,YAAA,EAAU,MAAM;QACd,OAAO;YACL,WAAW,MAAM,SAAA,IAAa;YAC9B,UAAU,MAAM,QAAA,IAAY,YAAY;YACxC,GAAI,MAAM,KAAA,KAAU,KAAA,KAAa;gBAAE,kBAAkB;YAAK,CAAA;YAC1D,GAAG,KAAA;QACL,CAAC;IAEH,GAAG,CAAC,CAAC;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAA,EAAU,MAAM;QAEd,IAAI,MAAM,KAAA,IAAS,MAAM,IAAA,EAAM;YAC7B,SAAS;gBAAE,OAAO,MAAM,KAAA;gBAAO,MAAM,MAAM,IAAA;YAAK,CAAC;QACnD;IACF,GAAG;QAAC,MAAM,KAAA;QAAO,MAAM,IAAI;KAAC;IAE5B,OAAO;AACT", "ignoreList": [0, 1, 2, 3, 4, 5], "debugId": null}}, {"offset": {"line": 891, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Development/BuzzEdge/buzzedge-frontend/node_modules/react-ga4/dist/gtag.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\nvar gtag = function gtag() {\n  for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n    args[_key] = arguments[_key];\n  }\n  if (typeof window !== \"undefined\") {\n    var _window;\n    if (typeof window.gtag === \"undefined\") {\n      window.dataLayer = window.dataLayer || [];\n      window.gtag = function gtag() {\n        window.dataLayer.push(arguments);\n      };\n    }\n    (_window = window).gtag.apply(_window, args);\n  }\n};\nvar _default = gtag;\nexports[\"default\"] = _default;"], "names": [], "mappings": "AAAA;AAEA,OAAO,cAAc,CAAC,SAAS,cAAc;IAC3C,OAAO;AACT;AACA,OAAO,CAAC,UAAU,GAAG,KAAK;AAC1B,IAAI,OAAO,SAAS;IAClB,IAAK,IAAI,OAAO,UAAU,MAAM,EAAE,OAAO,IAAI,MAAM,OAAO,OAAO,GAAG,OAAO,MAAM,OAAQ;QACvF,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC,KAAK;IAC9B;IACA,IAAI,OAAO,WAAW,aAAa;QACjC,IAAI;QACJ,IAAI,OAAO,OAAO,IAAI,KAAK,aAAa;YACtC,OAAO,SAAS,GAAG,OAAO,SAAS,IAAI,EAAE;YACzC,OAAO,IAAI,GAAG,SAAS;gBACrB,OAAO,SAAS,CAAC,IAAI,CAAC;YACxB;QACF;QACA,CAAC,UAAU,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,SAAS;IACzC;AACF;AACA,IAAI,WAAW;AACf,OAAO,CAAC,UAAU,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 918, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Development/BuzzEdge/buzzedge-frontend/node_modules/react-ga4/dist/format.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = format;\nvar smallWords = /^(a|an|and|as|at|but|by|en|for|if|in|nor|of|on|or|per|the|to|vs?\\.?|via)$/i;\nfunction toTitleCase(string) {\n  return string.toString().trim().replace(/[A-Za-z0-9\\u00C0-\\u00FF]+[^\\s-]*/g, function (match, index, title) {\n    if (index > 0 && index + match.length !== title.length && match.search(smallWords) > -1 && title.charAt(index - 2) !== \":\" && (title.charAt(index + match.length) !== \"-\" || title.charAt(index - 1) === \"-\") && title.charAt(index - 1).search(/[^\\s-]/) < 0) {\n      return match.toLowerCase();\n    }\n    if (match.substr(1).search(/[A-Z]|\\../) > -1) {\n      return match;\n    }\n    return match.charAt(0).toUpperCase() + match.substr(1);\n  });\n}\n\n// See if s could be an email address. We don't want to send personal data like email.\n// https://support.google.com/analytics/answer/2795983?hl=en\nfunction mightBeEmail(s) {\n  // There's no point trying to validate rfc822 fully, just look for ...@...\n  return typeof s === \"string\" && s.indexOf(\"@\") !== -1;\n}\nvar redacted = \"REDACTED (Potential Email Address)\";\nfunction redactEmail(string) {\n  if (mightBeEmail(string)) {\n    console.warn(\"This arg looks like an email address, redacting.\");\n    return redacted;\n  }\n  return string;\n}\nfunction format() {\n  var s = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : \"\";\n  var titleCase = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : true;\n  var redactingEmail = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : true;\n  var _str = s || \"\";\n  if (titleCase) {\n    _str = toTitleCase(s);\n  }\n  if (redactingEmail) {\n    _str = redactEmail(_str);\n  }\n  return _str;\n}"], "names": [], "mappings": "AAAA;AAEA,OAAO,cAAc,CAAC,SAAS,cAAc;IAC3C,OAAO;AACT;AACA,OAAO,CAAC,UAAU,GAAG;AACrB,IAAI,aAAa;AACjB,SAAS,YAAY,MAAM;IACzB,OAAO,OAAO,QAAQ,GAAG,IAAI,GAAG,OAAO,CAAC,qCAAqC,SAAU,KAAK,EAAE,KAAK,EAAE,KAAK;QACxG,IAAI,QAAQ,KAAK,QAAQ,MAAM,MAAM,KAAK,MAAM,MAAM,IAAI,MAAM,MAAM,CAAC,cAAc,CAAC,KAAK,MAAM,MAAM,CAAC,QAAQ,OAAO,OAAO,CAAC,MAAM,MAAM,CAAC,QAAQ,MAAM,MAAM,MAAM,OAAO,MAAM,MAAM,CAAC,QAAQ,OAAO,GAAG,KAAK,MAAM,MAAM,CAAC,QAAQ,GAAG,MAAM,CAAC,YAAY,GAAG;YAC7P,OAAO,MAAM,WAAW;QAC1B;QACA,IAAI,MAAM,MAAM,CAAC,GAAG,MAAM,CAAC,eAAe,CAAC,GAAG;YAC5C,OAAO;QACT;QACA,OAAO,MAAM,MAAM,CAAC,GAAG,WAAW,KAAK,MAAM,MAAM,CAAC;IACtD;AACF;AAEA,sFAAsF;AACtF,4DAA4D;AAC5D,SAAS,aAAa,CAAC;IACrB,0EAA0E;IAC1E,OAAO,OAAO,MAAM,YAAY,EAAE,OAAO,CAAC,SAAS,CAAC;AACtD;AACA,IAAI,WAAW;AACf,SAAS,YAAY,MAAM;IACzB,IAAI,aAAa,SAAS;QACxB,QAAQ,IAAI,CAAC;QACb,OAAO;IACT;IACA,OAAO;AACT;AACA,SAAS;IACP,IAAI,IAAI,UAAU,MAAM,GAAG,KAAK,SAAS,CAAC,EAAE,KAAK,YAAY,SAAS,CAAC,EAAE,GAAG;IAC5E,IAAI,YAAY,UAAU,MAAM,GAAG,KAAK,SAAS,CAAC,EAAE,KAAK,YAAY,SAAS,CAAC,EAAE,GAAG;IACpF,IAAI,iBAAiB,UAAU,MAAM,GAAG,KAAK,SAAS,CAAC,EAAE,KAAK,YAAY,SAAS,CAAC,EAAE,GAAG;IACzF,IAAI,OAAO,KAAK;IAChB,IAAI,WAAW;QACb,OAAO,YAAY;IACrB;IACA,IAAI,gBAAgB;QAClB,OAAO,YAAY;IACrB;IACA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 967, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Development/BuzzEdge/buzzedge-frontend/node_modules/react-ga4/dist/ga4.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = exports.GA4 = void 0;\nvar _gtag = _interopRequireDefault(require(\"./gtag\"));\nvar _format = _interopRequireDefault(require(\"./format\"));\nvar _excluded = [\"eventCategory\", \"eventAction\", \"eventLabel\", \"eventValue\", \"hitType\"],\n  _excluded2 = [\"title\", \"location\"],\n  _excluded3 = [\"page\", \"hitType\"];\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { \"default\": obj }; }\nfunction _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }\nfunction _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }\nfunction _typeof(obj) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }, _typeof(obj); }\nfunction _toConsumableArray(arr) { return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread(); }\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _iterableToArray(iter) { if (typeof Symbol !== \"undefined\" && iter[Symbol.iterator] != null || iter[\"@@iterator\"] != null) return Array.from(iter); }\nfunction _arrayWithoutHoles(arr) { if (Array.isArray(arr)) return _arrayLikeToArray(arr); }\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\nfunction _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }\nfunction _iterableToArrayLimit(arr, i) { var _i = null == arr ? null : \"undefined\" != typeof Symbol && arr[Symbol.iterator] || arr[\"@@iterator\"]; if (null != _i) { var _s, _e, _x, _r, _arr = [], _n = !0, _d = !1; try { if (_x = (_i = _i.call(arr)).next, 0 === i) { if (Object(_i) !== _i) return; _n = !1; } else for (; !(_n = (_s = _x.call(_i)).done) && (_arr.push(_s.value), _arr.length !== i); _n = !0); } catch (err) { _d = !0, _e = err; } finally { try { if (!_n && null != _i[\"return\"] && (_r = _i[\"return\"](), Object(_r) !== _r)) return; } finally { if (_d) throw _e; } } return _arr; } }\nfunction _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor); } }\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, \"prototype\", { writable: false }); return Constructor; }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(arg) { var key = _toPrimitive(arg, \"string\"); return _typeof(key) === \"symbol\" ? key : String(key); }\nfunction _toPrimitive(input, hint) { if (_typeof(input) !== \"object\" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || \"default\"); if (_typeof(res) !== \"object\") return res; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (hint === \"string\" ? String : Number)(input); }\n/*\nLinks\nhttps://developers.google.com/gtagjs/reference/api\nhttps://developers.google.com/tag-platform/gtagjs/reference\n*/\n/**\n * @typedef GaOptions\n * @type {Object}\n * @property {boolean} [cookieUpdate=true]\n * @property {number} [cookieExpires=63072000] Default two years\n * @property {string} [cookieDomain=\"auto\"]\n * @property {string} [cookieFlags]\n * @property {string} [userId]\n * @property {string} [clientId]\n * @property {boolean} [anonymizeIp]\n * @property {string} [contentGroup1]\n * @property {string} [contentGroup2]\n * @property {string} [contentGroup3]\n * @property {string} [contentGroup4]\n * @property {string} [contentGroup5]\n * @property {boolean} [allowAdFeatures=true]\n * @property {boolean} [allowAdPersonalizationSignals]\n * @property {boolean} [nonInteraction]\n * @property {string} [page]\n */\n/**\n * @typedef UaEventOptions\n * @type {Object}\n * @property {string} action\n * @property {string} category\n * @property {string} [label]\n * @property {number} [value]\n * @property {boolean} [nonInteraction]\n * @property {('beacon'|'xhr'|'image')} [transport]\n */\n/**\n * @typedef InitOptions\n * @type {Object}\n * @property {string} trackingId\n * @property {GaOptions|any} [gaOptions]\n * @property {Object} [gtagOptions] New parameter\n */\nvar GA4 = /*#__PURE__*/function () {\n  function GA4() {\n    var _this = this;\n    _classCallCheck(this, GA4);\n    _defineProperty(this, \"reset\", function () {\n      _this.isInitialized = false;\n      _this._testMode = false;\n      _this._currentMeasurementId;\n      _this._hasLoadedGA = false;\n      _this._isQueuing = false;\n      _this._queueGtag = [];\n    });\n    _defineProperty(this, \"_gtag\", function () {\n      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n        args[_key] = arguments[_key];\n      }\n      if (!_this._testMode) {\n        if (_this._isQueuing) {\n          _this._queueGtag.push(args);\n        } else {\n          _gtag[\"default\"].apply(void 0, args);\n        }\n      } else {\n        _this._queueGtag.push(args);\n      }\n    });\n    _defineProperty(this, \"_loadGA\", function (GA_MEASUREMENT_ID, nonce) {\n      var gtagUrl = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : \"https://www.googletagmanager.com/gtag/js\";\n      if (typeof window === \"undefined\" || typeof document === \"undefined\") {\n        return;\n      }\n      if (!_this._hasLoadedGA) {\n        // Global Site Tag (gtag.js) - Google Analytics\n        var script = document.createElement(\"script\");\n        script.async = true;\n        script.src = \"\".concat(gtagUrl, \"?id=\").concat(GA_MEASUREMENT_ID);\n        if (nonce) {\n          script.setAttribute(\"nonce\", nonce);\n        }\n        document.body.appendChild(script);\n        window.dataLayer = window.dataLayer || [];\n        window.gtag = function gtag() {\n          window.dataLayer.push(arguments);\n        };\n        _this._hasLoadedGA = true;\n      }\n    });\n    _defineProperty(this, \"_toGtagOptions\", function (gaOptions) {\n      if (!gaOptions) {\n        return;\n      }\n      var mapFields = {\n        // Old https://developers.google.com/analytics/devguides/collection/analyticsjs/field-reference#cookieUpdate\n        // New https://developers.google.com/analytics/devguides/collection/gtagjs/cookies-user-id#cookie_update\n        cookieUpdate: \"cookie_update\",\n        cookieExpires: \"cookie_expires\",\n        cookieDomain: \"cookie_domain\",\n        cookieFlags: \"cookie_flags\",\n        // must be in set method?\n        userId: \"user_id\",\n        clientId: \"client_id\",\n        anonymizeIp: \"anonymize_ip\",\n        // https://support.google.com/analytics/answer/2853546?hl=en#zippy=%2Cin-this-article\n        contentGroup1: \"content_group1\",\n        contentGroup2: \"content_group2\",\n        contentGroup3: \"content_group3\",\n        contentGroup4: \"content_group4\",\n        contentGroup5: \"content_group5\",\n        // https://support.google.com/analytics/answer/9050852?hl=en\n        allowAdFeatures: \"allow_google_signals\",\n        allowAdPersonalizationSignals: \"allow_ad_personalization_signals\",\n        nonInteraction: \"non_interaction\",\n        page: \"page_path\",\n        hitCallback: \"event_callback\"\n      };\n      var gtagOptions = Object.entries(gaOptions).reduce(function (prev, _ref) {\n        var _ref2 = _slicedToArray(_ref, 2),\n          key = _ref2[0],\n          value = _ref2[1];\n        if (mapFields[key]) {\n          prev[mapFields[key]] = value;\n        } else {\n          prev[key] = value;\n        }\n        return prev;\n      }, {});\n      return gtagOptions;\n    });\n    _defineProperty(this, \"initialize\", function (GA_MEASUREMENT_ID) {\n      var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n      if (!GA_MEASUREMENT_ID) {\n        throw new Error(\"Require GA_MEASUREMENT_ID\");\n      }\n      var initConfigs = typeof GA_MEASUREMENT_ID === \"string\" ? [{\n        trackingId: GA_MEASUREMENT_ID\n      }] : GA_MEASUREMENT_ID;\n      _this._currentMeasurementId = initConfigs[0].trackingId;\n      var gaOptions = options.gaOptions,\n        gtagOptions = options.gtagOptions,\n        nonce = options.nonce,\n        _options$testMode = options.testMode,\n        testMode = _options$testMode === void 0 ? false : _options$testMode,\n        gtagUrl = options.gtagUrl;\n      _this._testMode = testMode;\n      if (!testMode) {\n        _this._loadGA(_this._currentMeasurementId, nonce, gtagUrl);\n      }\n      if (!_this.isInitialized) {\n        _this._gtag(\"js\", new Date());\n        initConfigs.forEach(function (config) {\n          var mergedGtagOptions = _objectSpread(_objectSpread(_objectSpread({}, _this._toGtagOptions(_objectSpread(_objectSpread({}, gaOptions), config.gaOptions))), gtagOptions), config.gtagOptions);\n          if (Object.keys(mergedGtagOptions).length) {\n            _this._gtag(\"config\", config.trackingId, mergedGtagOptions);\n          } else {\n            _this._gtag(\"config\", config.trackingId);\n          }\n        });\n      }\n      _this.isInitialized = true;\n      if (!testMode) {\n        var queues = _toConsumableArray(_this._queueGtag);\n        _this._queueGtag = [];\n        _this._isQueuing = false;\n        while (queues.length) {\n          var queue = queues.shift();\n          _this._gtag.apply(_this, _toConsumableArray(queue));\n          if (queue[0] === \"get\") {\n            _this._isQueuing = true;\n          }\n        }\n      }\n    });\n    _defineProperty(this, \"set\", function (fieldsObject) {\n      if (!fieldsObject) {\n        console.warn(\"`fieldsObject` is required in .set()\");\n        return;\n      }\n      if (_typeof(fieldsObject) !== \"object\") {\n        console.warn(\"Expected `fieldsObject` arg to be an Object\");\n        return;\n      }\n      if (Object.keys(fieldsObject).length === 0) {\n        console.warn(\"empty `fieldsObject` given to .set()\");\n      }\n      _this._gaCommand(\"set\", fieldsObject);\n    });\n    _defineProperty(this, \"_gaCommandSendEvent\", function (eventCategory, eventAction, eventLabel, eventValue, fieldsObject) {\n      _this._gtag(\"event\", eventAction, _objectSpread(_objectSpread({\n        event_category: eventCategory,\n        event_label: eventLabel,\n        value: eventValue\n      }, fieldsObject && {\n        non_interaction: fieldsObject.nonInteraction\n      }), _this._toGtagOptions(fieldsObject)));\n    });\n    _defineProperty(this, \"_gaCommandSendEventParameters\", function () {\n      for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n        args[_key2] = arguments[_key2];\n      }\n      if (typeof args[0] === \"string\") {\n        _this._gaCommandSendEvent.apply(_this, _toConsumableArray(args.slice(1)));\n      } else {\n        var _args$ = args[0],\n          eventCategory = _args$.eventCategory,\n          eventAction = _args$.eventAction,\n          eventLabel = _args$.eventLabel,\n          eventValue = _args$.eventValue,\n          hitType = _args$.hitType,\n          rest = _objectWithoutProperties(_args$, _excluded);\n        _this._gaCommandSendEvent(eventCategory, eventAction, eventLabel, eventValue, rest);\n      }\n    });\n    _defineProperty(this, \"_gaCommandSendTiming\", function (timingCategory, timingVar, timingValue, timingLabel) {\n      _this._gtag(\"event\", \"timing_complete\", {\n        name: timingVar,\n        value: timingValue,\n        event_category: timingCategory,\n        event_label: timingLabel\n      });\n    });\n    _defineProperty(this, \"_gaCommandSendPageview\", function (page, fieldsObject) {\n      if (fieldsObject && Object.keys(fieldsObject).length) {\n        var _this$_toGtagOptions = _this._toGtagOptions(fieldsObject),\n          title = _this$_toGtagOptions.title,\n          location = _this$_toGtagOptions.location,\n          rest = _objectWithoutProperties(_this$_toGtagOptions, _excluded2);\n        _this._gtag(\"event\", \"page_view\", _objectSpread(_objectSpread(_objectSpread(_objectSpread({}, page && {\n          page_path: page\n        }), title && {\n          page_title: title\n        }), location && {\n          page_location: location\n        }), rest));\n      } else if (page) {\n        _this._gtag(\"event\", \"page_view\", {\n          page_path: page\n        });\n      } else {\n        _this._gtag(\"event\", \"page_view\");\n      }\n    });\n    _defineProperty(this, \"_gaCommandSendPageviewParameters\", function () {\n      for (var _len3 = arguments.length, args = new Array(_len3), _key3 = 0; _key3 < _len3; _key3++) {\n        args[_key3] = arguments[_key3];\n      }\n      if (typeof args[0] === \"string\") {\n        _this._gaCommandSendPageview.apply(_this, _toConsumableArray(args.slice(1)));\n      } else {\n        var _args$2 = args[0],\n          page = _args$2.page,\n          hitType = _args$2.hitType,\n          rest = _objectWithoutProperties(_args$2, _excluded3);\n        _this._gaCommandSendPageview(page, rest);\n      }\n    });\n    _defineProperty(this, \"_gaCommandSend\", function () {\n      for (var _len4 = arguments.length, args = new Array(_len4), _key4 = 0; _key4 < _len4; _key4++) {\n        args[_key4] = arguments[_key4];\n      }\n      var hitType = typeof args[0] === \"string\" ? args[0] : args[0].hitType;\n      switch (hitType) {\n        case \"event\":\n          _this._gaCommandSendEventParameters.apply(_this, args);\n          break;\n        case \"pageview\":\n          _this._gaCommandSendPageviewParameters.apply(_this, args);\n          break;\n        case \"timing\":\n          _this._gaCommandSendTiming.apply(_this, _toConsumableArray(args.slice(1)));\n          break;\n        case \"screenview\":\n        case \"transaction\":\n        case \"item\":\n        case \"social\":\n        case \"exception\":\n          console.warn(\"Unsupported send command: \".concat(hitType));\n          break;\n        default:\n          console.warn(\"Send command doesn't exist: \".concat(hitType));\n      }\n    });\n    _defineProperty(this, \"_gaCommandSet\", function () {\n      for (var _len5 = arguments.length, args = new Array(_len5), _key5 = 0; _key5 < _len5; _key5++) {\n        args[_key5] = arguments[_key5];\n      }\n      if (typeof args[0] === \"string\") {\n        args[0] = _defineProperty({}, args[0], args[1]);\n      }\n      _this._gtag(\"set\", _this._toGtagOptions(args[0]));\n    });\n    _defineProperty(this, \"_gaCommand\", function (command) {\n      for (var _len6 = arguments.length, args = new Array(_len6 > 1 ? _len6 - 1 : 0), _key6 = 1; _key6 < _len6; _key6++) {\n        args[_key6 - 1] = arguments[_key6];\n      }\n      switch (command) {\n        case \"send\":\n          _this._gaCommandSend.apply(_this, args);\n          break;\n        case \"set\":\n          _this._gaCommandSet.apply(_this, args);\n          break;\n        default:\n          console.warn(\"Command doesn't exist: \".concat(command));\n      }\n    });\n    _defineProperty(this, \"ga\", function () {\n      for (var _len7 = arguments.length, args = new Array(_len7), _key7 = 0; _key7 < _len7; _key7++) {\n        args[_key7] = arguments[_key7];\n      }\n      if (typeof args[0] === \"string\") {\n        _this._gaCommand.apply(_this, args);\n      } else {\n        var readyCallback = args[0];\n        _this._gtag(\"get\", _this._currentMeasurementId, \"client_id\", function (clientId) {\n          _this._isQueuing = false;\n          var queues = _this._queueGtag;\n          readyCallback({\n            get: function get(property) {\n              return property === \"clientId\" ? clientId : property === \"trackingId\" ? _this._currentMeasurementId : property === \"apiVersion\" ? \"1\" : undefined;\n            }\n          });\n          while (queues.length) {\n            var queue = queues.shift();\n            _this._gtag.apply(_this, _toConsumableArray(queue));\n          }\n        });\n        _this._isQueuing = true;\n      }\n      return _this.ga;\n    });\n    _defineProperty(this, \"event\", function (optionsOrName, params) {\n      if (typeof optionsOrName === \"string\") {\n        _this._gtag(\"event\", optionsOrName, _this._toGtagOptions(params));\n      } else {\n        var action = optionsOrName.action,\n          category = optionsOrName.category,\n          label = optionsOrName.label,\n          value = optionsOrName.value,\n          nonInteraction = optionsOrName.nonInteraction,\n          transport = optionsOrName.transport;\n        if (!category || !action) {\n          console.warn(\"args.category AND args.action are required in event()\");\n          return;\n        }\n\n        // Required Fields\n        var fieldObject = {\n          hitType: \"event\",\n          eventCategory: (0, _format[\"default\"])(category),\n          eventAction: (0, _format[\"default\"])(action)\n        };\n\n        // Optional Fields\n        if (label) {\n          fieldObject.eventLabel = (0, _format[\"default\"])(label);\n        }\n        if (typeof value !== \"undefined\") {\n          if (typeof value !== \"number\") {\n            console.warn(\"Expected `args.value` arg to be a Number.\");\n          } else {\n            fieldObject.eventValue = value;\n          }\n        }\n        if (typeof nonInteraction !== \"undefined\") {\n          if (typeof nonInteraction !== \"boolean\") {\n            console.warn(\"`args.nonInteraction` must be a boolean.\");\n          } else {\n            fieldObject.nonInteraction = nonInteraction;\n          }\n        }\n        if (typeof transport !== \"undefined\") {\n          if (typeof transport !== \"string\") {\n            console.warn(\"`args.transport` must be a string.\");\n          } else {\n            if ([\"beacon\", \"xhr\", \"image\"].indexOf(transport) === -1) {\n              console.warn(\"`args.transport` must be either one of these values: `beacon`, `xhr` or `image`\");\n            }\n            fieldObject.transport = transport;\n          }\n        }\n        _this._gaCommand(\"send\", fieldObject);\n      }\n    });\n    _defineProperty(this, \"send\", function (fieldObject) {\n      _this._gaCommand(\"send\", fieldObject);\n    });\n    this.reset();\n  }\n  _createClass(GA4, [{\n    key: \"gtag\",\n    value: function gtag() {\n      this._gtag.apply(this, arguments);\n    }\n  }]);\n  return GA4;\n}();\nexports.GA4 = GA4;\nvar _default = new GA4();\nexports[\"default\"] = _default;"], "names": [], "mappings": "AAAA;AAEA,OAAO,cAAc,CAAC,SAAS,cAAc;IAC3C,OAAO;AACT;AACA,OAAO,CAAC,UAAU,GAAG,QAAQ,GAAG,GAAG,KAAK;AACxC,IAAI,QAAQ;AACZ,IAAI,UAAU;AACd,IAAI,YAAY;IAAC;IAAiB;IAAe;IAAc;IAAc;CAAU,EACrF,aAAa;IAAC;IAAS;CAAW,EAClC,aAAa;IAAC;IAAQ;CAAU;AAClC,SAAS,uBAAuB,GAAG;IAAI,OAAO,OAAO,IAAI,UAAU,GAAG,MAAM;QAAE,WAAW;IAAI;AAAG;AAChG,SAAS,yBAAyB,MAAM,EAAE,QAAQ;IAAI,IAAI,UAAU,MAAM,OAAO,CAAC;IAAG,IAAI,SAAS,8BAA8B,QAAQ;IAAW,IAAI,KAAK;IAAG,IAAI,OAAO,qBAAqB,EAAE;QAAE,IAAI,mBAAmB,OAAO,qBAAqB,CAAC;QAAS,IAAK,IAAI,GAAG,IAAI,iBAAiB,MAAM,EAAE,IAAK;YAAE,MAAM,gBAAgB,CAAC,EAAE;YAAE,IAAI,SAAS,OAAO,CAAC,QAAQ,GAAG;YAAU,IAAI,CAAC,OAAO,SAAS,CAAC,oBAAoB,CAAC,IAAI,CAAC,QAAQ,MAAM;YAAU,MAAM,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI;QAAE;IAAE;IAAE,OAAO;AAAQ;AAC3e,SAAS,8BAA8B,MAAM,EAAE,QAAQ;IAAI,IAAI,UAAU,MAAM,OAAO,CAAC;IAAG,IAAI,SAAS,CAAC;IAAG,IAAI,aAAa,OAAO,IAAI,CAAC;IAAS,IAAI,KAAK;IAAG,IAAK,IAAI,GAAG,IAAI,WAAW,MAAM,EAAE,IAAK;QAAE,MAAM,UAAU,CAAC,EAAE;QAAE,IAAI,SAAS,OAAO,CAAC,QAAQ,GAAG;QAAU,MAAM,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI;IAAE;IAAE,OAAO;AAAQ;AAClT,SAAS,QAAQ,GAAG;IAAI;IAA2B,OAAO,UAAU,cAAc,OAAO,UAAU,YAAY,OAAO,OAAO,QAAQ,GAAG,SAAU,GAAG;QAAI,OAAO,OAAO;IAAK,IAAI,SAAU,GAAG;QAAI,OAAO,OAAO,cAAc,OAAO,UAAU,IAAI,WAAW,KAAK,UAAU,QAAQ,OAAO,SAAS,GAAG,WAAW,OAAO;IAAK,GAAG,QAAQ;AAAM;AAC/U,SAAS,mBAAmB,GAAG;IAAI,OAAO,mBAAmB,QAAQ,iBAAiB,QAAQ,4BAA4B,QAAQ;AAAsB;AACxJ,SAAS;IAAuB,MAAM,IAAI,UAAU;AAAyI;AAC7L,SAAS,iBAAiB,IAAI;IAAI,IAAI,OAAO,WAAW,eAAe,IAAI,CAAC,OAAO,QAAQ,CAAC,IAAI,QAAQ,IAAI,CAAC,aAAa,IAAI,MAAM,OAAO,MAAM,IAAI,CAAC;AAAO;AAC7J,SAAS,mBAAmB,GAAG;IAAI,IAAI,MAAM,OAAO,CAAC,MAAM,OAAO,kBAAkB;AAAM;AAC1F,SAAS,QAAQ,MAAM,EAAE,cAAc;IAAI,IAAI,OAAO,OAAO,IAAI,CAAC;IAAS,IAAI,OAAO,qBAAqB,EAAE;QAAE,IAAI,UAAU,OAAO,qBAAqB,CAAC;QAAS,kBAAkB,CAAC,UAAU,QAAQ,MAAM,CAAC,SAAU,GAAG;YAAI,OAAO,OAAO,wBAAwB,CAAC,QAAQ,KAAK,UAAU;QAAE,EAAE,GAAG,KAAK,IAAI,CAAC,KAAK,CAAC,MAAM;IAAU;IAAE,OAAO;AAAM;AACpV,SAAS,cAAc,MAAM;IAAI,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAAK;QAAE,IAAI,SAAS,QAAQ,SAAS,CAAC,EAAE,GAAG,SAAS,CAAC,EAAE,GAAG,CAAC;QAAG,IAAI,IAAI,QAAQ,OAAO,SAAS,CAAC,GAAG,OAAO,CAAC,SAAU,GAAG;YAAI,gBAAgB,QAAQ,KAAK,MAAM,CAAC,IAAI;QAAG,KAAK,OAAO,yBAAyB,GAAG,OAAO,gBAAgB,CAAC,QAAQ,OAAO,yBAAyB,CAAC,WAAW,QAAQ,OAAO,SAAS,OAAO,CAAC,SAAU,GAAG;YAAI,OAAO,cAAc,CAAC,QAAQ,KAAK,OAAO,wBAAwB,CAAC,QAAQ;QAAO;IAAI;IAAE,OAAO;AAAQ;AACzf,SAAS,eAAe,GAAG,EAAE,CAAC;IAAI,OAAO,gBAAgB,QAAQ,sBAAsB,KAAK,MAAM,4BAA4B,KAAK,MAAM;AAAoB;AAC7J,SAAS;IAAqB,MAAM,IAAI,UAAU;AAA8I;AAChM,SAAS,4BAA4B,CAAC,EAAE,MAAM;IAAI,IAAI,CAAC,GAAG;IAAQ,IAAI,OAAO,MAAM,UAAU,OAAO,kBAAkB,GAAG;IAAS,IAAI,IAAI,OAAO,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC;IAAI,IAAI,MAAM,YAAY,EAAE,WAAW,EAAE,IAAI,EAAE,WAAW,CAAC,IAAI;IAAE,IAAI,MAAM,SAAS,MAAM,OAAO,OAAO,MAAM,IAAI,CAAC;IAAI,IAAI,MAAM,eAAe,2CAA2C,IAAI,CAAC,IAAI,OAAO,kBAAkB,GAAG;AAAS;AAC/Z,SAAS,kBAAkB,GAAG,EAAE,GAAG;IAAI,IAAI,OAAO,QAAQ,MAAM,IAAI,MAAM,EAAE,MAAM,IAAI,MAAM;IAAE,IAAK,IAAI,IAAI,GAAG,OAAO,IAAI,MAAM,MAAM,IAAI,KAAK,IAAK,IAAI,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE;IAAE,OAAO;AAAM;AAClL,SAAS,sBAAsB,GAAG,EAAE,CAAC;IAAI,IAAI,KAAK,QAAQ,MAAM,OAAO,eAAe,OAAO,UAAU,GAAG,CAAC,OAAO,QAAQ,CAAC,IAAI,GAAG,CAAC,aAAa;IAAE,IAAI,QAAQ,IAAI;QAAE,IAAI,IAAI,IAAI,IAAI,IAAI,OAAO,EAAE,EAAE,KAAK,CAAC,GAAG,KAAK,CAAC;QAAG,IAAI;YAAE,IAAI,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,MAAM,GAAG;gBAAE,IAAI,OAAO,QAAQ,IAAI;gBAAQ,KAAK,CAAC;YAAG,OAAO,MAAO,CAAC,CAAC,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,EAAE,IAAI,KAAK,CAAC,KAAK,IAAI,CAAC,GAAG,KAAK,GAAG,KAAK,MAAM,KAAK,CAAC,GAAG,KAAK,CAAC;QAAI,EAAE,OAAO,KAAK;YAAE,KAAK,CAAC,GAAG,KAAK;QAAK,SAAU;YAAE,IAAI;gBAAE,IAAI,CAAC,MAAM,QAAQ,EAAE,CAAC,SAAS,IAAI,CAAC,KAAK,EAAE,CAAC,SAAS,IAAI,OAAO,QAAQ,EAAE,GAAG;YAAQ,SAAU;gBAAE,IAAI,IAAI,MAAM;YAAI;QAAE;QAAE,OAAO;IAAM;AAAE;AACjlB,SAAS,gBAAgB,GAAG;IAAI,IAAI,MAAM,OAAO,CAAC,MAAM,OAAO;AAAK;AACpE,SAAS,gBAAgB,QAAQ,EAAE,WAAW;IAAI,IAAI,CAAC,CAAC,oBAAoB,WAAW,GAAG;QAAE,MAAM,IAAI,UAAU;IAAsC;AAAE;AACxJ,SAAS,kBAAkB,MAAM,EAAE,KAAK;IAAI,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;QAAE,IAAI,aAAa,KAAK,CAAC,EAAE;QAAE,WAAW,UAAU,GAAG,WAAW,UAAU,IAAI;QAAO,WAAW,YAAY,GAAG;QAAM,IAAI,WAAW,YAAY,WAAW,QAAQ,GAAG;QAAM,OAAO,cAAc,CAAC,QAAQ,eAAe,WAAW,GAAG,GAAG;IAAa;AAAE;AAC5U,SAAS,aAAa,WAAW,EAAE,UAAU,EAAE,WAAW;IAAI,IAAI,YAAY,kBAAkB,YAAY,SAAS,EAAE;IAAa,IAAI,aAAa,kBAAkB,aAAa;IAAc,OAAO,cAAc,CAAC,aAAa,aAAa;QAAE,UAAU;IAAM;IAAI,OAAO;AAAa;AAC5R,SAAS,gBAAgB,GAAG,EAAE,GAAG,EAAE,KAAK;IAAI,MAAM,eAAe;IAAM,IAAI,OAAO,KAAK;QAAE,OAAO,cAAc,CAAC,KAAK,KAAK;YAAE,OAAO;YAAO,YAAY;YAAM,cAAc;YAAM,UAAU;QAAK;IAAI,OAAO;QAAE,GAAG,CAAC,IAAI,GAAG;IAAO;IAAE,OAAO;AAAK;AAC3O,SAAS,eAAe,GAAG;IAAI,IAAI,MAAM,aAAa,KAAK;IAAW,OAAO,QAAQ,SAAS,WAAW,MAAM,OAAO;AAAM;AAC5H,SAAS,aAAa,KAAK,EAAE,IAAI;IAAI,IAAI,QAAQ,WAAW,YAAY,UAAU,MAAM,OAAO;IAAO,IAAI,OAAO,KAAK,CAAC,OAAO,WAAW,CAAC;IAAE,IAAI,SAAS,WAAW;QAAE,IAAI,MAAM,KAAK,IAAI,CAAC,OAAO,QAAQ;QAAY,IAAI,QAAQ,SAAS,UAAU,OAAO;QAAK,MAAM,IAAI,UAAU;IAAiD;IAAE,OAAO,CAAC,SAAS,WAAW,SAAS,MAAM,EAAE;AAAQ;AAC5X;;;;AAIA,GACA;;;;;;;;;;;;;;;;;;;CAmBC,GACD;;;;;;;;;CASC,GACD;;;;;;CAMC,GACD,IAAI,MAAM,WAAW,GAAE;IACrB,SAAS;QACP,IAAI,QAAQ,IAAI;QAChB,gBAAgB,IAAI,EAAE;QACtB,gBAAgB,IAAI,EAAE,SAAS;YAC7B,MAAM,aAAa,GAAG;YACtB,MAAM,SAAS,GAAG;YAClB,MAAM,qBAAqB;YAC3B,MAAM,YAAY,GAAG;YACrB,MAAM,UAAU,GAAG;YACnB,MAAM,UAAU,GAAG,EAAE;QACvB;QACA,gBAAgB,IAAI,EAAE,SAAS;YAC7B,IAAK,IAAI,OAAO,UAAU,MAAM,EAAE,OAAO,IAAI,MAAM,OAAO,OAAO,GAAG,OAAO,MAAM,OAAQ;gBACvF,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC,KAAK;YAC9B;YACA,IAAI,CAAC,MAAM,SAAS,EAAE;gBACpB,IAAI,MAAM,UAAU,EAAE;oBACpB,MAAM,UAAU,CAAC,IAAI,CAAC;gBACxB,OAAO;oBACL,KAAK,CAAC,UAAU,CAAC,KAAK,CAAC,KAAK,GAAG;gBACjC;YACF,OAAO;gBACL,MAAM,UAAU,CAAC,IAAI,CAAC;YACxB;QACF;QACA,gBAAgB,IAAI,EAAE,WAAW,SAAU,iBAAiB,EAAE,KAAK;YACjE,IAAI,UAAU,UAAU,MAAM,GAAG,KAAK,SAAS,CAAC,EAAE,KAAK,YAAY,SAAS,CAAC,EAAE,GAAG;YAClF,IAAI,OAAO,WAAW,eAAe,OAAO,aAAa,aAAa;gBACpE;YACF;YACA,IAAI,CAAC,MAAM,YAAY,EAAE;gBACvB,+CAA+C;gBAC/C,IAAI,SAAS,SAAS,aAAa,CAAC;gBACpC,OAAO,KAAK,GAAG;gBACf,OAAO,GAAG,GAAG,GAAG,MAAM,CAAC,SAAS,QAAQ,MAAM,CAAC;gBAC/C,IAAI,OAAO;oBACT,OAAO,YAAY,CAAC,SAAS;gBAC/B;gBACA,SAAS,IAAI,CAAC,WAAW,CAAC;gBAC1B,OAAO,SAAS,GAAG,OAAO,SAAS,IAAI,EAAE;gBACzC,OAAO,IAAI,GAAG,SAAS;oBACrB,OAAO,SAAS,CAAC,IAAI,CAAC;gBACxB;gBACA,MAAM,YAAY,GAAG;YACvB;QACF;QACA,gBAAgB,IAAI,EAAE,kBAAkB,SAAU,SAAS;YACzD,IAAI,CAAC,WAAW;gBACd;YACF;YACA,IAAI,YAAY;gBACd,4GAA4G;gBAC5G,wGAAwG;gBACxG,cAAc;gBACd,eAAe;gBACf,cAAc;gBACd,aAAa;gBACb,yBAAyB;gBACzB,QAAQ;gBACR,UAAU;gBACV,aAAa;gBACb,qFAAqF;gBACrF,eAAe;gBACf,eAAe;gBACf,eAAe;gBACf,eAAe;gBACf,eAAe;gBACf,4DAA4D;gBAC5D,iBAAiB;gBACjB,+BAA+B;gBAC/B,gBAAgB;gBAChB,MAAM;gBACN,aAAa;YACf;YACA,IAAI,cAAc,OAAO,OAAO,CAAC,WAAW,MAAM,CAAC,SAAU,IAAI,EAAE,IAAI;gBACrE,IAAI,QAAQ,eAAe,MAAM,IAC/B,MAAM,KAAK,CAAC,EAAE,EACd,QAAQ,KAAK,CAAC,EAAE;gBAClB,IAAI,SAAS,CAAC,IAAI,EAAE;oBAClB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG;gBACzB,OAAO;oBACL,IAAI,CAAC,IAAI,GAAG;gBACd;gBACA,OAAO;YACT,GAAG,CAAC;YACJ,OAAO;QACT;QACA,gBAAgB,IAAI,EAAE,cAAc,SAAU,iBAAiB;YAC7D,IAAI,UAAU,UAAU,MAAM,GAAG,KAAK,SAAS,CAAC,EAAE,KAAK,YAAY,SAAS,CAAC,EAAE,GAAG,CAAC;YACnF,IAAI,CAAC,mBAAmB;gBACtB,MAAM,IAAI,MAAM;YAClB;YACA,IAAI,cAAc,OAAO,sBAAsB,WAAW;gBAAC;oBACzD,YAAY;gBACd;aAAE,GAAG;YACL,MAAM,qBAAqB,GAAG,WAAW,CAAC,EAAE,CAAC,UAAU;YACvD,IAAI,YAAY,QAAQ,SAAS,EAC/B,cAAc,QAAQ,WAAW,EACjC,QAAQ,QAAQ,KAAK,EACrB,oBAAoB,QAAQ,QAAQ,EACpC,WAAW,sBAAsB,KAAK,IAAI,QAAQ,mBAClD,UAAU,QAAQ,OAAO;YAC3B,MAAM,SAAS,GAAG;YAClB,IAAI,CAAC,UAAU;gBACb,MAAM,OAAO,CAAC,MAAM,qBAAqB,EAAE,OAAO;YACpD;YACA,IAAI,CAAC,MAAM,aAAa,EAAE;gBACxB,MAAM,KAAK,CAAC,MAAM,IAAI;gBACtB,YAAY,OAAO,CAAC,SAAU,MAAM;oBAClC,IAAI,oBAAoB,cAAc,cAAc,cAAc,CAAC,GAAG,MAAM,cAAc,CAAC,cAAc,cAAc,CAAC,GAAG,YAAY,OAAO,SAAS,KAAK,cAAc,OAAO,WAAW;oBAC5L,IAAI,OAAO,IAAI,CAAC,mBAAmB,MAAM,EAAE;wBACzC,MAAM,KAAK,CAAC,UAAU,OAAO,UAAU,EAAE;oBAC3C,OAAO;wBACL,MAAM,KAAK,CAAC,UAAU,OAAO,UAAU;oBACzC;gBACF;YACF;YACA,MAAM,aAAa,GAAG;YACtB,IAAI,CAAC,UAAU;gBACb,IAAI,SAAS,mBAAmB,MAAM,UAAU;gBAChD,MAAM,UAAU,GAAG,EAAE;gBACrB,MAAM,UAAU,GAAG;gBACnB,MAAO,OAAO,MAAM,CAAE;oBACpB,IAAI,QAAQ,OAAO,KAAK;oBACxB,MAAM,KAAK,CAAC,KAAK,CAAC,OAAO,mBAAmB;oBAC5C,IAAI,KAAK,CAAC,EAAE,KAAK,OAAO;wBACtB,MAAM,UAAU,GAAG;oBACrB;gBACF;YACF;QACF;QACA,gBAAgB,IAAI,EAAE,OAAO,SAAU,YAAY;YACjD,IAAI,CAAC,cAAc;gBACjB,QAAQ,IAAI,CAAC;gBACb;YACF;YACA,IAAI,QAAQ,kBAAkB,UAAU;gBACtC,QAAQ,IAAI,CAAC;gBACb;YACF;YACA,IAAI,OAAO,IAAI,CAAC,cAAc,MAAM,KAAK,GAAG;gBAC1C,QAAQ,IAAI,CAAC;YACf;YACA,MAAM,UAAU,CAAC,OAAO;QAC1B;QACA,gBAAgB,IAAI,EAAE,uBAAuB,SAAU,aAAa,EAAE,WAAW,EAAE,UAAU,EAAE,UAAU,EAAE,YAAY;YACrH,MAAM,KAAK,CAAC,SAAS,aAAa,cAAc,cAAc;gBAC5D,gBAAgB;gBAChB,aAAa;gBACb,OAAO;YACT,GAAG,gBAAgB;gBACjB,iBAAiB,aAAa,cAAc;YAC9C,IAAI,MAAM,cAAc,CAAC;QAC3B;QACA,gBAAgB,IAAI,EAAE,iCAAiC;YACrD,IAAK,IAAI,QAAQ,UAAU,MAAM,EAAE,OAAO,IAAI,MAAM,QAAQ,QAAQ,GAAG,QAAQ,OAAO,QAAS;gBAC7F,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC,MAAM;YAChC;YACA,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,UAAU;gBAC/B,MAAM,mBAAmB,CAAC,KAAK,CAAC,OAAO,mBAAmB,KAAK,KAAK,CAAC;YACvE,OAAO;gBACL,IAAI,SAAS,IAAI,CAAC,EAAE,EAClB,gBAAgB,OAAO,aAAa,EACpC,cAAc,OAAO,WAAW,EAChC,aAAa,OAAO,UAAU,EAC9B,aAAa,OAAO,UAAU,EAC9B,UAAU,OAAO,OAAO,EACxB,OAAO,yBAAyB,QAAQ;gBAC1C,MAAM,mBAAmB,CAAC,eAAe,aAAa,YAAY,YAAY;YAChF;QACF;QACA,gBAAgB,IAAI,EAAE,wBAAwB,SAAU,cAAc,EAAE,SAAS,EAAE,WAAW,EAAE,WAAW;YACzG,MAAM,KAAK,CAAC,SAAS,mBAAmB;gBACtC,MAAM;gBACN,OAAO;gBACP,gBAAgB;gBAChB,aAAa;YACf;QACF;QACA,gBAAgB,IAAI,EAAE,0BAA0B,SAAU,IAAI,EAAE,YAAY;YAC1E,IAAI,gBAAgB,OAAO,IAAI,CAAC,cAAc,MAAM,EAAE;gBACpD,IAAI,uBAAuB,MAAM,cAAc,CAAC,eAC9C,QAAQ,qBAAqB,KAAK,EAClC,WAAW,qBAAqB,QAAQ,EACxC,OAAO,yBAAyB,sBAAsB;gBACxD,MAAM,KAAK,CAAC,SAAS,aAAa,cAAc,cAAc,cAAc,cAAc,CAAC,GAAG,QAAQ;oBACpG,WAAW;gBACb,IAAI,SAAS;oBACX,YAAY;gBACd,IAAI,YAAY;oBACd,eAAe;gBACjB,IAAI;YACN,OAAO,IAAI,MAAM;gBACf,MAAM,KAAK,CAAC,SAAS,aAAa;oBAChC,WAAW;gBACb;YACF,OAAO;gBACL,MAAM,KAAK,CAAC,SAAS;YACvB;QACF;QACA,gBAAgB,IAAI,EAAE,oCAAoC;YACxD,IAAK,IAAI,QAAQ,UAAU,MAAM,EAAE,OAAO,IAAI,MAAM,QAAQ,QAAQ,GAAG,QAAQ,OAAO,QAAS;gBAC7F,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC,MAAM;YAChC;YACA,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,UAAU;gBAC/B,MAAM,sBAAsB,CAAC,KAAK,CAAC,OAAO,mBAAmB,KAAK,KAAK,CAAC;YAC1E,OAAO;gBACL,IAAI,UAAU,IAAI,CAAC,EAAE,EACnB,OAAO,QAAQ,IAAI,EACnB,UAAU,QAAQ,OAAO,EACzB,OAAO,yBAAyB,SAAS;gBAC3C,MAAM,sBAAsB,CAAC,MAAM;YACrC;QACF;QACA,gBAAgB,IAAI,EAAE,kBAAkB;YACtC,IAAK,IAAI,QAAQ,UAAU,MAAM,EAAE,OAAO,IAAI,MAAM,QAAQ,QAAQ,GAAG,QAAQ,OAAO,QAAS;gBAC7F,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC,MAAM;YAChC;YACA,IAAI,UAAU,OAAO,IAAI,CAAC,EAAE,KAAK,WAAW,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,CAAC,OAAO;YACrE,OAAQ;gBACN,KAAK;oBACH,MAAM,6BAA6B,CAAC,KAAK,CAAC,OAAO;oBACjD;gBACF,KAAK;oBACH,MAAM,gCAAgC,CAAC,KAAK,CAAC,OAAO;oBACpD;gBACF,KAAK;oBACH,MAAM,oBAAoB,CAAC,KAAK,CAAC,OAAO,mBAAmB,KAAK,KAAK,CAAC;oBACtE;gBACF,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;oBACH,QAAQ,IAAI,CAAC,6BAA6B,MAAM,CAAC;oBACjD;gBACF;oBACE,QAAQ,IAAI,CAAC,+BAA+B,MAAM,CAAC;YACvD;QACF;QACA,gBAAgB,IAAI,EAAE,iBAAiB;YACrC,IAAK,IAAI,QAAQ,UAAU,MAAM,EAAE,OAAO,IAAI,MAAM,QAAQ,QAAQ,GAAG,QAAQ,OAAO,QAAS;gBAC7F,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC,MAAM;YAChC;YACA,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,UAAU;gBAC/B,IAAI,CAAC,EAAE,GAAG,gBAAgB,CAAC,GAAG,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE;YAChD;YACA,MAAM,KAAK,CAAC,OAAO,MAAM,cAAc,CAAC,IAAI,CAAC,EAAE;QACjD;QACA,gBAAgB,IAAI,EAAE,cAAc,SAAU,OAAO;YACnD,IAAK,IAAI,QAAQ,UAAU,MAAM,EAAE,OAAO,IAAI,MAAM,QAAQ,IAAI,QAAQ,IAAI,IAAI,QAAQ,GAAG,QAAQ,OAAO,QAAS;gBACjH,IAAI,CAAC,QAAQ,EAAE,GAAG,SAAS,CAAC,MAAM;YACpC;YACA,OAAQ;gBACN,KAAK;oBACH,MAAM,cAAc,CAAC,KAAK,CAAC,OAAO;oBAClC;gBACF,KAAK;oBACH,MAAM,aAAa,CAAC,KAAK,CAAC,OAAO;oBACjC;gBACF;oBACE,QAAQ,IAAI,CAAC,0BAA0B,MAAM,CAAC;YAClD;QACF;QACA,gBAAgB,IAAI,EAAE,MAAM;YAC1B,IAAK,IAAI,QAAQ,UAAU,MAAM,EAAE,OAAO,IAAI,MAAM,QAAQ,QAAQ,GAAG,QAAQ,OAAO,QAAS;gBAC7F,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC,MAAM;YAChC;YACA,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,UAAU;gBAC/B,MAAM,UAAU,CAAC,KAAK,CAAC,OAAO;YAChC,OAAO;gBACL,IAAI,gBAAgB,IAAI,CAAC,EAAE;gBAC3B,MAAM,KAAK,CAAC,OAAO,MAAM,qBAAqB,EAAE,aAAa,SAAU,QAAQ;oBAC7E,MAAM,UAAU,GAAG;oBACnB,IAAI,SAAS,MAAM,UAAU;oBAC7B,cAAc;wBACZ,KAAK,SAAS,IAAI,QAAQ;4BACxB,OAAO,aAAa,aAAa,WAAW,aAAa,eAAe,MAAM,qBAAqB,GAAG,aAAa,eAAe,MAAM;wBAC1I;oBACF;oBACA,MAAO,OAAO,MAAM,CAAE;wBACpB,IAAI,QAAQ,OAAO,KAAK;wBACxB,MAAM,KAAK,CAAC,KAAK,CAAC,OAAO,mBAAmB;oBAC9C;gBACF;gBACA,MAAM,UAAU,GAAG;YACrB;YACA,OAAO,MAAM,EAAE;QACjB;QACA,gBAAgB,IAAI,EAAE,SAAS,SAAU,aAAa,EAAE,MAAM;YAC5D,IAAI,OAAO,kBAAkB,UAAU;gBACrC,MAAM,KAAK,CAAC,SAAS,eAAe,MAAM,cAAc,CAAC;YAC3D,OAAO;gBACL,IAAI,SAAS,cAAc,MAAM,EAC/B,WAAW,cAAc,QAAQ,EACjC,QAAQ,cAAc,KAAK,EAC3B,QAAQ,cAAc,KAAK,EAC3B,iBAAiB,cAAc,cAAc,EAC7C,YAAY,cAAc,SAAS;gBACrC,IAAI,CAAC,YAAY,CAAC,QAAQ;oBACxB,QAAQ,IAAI,CAAC;oBACb;gBACF;gBAEA,kBAAkB;gBAClB,IAAI,cAAc;oBAChB,SAAS;oBACT,eAAe,CAAC,GAAG,OAAO,CAAC,UAAU,EAAE;oBACvC,aAAa,CAAC,GAAG,OAAO,CAAC,UAAU,EAAE;gBACvC;gBAEA,kBAAkB;gBAClB,IAAI,OAAO;oBACT,YAAY,UAAU,GAAG,CAAC,GAAG,OAAO,CAAC,UAAU,EAAE;gBACnD;gBACA,IAAI,OAAO,UAAU,aAAa;oBAChC,IAAI,OAAO,UAAU,UAAU;wBAC7B,QAAQ,IAAI,CAAC;oBACf,OAAO;wBACL,YAAY,UAAU,GAAG;oBAC3B;gBACF;gBACA,IAAI,OAAO,mBAAmB,aAAa;oBACzC,IAAI,OAAO,mBAAmB,WAAW;wBACvC,QAAQ,IAAI,CAAC;oBACf,OAAO;wBACL,YAAY,cAAc,GAAG;oBAC/B;gBACF;gBACA,IAAI,OAAO,cAAc,aAAa;oBACpC,IAAI,OAAO,cAAc,UAAU;wBACjC,QAAQ,IAAI,CAAC;oBACf,OAAO;wBACL,IAAI;4BAAC;4BAAU;4BAAO;yBAAQ,CAAC,OAAO,CAAC,eAAe,CAAC,GAAG;4BACxD,QAAQ,IAAI,CAAC;wBACf;wBACA,YAAY,SAAS,GAAG;oBAC1B;gBACF;gBACA,MAAM,UAAU,CAAC,QAAQ;YAC3B;QACF;QACA,gBAAgB,IAAI,EAAE,QAAQ,SAAU,WAAW;YACjD,MAAM,UAAU,CAAC,QAAQ;QAC3B;QACA,IAAI,CAAC,KAAK;IACZ;IACA,aAAa,KAAK;QAAC;YACjB,KAAK;YACL,OAAO,SAAS;gBACd,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE;YACzB;QACF;KAAE;IACF,OAAO;AACT;AACA,QAAQ,GAAG,GAAG;AACd,IAAI,WAAW,IAAI;AACnB,OAAO,CAAC,UAAU,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1537, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Development/BuzzEdge/buzzedge-frontend/node_modules/react-ga4/dist/index.js"], "sourcesContent": ["\"use strict\";\n\nfunction _typeof(obj) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }, _typeof(obj); }\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = exports.ReactGAImplementation = void 0;\nvar _ga = _interopRequireWildcard(require(\"./ga4\"));\nfunction _getRequireWildcardCache(nodeInterop) { if (typeof WeakMap !== \"function\") return null; var cacheBabelInterop = new WeakMap(); var cacheNodeInterop = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(nodeInterop) { return nodeInterop ? cacheNodeInterop : cacheBabelInterop; })(nodeInterop); }\nfunction _interopRequireWildcard(obj, nodeInterop) { if (!nodeInterop && obj && obj.__esModule) { return obj; } if (obj === null || _typeof(obj) !== \"object\" && typeof obj !== \"function\") { return { \"default\": obj }; } var cache = _getRequireWildcardCache(nodeInterop); if (cache && cache.has(obj)) { return cache.get(obj); } var newObj = {}; var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var key in obj) { if (key !== \"default\" && Object.prototype.hasOwnProperty.call(obj, key)) { var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null; if (desc && (desc.get || desc.set)) { Object.defineProperty(newObj, key, desc); } else { newObj[key] = obj[key]; } } } newObj[\"default\"] = obj; if (cache) { cache.set(obj, newObj); } return newObj; }\nvar ReactGAImplementation = _ga.GA4;\nexports.ReactGAImplementation = ReactGAImplementation;\nvar _default = _ga[\"default\"];\nexports[\"default\"] = _default;"], "names": [], "mappings": "AAAA;AAEA,SAAS,QAAQ,GAAG;IAAI;IAA2B,OAAO,UAAU,cAAc,OAAO,UAAU,YAAY,OAAO,OAAO,QAAQ,GAAG,SAAU,GAAG;QAAI,OAAO,OAAO;IAAK,IAAI,SAAU,GAAG;QAAI,OAAO,OAAO,cAAc,OAAO,UAAU,IAAI,WAAW,KAAK,UAAU,QAAQ,OAAO,SAAS,GAAG,WAAW,OAAO;IAAK,GAAG,QAAQ;AAAM;AAC/U,OAAO,cAAc,CAAC,SAAS,cAAc;IAC3C,OAAO;AACT;AACA,OAAO,CAAC,UAAU,GAAG,QAAQ,qBAAqB,GAAG,KAAK;AAC1D,IAAI,MAAM;AACV,SAAS,yBAAyB,WAAW;IAAI,IAAI,OAAO,YAAY,YAAY,OAAO;IAAM,IAAI,oBAAoB,IAAI;IAAW,IAAI,mBAAmB,IAAI;IAAW,OAAO,CAAC,2BAA2B,SAAS,yBAAyB,WAAW;QAAI,OAAO,cAAc,mBAAmB;IAAmB,CAAC,EAAE;AAAc;AAC9U,SAAS,wBAAwB,GAAG,EAAE,WAAW;IAAI,IAAI,CAAC,eAAe,OAAO,IAAI,UAAU,EAAE;QAAE,OAAO;IAAK;IAAE,IAAI,QAAQ,QAAQ,QAAQ,SAAS,YAAY,OAAO,QAAQ,YAAY;QAAE,OAAO;YAAE,WAAW;QAAI;IAAG;IAAE,IAAI,QAAQ,yBAAyB;IAAc,IAAI,SAAS,MAAM,GAAG,CAAC,MAAM;QAAE,OAAO,MAAM,GAAG,CAAC;IAAM;IAAE,IAAI,SAAS,CAAC;IAAG,IAAI,wBAAwB,OAAO,cAAc,IAAI,OAAO,wBAAwB;IAAE,IAAK,IAAI,OAAO,IAAK;QAAE,IAAI,QAAQ,aAAa,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,MAAM;YAAE,IAAI,OAAO,wBAAwB,OAAO,wBAAwB,CAAC,KAAK,OAAO;YAAM,IAAI,QAAQ,CAAC,KAAK,GAAG,IAAI,KAAK,GAAG,GAAG;gBAAE,OAAO,cAAc,CAAC,QAAQ,KAAK;YAAO,OAAO;gBAAE,MAAM,CAAC,IAAI,GAAG,GAAG,CAAC,IAAI;YAAE;QAAE;IAAE;IAAE,MAAM,CAAC,UAAU,GAAG;IAAK,IAAI,OAAO;QAAE,MAAM,GAAG,CAAC,KAAK;IAAS;IAAE,OAAO;AAAQ;AAC1yB,IAAI,wBAAwB,IAAI,GAAG;AACnC,QAAQ,qBAAqB,GAAG;AAChC,IAAI,WAAW,GAAG,CAAC,UAAU;AAC7B,OAAO,CAAC,UAAU,GAAG", "ignoreList": [0], "debugId": null}}]}