import { apiClient } from "@/lib/api";
import {
  BlogListResponse,
  BlogPostResponse,
  SearchResponse,
  CategoryResponse,
  BlogPostSummary,
} from "@/types/blog";

export class BlogService {
  /**
   * Get all published blog posts with pagination
   */
  static async getAllPosts(
    page: number = 1,
    limit: number = 10
  ): Promise<BlogListResponse> {
    return apiClient.get<BlogListResponse>(
      "/blogs",
      {
        page: page.toString(),
        limit: limit.toString(),
      },
      { cache: true, ttl: 300 } // 5 minutes cache
    );
  }

  /**
   * Get a single blog post by slug
   */
  static async getPostBySlug(slug: string): Promise<BlogPostResponse> {
    return apiClient.get<BlogPostResponse>(
      `/blogs/${slug}`,
      undefined,
      { cache: true, ttl: 600 } // 10 minutes cache
    );
  }

  /**
   * Search blog posts
   */
  static async searchPosts(
    query: string,
    page: number = 1,
    limit: number = 10
  ): Promise<SearchResponse> {
    return apiClient.get<SearchResponse>(
      "/blogs/search",
      {
        q: query,
        page: page.toString(),
        limit: limit.toString(),
      },
      { cache: true, ttl: 60 } // 1 minute cache for search
    );
  }

  /**
   * Get posts by category
   */
  static async getPostsByCategory(
    category: string,
    page: number = 1,
    limit: number = 10
  ): Promise<CategoryResponse> {
    return apiClient.get<CategoryResponse>(
      `/blogs/category/${category}`,
      {
        page: page.toString(),
        limit: limit.toString(),
      },
      { cache: true, ttl: 300 } // 5 minutes cache
    );
  }

  /**
   * Increment post view count
   */
  static async incrementPostViews(slug: string): Promise<{ views: number }> {
    return apiClient.post(`/blogs/${slug}/view`);
  }

  /**
   * Get featured posts (most viewed)
   */
  static async getFeaturedPosts(limit: number = 5): Promise<BlogPostSummary[]> {
    const response = await this.getAllPosts(1, limit);
    return response.data.posts;
  }

  /**
   * Get recent posts
   */
  static async getRecentPosts(limit: number = 5): Promise<BlogPostSummary[]> {
    const response = await this.getAllPosts(1, limit);
    return response.data.posts;
  }
}

// Utility functions for client-side data processing
export const blogUtils = {
  /**
   * Format date for display
   */
  formatDate: (dateString: string): string => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  },

  /**
   * Calculate reading time based on content length
   */
  calculateReadingTime: (content: string): number => {
    const wordsPerMinute = 200;
    const wordCount = content.split(/\s+/).length;
    return Math.ceil(wordCount / wordsPerMinute);
  },

  /**
   * Get category color class
   */
  getCategoryColor: (category: string): string => {
    switch (category?.toLowerCase()) {
      case "ai tools":
        return "bg-blue-100 text-blue-800";
      case "productivity":
        return "bg-green-100 text-green-800";
      case "developer tools":
        return "bg-purple-100 text-purple-800";
      case "design tools":
        return "bg-pink-100 text-pink-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  },

  /**
   * Generate SEO-friendly URL slug
   */
  generateSlug: (title: string): string => {
    return title
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, "-")
      .replace(/(^-|-$)/g, "");
  },

  /**
   * Truncate text to specified length
   */
  truncateText: (text: string, maxLength: number): string => {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength).replace(/\s+\S*$/, "") + "...";
  },
};
