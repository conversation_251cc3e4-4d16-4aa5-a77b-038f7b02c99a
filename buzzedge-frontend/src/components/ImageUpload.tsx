'use client';

import { useCallback, useState } from 'react';
import { useDropzone } from 'react-dropzone';
import Image from 'next/image';

interface ImageUploadProps {
  value?: string;
  onChange: (url: string) => void;
  onRemove?: () => void;
  disabled?: boolean;
}

export function ImageUpload({ value, onChange, onRemove, disabled }: ImageUploadProps) {
  const [isUploading, setIsUploading] = useState(false);

  const onDrop = useCallback(async (acceptedFiles: File[]) => {
    if (acceptedFiles.length === 0) return;

    const file = acceptedFiles[0];
    setIsUploading(true);

    try {
      // For demo purposes, we'll use a placeholder service
      // In production, you'd upload to your own server or cloud storage
      const formData = new FormData();
      formData.append('file', file);

      // Simulate upload delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      // For demo, we'll use a placeholder image URL
      // In production, this would be the actual uploaded image URL
      const demoUrl = `https://images.unsplash.com/photo-1486312338219-ce68d2c6f44d?w=800&h=600&fit=crop&crop=center`;
      onChange(demoUrl);
    } catch (error) {
      console.error('Upload failed:', error);
      alert('Upload failed. Please try again.');
    } finally {
      setIsUploading(false);
    }
  }, [onChange]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/*': ['.jpeg', '.jpg', '.png', '.gif', '.webp']
    },
    maxFiles: 1,
    disabled: disabled || isUploading,
  });

  if (value) {
    return (
      <div className="relative">
        <div className="relative w-full h-64 rounded-lg overflow-hidden border border-gray-300">
          <Image
            src={value}
            alt="Uploaded image"
            fill
            className="object-cover"
          />
        </div>
        {onRemove && (
          <button
            onClick={onRemove}
            disabled={disabled}
            className="absolute top-2 right-2 bg-red-500 text-white rounded-full w-8 h-8 flex items-center justify-center hover:bg-red-600 disabled:opacity-50"
          >
            ✕
          </button>
        )}
        <div className="mt-2 text-sm text-gray-500">
          Click to change image
        </div>
      </div>
    );
  }

  return (
    <div
      {...getRootProps()}
      className={`
        border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors
        ${isDragActive ? 'border-blue-500 bg-blue-50' : 'border-gray-300 hover:border-gray-400'}
        ${disabled || isUploading ? 'opacity-50 cursor-not-allowed' : ''}
      `}
    >
      <input {...getInputProps()} />
      
      {isUploading ? (
        <div className="flex flex-col items-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mb-4"></div>
          <p className="text-gray-600">Uploading image...</p>
        </div>
      ) : (
        <div className="flex flex-col items-center">
          <div className="text-4xl mb-4">📷</div>
          {isDragActive ? (
            <p className="text-blue-600 font-medium">Drop the image here...</p>
          ) : (
            <div>
              <p className="text-gray-600 font-medium mb-2">
                Drag & drop an image here, or click to select
              </p>
              <p className="text-sm text-gray-500">
                Supports: JPEG, PNG, GIF, WebP (max 10MB)
              </p>
            </div>
          )}
        </div>
      )}
    </div>
  );
}

// URL Input Alternative
interface ImageUrlInputProps {
  value?: string;
  onChange: (url: string) => void;
  placeholder?: string;
}

export function ImageUrlInput({ value, onChange, placeholder = 'Enter image URL...' }: ImageUrlInputProps) {
  const [url, setUrl] = useState(value || '');
  const [isValidating, setIsValidating] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!url.trim()) return;

    setIsValidating(true);
    
    // Validate image URL
    try {
      const img = new window.Image();
      img.onload = () => {
        onChange(url);
        setIsValidating(false);
      };
      img.onerror = () => {
        alert('Invalid image URL. Please check the URL and try again.');
        setIsValidating(false);
      };
      img.src = url;
    } catch (error) {
      alert('Invalid image URL. Please check the URL and try again.');
      setIsValidating(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-3">
      <div className="flex space-x-2">
        <input
          type="url"
          value={url}
          onChange={(e) => setUrl(e.target.value)}
          placeholder={placeholder}
          className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        />
        <button
          type="submit"
          disabled={!url.trim() || isValidating}
          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isValidating ? 'Validating...' : 'Add'}
        </button>
      </div>
      
      {value && (
        <div className="relative w-full h-32 rounded-lg overflow-hidden border border-gray-300">
          <Image
            src={value}
            alt="Preview"
            fill
            className="object-cover"
          />
        </div>
      )}
    </form>
  );
}
