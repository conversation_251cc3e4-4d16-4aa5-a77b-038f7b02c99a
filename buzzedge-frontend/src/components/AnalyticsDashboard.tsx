'use client';

import { useState, useEffect } from 'react';
import { analyticsUtils } from '@/lib/analytics';

interface AnalyticsData {
  total: {
    events: number;
    pageViews: number;
    userActions: number;
    contentInteractions: number;
  };
  today: {
    events: number;
    pageViews: number;
    userActions: number;
  };
  week: {
    events: number;
    pageViews: number;
    userActions: number;
  };
  month: {
    events: number;
    pageViews: number;
    userActions: number;
  };
}

interface PopularPage {
  path: string;
  views: number;
}

export function AnalyticsDashboard() {
  const [data, setData] = useState<AnalyticsData | null>(null);
  const [popularPages, setPopularPages] = useState<PopularPage[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    loadAnalyticsData();
  }, []);

  const loadAnalyticsData = () => {
    try {
      setIsLoading(true);
      const summary = analyticsUtils.getSummary();
      const popular = analyticsUtils.getPopularPages(10);
      
      setData(summary);
      setPopularPages(popular);
    } catch (error) {
      console.error('Failed to load analytics data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const clearAnalytics = () => {
    if (confirm('Are you sure you want to clear all analytics data? This action cannot be undone.')) {
      analyticsUtils.clearData();
      loadAnalyticsData();
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 mb-8">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="bg-white overflow-hidden shadow rounded-lg">
                <div className="p-5">
                  <div className="h-6 bg-gray-300 rounded w-20 mb-2"></div>
                  <div className="h-8 bg-gray-300 rounded w-16"></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (!data) {
    return (
      <div className="text-center py-12">
        <div className="text-gray-500">
          <div className="text-4xl mb-4">📊</div>
          <h3 className="text-lg font-medium mb-2">No Analytics Data</h3>
          <p>Start browsing your site to collect analytics data.</p>
        </div>
      </div>
    );
  }

  const statCards = [
    {
      name: 'Total Page Views',
      value: data.total.pageViews,
      change: data.today.pageViews,
      changeLabel: 'today',
      icon: '👁️',
      color: 'bg-blue-500',
    },
    {
      name: 'User Actions',
      value: data.total.userActions,
      change: data.today.userActions,
      changeLabel: 'today',
      icon: '🎯',
      color: 'bg-green-500',
    },
    {
      name: 'Content Interactions',
      value: data.total.contentInteractions,
      change: data.week.pageViews - data.today.pageViews,
      changeLabel: 'this week',
      icon: '💬',
      color: 'bg-purple-500',
    },
    {
      name: 'Total Events',
      value: data.total.events,
      change: data.month.events - data.week.events,
      changeLabel: 'this month',
      icon: '📈',
      color: 'bg-orange-500',
    },
  ];

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Analytics Dashboard</h2>
          <p className="mt-1 text-sm text-gray-500">
            Track your website performance and user engagement
          </p>
        </div>
        <div className="flex space-x-3">
          <button
            onClick={loadAnalyticsData}
            className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50"
          >
            Refresh
          </button>
          <button
            onClick={clearAnalytics}
            className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
          >
            Clear Data
          </button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
        {statCards.map((stat) => (
          <div key={stat.name} className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className={`${stat.color} rounded-md p-3`}>
                    <span className="text-white text-xl">{stat.icon}</span>
                  </div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">
                      {stat.name}
                    </dt>
                    <dd className="flex items-baseline">
                      <div className="text-2xl font-semibold text-gray-900">
                        {stat.value.toLocaleString()}
                      </div>
                      <div className="ml-2 flex items-baseline text-sm">
                        <span className="text-gray-500">
                          +{stat.change} {stat.changeLabel}
                        </span>
                      </div>
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Time Period Stats */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
            Performance Over Time
          </h3>
          <div className="grid grid-cols-1 gap-5 sm:grid-cols-3">
            <div className="text-center">
              <div className="text-2xl font-semibold text-gray-900">
                {data.today.pageViews}
              </div>
              <div className="text-sm text-gray-500">Page Views Today</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-semibold text-gray-900">
                {data.week.pageViews}
              </div>
              <div className="text-sm text-gray-500">Page Views This Week</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-semibold text-gray-900">
                {data.month.pageViews}
              </div>
              <div className="text-sm text-gray-500">Page Views This Month</div>
            </div>
          </div>
        </div>
      </div>

      {/* Popular Pages */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
            Most Popular Pages
          </h3>
          {popularPages.length > 0 ? (
            <div className="space-y-3">
              {popularPages.map((page, index) => (
                <div key={page.path} className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div className="flex-shrink-0 w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center text-sm font-medium text-gray-600">
                      {index + 1}
                    </div>
                    <div className="ml-3">
                      <div className="text-sm font-medium text-gray-900">
                        {page.path === '/' ? 'Homepage' : page.path}
                      </div>
                      <div className="text-sm text-gray-500">
                        {page.path}
                      </div>
                    </div>
                  </div>
                  <div className="text-sm font-medium text-gray-900">
                    {page.views} views
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-6 text-gray-500">
              <div className="text-2xl mb-2">📄</div>
              <p>No page view data available yet.</p>
            </div>
          )}
        </div>
      </div>

      {/* Real-time Stats */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
            Real-time Analytics
          </h3>
          <div className="grid grid-cols-1 gap-5 sm:grid-cols-2">
            <div>
              <div className="text-sm font-medium text-gray-500 mb-2">
                Events Today
              </div>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Page Views</span>
                  <span className="text-sm font-medium">{data.today.pageViews}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">User Actions</span>
                  <span className="text-sm font-medium">{data.today.userActions}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Total Events</span>
                  <span className="text-sm font-medium">{data.today.events}</span>
                </div>
              </div>
            </div>
            <div>
              <div className="text-sm font-medium text-gray-500 mb-2">
                Growth Metrics
              </div>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Weekly Growth</span>
                  <span className="text-sm font-medium text-green-600">
                    +{((data.week.pageViews / Math.max(data.month.pageViews - data.week.pageViews, 1)) * 100).toFixed(1)}%
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Monthly Active</span>
                  <span className="text-sm font-medium">{data.month.pageViews}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Engagement Rate</span>
                  <span className="text-sm font-medium">
                    {data.total.pageViews > 0 ? ((data.total.userActions / data.total.pageViews) * 100).toFixed(1) : 0}%
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
