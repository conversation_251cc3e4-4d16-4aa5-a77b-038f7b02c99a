'use client';

import { useEffect } from 'react';
import { useAnalytics, useContentTracking } from '@/lib/analytics';

interface BlogPostAnalyticsProps {
  postId: string;
  postTitle: string;
  category: string;
}

export function BlogPostAnalytics({ postId, postTitle, category }: BlogPostAnalyticsProps) {
  const { trackEvent, trackPageView } = useAnalytics();
  const { trackShare, trackLike } = useContentTracking('blog_post', postId);

  useEffect(() => {
    // Track page view
    trackPageView(window.location.pathname, postTitle);
    
    // Track blog post view
    trackEvent('blog_post_view', {
      post_id: postId,
      post_title: postTitle,
      category: category,
    });

    // Track reading time
    const startTime = Date.now();
    
    return () => {
      const readingTime = Math.round((Date.now() - startTime) / 1000);
      trackEvent('blog_post_reading_time', {
        post_id: postId,
        reading_time_seconds: readingTime,
      });
    };
  }, [postId, postTitle, category, trackEvent, trackPageView]);

  // Track scroll depth
  useEffect(() => {
    let maxScrollDepth = 0;
    
    const handleScroll = () => {
      const scrollTop = window.pageYOffset;
      const docHeight = document.documentElement.scrollHeight - window.innerHeight;
      const scrollPercent = Math.round((scrollTop / docHeight) * 100);
      
      if (scrollPercent > maxScrollDepth) {
        maxScrollDepth = scrollPercent;
        
        // Track milestone scroll depths
        if (scrollPercent >= 25 && maxScrollDepth < 25) {
          trackEvent('blog_post_scroll', { post_id: postId, depth: 25 });
        } else if (scrollPercent >= 50 && maxScrollDepth < 50) {
          trackEvent('blog_post_scroll', { post_id: postId, depth: 50 });
        } else if (scrollPercent >= 75 && maxScrollDepth < 75) {
          trackEvent('blog_post_scroll', { post_id: postId, depth: 75 });
        } else if (scrollPercent >= 90 && maxScrollDepth < 90) {
          trackEvent('blog_post_scroll', { post_id: postId, depth: 90 });
        }
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, [postId, trackEvent]);

  return null; // This component doesn't render anything
}

// Analytics tracking for blog cards
interface BlogCardAnalyticsProps {
  postId: string;
  postTitle: string;
  position: number;
}

export function BlogCardAnalytics({ postId, postTitle, position }: BlogCardAnalyticsProps) {
  const { trackEvent } = useAnalytics();

  const handleClick = () => {
    trackEvent('blog_card_click', {
      post_id: postId,
      post_title: postTitle,
      position: position,
    });
  };

  const handleView = () => {
    trackEvent('blog_card_view', {
      post_id: postId,
      post_title: postTitle,
      position: position,
    });
  };

  useEffect(() => {
    // Track when card comes into view
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            handleView();
            observer.unobserve(entry.target);
          }
        });
      },
      { threshold: 0.5 }
    );

    const cardElement = document.querySelector(`[data-post-id="${postId}"]`);
    if (cardElement) {
      observer.observe(cardElement);
    }

    return () => observer.disconnect();
  }, [postId]);

  return {
    onClick: handleClick,
  };
}

// Search analytics
export function useSearchAnalytics() {
  const { trackEvent } = useAnalytics();

  const trackSearch = (query: string, resultsCount: number) => {
    trackEvent('search_performed', {
      search_query: query,
      results_count: resultsCount,
    });
  };

  const trackSearchResultClick = (query: string, postId: string, position: number) => {
    trackEvent('search_result_click', {
      search_query: query,
      post_id: postId,
      position: position,
    });
  };

  return {
    trackSearch,
    trackSearchResultClick,
  };
}

// Navigation analytics
export function useNavigationAnalytics() {
  const { trackUserAction } = useAnalytics();

  const trackMenuClick = (menuItem: string) => {
    trackUserAction('menu_click', 'navigation', menuItem);
  };

  const trackCategoryClick = (category: string) => {
    trackUserAction('category_click', 'navigation', category);
  };

  const trackLogoClick = () => {
    trackUserAction('logo_click', 'navigation');
  };

  return {
    trackMenuClick,
    trackCategoryClick,
    trackLogoClick,
  };
}
