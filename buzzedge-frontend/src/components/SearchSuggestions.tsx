'use client';

import { useState, useEffect, useRef } from 'react';
import Link from 'next/link';
import { BlogService } from '@/services/blogService';
import { BlogPostSummary } from '@/types/blog';

interface SearchSuggestionsProps {
  query: string;
  onSelect: () => void;
}

export function SearchSuggestions({ query, onSelect }: SearchSuggestionsProps) {
  const [suggestions, setSuggestions] = useState<BlogPostSummary[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isVisible, setIsVisible] = useState(false);
  const timeoutRef = useRef<NodeJS.Timeout>();

  useEffect(() => {
    // Clear previous timeout
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    // Don't search for very short queries
    if (query.trim().length < 2) {
      setSuggestions([]);
      setIsVisible(false);
      return;
    }

    // Debounce the search
    timeoutRef.current = setTimeout(async () => {
      setIsLoading(true);
      try {
        const response = await BlogService.searchPosts(query.trim(), 1, 5);
        setSuggestions(response.data.posts);
        setIsVisible(response.data.posts.length > 0);
      } catch (error) {
        console.error('Failed to fetch search suggestions:', error);
        setSuggestions([]);
        setIsVisible(false);
      } finally {
        setIsLoading(false);
      }
    }, 300); // 300ms debounce

    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [query]);

  if (!isVisible && !isLoading) {
    return null;
  }

  return (
    <div className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-md shadow-lg z-50 max-h-96 overflow-y-auto">
      {isLoading ? (
        <div className="p-4 text-center">
          <div className="inline-flex items-center">
            <svg className="animate-spin -ml-1 mr-3 h-4 w-4 text-gray-400" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" />
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
            </svg>
            <span className="text-sm text-gray-500">Searching...</span>
          </div>
        </div>
      ) : suggestions.length > 0 ? (
        <>
          <div className="p-2 border-b border-gray-100">
            <p className="text-xs text-gray-500 font-medium uppercase tracking-wide">
              Suggestions
            </p>
          </div>
          <div className="py-1">
            {suggestions.map((post) => (
              <Link
                key={post._id}
                href={`/blog/${post.slug}`}
                onClick={onSelect}
                className="block px-4 py-3 hover:bg-gray-50 transition-colors"
              >
                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0">
                    <img
                      src={post.featuredImage.url}
                      alt={post.featuredImage.alt}
                      className="w-12 h-12 rounded object-cover"
                    />
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900 line-clamp-1">
                      {post.title}
                    </p>
                    <p className="text-xs text-gray-500 line-clamp-2 mt-1">
                      {post.excerpt}
                    </p>
                    <div className="flex items-center mt-2 space-x-2">
                      <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800">
                        {post.category}
                      </span>
                      <span className="text-xs text-gray-400">
                        {post.analytics.views} views
                      </span>
                    </div>
                  </div>
                </div>
              </Link>
            ))}
          </div>
          <div className="p-2 border-t border-gray-100">
            <Link
              href={`/search?q=${encodeURIComponent(query)}`}
              onClick={onSelect}
              className="block w-full text-center py-2 text-sm text-blue-600 hover:text-blue-800 font-medium"
            >
              View all results for "{query}"
            </Link>
          </div>
        </>
      ) : null}
    </div>
  );
}
