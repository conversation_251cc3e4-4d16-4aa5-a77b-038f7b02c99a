import { Metadata } from 'next';

interface SEOProps {
  title: string;
  description: string;
  keywords?: string[];
  canonicalUrl?: string;
  ogImage?: string;
  ogType?: 'website' | 'article';
  publishedTime?: string;
  modifiedTime?: string;
  author?: string;
  category?: string;
  tags?: string[];
  noIndex?: boolean;
}

export function generateMetadata({
  title,
  description,
  keywords = [],
  canonicalUrl,
  ogImage = '/og-default.jpg',
  ogType = 'website',
  publishedTime,
  modifiedTime,
  author,
  category,
  tags = [],
  noIndex = false
}: SEOProps): Metadata {
  const siteName = 'BuzzEdge';
  const siteUrl = process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3002';
  const fullTitle = title.includes(siteName) ? title : `${title} | ${siteName}`;
  const fullCanonicalUrl = canonicalUrl ? `${siteUrl}${canonicalUrl}` : siteUrl;
  const fullOgImage = ogImage.startsWith('http') ? ogImage : `${siteUrl}${ogImage}`;

  // Combine default keywords with page-specific ones
  const defaultKeywords = [
    'tech reviews',
    'tool comparisons',
    'software reviews',
    'productivity tools',
    'AI tools',
    'developer tools',
    'design tools',
    'BuzzEdge'
  ];
  const allKeywords = [...new Set([...keywords, ...defaultKeywords])];

  const metadata: Metadata = {
    title: fullTitle,
    description,
    keywords: allKeywords.join(', '),
    authors: author ? [{ name: author }] : [{ name: 'BuzzEdge Team' }],
    creator: 'BuzzEdge',
    publisher: 'BuzzEdge',
    robots: noIndex ? 'noindex, nofollow' : 'index, follow',
    
    // Open Graph
    openGraph: {
      title: fullTitle,
      description,
      url: fullCanonicalUrl,
      siteName,
      images: [
        {
          url: fullOgImage,
          width: 1200,
          height: 630,
          alt: title,
        }
      ],
      locale: 'en_US',
      type: ogType,
      ...(publishedTime && { publishedTime }),
      ...(modifiedTime && { modifiedTime }),
    },

    // Twitter Card
    twitter: {
      card: 'summary_large_image',
      title: fullTitle,
      description,
      images: [fullOgImage],
      creator: '@buzzedge',
      site: '@buzzedge',
    },

    // Additional meta tags
    other: {
      'article:author': author || 'BuzzEdge Team',
      ...(category && { 'article:section': category }),
      ...(tags.length > 0 && { 'article:tag': tags.join(', ') }),
    },

    // Canonical URL
    alternates: {
      canonical: fullCanonicalUrl,
    },

    // Verification tags (you can add these when you have the actual values)
    verification: {
      // google: 'your-google-verification-code',
      // yandex: 'your-yandex-verification-code',
      // yahoo: 'your-yahoo-verification-code',
    },
  };

  return metadata;
}

// Predefined SEO configurations for common pages
export const seoConfigs = {
  home: {
    title: 'BuzzEdge - Tech Tool Reviews & Comparisons',
    description: 'Your ultimate source for tech tool reviews, AI comparisons, and productivity insights. Stay ahead with our comprehensive analysis of the latest tools and platforms.',
    keywords: ['tech reviews', 'tool comparisons', 'AI tools', 'productivity', 'software reviews'],
    canonicalUrl: '/',
  },

  search: (query: string, resultCount: number) => ({
    title: `Search Results for "${query}" - BuzzEdge`,
    description: `Found ${resultCount} results for "${query}". Discover tech tool reviews and comparisons on BuzzEdge.`,
    keywords: ['search', 'tech tools', query],
    canonicalUrl: `/search?q=${encodeURIComponent(query)}`,
    noIndex: true, // Don't index search result pages
  }),

  category: (categoryName: string, postCount: number) => ({
    title: `${categoryName} Reviews & Comparisons - BuzzEdge`,
    description: `Explore ${postCount} comprehensive reviews and comparisons of ${categoryName.toLowerCase()}. Find the best tools for your needs.`,
    keywords: [categoryName.toLowerCase(), 'reviews', 'comparisons', 'tools'],
    canonicalUrl: `/category/${categoryName.toLowerCase().replace(/\s+/g, '-')}`,
  }),

  blogPost: (post: {
    title: string;
    excerpt: string;
    slug: string;
    category: string;
    tags: string[];
    publishedAt: string;
    featuredImage?: { url: string };
    seo?: {
      metaTitle?: string;
      metaDescription?: string;
      keywords?: string[];
    };
  }) => ({
    title: post.seo?.metaTitle || post.title,
    description: post.seo?.metaDescription || post.excerpt,
    keywords: post.seo?.keywords || post.tags,
    canonicalUrl: `/blog/${post.slug}`,
    ogImage: post.featuredImage?.url,
    ogType: 'article' as const,
    publishedTime: post.publishedAt,
    author: 'BuzzEdge Team',
    category: post.category,
    tags: post.tags,
  }),
};

// JSON-LD structured data generators
export const generateStructuredData = {
  website: () => ({
    '@context': 'https://schema.org',
    '@type': 'WebSite',
    name: 'BuzzEdge',
    description: 'Tech tool reviews, AI comparisons, and productivity insights',
    url: process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3002',
    potentialAction: {
      '@type': 'SearchAction',
      target: {
        '@type': 'EntryPoint',
        urlTemplate: `${process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3002'}/search?q={search_term_string}`,
      },
      'query-input': 'required name=search_term_string',
    },
  }),

  organization: () => ({
    '@context': 'https://schema.org',
    '@type': 'Organization',
    name: 'BuzzEdge',
    description: 'Tech tool reviews and comparisons platform',
    url: process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3002',
    logo: `${process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3002'}/logo.png`,
    sameAs: [
      'https://twitter.com/buzzedge',
      'https://linkedin.com/company/buzzedge',
    ],
  }),

  article: (post: {
    title: string;
    excerpt: string;
    slug: string;
    category: string;
    tags: string[];
    publishedAt: string;
    featuredImage?: { url: string };
  }) => ({
    '@context': 'https://schema.org',
    '@type': 'Article',
    headline: post.title,
    description: post.excerpt,
    image: post.featuredImage?.url,
    datePublished: post.publishedAt,
    dateModified: post.publishedAt,
    author: {
      '@type': 'Organization',
      name: 'BuzzEdge',
    },
    publisher: {
      '@type': 'Organization',
      name: 'BuzzEdge',
      logo: {
        '@type': 'ImageObject',
        url: `${process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3002'}/logo.png`,
      },
    },
    mainEntityOfPage: {
      '@type': 'WebPage',
      '@id': `${process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3002'}/blog/${post.slug}`,
    },
    articleSection: post.category,
    keywords: post.tags.join(', '),
  }),

  breadcrumb: (items: { name: string; url: string }[]) => ({
    '@context': 'https://schema.org',
    '@type': 'BreadcrumbList',
    itemListElement: items.map((item, index) => ({
      '@type': 'ListItem',
      position: index + 1,
      name: item.name,
      item: `${process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3002'}${item.url}`,
    })),
  }),
};
