"use client";

import { useState } from "react";
import { usePublicAuth } from "@/lib/publicAuth";
import { commentHelpers } from "@/lib/commentAPI";

interface CommentFormData {
  content: string;
  parentCommentId?: string;
  author: {
    name: string;
    email: string;
    avatar?: string;
    userId?: string;
  };
}

interface CommentFormProps {
  onSubmit: (commentData: CommentFormData) => Promise<void>;
  onAuthRequired: (mode: "login" | "register") => void;
  parentCommentId?: string;
  placeholder?: string;
  buttonText?: string;
}

export function CommentForm({
  onSubmit,
  onAuthRequired,
  parentCommentId,
  placeholder = "Share your thoughts...",
  buttonText = "Post Comment",
}: CommentFormProps) {
  const [content, setContent] = useState("");
  const [guestName, setGuestName] = useState("");
  const [guestEmail, setGuestEmail] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showGuestForm, setShowGuestForm] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  const { user } = usePublicAuth();

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    // Validate content
    const contentValidation = commentHelpers.validateComment(content);
    if (!contentValidation.isValid) {
      newErrors.content = contentValidation.error!;
    }

    // If not logged in, validate guest fields
    if (!user && showGuestForm) {
      const nameValidation = commentHelpers.validateName(guestName);
      if (!nameValidation.isValid) {
        newErrors.name = nameValidation.error!;
      }

      if (!guestEmail.trim()) {
        newErrors.email = "Email is required";
      } else if (!commentHelpers.validateEmail(guestEmail)) {
        newErrors.email = "Please enter a valid email";
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      const commentData = {
        content: content.trim(),
        parentCommentId,
        author: user
          ? {
              name: user.name,
              email: user.email,
              avatar: user.avatar,
              userId: user.id,
            }
          : {
              name: guestName.trim(),
              email: guestEmail.trim(),
            },
      };

      await onSubmit(commentData);

      // Reset form
      setContent("");
      if (!user) {
        setGuestName("");
        setGuestEmail("");
        setShowGuestForm(false);
      }
      setErrors({});
    } catch {
      // Error handling is done in parent component
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleGuestComment = () => {
    setShowGuestForm(true);
  };

  const handleLoginRequired = () => {
    onAuthRequired("login");
  };

  const handleRegisterRequired = () => {
    onAuthRequired("register");
  };

  return (
    <div className="bg-gray-50 rounded-lg p-6">
      <form onSubmit={handleSubmit} className="space-y-4">
        {/* Comment Content */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            {user ? `Comment as ${user.name}` : "Add a comment"}
          </label>
          <textarea
            value={content}
            onChange={(e) => setContent(e.target.value)}
            placeholder={placeholder}
            rows={4}
            className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none ${
              errors.content ? "border-red-500" : "border-gray-300"
            }`}
            maxLength={1000}
          />
          {errors.content && (
            <p className="mt-1 text-sm text-red-600">{errors.content}</p>
          )}
          <div className="mt-1 text-sm text-gray-500 text-right">
            {content.length}/1000 characters
          </div>
        </div>

        {/* Guest User Fields */}
        {!user && showGuestForm && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-4 bg-blue-50 rounded-md">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Your Name *
              </label>
              <input
                type="text"
                value={guestName}
                onChange={(e) => setGuestName(e.target.value)}
                className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                  errors.name ? "border-red-500" : "border-gray-300"
                }`}
                placeholder="Enter your name"
                maxLength={50}
              />
              {errors.name && (
                <p className="mt-1 text-sm text-red-600">{errors.name}</p>
              )}
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Your Email *
              </label>
              <input
                type="email"
                value={guestEmail}
                onChange={(e) => setGuestEmail(e.target.value)}
                className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                  errors.email ? "border-red-500" : "border-gray-300"
                }`}
                placeholder="Enter your email"
              />
              {errors.email && (
                <p className="mt-1 text-sm text-red-600">{errors.email}</p>
              )}
            </div>
            <div className="md:col-span-2">
              <p className="text-sm text-gray-600">
                Your email will not be published. It's only used for
                notifications and Gravatar.
              </p>
            </div>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            {user ? (
              <div className="flex items-center space-x-2">
                <img
                  src={user.avatar}
                  alt={user.name}
                  className="w-8 h-8 rounded-full"
                />
                <span className="text-sm text-gray-600">
                  Commenting as <span className="font-medium">{user.name}</span>
                </span>
              </div>
            ) : (
              <div className="flex items-center space-x-2 text-sm">
                <button
                  type="button"
                  onClick={handleLoginRequired}
                  className="text-blue-600 hover:text-blue-800 font-medium"
                >
                  Sign in
                </button>
                <span className="text-gray-400">or</span>
                <button
                  type="button"
                  onClick={handleRegisterRequired}
                  className="text-blue-600 hover:text-blue-800 font-medium"
                >
                  create account
                </button>
                <span className="text-gray-400">or</span>
                <button
                  type="button"
                  onClick={handleGuestComment}
                  className="text-gray-600 hover:text-gray-800"
                >
                  comment as guest
                </button>
              </div>
            )}
          </div>

          <div className="flex items-center space-x-3">
            {!user && showGuestForm && (
              <button
                type="button"
                onClick={() => {
                  setShowGuestForm(false);
                  setGuestName("");
                  setGuestEmail("");
                  setErrors({});
                }}
                className="px-4 py-2 text-gray-600 hover:text-gray-800"
              >
                Cancel
              </button>
            )}

            <button
              type="submit"
              disabled={
                isSubmitting || !content.trim() || (!user && !showGuestForm)
              }
              className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isSubmitting ? "Posting..." : buttonText}
            </button>
          </div>
        </div>

        {/* Help Text */}
        {!user && !showGuestForm && (
          <div className="text-sm text-gray-500 bg-blue-50 p-3 rounded-md">
            💡 <strong>Tip:</strong> Sign in or create an account to get
            notifications when someone replies to your comments, and to build
            your commenting reputation in our community.
          </div>
        )}
      </form>
    </div>
  );
}
