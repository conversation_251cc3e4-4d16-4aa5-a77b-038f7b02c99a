"use client";

import { useState, useEffect } from "react";
import { usePublicAuth } from "@/lib/publicAuth";
import { commentAPI, Comment, commentHelpers } from "@/lib/commentAPI";
import { UserAuth } from "./UserAuth";
import { CommentForm } from "./CommentForm";
import { CommentItem } from "./CommentItem";

interface CommentSectionProps {
  postId: string;
  postTitle: string;
}

export function CommentSection({ postId, postTitle }: CommentSectionProps) {
  const [comments, setComments] = useState<Comment[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState("");
  const [showAuth, setShowAuth] = useState(false);
  const [authMode, setAuthMode] = useState<"login" | "register">("login");
  const [sortOrder, setSortOrder] = useState<"newest" | "oldest">("newest");
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(false);
  const [totalComments, setTotalComments] = useState(0);

  const { user } = usePublicAuth();

  useEffect(() => {
    loadComments();
  }, [postId, page, sortOrder]);

  const loadComments = async () => {
    try {
      setIsLoading(true);
      const response = await commentAPI.getComments(postId, page, 10);

      if (page === 1) {
        setComments(response.data.comments);
      } else {
        setComments((prev) => [...prev, ...response.data.comments]);
      }

      setHasMore(response.data.pagination.hasNext);
      setTotalComments(response.data.pagination.totalComments);
      setError("");
    } catch (err: any) {
      setError(err.message || "Failed to load comments");
    } finally {
      setIsLoading(false);
    }
  };

  const handleCommentSubmit = async (commentData: {
    content: string;
    parentCommentId?: string;
    author: {
      name: string;
      email: string;
      avatar?: string;
      userId?: string;
    };
  }) => {
    try {
      await commentAPI.createComment({
        ...commentData,
        postId,
      });

      // Show success message
      alert("Comment submitted for moderation. It will appear once approved.");

      // Optionally reload comments to show pending comment if you want
      // loadComments();
    } catch (err: unknown) {
      alert((err as Error).message || "Failed to submit comment");
    }
  };

  const handleCommentUpdate = (commentId: string, updatedComment: Comment) => {
    setComments((prev) =>
      prev.map((comment) =>
        comment._id === commentId ? updatedComment : comment
      )
    );
  };

  const handleCommentDelete = (commentId: string) => {
    setComments((prev) => prev.filter((comment) => comment._id !== commentId));
    setTotalComments((prev) => prev - 1);
  };

  const handleLikeToggle = async (commentId: string) => {
    if (!user) {
      setAuthMode("login");
      setShowAuth(true);
      return;
    }

    try {
      const response = await commentAPI.toggleLike(commentId);

      setComments((prev) =>
        prev.map((comment) =>
          comment._id === commentId
            ? {
                ...comment,
                likes: response.data.likes,
                likedBy: response.data.hasLiked
                  ? [...comment.likedBy, user.id]
                  : comment.likedBy.filter((id) => id !== user.id),
              }
            : comment
        )
      );
    } catch (err: unknown) {
      alert((err as Error).message || "Failed to toggle like");
    }
  };

  const handleLoadMore = () => {
    setPage((prev) => prev + 1);
  };

  const handleSortChange = (newOrder: "newest" | "oldest") => {
    setSortOrder(newOrder);
    setPage(1);
  };

  const sortedComments = commentHelpers.sortComments(comments, sortOrder);

  return (
    <div className="mt-12 border-t border-gray-200 pt-8">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-2xl font-bold text-gray-900">
          Comments ({totalComments})
        </h3>

        {/* Sort Options */}
        <div className="flex items-center space-x-2">
          <span className="text-sm text-gray-500">Sort by:</span>
          <select
            value={sortOrder}
            onChange={(e) =>
              handleSortChange(e.target.value as "newest" | "oldest")
            }
            className="text-sm border border-gray-300 rounded px-2 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="newest">Newest first</option>
            <option value="oldest">Oldest first</option>
          </select>
        </div>
      </div>

      {/* Comment Form */}
      <div className="mb-8">
        <CommentForm
          onSubmit={handleCommentSubmit}
          onAuthRequired={(mode) => {
            setAuthMode(mode);
            setShowAuth(true);
          }}
        />
      </div>

      {/* Comments List */}
      <div className="space-y-6">
        {isLoading && page === 1 ? (
          <div className="flex justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        ) : error ? (
          <div className="text-center py-8">
            <div className="text-red-600 mb-2">{error}</div>
            <button
              onClick={() => {
                setPage(1);
                loadComments();
              }}
              className="text-blue-600 hover:text-blue-800"
            >
              Try again
            </button>
          </div>
        ) : sortedComments.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <div className="text-4xl mb-4">💬</div>
            <h4 className="text-lg font-medium mb-2">No comments yet</h4>
            <p>Be the first to share your thoughts on "{postTitle}"</p>
          </div>
        ) : (
          <>
            {sortedComments.map((comment) => (
              <CommentItem
                key={comment._id}
                comment={comment}
                onUpdate={handleCommentUpdate}
                onDelete={handleCommentDelete}
                onLike={handleLikeToggle}
                onReply={(parentId) => {
                  // Handle reply functionality
                  console.log("Reply to:", parentId);
                }}
                onAuthRequired={(mode) => {
                  setAuthMode(mode);
                  setShowAuth(true);
                }}
              />
            ))}

            {/* Load More Button */}
            {hasMore && (
              <div className="text-center pt-6">
                <button
                  onClick={handleLoadMore}
                  disabled={isLoading}
                  className="px-6 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50"
                >
                  {isLoading ? "Loading..." : "Load More Comments"}
                </button>
              </div>
            )}
          </>
        )}
      </div>

      {/* Authentication Modal */}
      {showAuth && (
        <UserAuth onClose={() => setShowAuth(false)} defaultMode={authMode} />
      )}
    </div>
  );
}
