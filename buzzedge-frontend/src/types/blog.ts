export interface BlogPost {
  _id: string;
  title: string;
  slug: string;
  excerpt: string;
  content: string;
  featuredImage: {
    url: string;
    alt: string;
    cloudinaryId?: string;
  };
  seo: {
    metaTitle: string;
    metaDescription: string;
    keywords: string[];
    canonicalUrl?: string;
  };
  category: string;
  tags: string[];
  toolInfo?: {
    name: string;
    website?: string;
    pricing: {
      free: boolean;
      paidPlans: string[];
    };
    rating?: number;
    pros: string[];
    cons: string[];
  };
  status: 'draft' | 'pending' | 'published' | 'archived';
  publishedAt?: string;
  scheduledFor?: string;
  author: string;
  analytics: {
    views: number;
    shares: number;
    timeOnPage: number;
    bounceRate: number;
  };
  aiGeneration: {
    prompt: string;
    model: string;
    generatedAt: string;
    qualityScore: number;
    humanEdited: boolean;
  };
  createdAt: string;
  updatedAt: string;
}

export interface BlogPostSummary {
  _id: string;
  title: string;
  slug: string;
  excerpt: string;
  featuredImage: {
    url: string;
    alt: string;
  };
  category: string;
  tags: string[];
  publishedAt: string;
  analytics: {
    views: number;
  };
}

export interface PaginationInfo {
  currentPage: number;
  totalPages: number;
  totalPosts: number;
  hasNext: boolean;
  hasPrev: boolean;
}

export interface BlogListResponse {
  success: boolean;
  data: {
    posts: BlogPostSummary[];
    pagination: PaginationInfo;
  };
}

export interface BlogPostResponse {
  success: boolean;
  data: {
    post: BlogPost;
    relatedPosts: BlogPostSummary[];
  };
}

export interface SearchResponse {
  success: boolean;
  data: {
    posts: BlogPostSummary[];
    query: string;
    pagination: PaginationInfo;
  };
}

export interface CategoryResponse {
  success: boolean;
  data: {
    posts: BlogPostSummary[];
    category: string;
    pagination: PaginationInfo;
  };
}
