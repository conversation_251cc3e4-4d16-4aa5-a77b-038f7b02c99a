'use client';

import { createContext, useContext, useEffect, ReactNode } from 'react';
import { Analytics } from '@vercel/analytics/react';
import ReactGA from 'react-ga4';

interface AnalyticsContextType {
  trackEvent: (eventName: string, parameters?: Record<string, any>) => void;
  trackPageView: (path: string, title?: string) => void;
  trackUserAction: (action: string, category: string, label?: string) => void;
  trackContentInteraction: (contentType: string, contentId: string, action: string) => void;
}

const AnalyticsContext = createContext<AnalyticsContextType | undefined>(undefined);

interface AnalyticsProviderProps {
  children: ReactNode;
  gaId?: string;
  enabled?: boolean;
}

export function AnalyticsProvider({ 
  children, 
  gaId = process.env.NEXT_PUBLIC_GA_ID,
  enabled = process.env.NODE_ENV === 'production'
}: AnalyticsProviderProps) {
  
  useEffect(() => {
    if (enabled && gaId) {
      // Initialize Google Analytics
      ReactGA.initialize(gaId);
      
      // Track initial page view
      ReactGA.send({ hitType: 'pageview', page: window.location.pathname });
    }
  }, [enabled, gaId]);

  const trackEvent = (eventName: string, parameters?: Record<string, any>) => {
    if (!enabled) {
      console.log('Analytics Event:', eventName, parameters);
      return;
    }

    // Google Analytics 4
    if (gaId) {
      ReactGA.event(eventName, parameters);
    }

    // Custom analytics (could be sent to your own API)
    if (typeof window !== 'undefined') {
      // Store analytics data locally for admin dashboard
      const analyticsData = JSON.parse(localStorage.getItem('buzzedge_analytics') || '[]');
      analyticsData.push({
        event: eventName,
        parameters,
        timestamp: new Date().toISOString(),
        url: window.location.pathname,
      });
      
      // Keep only last 1000 events
      if (analyticsData.length > 1000) {
        analyticsData.splice(0, analyticsData.length - 1000);
      }
      
      localStorage.setItem('buzzedge_analytics', JSON.stringify(analyticsData));
    }
  };

  const trackPageView = (path: string, title?: string) => {
    if (!enabled) {
      console.log('Page View:', path, title);
      return;
    }

    if (gaId) {
      ReactGA.send({ 
        hitType: 'pageview', 
        page: path,
        title: title || document.title
      });
    }

    trackEvent('page_view', {
      page_path: path,
      page_title: title || document.title,
    });
  };

  const trackUserAction = (action: string, category: string, label?: string) => {
    trackEvent('user_action', {
      action,
      category,
      label,
    });
  };

  const trackContentInteraction = (contentType: string, contentId: string, action: string) => {
    trackEvent('content_interaction', {
      content_type: contentType,
      content_id: contentId,
      action,
    });
  };

  const value = {
    trackEvent,
    trackPageView,
    trackUserAction,
    trackContentInteraction,
  };

  return (
    <AnalyticsContext.Provider value={value}>
      {children}
      {enabled && <Analytics />}
    </AnalyticsContext.Provider>
  );
}

export function useAnalytics() {
  const context = useContext(AnalyticsContext);
  if (context === undefined) {
    throw new Error('useAnalytics must be used within an AnalyticsProvider');
  }
  return context;
}

// Analytics hooks for common use cases
export function usePageTracking() {
  const { trackPageView } = useAnalytics();
  
  useEffect(() => {
    trackPageView(window.location.pathname);
  }, [trackPageView]);
}

export function useContentTracking(contentType: string, contentId: string) {
  const { trackContentInteraction } = useAnalytics();
  
  const trackView = () => trackContentInteraction(contentType, contentId, 'view');
  const trackShare = () => trackContentInteraction(contentType, contentId, 'share');
  const trackLike = () => trackContentInteraction(contentType, contentId, 'like');
  const trackComment = () => trackContentInteraction(contentType, contentId, 'comment');
  
  useEffect(() => {
    trackView();
  }, [contentType, contentId]);
  
  return {
    trackView,
    trackShare,
    trackLike,
    trackComment,
  };
}

// Analytics utilities
export const analyticsUtils = {
  // Get analytics data from localStorage
  getLocalAnalytics: (): any[] => {
    if (typeof window === 'undefined') return [];
    return JSON.parse(localStorage.getItem('buzzedge_analytics') || '[]');
  },

  // Get page views for a specific path
  getPageViews: (path: string): number => {
    const data = analyticsUtils.getLocalAnalytics();
    return data.filter(event => 
      event.event === 'page_view' && event.parameters?.page_path === path
    ).length;
  },

  // Get most popular pages
  getPopularPages: (limit: number = 10): Array<{ path: string; views: number }> => {
    const data = analyticsUtils.getLocalAnalytics();
    const pageViews = data.filter(event => event.event === 'page_view');
    
    const pathCounts: Record<string, number> = {};
    pageViews.forEach(event => {
      const path = event.parameters?.page_path || event.url;
      pathCounts[path] = (pathCounts[path] || 0) + 1;
    });

    return Object.entries(pathCounts)
      .map(([path, views]) => ({ path, views }))
      .sort((a, b) => b.views - a.views)
      .slice(0, limit);
  },

  // Get analytics summary
  getSummary: () => {
    const data = analyticsUtils.getLocalAnalytics();
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const thisWeek = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
    const thisMonth = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);

    const todayEvents = data.filter(event => new Date(event.timestamp) >= today);
    const weekEvents = data.filter(event => new Date(event.timestamp) >= thisWeek);
    const monthEvents = data.filter(event => new Date(event.timestamp) >= thisMonth);

    return {
      total: {
        events: data.length,
        pageViews: data.filter(e => e.event === 'page_view').length,
        userActions: data.filter(e => e.event === 'user_action').length,
        contentInteractions: data.filter(e => e.event === 'content_interaction').length,
      },
      today: {
        events: todayEvents.length,
        pageViews: todayEvents.filter(e => e.event === 'page_view').length,
        userActions: todayEvents.filter(e => e.event === 'user_action').length,
      },
      week: {
        events: weekEvents.length,
        pageViews: weekEvents.filter(e => e.event === 'page_view').length,
        userActions: weekEvents.filter(e => e.event === 'user_action').length,
      },
      month: {
        events: monthEvents.length,
        pageViews: monthEvents.filter(e => e.event === 'page_view').length,
        userActions: monthEvents.filter(e => e.event === 'user_action').length,
      },
    };
  },

  // Clear analytics data
  clearData: () => {
    if (typeof window !== 'undefined') {
      localStorage.removeItem('buzzedge_analytics');
    }
  },
};
