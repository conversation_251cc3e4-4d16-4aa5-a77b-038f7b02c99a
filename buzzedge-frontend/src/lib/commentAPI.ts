const API_BASE_URL =
  process.env.NEXT_PUBLIC_API_URL || "http://localhost:5001/api";

export interface Comment {
  _id: string;
  content: string;
  author: {
    name: string;
    email: string;
    avatar: string;
    userId?: string;
  };
  post: string;
  parentComment?: string;
  status: "pending" | "approved" | "rejected" | "spam";
  likes: number;
  likedBy: string[];
  isEdited: boolean;
  editedAt?: string;
  replies?: Comment[];
  createdAt: string;
  updatedAt: string;
}

export interface CommentFormData {
  content: string;
  postId: string;
  parentCommentId?: string;
  author: {
    name: string;
    email: string;
    avatar?: string;
    userId?: string;
  };
}

export interface CommentsResponse {
  success: boolean;
  data: {
    comments: Comment[];
    pagination: {
      currentPage: number;
      totalPages: number;
      totalComments: number;
      hasNext: boolean;
      hasPrev: boolean;
    };
  };
}

export interface CommentResponse {
  success: boolean;
  data: {
    comment: Comment;
  };
  message?: string;
}

export const commentAPI = {
  // Get comments for a post
  getComments: async (
    postId: string,
    page: number = 1,
    limit: number = 10
  ): Promise<CommentsResponse> => {
    const response = await fetch(
      `${API_BASE_URL}/comments/${postId}?page=${page}&limit=${limit}`,
      {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
      }
    );

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || "Failed to fetch comments");
    }

    return response.json();
  },

  // Create a new comment
  createComment: async (
    commentData: CommentFormData
  ): Promise<CommentResponse> => {
    const response = await fetch(`${API_BASE_URL}/comments`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(commentData),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || "Failed to create comment");
    }

    return response.json();
  },

  // Update a comment (authenticated users only)
  updateComment: async (
    commentId: string,
    content: string
  ): Promise<CommentResponse> => {
    const token = localStorage.getItem("buzzedge_user_token");

    if (!token) {
      throw new Error("Authentication required");
    }

    const response = await fetch(`${API_BASE_URL}/comments/${commentId}`, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify({ content }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || "Failed to update comment");
    }

    return response.json();
  },

  // Delete a comment (authenticated users only)
  deleteComment: async (
    commentId: string
  ): Promise<{ success: boolean; message: string }> => {
    const token = localStorage.getItem("buzzedge_user_token");

    if (!token) {
      throw new Error("Authentication required");
    }

    const response = await fetch(`${API_BASE_URL}/comments/${commentId}`, {
      method: "DELETE",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || "Failed to delete comment");
    }

    return response.json();
  },

  // Like/unlike a comment (authenticated users only)
  toggleLike: async (
    commentId: string
  ): Promise<{
    success: boolean;
    data: { likes: number; hasLiked: boolean };
  }> => {
    const token = localStorage.getItem("buzzedge_user_token");

    if (!token) {
      throw new Error("Authentication required");
    }

    const response = await fetch(`${API_BASE_URL}/comments/${commentId}/like`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || "Failed to toggle like");
    }

    return response.json();
  },

  // Get comment statistics (admin only)
  getStats: async (): Promise<{ success: boolean; data: { stats: any } }> => {
    const token = localStorage.getItem("buzzedge_admin_token"); // Admin token

    if (!token) {
      throw new Error("Admin authentication required");
    }

    const response = await fetch(`${API_BASE_URL}/comments/admin/stats`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || "Failed to fetch comment stats");
    }

    return response.json();
  },
};

// Helper functions
export const commentHelpers = {
  // Format comment date
  formatDate: (dateString: string): string => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

    if (diffInSeconds < 60) {
      return "just now";
    } else if (diffInSeconds < 3600) {
      const minutes = Math.floor(diffInSeconds / 60);
      return `${minutes} minute${minutes > 1 ? "s" : ""} ago`;
    } else if (diffInSeconds < 86400) {
      const hours = Math.floor(diffInSeconds / 3600);
      return `${hours} hour${hours > 1 ? "s" : ""} ago`;
    } else if (diffInSeconds < 604800) {
      const days = Math.floor(diffInSeconds / 86400);
      return `${days} day${days > 1 ? "s" : ""} ago`;
    } else {
      return date.toLocaleDateString();
    }
  },

  // Validate comment content
  validateComment: (content: string): { isValid: boolean; error?: string } => {
    if (!content.trim()) {
      return { isValid: false, error: "Comment cannot be empty" };
    }

    if (content.length > 1000) {
      return {
        isValid: false,
        error: "Comment must be less than 1000 characters",
      };
    }

    return { isValid: true };
  },

  // Validate email
  validateEmail: (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  },

  // Validate name
  validateName: (name: string): { isValid: boolean; error?: string } => {
    if (!name.trim()) {
      return { isValid: false, error: "Name is required" };
    }

    if (name.length > 50) {
      return { isValid: false, error: "Name must be less than 50 characters" };
    }

    return { isValid: true };
  },

  // Sort comments by date
  sortComments: (
    comments: Comment[],
    order: "newest" | "oldest" = "newest"
  ): Comment[] => {
    return [...comments].sort((a, b) => {
      const dateA = new Date(a.createdAt).getTime();
      const dateB = new Date(b.createdAt).getTime();

      return order === "newest" ? dateB - dateA : dateA - dateB;
    });
  },

  // Filter comments by status
  filterComments: (
    comments: Comment[],
    status: Comment["status"]
  ): Comment[] => {
    return comments.filter((comment) => comment.status === status);
  },

  // Get approved comments only
  getApprovedComments: (comments: Comment[]): Comment[] => {
    return commentHelpers.filterComments(comments, "approved");
  },

  // Count total replies for a comment
  countReplies: (comment: Comment): number => {
    if (!comment.replies) return 0;

    let count = comment.replies.length;
    comment.replies.forEach((reply) => {
      count += commentHelpers.countReplies(reply);
    });

    return count;
  },
};
