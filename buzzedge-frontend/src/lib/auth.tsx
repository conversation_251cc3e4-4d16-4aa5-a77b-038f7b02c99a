"use client";

import {
  create<PERSON>ontext,
  useContext,
  useEffect,
  useState,
  ReactNode,
} from "react";
import { apiClient } from "./api";

export interface User {
  _id: string;
  email: string;
  name: string;
  role: "admin" | "editor" | "author";
  createdAt: string;
}

export interface AuthResponse {
  success: boolean;
  data: {
    user: User;
    token: string;
  };
  message?: string;
}

export interface LoginCredentials {
  email: string;
  password: string;
}

class AuthService {
  private static TOKEN_KEY = "buzzedge_auth_token";
  private static USER_KEY = "buzzedge_user";

  // Login
  static async login(credentials: LoginCredentials): Promise<AuthResponse> {
    try {
      const response = await apiClient.post<AuthResponse>(
        "/api/auth/login",
        credentials
      );

      if (response.success && response.data.token) {
        this.setToken(response.data.token);
        this.setUser(response.data.user);
      }

      return response;
    } catch (error) {
      throw new Error("Login failed");
    }
  }

  // Logout
  static logout(): void {
    if (typeof window !== "undefined") {
      localStorage.removeItem(this.TOKEN_KEY);
      localStorage.removeItem(this.USER_KEY);
    }
  }

  // Get stored token
  static getToken(): string | null {
    if (typeof window !== "undefined") {
      return localStorage.getItem(this.TOKEN_KEY);
    }
    return null;
  }

  // Set token
  static setToken(token: string): void {
    if (typeof window !== "undefined") {
      localStorage.setItem(this.TOKEN_KEY, token);
    }
  }

  // Get stored user
  static getUser(): User | null {
    if (typeof window !== "undefined") {
      const userStr = localStorage.getItem(this.USER_KEY);
      return userStr ? JSON.parse(userStr) : null;
    }
    return null;
  }

  // Set user
  static setUser(user: User): void {
    if (typeof window !== "undefined") {
      localStorage.setItem(this.USER_KEY, JSON.stringify(user));
    }
  }

  // Check if user is authenticated
  static isAuthenticated(): boolean {
    return !!this.getToken();
  }

  // Check if user has admin role
  static isAdmin(): boolean {
    const user = this.getUser();
    return user?.role === "admin";
  }

  // Check if user can edit content
  static canEdit(): boolean {
    const user = this.getUser();
    return user?.role === "admin" || user?.role === "editor";
  }

  // Verify token with server
  static async verifyToken(): Promise<boolean> {
    try {
      const token = this.getToken();
      if (!token) return false;

      const response = await apiClient.get<{
        success: boolean;
        data: { user: User };
      }>("/api/auth/verify");

      if (response.success) {
        this.setUser(response.data.user);
        return true;
      } else {
        this.logout();
        return false;
      }
    } catch (error) {
      this.logout();
      return false;
    }
  }

  // Get authorization header
  static getAuthHeader(): Record<string, string> {
    const token = this.getToken();
    return token ? { Authorization: `Bearer ${token}` } : {};
  }
}

// Auth context hook

interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (credentials: LoginCredentials) => Promise<AuthResponse>;
  logout: () => void;
  verifyAuth: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const verifyAuth = async () => {
    setIsLoading(true);
    try {
      const isValid = await AuthService.verifyToken();
      if (isValid) {
        setUser(AuthService.getUser());
      } else {
        setUser(null);
      }
    } catch (error) {
      setUser(null);
    } finally {
      setIsLoading(false);
    }
  };

  const login = async (
    credentials: LoginCredentials
  ): Promise<AuthResponse> => {
    const response = await AuthService.login(credentials);
    if (response.success) {
      setUser(response.data.user);
    }
    return response;
  };

  const logout = () => {
    AuthService.logout();
    setUser(null);
  };

  useEffect(() => {
    verifyAuth();
  }, []);

  const value = {
    user,
    isAuthenticated: !!user,
    isLoading,
    login,
    logout,
    verifyAuth,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
}

export { AuthService };
