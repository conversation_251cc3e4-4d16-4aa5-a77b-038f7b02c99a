// API Configuration
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || "http://localhost:5001";

export class ApiError extends Error {
  constructor(message: string, public status: number, public response?: any) {
    super(message);
    this.name = "ApiError";
  }
}

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

class ApiClient {
  private baseURL: string;
  private cache: Map<string, { data: any; timestamp: number; ttl: number }>;

  constructor(baseURL: string) {
    this.baseURL = baseURL;
    this.cache = new Map();
  }

  private getCacheKey(
    endpoint: string,
    params?: Record<string, string>
  ): string {
    const url = new URL(endpoint, this.baseURL);
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        url.searchParams.append(key, value);
      });
    }
    return url.pathname + url.search;
  }

  private isValidCache(cacheEntry: {
    timestamp: number;
    ttl: number;
  }): boolean {
    return Date.now() - cacheEntry.timestamp < cacheEntry.ttl * 1000;
  }

  private setCache(key: string, data: any, ttl: number = 300): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl,
    });
  }

  private getCache(key: string): any | null {
    const entry = this.cache.get(key);
    if (entry && this.isValidCache(entry)) {
      return entry.data;
    }
    if (entry) {
      this.cache.delete(key); // Remove expired cache
    }
    return null;
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseURL}${endpoint}`;

    // Get auth token if available
    const getAuthToken = () => {
      if (typeof window !== "undefined") {
        return localStorage.getItem("buzzedge_auth_token");
      }
      return null;
    };

    const token = getAuthToken();
    const authHeaders: Record<string, string> = token
      ? { Authorization: `Bearer ${token}` }
      : {};

    const config: RequestInit = {
      headers: {
        "Content-Type": "application/json",
        ...authHeaders,
        ...options.headers,
      },
      ...options,
    };

    try {
      const response = await fetch(url, config);

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new ApiError(
          errorData.error || `HTTP ${response.status}: ${response.statusText}`,
          response.status,
          errorData
        );
      }

      const data = await response.json();
      return data;
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }

      // Network or other errors
      throw new ApiError(
        error instanceof Error ? error.message : "Network error occurred",
        0
      );
    }
  }

  async get<T>(
    endpoint: string,
    params?: Record<string, string>,
    options: { cache?: boolean; ttl?: number } = {}
  ): Promise<T> {
    const { cache = true, ttl = 300 } = options;
    const cacheKey = this.getCacheKey(endpoint, params);

    // Check cache first
    if (cache) {
      const cachedData = this.getCache(cacheKey);
      if (cachedData) {
        return cachedData;
      }
    }

    const url = new URL(endpoint, this.baseURL);
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        url.searchParams.append(key, value);
      });
    }

    const data = await this.request<T>(url.pathname + url.search);

    // Cache the response
    if (cache) {
      this.setCache(cacheKey, data, ttl);
    }

    return data;
  }

  async post<T>(endpoint: string, data?: any): Promise<T> {
    return this.request<T>(endpoint, {
      method: "POST",
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  async put<T>(endpoint: string, data?: any): Promise<T> {
    return this.request<T>(endpoint, {
      method: "PUT",
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  async delete<T>(endpoint: string): Promise<T> {
    return this.request<T>(endpoint, {
      method: "DELETE",
    });
  }

  // Cache management methods
  clearCache(): void {
    this.cache.clear();
  }

  clearCacheByPattern(pattern: string): void {
    const regex = new RegExp(pattern);
    for (const [key] of this.cache) {
      if (regex.test(key)) {
        this.cache.delete(key);
      }
    }
  }

  getCacheStats(): { size: number; keys: string[] } {
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys()),
    };
  }
}

// Create and export the API client instance
export const apiClient = new ApiClient(API_BASE_URL);

// Health check function
export const checkApiHealth = async (): Promise<boolean> => {
  try {
    await apiClient.get("/health");
    return true;
  } catch (error) {
    console.error("API health check failed:", error);
    return false;
  }
};
