import { notFound } from "next/navigation";
import Link from "next/link";
import { Header } from "@/components/Header";
import { Footer } from "@/components/Footer";
import { BlogCard } from "@/components/BlogCard";
import { BlogService } from "@/services/blogService";
import { BlogPostSummary } from "@/types/blog";
import {
  generateMetadata as generateSEOMetadata,
  seoConfigs,
  generateStructuredData,
} from "@/components/SEOHead";

interface CategoryPageProps {
  params: {
    slug: string;
  };
  searchParams: {
    page?: string;
  };
}

// Category metadata mapping
const categoryMetadata = {
  "ai-tools": {
    name: "AI Tools",
    description:
      "Comprehensive reviews and comparisons of the latest AI tools and platforms. Discover the best artificial intelligence software for your needs.",
    icon: "🤖",
    color: "from-blue-500 to-purple-600",
    bgColor: "bg-blue-50",
    textColor: "text-blue-700",
  },
  productivity: {
    name: "Productivity",
    description:
      "Boost your efficiency with our reviews of the best productivity tools, apps, and workflows. Streamline your work and get more done.",
    icon: "⚡",
    color: "from-green-500 to-teal-600",
    bgColor: "bg-green-50",
    textColor: "text-green-700",
  },
  "developer-tools": {
    name: "Developer Tools",
    description:
      "In-depth reviews of development tools, IDEs, frameworks, and platforms for software engineers and programmers.",
    icon: "💻",
    color: "from-purple-500 to-pink-600",
    bgColor: "bg-purple-50",
    textColor: "text-purple-700",
  },
  "design-tools": {
    name: "Design Tools",
    description:
      "Explore the best design tools, software, and platforms for UI/UX designers, graphic designers, and creative professionals.",
    icon: "🎨",
    color: "from-pink-500 to-rose-600",
    bgColor: "bg-pink-50",
    textColor: "text-pink-700",
  },
};

async function getCategoryPosts(
  categorySlug: string,
  page: number = 1
): Promise<{
  posts: BlogPostSummary[];
  category: string;
  pagination: {
    currentPage: number;
    totalPages: number;
    totalPosts: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
} | null> {
  try {
    const response = await BlogService.getPostsByCategory(
      categorySlug,
      page,
      12
    );
    return response.data;
  } catch (error) {
    console.error("Failed to fetch category posts:", error);
    return null;
  }
}

// Generate metadata for SEO
export async function generateMetadata({
  params,
}: {
  params: { slug: string };
}) {
  const { slug } = await params;
  const categoryInfo = categoryMetadata[slug as keyof typeof categoryMetadata];

  if (!categoryInfo) {
    return generateSEOMetadata({
      title: "Category Not Found",
      description: "The requested category could not be found.",
      noIndex: true,
    });
  }

  // Get post count for this category
  const data = await getCategoryPosts(slug, 1);
  const postCount = data?.pagination.totalPosts || 0;

  return generateSEOMetadata(seoConfigs.category(categoryInfo.name, postCount));
}

export default async function CategoryPage({
  params,
  searchParams,
}: CategoryPageProps) {
  const { slug } = await params;
  const { page } = await searchParams;
  const currentPage = parseInt(page || "1", 10);

  // Get category metadata
  const categoryInfo = categoryMetadata[slug as keyof typeof categoryMetadata];

  if (!categoryInfo) {
    notFound();
  }

  const data = await getCategoryPosts(slug, currentPage);

  if (!data) {
    notFound();
  }

  const { posts, pagination } = data;

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Breadcrumb */}
        <nav className="mb-8">
          <ol className="flex items-center space-x-2 text-sm text-gray-500">
            <li>
              <Link href="/" className="hover:text-gray-700">
                Home
              </Link>
            </li>
            <li>/</li>
            <li className="text-gray-900 font-medium">{categoryInfo.name}</li>
          </ol>
        </nav>

        {/* Category Header */}
        <div className={`${categoryInfo.bgColor} rounded-lg p-8 mb-12`}>
          <div className="max-w-3xl">
            <div className="flex items-center mb-4">
              <span className="text-4xl mr-4">{categoryInfo.icon}</span>
              <h1 className="text-4xl md:text-5xl font-bold text-gray-900">
                {categoryInfo.name}
              </h1>
            </div>
            <p className="text-xl text-gray-600 leading-relaxed mb-6">
              {categoryInfo.description}
            </p>
            <div className="flex items-center space-x-4">
              <span
                className={`px-4 py-2 ${categoryInfo.textColor} bg-white rounded-full text-sm font-medium shadow-sm`}
              >
                {pagination.totalPosts}{" "}
                {pagination.totalPosts === 1 ? "Review" : "Reviews"}
              </span>
              <span className="text-gray-500 text-sm">
                Updated regularly with the latest tools
              </span>
            </div>
          </div>
        </div>

        {/* Posts Grid */}
        {posts.length > 0 ? (
          <>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
              {posts.map((post) => (
                <BlogCard key={post._id} post={post} />
              ))}
            </div>

            {/* Pagination */}
            {pagination.totalPages > 1 && (
              <div className="flex items-center justify-between border-t border-gray-200 bg-white px-4 py-3 sm:px-6 rounded-lg">
                <div className="flex flex-1 justify-between sm:hidden">
                  {pagination.hasPrev && (
                    <Link
                      href={`/category/${slug}?page=${
                        pagination.currentPage - 1
                      }`}
                      className="relative inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50"
                    >
                      Previous
                    </Link>
                  )}
                  {pagination.hasNext && (
                    <Link
                      href={`/category/${slug}?page=${
                        pagination.currentPage + 1
                      }`}
                      className="relative ml-3 inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50"
                    >
                      Next
                    </Link>
                  )}
                </div>
                <div className="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
                  <div>
                    <p className="text-sm text-gray-700">
                      Showing{" "}
                      <span className="font-medium">
                        {(pagination.currentPage - 1) * 12 + 1}
                      </span>{" "}
                      to{" "}
                      <span className="font-medium">
                        {Math.min(
                          pagination.currentPage * 12,
                          pagination.totalPosts
                        )}
                      </span>{" "}
                      of{" "}
                      <span className="font-medium">
                        {pagination.totalPosts}
                      </span>{" "}
                      results
                    </p>
                  </div>
                  <div>
                    <nav
                      className="isolate inline-flex -space-x-px rounded-md shadow-sm"
                      aria-label="Pagination"
                    >
                      {pagination.hasPrev && (
                        <Link
                          href={`/category/${slug}?page=${
                            pagination.currentPage - 1
                          }`}
                          className="relative inline-flex items-center rounded-l-md px-2 py-2 text-gray-400 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0"
                        >
                          <span className="sr-only">Previous</span>
                          <svg
                            className="h-5 w-5"
                            viewBox="0 0 20 20"
                            fill="currentColor"
                            aria-hidden="true"
                          >
                            <path
                              fillRule="evenodd"
                              d="M12.79 5.23a.75.75 0 01-.02 1.06L8.832 10l3.938 3.71a.75.75 0 11-1.04 1.08l-4.5-4.25a.75.75 0 010-1.08l4.5-4.25a.75.75 0 011.06.02z"
                              clipRule="evenodd"
                            />
                          </svg>
                        </Link>
                      )}

                      {/* Page numbers */}
                      {Array.from(
                        { length: Math.min(5, pagination.totalPages) },
                        (_, i) => {
                          const pageNum =
                            Math.max(1, pagination.currentPage - 2) + i;
                          if (pageNum > pagination.totalPages) return null;

                          return (
                            <Link
                              key={pageNum}
                              href={`/category/${slug}?page=${pageNum}`}
                              className={`relative inline-flex items-center px-4 py-2 text-sm font-semibold ${
                                pageNum === pagination.currentPage
                                  ? "z-10 bg-blue-600 text-white focus:z-20 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600"
                                  : "text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0"
                              }`}
                            >
                              {pageNum}
                            </Link>
                          );
                        }
                      )}

                      {pagination.hasNext && (
                        <Link
                          href={`/category/${slug}?page=${
                            pagination.currentPage + 1
                          }`}
                          className="relative inline-flex items-center rounded-r-md px-2 py-2 text-gray-400 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0"
                        >
                          <span className="sr-only">Next</span>
                          <svg
                            className="h-5 w-5"
                            viewBox="0 0 20 20"
                            fill="currentColor"
                            aria-hidden="true"
                          >
                            <path
                              fillRule="evenodd"
                              d="M7.21 14.77a.75.75 0 01.02-1.06L11.168 10 7.23 6.29a.75.75 0 111.04-1.08l4.5 4.25a.75.75 0 010 1.08l-4.5 4.25a.75.75 0 01-1.06-.02z"
                              clipRule="evenodd"
                            />
                          </svg>
                        </Link>
                      )}
                    </nav>
                  </div>
                </div>
              </div>
            )}
          </>
        ) : (
          <div className="text-center py-12">
            <div className="max-w-md mx-auto">
              <span className="text-6xl">{categoryInfo.icon}</span>
              <h3 className="mt-4 text-lg font-medium text-gray-900">
                No posts yet
              </h3>
              <p className="mt-2 text-gray-500">
                We're working on adding more {categoryInfo.name.toLowerCase()}{" "}
                reviews. Check back soon!
              </p>
              <div className="mt-6">
                <Link
                  href="/"
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700"
                >
                  Browse all posts
                </Link>
              </div>
            </div>
          </div>
        )}

        {/* Related Categories */}
        <div className="mt-16 border-t border-gray-200 pt-12">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">
            Explore Other Categories
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {Object.entries(categoryMetadata)
              .filter(([key]) => key !== slug)
              .map(([key, info]) => (
                <Link
                  key={key}
                  href={`/category/${key}`}
                  className={`${info.bgColor} rounded-lg p-6 hover:shadow-md transition-shadow`}
                >
                  <div className="flex items-center mb-3">
                    <span className="text-2xl mr-3">{info.icon}</span>
                    <h3 className="text-lg font-semibold text-gray-900">
                      {info.name}
                    </h3>
                  </div>
                  <p className="text-gray-600 text-sm">{info.description}</p>
                </Link>
              ))}
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
}

// Generate static params for better performance
export async function generateStaticParams() {
  return Object.keys(categoryMetadata).map((slug) => ({
    slug,
  }));
}
