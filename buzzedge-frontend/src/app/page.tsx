import { BlogCard } from '@/components/BlogCard';
import { Header } from '@/components/Header';
import { Footer } from '@/components/Footer';

// Mock data for development
const mockPosts = [
  {
    _id: '1',
    title: 'ChatGP<PERSON> vs <PERSON>: Ultimate AI Assistant Comparison 2024',
    slug: 'chatgpt-vs-claude-ai-assistant-comparison-2024',
    excerpt: 'A comprehensive comparison between <PERSON><PERSON><PERSON><PERSON> and <PERSON>, two leading AI assistants. We analyze features, pricing, capabilities, and help you choose the right AI tool.',
    featuredImage: {
      url: 'https://images.unsplash.com/photo-1677442136019-21780ecad995?w=800',
      alt: 'AI comparison illustration'
    },
    category: 'AI Tools',
    tags: ['AI', 'ChatGPT', 'Claude', 'Comparison'],
    publishedAt: '2025-06-01T10:29:31.727Z',
    analytics: { views: 1250 }
  },
  {
    _id: '2',
    title: 'Notion AI vs ClickUp AI: Smart Workspace Comparison',
    slug: 'notion-ai-vs-clickup-ai-smart-workspace-comparison',
    excerpt: 'Compare Notion AI and ClickUp AI features, pricing, and capabilities. Find the best AI-powered workspace tool for your productivity needs.',
    featuredImage: {
      url: 'https://images.unsplash.com/photo-1611224923853-80b023f02d71?w=800',
      alt: 'Productivity workspace illustration'
    },
    category: 'Productivity',
    tags: ['Productivity', 'Notion', 'ClickUp', 'AI'],
    publishedAt: '2025-05-31T10:29:31.727Z',
    analytics: { views: 890 }
  },
  {
    _id: '3',
    title: 'GitHub Copilot vs Tabnine: AI Code Assistant Review',
    slug: 'github-copilot-vs-tabnine-ai-code-assistant-review',
    excerpt: 'Detailed comparison of GitHub Copilot and Tabnine AI coding assistants. Features, pricing, IDE support, and code quality analysis.',
    featuredImage: {
      url: 'https://images.unsplash.com/photo-1555066931-4365d14bab8c?w=800',
      alt: 'Code development illustration'
    },
    category: 'Developer Tools',
    tags: ['Development', 'GitHub', 'Copilot', 'Tabnine', 'AI'],
    publishedAt: '2025-05-30T10:29:31.727Z',
    analytics: { views: 1456 }
  }
];

export default function Home() {
  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Hero Section */}
        <section className="text-center mb-12">
          <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-4">
            BuzzEdge
          </h1>
          <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
            Your ultimate source for tech tool reviews, AI comparisons, and productivity insights. 
            Stay ahead with our comprehensive analysis of the latest tools and platforms.
          </p>
          <div className="flex flex-wrap justify-center gap-4">
            <span className="px-4 py-2 bg-blue-100 text-blue-800 rounded-full text-sm font-medium">
              AI Tools
            </span>
            <span className="px-4 py-2 bg-green-100 text-green-800 rounded-full text-sm font-medium">
              Productivity
            </span>
            <span className="px-4 py-2 bg-purple-100 text-purple-800 rounded-full text-sm font-medium">
              Developer Tools
            </span>
            <span className="px-4 py-2 bg-pink-100 text-pink-800 rounded-full text-sm font-medium">
              Design Tools
            </span>
          </div>
        </section>

        {/* Latest Posts */}
        <section>
          <h2 className="text-3xl font-bold text-gray-900 mb-8">Latest Reviews</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {mockPosts.map((post) => (
              <BlogCard key={post._id} post={post} />
            ))}
          </div>
        </section>
      </main>

      <Footer />
    </div>
  );
}
