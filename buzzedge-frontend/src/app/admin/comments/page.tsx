"use client";

import { useState, useEffect } from "react";

const API_BASE_URL =
  process.env.NEXT_PUBLIC_API_URL || "http://localhost:5001/api";

interface Comment {
  _id: string;
  content: string;
  author: {
    name: string;
    email: string;
    avatar: string;
    userId?: string;
  };
  post: {
    _id?: string;
    title?: string;
    slug?: string;
  };
  status: "pending" | "approved" | "rejected" | "spam";
  moderationScore?: number;
  moderationNotes?: string;
  likes: number;
  likedBy: string[];
  isEdited: boolean;
  editedAt?: string;
  moderatedAt?: string;
  moderatedBy?: string;
  ipAddress?: string;
  userAgent?: string;
  createdAt: string;
  updatedAt: string;
}

interface CommentStats {
  total: number;
  pending: number;
  approved: number;
  rejected: number;
  spam: number;
}

interface ModerationStats {
  _id: string;
  count: number;
}

// interface AutoModerationStats {
//   avgConfidence: number;
//   autoApproved: number;
//   autoRejected: number;
//   pendingReview: number;
// }

export default function AdminCommentsPage() {
  const [comments, setComments] = useState<Comment[]>([]);
  const [stats, setStats] = useState<CommentStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectedComments, setSelectedComments] = useState<string[]>([]);
  const [currentPage] = useState(1);
  const [isUsingFallback, setIsUsingFallback] = useState(false);

  useEffect(() => {
    const loadData = async () => {
      await fetchComments();
      await fetchStats();
    };
    loadData();
  }, [currentPage]);

  const fetchComments = async () => {
    try {
      // Get auth token from localStorage
      const token = localStorage.getItem("token");

      // Try to fetch pending comments for admin (requires auth)
      if (token) {
        try {
          const response = await fetch(
            `${API_BASE_URL}/comments/admin/pending?page=${currentPage}&limit=20`,
            {
              headers: {
                Authorization: `Bearer ${token}`,
                "Content-Type": "application/json",
              },
            }
          );

          if (response.ok) {
            const data = await response.json();
            if (data.success) {
              setComments(data.data.comments || []);
              setIsUsingFallback(false);
              return;
            }
          }
        } catch (error) {
          console.warn("Admin endpoint failed, trying fallback", error);
        }
      }

      // Fallback: Fetch recent comments from a known post for demo
      await fetchFallbackComments();
    } catch (error) {
      console.error("Failed to fetch comments:", error);
      await fetchFallbackComments();
    } finally {
      setLoading(false);
    }
  };

  const fetchFallbackComments = async () => {
    try {
      // Fetch recent comments from any post for demo purposes
      const response = await fetch(
        `${API_BASE_URL}/comments/683c33ff9f1120145342617c?page=1&limit=10`
      );
      const data = await response.json();

      if (data.success && data.data.comments) {
        // Transform the comments to include post info and add missing fields
        const transformedComments = data.data.comments.map((comment: any) => ({
          ...comment,
          likes: comment.likes || 0,
          likedBy: comment.likedBy || [],
          isEdited: comment.isEdited || false,
          post: {
            _id: "683c33ff9f1120145342617c",
            title: "ChatGPT vs Claude AI Assistant Comparison 2024",
            slug: "chatgpt-vs-claude-ai-assistant-comparison-2024",
          },
        }));
        setComments(transformedComments);
        setIsUsingFallback(true);
      } else {
        // If no comments found, show empty state
        setComments([]);
        setIsUsingFallback(true);
      }
    } catch (error) {
      console.error("Fallback fetch failed:", error);
      setComments([]);
    }
  };

  const fetchStats = async () => {
    try {
      const token = localStorage.getItem("token");

      if (token) {
        try {
          const response = await fetch(`${API_BASE_URL}/comments/admin/stats`, {
            headers: {
              Authorization: `Bearer ${token}`,
              "Content-Type": "application/json",
            },
          });

          if (response.ok) {
            const data = await response.json();
            if (data.success) {
              // Transform API response to match our interface
              const moderationStats = data.data.moderationStats || [];
              const autoStats = data.data.autoModerationStats || {};

              const statsMap = moderationStats.reduce(
                (acc: Record<string, number>, stat: ModerationStats) => {
                  acc[stat._id] = stat.count;
                  return acc;
                },
                {} as Record<string, number>
              );

              const transformedStats: CommentStats = {
                total:
                  (statsMap.approved || 0) +
                  (statsMap.pending || 0) +
                  (statsMap.rejected || 0),
                pending: statsMap.pending || 0,
                approved: statsMap.approved || 0,
                rejected: statsMap.rejected || 0,
                spam: autoStats.autoRejected || 0,
              };

              setStats(transformedStats);
              return;
            }
          }
        } catch (error) {
          console.warn("Admin stats endpoint failed, using fallback", error);
        }
      }

      // Fallback stats based on current comments
      const currentStats: CommentStats = {
        total: comments.length,
        pending: comments.filter((c) => c.status === "pending").length,
        approved: comments.filter((c) => c.status === "approved").length,
        rejected: comments.filter((c) => c.status === "rejected").length,
        spam: comments.filter((c) => c.status === "spam").length,
      };
      setStats(currentStats);
    } catch (error) {
      console.error("Failed to fetch stats:", error);
      // Default stats
      setStats({
        total: 0,
        pending: 0,
        approved: 0,
        rejected: 0,
        spam: 0,
      });
    }
  };

  const handleApprove = async (commentId: string) => {
    try {
      const token = localStorage.getItem("token");

      if (token) {
        const response = await fetch(
          `${API_BASE_URL}/comments/admin/${commentId}/approve`,
          {
            method: "POST",
            headers: {
              Authorization: `Bearer ${token}`,
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              moderationNotes: "Manually approved by admin",
            }),
          }
        );

        if (response.ok) {
          const data = await response.json();
          if (data.success) {
            // Update local state
            setComments((prev) =>
              prev.map((comment) =>
                comment._id === commentId
                  ? {
                      ...comment,
                      status: "approved" as const,
                      moderationNotes: "Manually approved by admin",
                      moderatedAt: new Date().toISOString(),
                    }
                  : comment
              )
            );
            alert("Comment approved successfully!");
            // Refresh stats
            fetchStats();
            return;
          }
        }
      }

      // Fallback to local update if API fails
      setComments((prev) =>
        prev.map((comment) =>
          comment._id === commentId
            ? {
                ...comment,
                status: "approved" as const,
                moderationNotes: "Manually approved by admin",
              }
            : comment
        )
      );
      alert("Comment approved successfully!");
    } catch (error) {
      console.error("Failed to approve comment:", error);
      alert("Failed to approve comment");
    }
  };

  const handleReject = async (commentId: string) => {
    try {
      const token = localStorage.getItem("token");

      if (token) {
        const response = await fetch(
          `${API_BASE_URL}/comments/admin/${commentId}/reject`,
          {
            method: "POST",
            headers: {
              Authorization: `Bearer ${token}`,
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              moderationNotes: "Manually rejected by admin",
            }),
          }
        );

        if (response.ok) {
          const data = await response.json();
          if (data.success) {
            // Update local state
            setComments((prev) =>
              prev.map((comment) =>
                comment._id === commentId
                  ? {
                      ...comment,
                      status: "rejected" as const,
                      moderationNotes: "Manually rejected by admin",
                      moderatedAt: new Date().toISOString(),
                    }
                  : comment
              )
            );
            alert("Comment rejected successfully!");
            // Refresh stats
            fetchStats();
            return;
          }
        }
      }

      // Fallback to local update if API fails
      setComments((prev) =>
        prev.map((comment) =>
          comment._id === commentId
            ? {
                ...comment,
                status: "rejected" as const,
                moderationNotes: "Manually rejected by admin",
              }
            : comment
        )
      );
      alert("Comment rejected successfully!");
    } catch (error) {
      console.error("Failed to reject comment:", error);
      alert("Failed to reject comment");
    }
  };

  const handleBulkAction = async (action: "approve" | "reject") => {
    if (selectedComments.length === 0) {
      alert("Please select comments first");
      return;
    }

    try {
      const token = localStorage.getItem("token");

      if (token) {
        const response = await fetch(
          `${API_BASE_URL}/comments/admin/bulk-action`,
          {
            method: "POST",
            headers: {
              Authorization: `Bearer ${token}`,
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              commentIds: selectedComments,
              action: action,
              moderationNotes: `Bulk ${action}d by admin`,
            }),
          }
        );

        if (response.ok) {
          const data = await response.json();
          if (data.success) {
            // Update local state
            setComments((prev) =>
              prev.map((comment) =>
                selectedComments.includes(comment._id)
                  ? {
                      ...comment,
                      status:
                        action === "approve"
                          ? ("approved" as const)
                          : ("rejected" as const),
                      moderationNotes: `Bulk ${action}d by admin`,
                      moderatedAt: new Date().toISOString(),
                    }
                  : comment
              )
            );
            setSelectedComments([]);
            alert(
              `${selectedComments.length} comments ${action}d successfully!`
            );
            // Refresh stats
            fetchStats();
            return;
          }
        }
      }

      // Fallback to local update if API fails
      setComments((prev) =>
        prev.map((comment) =>
          selectedComments.includes(comment._id)
            ? {
                ...comment,
                status:
                  action === "approve"
                    ? ("approved" as const)
                    : ("rejected" as const),
                moderationNotes: `Bulk ${action}d by admin`,
              }
            : comment
        )
      );
      setSelectedComments([]);
      alert(`${selectedComments.length} comments ${action}d successfully!`);
    } catch (error) {
      console.error(`Failed to ${action} comments:`, error);
      alert(`Failed to ${action} comments`);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "approved":
        return "text-green-600 bg-green-100";
      case "rejected":
        return "text-red-600 bg-red-100";
      case "pending":
        return "text-yellow-600 bg-yellow-100";
      default:
        return "text-gray-600 bg-gray-100";
    }
  };

  const getConfidenceColor = (score?: number) => {
    if (!score) return "text-gray-500";
    if (score >= 80) return "text-green-600";
    if (score >= 50) return "text-yellow-600";
    return "text-red-600";
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading comments...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                Comment Moderation
              </h1>
              <p className="mt-2 text-gray-600">
                Manage and moderate user comments with automatic AI-powered
                filtering
              </p>
            </div>
            {isUsingFallback && (
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <svg
                      className="h-5 w-5 text-yellow-400"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                    >
                      <path
                        fillRule="evenodd"
                        d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <p className="text-sm text-yellow-800">
                      <strong>Demo Mode:</strong> Showing sample data. Login as
                      admin for full functionality.
                    </p>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Stats Cards */}
        {stats && (
          <div className="grid grid-cols-1 md:grid-cols-5 gap-6 mb-8">
            <div className="bg-white rounded-lg shadow p-6">
              <div className="text-2xl font-bold text-gray-900">
                {stats.total}
              </div>
              <div className="text-sm text-gray-600">Total Comments</div>
            </div>
            <div className="bg-white rounded-lg shadow p-6">
              <div className="text-2xl font-bold text-yellow-600">
                {stats.pending}
              </div>
              <div className="text-sm text-gray-600">Pending Review</div>
            </div>
            <div className="bg-white rounded-lg shadow p-6">
              <div className="text-2xl font-bold text-green-600">
                {stats.approved}
              </div>
              <div className="text-sm text-gray-600">Approved</div>
            </div>
            <div className="bg-white rounded-lg shadow p-6">
              <div className="text-2xl font-bold text-red-600">
                {stats.rejected}
              </div>
              <div className="text-sm text-gray-600">Rejected</div>
            </div>
            <div className="bg-white rounded-lg shadow p-6">
              <div className="text-2xl font-bold text-orange-600">
                {stats.spam}
              </div>
              <div className="text-sm text-gray-600">Spam Detected</div>
            </div>
          </div>
        )}

        {/* Bulk Actions */}
        {selectedComments.length > 0 && (
          <div className="bg-white rounded-lg shadow p-4 mb-6">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">
                {selectedComments.length} comment(s) selected
              </span>
              <div className="space-x-2">
                <button
                  onClick={() => handleBulkAction("approve")}
                  className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors"
                >
                  Bulk Approve
                </button>
                <button
                  onClick={() => handleBulkAction("reject")}
                  className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
                >
                  Bulk Reject
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Comments Table */}
        <div className="bg-white rounded-lg shadow overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    <input
                      type="checkbox"
                      onChange={(e) => {
                        if (e.target.checked) {
                          setSelectedComments(comments.map((c) => c._id));
                        } else {
                          setSelectedComments([]);
                        }
                      }}
                      checked={
                        selectedComments.length === comments.length &&
                        comments.length > 0
                      }
                    />
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Comment
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Author
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Post
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    AI Score
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {comments.map((comment) => (
                  <tr key={comment._id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <input
                        type="checkbox"
                        checked={selectedComments.includes(comment._id)}
                        onChange={(e) => {
                          if (e.target.checked) {
                            setSelectedComments((prev) => [
                              ...prev,
                              comment._id,
                            ]);
                          } else {
                            setSelectedComments((prev) =>
                              prev.filter((id) => id !== comment._id)
                            );
                          }
                        }}
                      />
                    </td>
                    <td className="px-6 py-4">
                      <div className="max-w-xs">
                        <p
                          className="text-sm text-gray-900 truncate"
                          title={comment.content}
                        >
                          {comment.content}
                        </p>
                        {comment.moderationNotes && (
                          <p className="text-xs text-gray-500 mt-1">
                            {comment.moderationNotes}
                          </p>
                        )}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <img
                          src={comment.author.avatar}
                          alt={comment.author.name}
                          className="w-8 h-8 rounded-full mr-3"
                        />
                        <div>
                          <p className="text-sm font-medium text-gray-900">
                            {comment.author.name}
                          </p>
                          <p className="text-sm text-gray-500">
                            {comment.author.email}
                          </p>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <p className="text-sm text-gray-900 max-w-xs truncate">
                        {comment.post.title}
                      </p>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span
                        className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(
                          comment.status
                        )}`}
                      >
                        {comment.status}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span
                        className={`text-sm font-medium ${getConfidenceColor(
                          comment.moderationScore
                        )}`}
                      >
                        {comment.moderationScore
                          ? `${comment.moderationScore}%`
                          : "N/A"}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                      {comment.status === "pending" && (
                        <>
                          <button
                            onClick={() => handleApprove(comment._id)}
                            className="text-green-600 hover:text-green-900"
                          >
                            Approve
                          </button>
                          <button
                            onClick={() => handleReject(comment._id)}
                            className="text-red-600 hover:text-red-900"
                          >
                            Reject
                          </button>
                        </>
                      )}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Auto-Moderation Info */}
        <div className="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-blue-900 mb-4">
            🤖 Automatic Moderation System
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div>
              <h4 className="font-medium text-blue-800">
                Auto-Approve (90%+ confidence)
              </h4>
              <p className="text-blue-700">Clean content from trusted users</p>
            </div>
            <div>
              <h4 className="font-medium text-blue-800">
                Manual Review (30-90% confidence)
              </h4>
              <p className="text-blue-700">
                Suspicious content requiring human review
              </p>
            </div>
            <div>
              <h4 className="font-medium text-blue-800">
                Auto-Reject (0-30% confidence)
              </h4>
              <p className="text-blue-700">
                Spam, inappropriate, or harmful content
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
