import { Suspense } from "react";
import Link from "next/link";
import { Header } from "@/components/Header";
import { Footer } from "@/components/Footer";
import { BlogCard } from "@/components/BlogCard";
import { BlogGridSkeleton } from "@/components/LoadingSpinner";
import { BlogService } from "@/services/blogService";
import { BlogPostSummary } from "@/types/blog";
import {
  generateMetadata as generateSEOMetadata,
  seoConfigs,
} from "@/components/SEOHead";

interface SearchPageProps {
  searchParams: {
    q?: string;
    page?: string;
  };
}

async function getSearchResults(
  query: string,
  page: number = 1
): Promise<{
  posts: BlogPostSummary[];
  query: string;
  pagination: {
    currentPage: number;
    totalPages: number;
    totalPosts: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
} | null> {
  try {
    if (!query || query.trim().length === 0) {
      return null;
    }

    const response = await BlogService.searchPosts(query.trim(), page, 12);
    return response.data;
  } catch (error) {
    console.error("Failed to search posts:", error);
    return null;
  }
}

function SearchResults({
  posts,
  query,
  pagination,
}: {
  posts: BlogPostSummary[];
  query: string;
  pagination: any;
}) {
  if (posts.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="max-w-md mx-auto">
          <svg
            className="mx-auto h-12 w-12 text-gray-400"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
            />
          </svg>
          <h3 className="mt-4 text-lg font-medium text-gray-900">
            No results found
          </h3>
          <p className="mt-2 text-gray-500">
            We couldn't find any posts matching "{query}". Try searching with
            different keywords.
          </p>
          <div className="mt-6">
            <Link
              href="/"
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              Browse all posts
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
        {posts.map((post) => (
          <BlogCard key={post._id} post={post} />
        ))}
      </div>

      {/* Pagination */}
      {pagination.totalPages > 1 && (
        <div className="flex items-center justify-between border-t border-gray-200 bg-white px-4 py-3 sm:px-6 rounded-lg">
          <div className="flex flex-1 justify-between sm:hidden">
            {pagination.hasPrev && (
              <Link
                href={`/search?q=${encodeURIComponent(query)}&page=${
                  pagination.currentPage - 1
                }`}
                className="relative inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50"
              >
                Previous
              </Link>
            )}
            {pagination.hasNext && (
              <Link
                href={`/search?q=${encodeURIComponent(query)}&page=${
                  pagination.currentPage + 1
                }`}
                className="relative ml-3 inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50"
              >
                Next
              </Link>
            )}
          </div>
          <div className="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
            <div>
              <p className="text-sm text-gray-700">
                Showing{" "}
                <span className="font-medium">
                  {(pagination.currentPage - 1) * 12 + 1}
                </span>{" "}
                to{" "}
                <span className="font-medium">
                  {Math.min(pagination.currentPage * 12, pagination.totalPosts)}
                </span>{" "}
                of <span className="font-medium">{pagination.totalPosts}</span>{" "}
                results
              </p>
            </div>
            <div>
              <nav
                className="isolate inline-flex -space-x-px rounded-md shadow-sm"
                aria-label="Pagination"
              >
                {pagination.hasPrev && (
                  <Link
                    href={`/search?q=${encodeURIComponent(query)}&page=${
                      pagination.currentPage - 1
                    }`}
                    className="relative inline-flex items-center rounded-l-md px-2 py-2 text-gray-400 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0"
                  >
                    <span className="sr-only">Previous</span>
                    <svg
                      className="h-5 w-5"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                      aria-hidden="true"
                    >
                      <path
                        fillRule="evenodd"
                        d="M12.79 5.23a.75.75 0 01-.02 1.06L8.832 10l3.938 3.71a.75.75 0 11-1.04 1.08l-4.5-4.25a.75.75 0 010-1.08l4.5-4.25a.75.75 0 011.06.02z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </Link>
                )}

                {/* Page numbers */}
                {Array.from(
                  { length: Math.min(5, pagination.totalPages) },
                  (_, i) => {
                    const pageNum = Math.max(1, pagination.currentPage - 2) + i;
                    if (pageNum > pagination.totalPages) return null;

                    return (
                      <Link
                        key={pageNum}
                        href={`/search?q=${encodeURIComponent(
                          query
                        )}&page=${pageNum}`}
                        className={`relative inline-flex items-center px-4 py-2 text-sm font-semibold ${
                          pageNum === pagination.currentPage
                            ? "z-10 bg-blue-600 text-white focus:z-20 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600"
                            : "text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0"
                        }`}
                      >
                        {pageNum}
                      </Link>
                    );
                  }
                )}

                {pagination.hasNext && (
                  <Link
                    href={`/search?q=${encodeURIComponent(query)}&page=${
                      pagination.currentPage + 1
                    }`}
                    className="relative inline-flex items-center rounded-r-md px-2 py-2 text-gray-400 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0"
                  >
                    <span className="sr-only">Next</span>
                    <svg
                      className="h-5 w-5"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                      aria-hidden="true"
                    >
                      <path
                        fillRule="evenodd"
                        d="M7.21 14.77a.75.75 0 01.02-1.06L11.168 10 7.23 6.29a.75.75 0 111.04-1.08l4.5 4.25a.75.75 0 010 1.08l-4.5 4.25a.75.75 0 01-1.06-.02z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </Link>
                )}
              </nav>
            </div>
          </div>
        </div>
      )}
    </>
  );
}

// Generate metadata for SEO
export async function generateMetadata({ searchParams }: SearchPageProps) {
  const { q: query } = searchParams;

  if (!query || query.trim().length === 0) {
    return generateSEOMetadata({
      title: "Search - BuzzEdge",
      description:
        "Search for tech tool reviews, AI comparisons, and productivity insights on BuzzEdge.",
      noIndex: true,
    });
  }

  // Get search results to determine count
  const searchData = await getSearchResults(query, 1);
  const resultCount = searchData?.pagination.totalPosts || 0;

  return generateSEOMetadata(seoConfigs.search(query, resultCount));
}

export default async function SearchPage({ searchParams }: SearchPageProps) {
  const { q: query, page } = searchParams;
  const currentPage = parseInt(page || "1", 10);

  if (!query || query.trim().length === 0) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />

        <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center py-12">
            <h1 className="text-3xl font-bold text-gray-900 mb-4">Search</h1>
            <p className="text-gray-600 mb-8">
              Enter a search term to find relevant blog posts and reviews.
            </p>
            <Link
              href="/"
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700"
            >
              Browse all posts
            </Link>
          </div>
        </main>

        <Footer />
      </div>
    );
  }

  const searchData = await getSearchResults(query, currentPage);

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Search Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Search Results
          </h1>
          <p className="text-gray-600">
            {searchData ? (
              <>
                Found {searchData.pagination.totalPosts} result
                {searchData.pagination.totalPosts !== 1 ? "s" : ""} for "{query}
                "
              </>
            ) : (
              `Searching for "${query}"...`
            )}
          </p>
        </div>

        {/* Search Results */}
        <Suspense fallback={<BlogGridSkeleton count={12} />}>
          {searchData ? (
            <SearchResults
              posts={searchData.posts}
              query={searchData.query}
              pagination={searchData.pagination}
            />
          ) : (
            <div className="text-center py-12">
              <p className="text-gray-500">
                Unable to load search results. Please try again.
              </p>
            </div>
          )}
        </Suspense>
      </main>

      <Footer />
    </div>
  );
}
