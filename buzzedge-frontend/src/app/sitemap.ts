import { MetadataRoute } from 'next';
import { BlogService } from '@/services/blogService';

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3002';

  // Static pages
  const staticPages = [
    {
      url: baseUrl,
      lastModified: new Date(),
      changeFrequency: 'daily' as const,
      priority: 1,
    },
    {
      url: `${baseUrl}/search`,
      lastModified: new Date(),
      changeFrequency: 'weekly' as const,
      priority: 0.5,
    },
  ];

  // Category pages
  const categories = ['ai-tools', 'productivity', 'developer-tools', 'design-tools'];
  const categoryPages = categories.map((category) => ({
    url: `${baseUrl}/category/${category}`,
    lastModified: new Date(),
    changeFrequency: 'weekly' as const,
    priority: 0.8,
  }));

  // Blog posts
  let blogPages: MetadataRoute.Sitemap = [];
  try {
    const response = await BlogService.getAllPosts(1, 1000); // Get all posts
    blogPages = response.data.posts.map((post) => ({
      url: `${baseUrl}/blog/${post.slug}`,
      lastModified: new Date(post.publishedAt || post.createdAt),
      changeFrequency: 'monthly' as const,
      priority: 0.9,
    }));
  } catch (error) {
    console.error('Failed to fetch posts for sitemap:', error);
  }

  return [...staticPages, ...categoryPages, ...blogPages];
}
