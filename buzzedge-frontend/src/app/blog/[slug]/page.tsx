import { notFound } from "next/navigation";
import Image from "next/image";
import Link from "next/link";
import { Header } from "@/components/Header";
import { Footer } from "@/components/Footer";
import { BlogCard } from "@/components/BlogCard";
import { MarkdownRenderer } from "@/components/MarkdownRenderer";
import { BlogService, blogUtils } from "@/services/blogService";
import { BlogPost, BlogPostSummary } from "@/types/blog";

interface BlogPostPageProps {
  params: {
    slug: string;
  };
}

async function getBlogPost(slug: string): Promise<{
  post: BlogPost;
  relatedPosts: BlogPostSummary[];
} | null> {
  try {
    const response = await BlogService.getPostBySlug(slug);
    return response.data;
  } catch (error) {
    console.error("Failed to fetch blog post:", error);
    return null;
  }
}

export default async function BlogPostPage({ params }: BlogPostPageProps) {
  const { slug } = await params;
  const data = await getBlogPost(slug);

  if (!data) {
    notFound();
  }

  const { post, relatedPosts } = data;
  const readingTime = blogUtils.calculateReadingTime(post.content);

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Breadcrumb */}
        <nav className="mb-8">
          <ol className="flex items-center space-x-2 text-sm text-gray-500">
            <li>
              <Link href="/" className="hover:text-gray-700">
                Home
              </Link>
            </li>
            <li>/</li>
            <li>
              <Link
                href={`/category/${post.category
                  .toLowerCase()
                  .replace(/\s+/g, "-")}`}
                className="hover:text-gray-700"
              >
                {post.category}
              </Link>
            </li>
            <li>/</li>
            <li className="text-gray-900 font-medium">{post.title}</li>
          </ol>
        </nav>

        {/* Article Header */}
        <header className="mb-8">
          <div className="mb-4">
            <span
              className={`px-3 py-1 rounded-full text-sm font-medium ${blogUtils.getCategoryColor(
                post.category
              )}`}
            >
              {post.category}
            </span>
          </div>

          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4 leading-tight">
            {post.title}
          </h1>

          <p className="text-xl text-gray-600 mb-6 leading-relaxed">
            {post.excerpt}
          </p>

          <div className="flex items-center justify-between text-sm text-gray-500 mb-8">
            <div className="flex items-center space-x-4">
              <span>By {post.author}</span>
              <span>•</span>
              <time>
                {blogUtils.formatDate(post.publishedAt || post.createdAt)}
              </time>
              <span>•</span>
              <span>{readingTime} min read</span>
            </div>
            <div className="flex items-center space-x-4">
              <span className="flex items-center">
                <svg
                  className="w-4 h-4 mr-1"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                  />
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                  />
                </svg>
                {post.analytics.views.toLocaleString()} views
              </span>
            </div>
          </div>
        </header>

        {/* Featured Image */}
        <div className="relative h-64 md:h-96 w-full mb-8 rounded-lg overflow-hidden">
          <Image
            src={post.featuredImage.url}
            alt={post.featuredImage.alt}
            fill
            className="object-cover"
            priority
          />
        </div>

        {/* Article Content */}
        <article className="mb-12">
          <MarkdownRenderer content={post.content} />
        </article>

        {/* Tool Info Card */}
        {post.toolInfo && (
          <div className="bg-white rounded-lg shadow-md p-6 mb-12">
            <h3 className="text-xl font-bold text-gray-900 mb-4">
              Tool Summary
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-semibold text-gray-900 mb-2">Pricing</h4>
                <p className="text-gray-600 mb-2">
                  {post.toolInfo.pricing.free
                    ? "✅ Free tier available"
                    : "❌ No free tier"}
                </p>
                {post.toolInfo.pricing.paidPlans.length > 0 && (
                  <ul className="text-gray-600">
                    {post.toolInfo.pricing.paidPlans.map((plan, index) => (
                      <li key={index}>• {plan}</li>
                    ))}
                  </ul>
                )}
              </div>

              {post.toolInfo.rating && (
                <div>
                  <h4 className="font-semibold text-gray-900 mb-2">Rating</h4>
                  <div className="flex items-center">
                    <div className="flex text-yellow-400">
                      {[...Array(5)].map((_, i) => (
                        <svg
                          key={i}
                          className={`w-5 h-5 ${
                            i < Math.floor(post.toolInfo!.rating!)
                              ? "fill-current"
                              : "text-gray-300"
                          }`}
                          viewBox="0 0 20 20"
                        >
                          <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                        </svg>
                      ))}
                    </div>
                    <span className="ml-2 text-gray-600">
                      {post.toolInfo.rating}/5
                    </span>
                  </div>
                </div>
              )}
            </div>

            {(post.toolInfo.pros.length > 0 ||
              post.toolInfo.cons.length > 0) && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
                {post.toolInfo.pros.length > 0 && (
                  <div>
                    <h4 className="font-semibold text-green-700 mb-2">Pros</h4>
                    <ul className="text-gray-600">
                      {post.toolInfo.pros.map((pro, index) => (
                        <li key={index} className="flex items-start">
                          <span className="text-green-500 mr-2">✓</span>
                          {pro}
                        </li>
                      ))}
                    </ul>
                  </div>
                )}

                {post.toolInfo.cons.length > 0 && (
                  <div>
                    <h4 className="font-semibold text-red-700 mb-2">Cons</h4>
                    <ul className="text-gray-600">
                      {post.toolInfo.cons.map((con, index) => (
                        <li key={index} className="flex items-start">
                          <span className="text-red-500 mr-2">✗</span>
                          {con}
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            )}
          </div>
        )}

        {/* Tags */}
        <div className="mb-12">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Tags</h3>
          <div className="flex flex-wrap gap-2">
            {post.tags.map((tag) => (
              <span
                key={tag}
                className="px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm hover:bg-gray-200 transition-colors"
              >
                #{tag}
              </span>
            ))}
          </div>
        </div>

        {/* Related Posts */}
        {relatedPosts.length > 0 && (
          <section>
            <h3 className="text-2xl font-bold text-gray-900 mb-6">
              Related Posts
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {relatedPosts.map((relatedPost) => (
                <BlogCard key={relatedPost._id} post={relatedPost} />
              ))}
            </div>
          </section>
        )}
      </main>

      <Footer />
    </div>
  );
}

// Generate static params for better performance
export async function generateStaticParams() {
  try {
    const response = await BlogService.getAllPosts(1, 100);
    return response.data.posts.map((post) => ({
      slug: post.slug,
    }));
  } catch (error) {
    console.error("Failed to generate static params:", error);
    return [];
  }
}
