# 🚀 BuzzEdge Deployment Guide - Complete Production Setup

## 🎯 **Deployment Overview**

This guide covers deploying the BuzzEdge application with:
- **Frontend**: Next.js application (Vercel/Netlify)
- **Backend**: Node.js/Express API (Railway/Render/DigitalOcean)
- **Database**: MongoDB Atlas (already configured)
- **AI Integration**: Google Gemini API

## 📋 **Pre-Deployment Checklist**

### ✅ **Required Accounts & Services**
- [ ] **MongoDB Atlas** (✅ Already configured)
- [ ] **Google AI Studio** (✅ Gemini API ready)
- [ ] **Vercel Account** (for frontend)
- [ ] **Railway Account** (for backend) - Recommended
- [ ] **Domain Name** (optional)

### ✅ **Environment Variables Ready**
- [ ] `MONGODB_URI` (✅ Already set)
- [ ] `JWT_SECRET` (✅ Already set)
- [ ] `GEMINI_API_KEY` (optional but recommended)
- [ ] `FRONTEND_URL` (will be set during deployment)

## 🚀 **Option 1: Quick Deploy (Recommended)**

### **Step 1: Deploy Backend to Railway**

#### **1.1 Create Railway Account**
1. Go to [Railway.app](https://railway.app/)
2. Sign up with GitHub account
3. Connect your GitHub repository

#### **1.2 Deploy Backend**
```bash
# 1. Install Railway CLI
npm install -g @railway/cli

# 2. Login to Railway
railway login

# 3. Navigate to backend directory
cd buzzedge-backend

# 4. Initialize Railway project
railway init

# 5. Deploy
railway up
```

#### **1.3 Configure Environment Variables**
In Railway dashboard:
```bash
MONGODB_URI=mongodb+srv://bhavanishankar:<EMAIL>/buzzedge?retryWrites=true&w=majority&appName=Cluster0
JWT_SECRET=your-super-secret-jwt-key-here-make-it-long-and-random
NODE_ENV=production
PORT=5001
CORS_ORIGIN=https://your-frontend-domain.vercel.app
GEMINI_API_KEY=your-gemini-api-key-here
```

### **Step 2: Deploy Frontend to Vercel**

#### **2.1 Deploy to Vercel**
```bash
# 1. Install Vercel CLI
npm install -g vercel

# 2. Navigate to frontend directory
cd buzzedge-frontend

# 3. Deploy
vercel

# Follow prompts:
# - Link to existing project? No
# - Project name: buzzedge
# - Directory: ./
# - Override settings? No
```

#### **2.2 Configure Environment Variables**
In Vercel dashboard:
```bash
NEXT_PUBLIC_API_URL=https://your-backend-domain.railway.app/api
NEXT_PUBLIC_SITE_URL=https://your-frontend-domain.vercel.app
```

### **Step 3: Update CORS Settings**
Update backend environment variable:
```bash
FRONTEND_URL=https://your-frontend-domain.vercel.app
```

## 🔧 **Option 2: Alternative Platforms**

### **Backend Deployment Options**

#### **A. Render (Free Tier Available)**
1. Go to [Render.com](https://render.com/)
2. Connect GitHub repository
3. Create new Web Service
4. Select `buzzedge-backend` directory
5. Configure:
   ```
   Build Command: npm install && npm run build
   Start Command: npm start
   ```

#### **B. DigitalOcean App Platform**
1. Go to [DigitalOcean Apps](https://cloud.digitalocean.com/apps)
2. Create new app from GitHub
3. Select repository and `buzzedge-backend` folder
4. Configure environment variables

#### **C. Heroku**
```bash
# Install Heroku CLI
npm install -g heroku

# Login and create app
heroku login
heroku create buzzedge-backend

# Set environment variables
heroku config:set MONGODB_URI="your-mongodb-uri"
heroku config:set JWT_SECRET="your-jwt-secret"

# Deploy
git subtree push --prefix buzzedge-backend heroku main
```

### **Frontend Deployment Options**

#### **A. Netlify**
1. Go to [Netlify.com](https://netlify.com/)
2. Connect GitHub repository
3. Configure:
   ```
   Build command: npm run build
   Publish directory: .next
   Base directory: buzzedge-frontend
   ```

#### **B. GitHub Pages (Static Export)**
```bash
# In buzzedge-frontend/next.config.ts
const nextConfig = {
  output: 'export',
  trailingSlash: true,
  images: {
    unoptimized: true
  }
}
```

## 🔐 **Environment Variables Setup**

### **Backend Environment Variables**
```bash
# Production Backend (.env)
NODE_ENV=production
PORT=5001
MONGODB_URI=mongodb+srv://bhavanishankar:<EMAIL>/buzzedge?retryWrites=true&w=majority&appName=Cluster0
JWT_SECRET=your-super-secret-jwt-key-here-make-it-long-and-random-at-least-32-characters
FRONTEND_URL=https://your-frontend-domain.vercel.app
CORS_ORIGIN=https://your-frontend-domain.vercel.app

# Optional: AI Integration
GEMINI_API_KEY=your-gemini-api-key-here

# Optional: Email (for future features)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password
```

### **Frontend Environment Variables**
```bash
# Production Frontend (.env.local)
NEXT_PUBLIC_API_URL=https://your-backend-domain.railway.app/api
NEXT_PUBLIC_SITE_URL=https://your-frontend-domain.vercel.app
NEXT_PUBLIC_SITE_NAME=BuzzEdge
NEXT_PUBLIC_SITE_DESCRIPTION=AI-Powered Tech Tool Reviews and Comparisons

# Optional: Analytics
NEXT_PUBLIC_GA_ID=G-XXXXXXXXXX
```

## 📦 **Build Configuration**

### **Backend Build Setup**
Create `buzzedge-backend/package.json` scripts:
```json
{
  "scripts": {
    "start": "node dist/index.js",
    "build": "tsc",
    "dev": "nodemon src/index.ts",
    "postinstall": "npm run build"
  }
}
```

### **Frontend Build Setup**
Update `buzzedge-frontend/package.json`:
```json
{
  "scripts": {
    "build": "next build",
    "start": "next start",
    "dev": "next dev",
    "export": "next export"
  }
}
```

## 🌐 **Domain Configuration**

### **Custom Domain Setup**

#### **1. Frontend Domain (Vercel)**
1. Go to Vercel dashboard
2. Select your project
3. Go to Settings → Domains
4. Add your custom domain
5. Configure DNS records as instructed

#### **2. Backend Domain (Railway)**
1. Go to Railway dashboard
2. Select your service
3. Go to Settings → Domains
4. Add custom domain or use Railway subdomain

#### **3. DNS Configuration**
```
# For frontend (example.com)
Type: CNAME
Name: @
Value: cname.vercel-dns.com

# For API (api.example.com)
Type: CNAME  
Name: api
Value: your-app.railway.app
```

## 🔒 **Security Configuration**

### **Production Security Settings**

#### **Backend Security**
```typescript
// In src/index.ts
import helmet from 'helmet';
import rateLimit from 'express-rate-limit';

// Security middleware
app.use(helmet());

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100 // limit each IP to 100 requests per windowMs
});
app.use('/api/', limiter);

// CORS configuration
app.use(cors({
  origin: process.env.FRONTEND_URL,
  credentials: true
}));
```

#### **Environment Security**
- ✅ Use strong JWT secrets (32+ characters)
- ✅ Enable MongoDB IP whitelist
- ✅ Use HTTPS only in production
- ✅ Set secure cookie flags
- ✅ Enable rate limiting

## 📊 **Monitoring & Analytics**

### **Application Monitoring**
```bash
# Add to package.json
npm install @sentry/node @sentry/nextjs

# Backend monitoring
import * as Sentry from "@sentry/node";
Sentry.init({ dsn: "your-sentry-dsn" });

# Frontend monitoring  
import * as Sentry from "@sentry/nextjs";
```

### **Performance Monitoring**
- ✅ **Vercel Analytics**: Built-in performance monitoring
- ✅ **Railway Metrics**: CPU, memory, and response time tracking
- ✅ **MongoDB Atlas Monitoring**: Database performance metrics

## 🚀 **Deployment Commands**

### **Quick Deployment Script**
```bash
#!/bin/bash
# deploy.sh

echo "🚀 Deploying BuzzEdge Application..."

# Deploy Backend
echo "📦 Deploying Backend to Railway..."
cd buzzedge-backend
railway up
cd ..

# Deploy Frontend  
echo "🌐 Deploying Frontend to Vercel..."
cd buzzedge-frontend
vercel --prod
cd ..

echo "✅ Deployment Complete!"
echo "🌐 Frontend: https://your-app.vercel.app"
echo "🔧 Backend: https://your-app.railway.app"
```

## 🔍 **Post-Deployment Testing**

### **Deployment Verification Checklist**
- [ ] **Frontend loads correctly**
- [ ] **API endpoints respond**
- [ ] **Database connection works**
- [ ] **Authentication functions**
- [ ] **Comment system works**
- [ ] **Admin dashboard accessible**
- [ ] **AI moderation active**
- [ ] **CORS configured correctly**
- [ ] **HTTPS enabled**
- [ ] **Environment variables set**

### **Test Commands**
```bash
# Test API health
curl https://your-backend-domain.railway.app/api/health

# Test comment creation
curl -X POST https://your-backend-domain.railway.app/api/comments \
  -H "Content-Type: application/json" \
  -d '{"content":"Test comment","postId":"test","author":{"name":"Test","email":"<EMAIL>"}}'

# Test frontend
curl -I https://your-frontend-domain.vercel.app
```

## 🎯 **Next Steps After Deployment**

1. **✅ Set up monitoring and alerts**
2. **✅ Configure backup strategies**
3. **✅ Set up CI/CD pipelines**
4. **✅ Add custom domain**
5. **✅ Enable analytics**
6. **✅ Set up error tracking**
7. **✅ Configure email notifications**
8. **✅ Add performance monitoring**

---

**Your BuzzEdge application is now ready for production deployment!** 🚀

Choose your preferred deployment option and follow the step-by-step instructions above.
